#!/bin/bash

# MyToDoSpace 最终验证脚本
# 确保所有功能正常工作并准备生产部署

echo "🎯 MyToDoSpace 最终验证检查"
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果统计
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# 函数：执行检查
check_item() {
    local check_name=$1
    local check_command=$2
    
    echo -e "${BLUE}🔍 检查: $check_name${NC}"
    
    if eval $check_command > /dev/null 2>&1; then
        echo -e "${GREEN}✅ $check_name - 通过${NC}"
        ((PASSED_CHECKS++))
    else
        echo -e "${RED}❌ $check_name - 失败${NC}"
        ((FAILED_CHECKS++))
    fi
    ((TOTAL_CHECKS++))
}

echo -e "${YELLOW}📋 开始系统性验证...${NC}"
echo ""

# 1. 项目结构验证
echo -e "${BLUE}📁 项目结构验证${NC}"
echo "=================================================="

check_item "Domain层结构" "test -d lib/domain && test -d lib/domain/models && test -d lib/domain/repositories"
check_item "Data层结构" "test -d lib/data && test -d lib/data/datasources && test -d lib/data/repositories"
check_item "Presentation层结构" "test -d lib/features && test -d lib/features/calendar && test -d lib/features/tasks && test -d lib/features/summary"
check_item "Core层结构" "test -d lib/core && test -d lib/core/di"
check_item "App层结构" "test -d lib/app"
check_item "测试结构" "test -d test && test -d test/unit && test -d test/widget && test -d test/integration"

echo ""

# 2. 关键文件验证
echo -e "${BLUE}📄 关键文件验证${NC}"
echo "=================================================="

# Domain层文件
check_item "Task模型" "test -f lib/domain/models/task_model.dart"
check_item "SummaryReport模型" "test -f lib/domain/models/summary_report_model.dart"
check_item "TaskRepository接口" "test -f lib/domain/repositories/task_repository.dart"
check_item "SummaryRepository接口" "test -f lib/domain/repositories/summary_repository.dart"

# Data层文件
check_item "LocalDatabase" "test -f lib/data/datasources/local_database.dart"
check_item "TaskRepositoryImpl" "test -f lib/data/repositories/task_repository_impl.dart"
check_item "SummaryRepositoryImpl" "test -f lib/data/repositories/summary_repository_impl.dart"

# Presentation层文件
check_item "CalendarBloc" "test -f lib/features/calendar/bloc/calendar_bloc.dart"
check_item "TaskListBloc" "test -f lib/features/tasks/bloc/task_list_bloc.dart"
check_item "SummaryBloc" "test -f lib/features/summary/bloc/summary_bloc.dart"
check_item "CalendarPage" "test -f lib/features/calendar/presentation/calendar_page.dart"
check_item "TaskEditorDialog" "test -f lib/features/tasks/presentation/task_editor_dialog.dart"
check_item "SummaryPage" "test -f lib/features/summary/presentation/summary_page.dart"

# 生成文件验证
check_item "Freezed生成文件" "test -f lib/domain/models/task_model.freezed.dart"
check_item "Injectable配置" "test -f lib/core/di/injection.config.dart"
check_item "Drift生成文件" "test -f lib/data/datasources/local_database.g.dart"

echo ""

# 3. 依赖和配置验证
echo -e "${BLUE}⚙️  依赖和配置验证${NC}"
echo "=================================================="

check_item "pubspec.yaml存在" "test -f pubspec.yaml"
check_item "Flutter依赖" "grep -q 'flutter:' pubspec.yaml"
check_item "BLoC依赖" "grep -q 'flutter_bloc:' pubspec.yaml"
check_item "Drift依赖" "grep -q 'drift:' pubspec.yaml"
check_item "Injectable依赖" "grep -q 'injectable:' pubspec.yaml"
check_item "测试依赖" "grep -q 'bloc_test:' pubspec.yaml"

echo ""

# 4. 代码质量验证
echo -e "${BLUE}🔍 代码质量验证${NC}"
echo "=================================================="

check_item "Dart格式检查" "dart format --set-exit-if-changed lib/"
check_item "导入组织" "dart fix --dry-run lib/"

echo ""

# 5. 测试验证
echo -e "${BLUE}🧪 测试验证${NC}"
echo "=================================================="

check_item "Domain模型测试" "flutter test test/unit/domain/models/ --no-sound-null-safety"
check_item "Repository测试" "flutter test test/unit/data/repositories/ --no-sound-null-safety"
check_item "BLoC测试" "flutter test test/unit/features/ --no-sound-null-safety"

echo ""

# 6. 功能完整性验证
echo -e "${BLUE}🎯 功能完整性验证${NC}"
echo "=================================================="

# 检查关键类和方法是否存在
check_item "Task模型完整性" "grep -q 'class Task' lib/domain/models/task_model.dart"
check_item "Priority枚举" "grep -q 'enum Priority' lib/domain/models/task_model.dart"
check_item "TaskRepository方法" "grep -q 'createTask\\|updateTask\\|deleteTask\\|watchTasksByDate' lib/domain/repositories/task_repository.dart"
check_item "CalendarBloc事件处理" "grep -q 'CalendarEvent' lib/features/calendar/bloc/calendar_bloc.dart"
check_item "TaskListBloc事件处理" "grep -q 'TaskListEvent' lib/features/tasks/bloc/task_list_bloc.dart"
check_item "SummaryBloc事件处理" "grep -q 'SummaryEvent' lib/features/summary/bloc/summary_bloc.dart"

echo ""

# 7. UI组件验证
echo -e "${BLUE}🎨 UI组件验证${NC}"
echo "=================================================="

check_item "CalendarMonthView" "grep -q 'class CalendarMonthView' lib/features/calendar/presentation/widgets/calendar_month_view.dart"
check_item "TaskListPanel" "grep -q 'class TaskListPanel' lib/features/tasks/presentation/task_list_panel.dart"
check_item "Sidebar导航" "grep -q 'class Sidebar' lib/features/calendar/presentation/widgets/sidebar.dart"
check_item "Summary组件" "grep -q 'class SummaryPage' lib/features/summary/presentation/summary_page.dart"

echo ""

# 8. 路由和导航验证
echo -e "${BLUE}🗺️  路由和导航验证${NC}"
echo "=================================================="

check_item "路由配置" "grep -q 'GoRouter' lib/app/router.dart"
check_item "Summary路由" "grep -q 'summary' lib/app/router.dart"
check_item "主题配置" "grep -q 'ThemeData' lib/app/theme.dart"

echo ""

# 9. 数据库验证
echo -e "${BLUE}🗄️  数据库验证${NC}"
echo "=================================================="

check_item "数据库表定义" "grep -q '@DataClassName' lib/data/datasources/local_database.dart"
check_item "任务表" "grep -q 'tasks' lib/data/datasources/local_database.dart"
check_item "子任务表" "grep -q 'subtasks' lib/data/datasources/local_database.dart"

echo ""

# 10. 最终报告
echo "=================================================="
echo -e "${BLUE}📊 验证总结报告${NC}"
echo "=================================================="

echo -e "总检查项: ${TOTAL_CHECKS}"
echo -e "${GREEN}通过: ${PASSED_CHECKS}${NC}"
echo -e "${RED}失败: ${FAILED_CHECKS}${NC}"

# 计算通过率
if [ $TOTAL_CHECKS -gt 0 ]; then
    PASS_RATE=$((PASSED_CHECKS * 100 / TOTAL_CHECKS))
    echo -e "通过率: ${PASS_RATE}%"
else
    PASS_RATE=0
fi

echo ""

# 最终评估
if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "${GREEN}🎉 恭喜！所有验证项目都通过了！${NC}"
    echo -e "${GREEN}✨ MyToDoSpace 已准备好进行生产部署！${NC}"
    echo ""
    echo -e "${BLUE}📋 部署建议：${NC}"
    echo "1. 运行完整测试套件: ./test_runner.sh"
    echo "2. 构建生产版本: flutter build macos --release"
    echo "3. 进行用户验收测试"
    echo "4. 准备发布文档"
    exit 0
elif [ $PASS_RATE -ge 90 ]; then
    echo -e "${YELLOW}⚠️  大部分验证通过，但有少量问题需要修复${NC}"
    echo -e "${YELLOW}📝 建议修复失败项后再进行部署${NC}"
    exit 1
else
    echo -e "${RED}❌ 验证失败项过多，需要进行重大修复${NC}"
    echo -e "${RED}🔧 请检查并修复所有失败项${NC}"
    exit 2
fi