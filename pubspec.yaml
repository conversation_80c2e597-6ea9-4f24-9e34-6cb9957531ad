name: mytodospace
description: A new Flutter macOS To-Do application.
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'
  flutter: ">=3.13.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State management
  flutter_bloc: ^8.1.3
  bloc: ^8.1.2

  # Dependency injection
  get_it: ^7.6.4
  injectable: ^2.3.2

  # Code generation for immutable data classes
  freezed_annotation: ^2.4.1
  json_annotation: ^4.9.0

  # Database
  drift: ^2.12.1
  sqlite3_flutter_libs: ^0.5.15
  path_provider: ^2.1.1
  path: ^1.8.3

  # Routing
  go_router: ^12.0.0

  # Date/Time handling & internationalization
  intl: ^0.20.2

  # Functional programming (for error handling)
  dartz: ^0.10.1

  # Utils
  equatable: ^2.0.5
  clock: ^1.1.2
  file_selector: ^1.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Testing
  bloc_test: ^9.1.4
  mockito: ^5.4.2
  mocktail: ^1.0.0
  integration_test:
    sdk: flutter

  # Linting
  flutter_lints: ^3.0.0

  # Code generation
  build_runner: ^2.4.7
  freezed: ^2.4.6

  json_serializable: ^6.7.1
  injectable_generator: ^2.4.1
  drift_dev: ^2.12.1

flutter:
  uses-material-design: true

  # macOS specific configuration
  generate: true