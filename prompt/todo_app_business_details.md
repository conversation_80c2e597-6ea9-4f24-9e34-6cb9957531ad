macOS To-Do 应用业务细节文档

本文档详细说明了应用中各项功能的具体业务规则和逻辑。
1. 任务实体与状态管理
1.1. 任务状态机

一个任务 (Task) 存在两种核心状态：

    Incomplete (未完成): 默认状态。任务在UI中正常显示。

    Completed (已完成): 用户勾选复选框后的状态。任务在UI中应有视觉区分（如删除线），并记录 completionDate。

状态转换:

    Incomplete -> Completed: 当用户点击未勾选的复选框时。

    Completed -> Incomplete: 当用户点击已勾选的复选框时。

1.2. 四象限分类规则

任务的优先级 (Priority) 严格遵循艾森豪威尔矩阵：

    重要且紧急: 需要立即处理的危机或重要问题。默认双击创建的任务归于此类。

    重要但不紧急: 对长期目标有益的规划、学习等。用户需主动规划。

    不重要但紧急: 来自外界的干扰，需要快速处理但价值不高。

    不重要不紧急: 价值较低的琐事或娱乐。

2. 日历视图业务规则
2.1. 月视图任务指示器渲染规则

    显示逻辑:

        获取某一天（Date Cell）的所有任务。

        按优先级排序：重要且紧急 > 重要但不紧急 > 不重要但紧急 > 不重要不紧急。

        取前N个任务（例如N=4），为每个任务渲染一个对应其优先级的彩色指示条/点。

        如果当天的任务总数 > N，则第N个指示器显示为 ...。

    颜色映射: 必须与 5.2. 色彩规范 中定义的颜色严格一致。

2.2. 年视图热力图计算规则

    目标: 通过颜色深浅直观反映每日的任务“负荷”。

    计算逻辑:

        为每个优先级分配一个权重值，例如：

            重要且紧急 = 4分

            重要但不紧急 = 3分

            不重要但紧急 = 2分

            不重要不紧急 = 1分

        计算某天的总负荷分数 (Load Score) = Σ (当天所有未完成任务的权重值)。

        确定一个基准最大值 (Max Score)，例如 20分。

        计算当天颜色的不透明度 (Opacity) = (Load Score / Max Score)，但最大不超过 1.0。

        单元格的背景色为某个基础颜色（如系统强调色），其不透明度由上一步计算得出。Load Score 为0则完全透明。

3. 总结报告生成规则
3.1. 统计指标计算方法

    时间范围:

        月度总结: 包含指定月份的1号00:00到月末最后一天的23:59之间 creationDate 的所有任务。

        年度总结: 包含指定年份的1月1号到12月31号之间 creationDate 的所有任务。

    已创建任务总数: 在时间范围内的任务总数。

    已完成任务总数: 在时间范围内创建，并且 isCompleted 为 true 的任务总数。

    完成率 (%): (已完成任务总数 / 已创建任务总数) * 100。如果已创建总数为0，则完成率为0。

3.2. “高光时刻”筛选标准

“高光时刻”旨在展示有价值的已完成任务。筛选规则如下 (取前5条)：

    筛选出在指定周期内完成的任务。

    按优先级排序：重要但不紧急 > 重要且紧急。 (因为“重要但不紧急”的任务通常更具规划性和长期价值)。

    在相同优先级内，按子任务数量降序排列（完成多子任务的大任务更有成就感）。

    在以上条件都相同时，按完成日期降序排列。

4. 交互操作业务规则
4.1. 拖放 (Drag & Drop) 逻辑

    数据负载: 拖动操作开始时，需要传递被拖动任务的 taskId。

    放置目标检测:

        日历单元格: 系统需要能识别鼠标下方的日期，并将其作为放置目标。

        象限区域: 系统需要能识别鼠标下方的象限，并将其作为放置目标。

    操作执行: 释放鼠标时，根据目标类型执行不同的更新操作：

        如果目标是日期，则更新 taskId 对应任务的 dueDate。

        如果目标是象限，则更新 taskId 对应任务的 priority。

    UI反馈: 拖动过程中，有效的放置目标区域必须有清晰的视觉高亮反馈。