macOS To-Do 应用业务流程文档

本文档描述了用户在使用 macOS To-Do 应用时的主要业务流程。
1. 核心流程：创建新任务
流程 1.1: 双击日历快速创建 (最高效)

    参与者: 用户

    前置条件: 应用处于月视图。

    流程步骤:

        用户在主内容区的月视图中，找到目标日期单元格。

        用户双击该日期单元格。

        系统响应:
        a. 该日期被设为选中状态。
        b. 右侧详情区更新为该日期的任务清单视图。
        c. 在“重要且紧急”象限列表的顶部，自动创建一个新的输入框，并使其获得焦点。

        用户输入任务标题。

        用户按下 Enter 键。

        系统响应:
        a. 创建一个新任务，属性为：标题（用户输入），日期（双击的日期），优先级（重要且紧急），状态（未完成）。
        b. 将新任务保-存到数据库。
        c. 在“重要且紧急”象限中显示这个新任务项。
        d. 更新主内容区日历上该日期的任务指示器。

    后置条件: 一个新的“重要且紧急”任务被成功创建并显示。

流程 1.2: 通过全局“+”按钮创建

    参与者: 用户

    前置条件: 应用已打开。

    流程步骤:

        用户点击主窗口工具栏上的 + 新建任务 按钮。

        系统响应: 右侧详情区切换到“任务创建/编辑视图”。默认日期为当前选中的日期（或今天）。

        用户填写任务标题（必填）。

        用户选择任务的优先级。

        用户选择任务的日期。

        用户（可选）填写备注和添加子任务。

        用户点击 保存 按钮。

        系统响应:
        a. 根据用户填写的信息创建一个新任务。
        b. 将新任务保-存到数据库。
        c. 如果新任务的日期是当前选中的日期，则在右侧任务清单的对应象限中显示该任务。
        d. 更新主内容区日历上该日期的任务指示器。

    后置条件: 一个包含详细信息的新任务被成功创建。

2. 核心流程：管理任务
流程 2.1: 完成/取消完成任务

    参与者: 用户

    前置条件: 右侧详情区显示某一天的任务清单。

    流程步骤:

        用户找到一个未完成的任务项。

        用户点击任务项前的复选框。

        系统响应:
        a. 任务状态更新为“已完成”。
        b. 任务项UI变化：标题添加删除线，整体变灰。
        c. 已完成的任务项平滑移动到其所在象限列表的底部。
        d. 触发完成动画。

        （可选）用户再次点击已完成任务的复选框。

        系统响应: 任务状态恢复为“未完成”，UI恢复正常，任务项移回原位。

    后置条件: 任务的完成状态被更新。

流程 2.2: 修改任务优先级 (拖拽)

    参与者: 用户

    前置条件: 右侧详情区显示任务清单。

    流程步骤:

        用户在任务清单中按住一个任务项并开始拖动。

        系统响应: 被拖动的任务项显示为一个半透明的副本。

        用户将任务项拖动到另一个象限的区域内。

        系统响应: 目标象限区域出现高亮，表示可以放置。

        用户释放鼠标。

        系统响应:
        a. 任务的优先级属性被更新为目标象限对应的优先级。
        b. 任务项从原象限移除，并添加到目标象限列表中。
        c. 更新日历上该日期的任务指示器颜色。

    后置条件: 任务的优先级被成功修改。

3. 核心流程：查看总结报告
流程 3.1: 查看月度总结

    参与者: 用户

    前置条件: 应用已打开。

    流程步骤:

        用户在左侧边栏点击 本月总结 按钮。

        系统响应:
        a. 右侧详情区切换到“总结视图”。
        b. 系统获取当前日历所处月份的所有任务数据。
        c. 计算统计数据（总数、完成率、分布等）。
        d. 将计算结果渲染到总结视图中。

    后置条件: 用户看到当前月份的任务总结报告。