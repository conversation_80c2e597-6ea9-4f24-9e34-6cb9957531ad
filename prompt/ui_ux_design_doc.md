macOS To-Do 应用 UI/UX 设计文档
1. 概述
1.1. 项目目标

本应用是一款基于 Flutter 开发的 macOS 个人待办事项（To-Do）管理软件。它旨在通过创新的日历视图和高效的四象限（Eisenhower Matrix）任务管理方法，帮助用户清晰地规划和执行每日、每月及每年的任务，最终实现极致的操作效率和流畅的交互体验。
1.2. 核心设计理念

    效率至上 (Efficiency First): 每一个操作都应设计得尽可能快。减少点击次数，提供直观的拖拽操作和必要的快捷键。

    视觉清晰 (Visual Clarity): 界面布局清晰，信息层次分明。通过颜色、字体和空间布局，让用户一眼就能获取核心信息（如任务的优先级和日期）。

    交互愉悦 (Engaging Interaction): 提供平滑、有意义的动画和即时反馈，让用户的每一次操作都感到舒适和得到确认。

    原生体验 (Native Feel): 虽然使用 Flutter 开发，但 UI 风格和交互行为应遵循 macOS 的设计规范，提供接近原生应用的体验。

2. 整体布局与结构

本应用采用经典的三栏式布局，以最大化信息密度和操作便捷性。

    左侧边栏 (Sidebar): 核心导航区。提供快速日期跳转的迷你日历和视图切换功能。

    中间主内容区 (Main Content): 核心工作区。展示月视图或年视图的日历。

    右侧详情区 (Inspector): 上下文关联区。显示选定日期的任务清单、任务详情或数据总结。

2.1. 主窗口与工具栏 (Main Window & Toolbar)

    窗口: 标准 macOS 窗口，包含红绿灯按钮。支持窗口大小调整。

    工具栏:

        < > (月份/年份切换): 点击 < 切换到上个月/年，点击 > 切换到下个月/年。

        月份年份标题: 显示当前视图的年月，例如 "2025年 9月"。点击此标题可以弹出一个快速跳转选择器（年份-月份滚动选择）。

        今天 (Today) 按钮: 点击后，日历视图和选中日期都将立即跳转到当前日期。

        + 新建任务 (New Task) 按钮: 全局新建任务按钮。点击后，右侧详情区切换到任务创建视图。

        搜索框: 实时搜索任务标题和内容。

3. 核心视图详解
3.1. 左侧边栏 (Sidebar)

    固定组件，背景采用 macOS 的毛玻璃/模糊效果 (Vibrancy)。

    迷你月历 (Mini Month Calendar):

        功能: 用于快速跨月份导航。

        交互:

            点击日期: 主内容区的月视图会平滑过渡到所选日期所在的月份，并高亮选中该日期。右侧详情区同时更新为该日期的任务列表。

            当前日期: 以特殊的背景色或圆点标记。

            有任务的日期: 在日期下方用一个微小的彩色点作为指示。

    视图切换 (Views):

        月视图: 默认视图，选中状态。

        年视图: 点击切换主内容区为年视图。

        四象限视图: 点击后，中间主内容区变为一个四列看板（对应四象限），展示所有“未完成”的任务卡片。右侧详情区此时可以变为一个全局统计面板，或者显示当前选中的任务卡片的详细信息。

    总结 (Summary):

        本月总结: 点击后，右侧详情区显示当前选中月份的数据总结报告。

        本年总结: 点击后，右侧详情区显示当前选中年份的数据总结报告。

3.2. 主内容区 (Main Content)
3.2.1. 月视图 (Month View) - 默认核心视图

    布局: 7列（星期日到星期六）x 6行 的网格布局。

    日期单元格 (Day Cell):

        显示: 左上角显示日期数字，例如 "1", "2", "3"。

        状态:

            今天: 日期数字带有醒目的圆形背景色。

            非本月日期: 字体颜色变浅（灰色）。

            选中日期: 整个单元格有一个清晰的边框或浅色背景，以示选中。

        任务指示:

            每个任务在日期下方显示为一个彩色的矩形条（或圆点）。

            颜色对应任务的优先级：

                红色: 重要且紧急

                橙色: 重要但不紧急

                蓝色: 不重要但紧急

                灰色: 不重要不紧急

            当任务过多时（例如超过4个），最后一条显示为 ...。

    交互:

        单击 (Click) 单元格:

            将该单元格设为“选中状态”。

            右侧详情区立即更新，显示该日期的详细任务清单。

            如果单击的是非本月日期，视图将自动平滑切换到对应的月份。

        双击 (Double Click) 单元格:

            执行单击操作的所有效果。

            在右侧详情区的“重要且紧急”象限列表顶部，自动创建一个新的输入框，并让用户可以立即输入任务标题。这是最高效的任务创建方式。

        悬停 (Hover) 单元格:

            出现一个小的浮动窗口 (Popover)，快速预览当天的前3个任务标题。

        拖拽任务至此 (Drag & Drop):

            从右侧任务列表拖动一个任务到某个日期单元格上。

            单元格出现高亮边框，表示可以放置。

            释放鼠标后，该任务的日期被修改为新日期。

3.2.2. 年视图 (Year View)

    布局: 4列 x 3行的网格，每个网格是一个完整的微缩月份日历。

    微缩日历:

        显示: 显示12个月份的概览。

        热力图 (Heat Map): 单元格的背景色深浅代表当天任务的数量和重要性。任务越多、越重要，颜色越深。这为用户回顾整年提供了一个宏观视角。

    交互:

        悬停 (Hover) 某一天: 显示完整的日期信息 Tooltip，例如 "2025年9月1日"。

        单击 (Click) 任何一个月份标题或月份内的任何一天: 主内容区将平滑切换回月视图，并定位到所点击的月份和日期。

3.3. 右侧详情区 (Inspector) - 上下文感知

此区域内容根据左侧和中间区域的选择动态变化。
3.3.1. 单日任务清单视图 (Daily Task List View)

    触发: 在月视图中单击某个日期时显示。

    顶部: 显示选中的完整日期，例如 "9月1日 星期一"。

    布局: 清晰地划分为四个横向区域，每个区域代表一个象限。

        重要且紧急 (红色标题)

        重要但不紧急 (橙色标题)

        不重要但紧急 (蓝色标题)

        不重要不紧急 (灰色标题)

    任务项 (Task Item):

        结构:

            [ ] 复选框: 用于标记完成。

            任务标题: 清晰显示。

            指示器: 如果任务有备注或子任务，标题后会有一个小图标。

        交互:

            单击复选框:

                复选框被勾选。

                任务标题添加删除线，整体变为灰色。

                触发一个简短、令人愉悦的完成动画。

                已完成任务自动下沉到该象限列表的底部。

            单击任务项文本区域:

                任务项展开，以内联形式显示任务的详细备注和子任务列表，而不是跳转到新页面。再次点击则收起。

            右键单击任务项:

                弹出上下文菜单，提供操作：编辑、删除、更改优先级 (移动到其他象限)、更改日期。

            拖拽任务项:

                在象限间拖拽: 可以将任务从一个象限拖到另一个象限，以快速调整其优先级。目标象限在拖动悬停时会高亮。

                拖拽到主内容区的日历上: 如 3.2.1 所述，用于修改日期。

    象限内操作:

        每个象限标题旁都有一个 + 按钮，点击后可以在该象限下快速添加新任务。

3.3.2. 任务创建/编辑视图

    触发: 点击全局 + 按钮，或编辑现有任务时。

    字段:

        任务标题输入框: 核心输入区，大而清晰。

        优先级选择器: 四个带颜色的按钮，分别代表四个象限，直观易选。

        日期选择器: 默认为当前选中的日期。

        备注: 一个多行文本区域。

        子任务列表: 可以动态添加/删除子任务项。

        保存 和 取消 按钮。

3.3.3. 总结视图 (Summary View)

    触发: 点击左侧边栏的“本月总结”或“本年总结”。

    内容:

        数据统计:

            已创建任务总数

            已完成任务总数

            完成率 (%)

        可视化图表:

            一个饼图或条形图，展示四个象限任务的数量分布情况。

        高光时刻: 列出本周期内完成的一些重要的任务。

4. 核心交互流程 (Workflows)
4.1. 创建一个新任务 (最高频操作)

    路径1 (最快): 在月视图双击目标日期 -> 右侧面板对应象限出现输入框 -> 输入标题 -> 按 Enter 键保存。任务已创建并归类。

    路径2 (标准): 点击工具栏 + 按钮 -> 右侧面板出现完整创建视图 -> 填写信息 -> 点击 保存。

    路径3 (上下文): 在右侧任务清单中，点击某个象限的 + 按钮 -> 在该象限下输入标题 -> 按 Enter 键保存。

4.2. 修改任务属性

    修改优先级: 在右侧清单中，直接将任务拖拽到目标象限。

    修改日期: 在右侧清单中，直接将任务拖拽到主内容区的目标日期单元格。

    修改内容: 单击任务项展开 -> 编辑文本 -> 点击外部区域或保存按钮自动保存。

5. 视觉与动效风格
5.1. 设计语言

    简洁、扁平化， 尊重 macOS 的现代设计风格。

    善用留白， 避免信息过载。

    支持浅色模式和深色模式 (Light/Dark Mode)。

5.2. 色彩规范

    主色调: 中性灰（浅色模式下接近白色，深色模式下为深灰）。

    强调色/功能色:

        红色 (Urgent & Important): #FF3B30

        橙色 (Important & Not Urgent): #FF9500

        蓝色 (Urgent & Not Important): #007AFF

        灰色 (Not Urgent & Not Important): #8E8E93

        高亮/选中色: 系统蓝色 #007AFF。

5.3. 字体规范

    西文: SF Pro (macOS 系统默认字体)。

    中文: 苹方 (PingFang SC)。

    层级: 通过字重 (Bold, Medium, Regular) 和字号大小区分信息层级。

5.4. 动效设计 (Animations)

    原则: 快速、平滑、有意义。所有动画时长建议在 150ms-300ms 之间。

    应用场景:

        视图切换: 月份/年份切换时，使用平滑的水平滑动或淡入淡出效果。

        任务完成: 复选框勾选时，有一个缩放和绘制的动画。

        面板过渡: 右侧详情区的视图切换（例如从清单到编辑）使用平滑过渡。

        拖拽: 拖动的任务项变为一个半透明的“影子”，流畅地跟随鼠标。

6. 快捷键支持

    Cmd + N: 新建任务 (同点击工具栏 + 按钮)。

    Cmd + F: 聚焦到搜索框。

    Cmd + T: 跳转到今天。

    ↑ ↓ ← → (方向键): 在月视图中，用于在日期单元格之间导航。

    Enter:

        当选中一个日期时，Enter 键可以在右侧快速创建一个新任务。

        在输入任务标题时，Enter 键用于保存。

    Esc: 关闭弹窗、取消编辑等。