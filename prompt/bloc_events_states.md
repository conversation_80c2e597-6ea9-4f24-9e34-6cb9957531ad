BLoC事件与状态定义文档

本文档详细定义了应用表现层（Presentation Layer）中核心BLoC的事件（Events）和状态（States）。所有BLoC的实现都必须严格遵循这些定义。所有State类都应使用 freezed 生成。
1. TaskListBloc

    文件路径: lib/features/tasks/bloc/task_list_bloc.dart

    职责: 管理特定日期下的任务列表，并处理任务的完成、删除等操作。

事件 (Events)

@freezed
abstract class TaskListEvent with _$TaskListEvent {
  /// 请求订阅指定日期的任务列表
  const factory TaskListEvent.subscriptionRequested(DateTime date) = _SubscriptionRequested;
  
  /// 切换任务的完成状态
  const factory TaskListEvent.completionToggled({
    required String taskId,
    required bool isCompleted,
  }) = _CompletionToggled;

  /// 删除一个任务
  const factory TaskListEvent.taskDeleted(String taskId) = _TaskDeleted;

  /// (内部使用) 当任务流发出新数据时触发
  const factory TaskListEvent.tasksUpdated(List<Task> tasks) = _TasksUpdated;
}

状态 (States)

@freezed
abstract class TaskListState with _$TaskListState {
  const factory TaskListState({
    required TaskListStatus status,
    required List<Task> tasks,
    required DateTime date, // 当前展示任务的日期
    String? errorMessage,
  }) = _TaskListState;

  factory TaskListState.initial() => TaskListState(
    status: TaskListStatus.initial,
    tasks: [],
    date: DateTime.now(),
  );
}

enum TaskListStatus { initial, loading, success, failure }

2. CalendarBloc

    文件路径: lib/features/calendar/bloc/calendar_bloc.dart

    职责: 管理日历视图的状态，包括当前显示的月份、用户选中的日期以及月份任务负荷数据。

事件 (Events)

@freezed
abstract class CalendarEvent with _$CalendarEvent {
  /// 当应用首次加载或需要刷新时触发
  const factory CalendarEvent.started() = _Started;
  
  /// 当用户在日历上选择了一个新的日期时触发
  const factory CalendarEvent.dateSelected(DateTime date) = _DateSelected;
  
  /// 当用户切换到上一个月或下一个月时触发
  const factory CalendarEvent.monthChanged(DateTime newMonthDate) = _MonthChanged;

  /// 当用户通过双击日历快速添加任务时触发
  const factory CalendarEvent.quickTaskAdded({
    required String title,
    required DateTime date,
  }) = _QuickTaskAdded;
}

状态 (States)

@freezed
abstract class CalendarState with _$CalendarState {
  const factory CalendarState({
    required CalendarStatus status,
    required DateTime selectedDate, // 用户当前选中的日期
    required DateTime displayMonthDate, // 日历当前显示的月份（取该月1号）
    required Map<DateTime, int> taskLoadByDate, // 当前月份的任务负荷数据
    String? errorMessage,
  }) = _CalendarState;

  factory CalendarState.initial() {
    final now = DateTime.now();
    return CalendarState(
      status: CalendarStatus.initial,
      selectedDate: now,
      displayMonthDate: DateTime(now.year, now.month, 1),
      taskLoadByDate: {},
    );
  }
}

enum CalendarStatus { initial, loading, success, failure }

