macOS To-Do 应用业务架构图文档
1. 总体架构

本应用采用分层架构，将表现、业务逻辑和数据分离，以提高代码的可维护性、可扩展性和可测试性。

+---------------------------------------------------+
|               用户界面层 (UI Layer)               |
|         (Flutter Widgets & UI Components)         |
+---------------------------------------------------+
| - 三栏式主窗口 (主视图、侧边栏、详情区)           |
| - 日历视图 (月视图、年视图)                     |
| - 任务列表与任务项 (四象限)                     |
| - 任务创建/编辑表单                             |
| - 总结报告视图                                  |
+---------------------------------------------------+
                       |
                       V (用户交互事件，视图模型更新)
+---------------------------------------------------+
|             业务逻辑层 (Business Logic Layer)       |
|                (State Management)                 |
+---------------------------------------------------+
| - 视图状态管理模块 (View State Management)        |
|   - 管理当前视图 (月/年/四象限)                   |
|   - 管理当前选中的日期、任务                        |
|---------------------------------------------------|
| - 任务管理模块 (Task Management)                |
|   - CRUD 操作 (创建、读取、更新、删除任务)          |
|   - 任务状态变更 (完成/未完成)                      |
|   - 优先级/日期变更逻辑                           |
|---------------------------------------------------|
| - 日历逻辑模块 (Calendar Logic)                 |
|   - 生成月/年视图所需的数据结构                   |
|   - 计算热力图数据                                |
|---------------------------------------------------|
| - 数据分析模块 (Analytics)                      |
|   - 计算月度/年度总结报告                         |
|   - 生成图表数据                                  |
+---------------------------------------------------+
                       |
                       V (数据读写请求)
+---------------------------------------------------+
|           数据持久层 (Data Persistence Layer)       |
+---------------------------------------------------+
| - 数据存储适配器 (Storage Adapter)                |
|   - 接口定义 (保存任务, 读取任务等)               |
|---------------------------------------------------|
| - 本地数据库实现 (e.g., SQLite, Hive)           |
|   - 任务表 (Tasks Table)                          |
|   - 子任务表 (SubTasks Table)                     |
+---------------------------------------------------+

2. 核心业务实体 (Core Business Entities)
2.1. 任务 (Task)

    id: String (唯一标识符)

    title: String (任务标题)

    notes: String (任务备注)

    creationDate: DateTime (创建日期)

    dueDate: DateTime (截止日期)

    isCompleted: bool (是否完成)

    completionDate: DateTime? (完成日期)

    priority: Enum (优先级，对应四象限)

        URGENT_IMPORTANT

        IMPORTANT_NOT_URGENT

        URGENT_NOT_IMPORTANT

        NOT_URGENT_NOT_IMPORTANT

    subtasks: List<SubTask> (子任务列表)

2.2. 子任务 (SubTask)

    id: String (唯一标识符)

    parentTaskId: String (父任务ID)

    title: String (子任务标题)

    isCompleted: bool (是否完成)

2.3. 总结报告 (SummaryReport)

    period: String ("2025年9月" 或 "2025年")

    totalTasksCreated: int

    totalTasksCompleted: int

    completionRate: double

    quadrantDistribution: Map<Priority, int> (各象限任务数量分布)

    highlights: List<Task> (高光时刻任务列表)

3. 模块关系图

[业务架构图]

    用户 与 UI层 交互。

    UI层 依赖 业务逻辑层 来处理用户操作和获取显示数据。

    业务逻辑层 封装了应用所有的核心业务规则。

    业务逻辑层 通过 数据持久层 的接口来读写数据，而不关心具体的存储实现。

    数据持久层 负责将业务实体序列化并存储到本地数据库中。