接口契约文档 (API Definitions)

本文档详细定义了应用领域层（Domain Layer）中所有Repository的抽象接口。数据层（Data Layer）的实现必须严格遵守这些契约。
1. TaskRepository

lib/domain/repositories/task_repository.dart

这是任务管理的核心接口，负责所有与任务相关的CRUD（创建、读取、更新、删除）操作。

import '../models/task_model.dart';

/// 任务管理的核心接口
abstract class TaskRepository {
  /// 创建一个新任务。
  ///
  /// [task]: 需要被创建的任务实体。
  /// 成功时返回 Future<void>。
  /// 如果数据库操作失败，则可能抛出 [DatabaseException]。
  Future<void> createTask(Task task);

  /// 更新一个已存在的任务。
  ///
  /// [task]: 包含更新后信息的任务实体。
  /// 成功时返回 Future<void>。
  /// 如果任务不存在或数据库操作失败，则可能抛出异常。
  Future<void> updateTask(Task task);

  /// 根据ID删除一个任务。
  ///
  /// [taskId]: 要删除的任务的唯一ID。
  /// 成功时返回 Future<void>。
  Future<void> deleteTask(String taskId);

  /// 监听指定日期的所有任务列表。
  ///
  /// [date]: 需要查询的日期。
  /// 返回一个任务列表的Stream，当该日期的任务数据发生
  /// 任何变化（增、删、改）时，Stream会发出新的列表。
  Stream<List<Task>> watchTasksByDate(DateTime date);

  /// 监听指定月份中每一天的任务“负荷”。
  ///
  /// 用于在日历月视图上显示任务指示器或热力图。
  /// [year]: 年份。
  /// [month]: 月份。
  /// 返回一个Map的Stream，其中Key是日期，Value是该日的任务“负荷”分数。
  /// “负荷”分数的计算规则见 `todo_app_business_details.md`。
  Stream<Map<DateTime, int>> watchTaskLoadForMonth({required int year, required int month});
}

2. SummaryRepository

lib/domain/repositories/summary_repository.dart

这个接口负责生成业务总结报告。

import '../models/summary_report_model.dart';

/// 总结报告生成的核心接口
abstract class SummaryRepository {
  /// 获取指定月份的总结报告。
  ///
  /// [year]: 年份。
  /// [month]: 月份。
  /// 返回一个包含该月统计数据的 [SummaryReport] 实体。
  Future<SummaryReport> getMonthlySummary({required int year, required int month});

  /// 获取指定年份的总结报告。
  ///
  /// [year]: 年份。
  /// 返回一个包含该年统计数据的 [SummaryReport] 实体。
  Future<SummaryReport> getYearlySummary({required int year});
}

