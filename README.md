# My ToDo Space

一个基于Flutter的macOS企业级To-Do应用，采用Clean Architecture架构和BLoC状态管理。

## 项目架构

### 技术栈
- **Framework**: Flutter 3.x & Dart 3.x
- **State Management**: BLoC / flutter_bloc
- **Database**: Drift (SQLite)
- **Dependency Injection**: get_it + injectable
- **Code Generation**: freezed, json_serializable
- **Routing**: go_router

### 目录结构
```
lib/
├── app/                    # 应用层配置
│   ├── theme.dart         # 主题配置
│   └── router.dart        # 路由配置
├── core/                  # 核心模块
│   └── di/                # 依赖注入
├── domain/                # 领域层
│   ├── models/           # 业务实体
│   └── repositories/     # 仓库抽象接口
├── data/                 # 数据层
│   ├── datasources/     # 数据源
│   └── repositories/    # 仓库具体实现
└── features/            # 功能模块
    ├── calendar/        # 日历功能
    ├── tasks/          # 任务管理
    └── summary/        # 数据总结
```

## 功能特性

### 已实现功能
1. **Clean Architecture分层架构**
   - Domain层：业务实体和仓库接口
   - Data层：Drift数据库和仓库实现
   - Presentation层：BLoC状态管理和UI组件

2. **日历视图**
   - 月视图日历显示
   - 任务负荷热力图
   - 快速任务创建（双击日期）

3. **任务管理**
   - 四象限任务分类（重要/紧急矩阵）
   - 任务完成状态切换
   - 子任务支持
   - 任务备注

4. **响应式数据**
   - 实时任务列表更新
   - 日历任务负荷实时计算

### 待完成功能
1. **Summary Feature** - 数据总结报告
2. **任务编辑功能**
3. **搜索功能**
4. **拖拽操作**
5. **年视图和四象限视图**

## 运行项目

1. 安装依赖：
```bash
flutter pub get
```

2. 生成代码：
```bash
flutter packages pub run build_runner build
```

3. 运行应用：
```bash
flutter run
```

## 代码生成

项目使用以下代码生成器：
- **freezed**: 生成不可变数据类
- **json_serializable**: 生成JSON序列化代码
- **injectable**: 生成依赖注入代码
- **drift**: 生成数据库访问代码

运行代码生成：
```bash
flutter packages pub run build_runner build --delete-conflicting-outputs
```

## 设计理念

- **效率至上**: 减少操作步骤，提供直观交互
- **视觉清晰**: 清晰的界面布局和信息层次
- **原生体验**: 遵循macOS设计规范

## 优先级颜色方案

- 🔴 重要且紧急: #FF3B30
- 🟠 重要但不紧急: #FF9500  
- 🔵 不重要但紧急: #007AFF
- ⚪ 不重要不紧急: #8E8E93