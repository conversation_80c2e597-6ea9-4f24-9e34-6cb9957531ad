// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:clock/clock.dart' as _i454;
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;
import 'package:mytodospace/core/di/clock_module.dart' as _i645;
import 'package:mytodospace/core/di/database_module.dart' as _i332;
import 'package:mytodospace/data/datasources/local_database.dart' as _i557;
import 'package:mytodospace/data/repositories/summary_repository_impl.dart'
    as _i1012;
import 'package:mytodospace/data/repositories/task_repository_impl.dart'
    as _i776;
import 'package:mytodospace/domain/domain.dart' as _i520;
import 'package:mytodospace/domain/repositories/summary_repository.dart' as _i8;
import 'package:mytodospace/features/calendar/bloc/calendar_bloc.dart' as _i582;
import 'package:mytodospace/features/summary/bloc/summary_bloc.dart' as _i171;
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart' as _i610;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final databaseModule = _$DatabaseModule();
    final clockModule = _$ClockModule();
    gh.lazySingleton<_i557.LocalDatabase>(() => databaseModule.database);
    gh.lazySingleton<_i454.Clock>(() => clockModule.clock);
    gh.lazySingleton<_i520.TaskRepository>(
        () => _i776.TaskRepositoryImpl(gh<_i557.LocalDatabase>()));
    gh.lazySingleton<_i520.SummaryRepository>(
        () => _i1012.SummaryRepositoryImpl(gh<_i557.LocalDatabase>()));
    gh.factory<_i610.TaskListBloc>(() => _i610.TaskListBloc(
          gh<_i520.TaskRepository>(),
          gh<_i454.Clock>(),
        ));
    gh.factory<_i582.CalendarBloc>(() => _i582.CalendarBloc(
          gh<_i520.TaskRepository>(),
          gh<_i454.Clock>(),
        ));
    gh.factory<_i171.SummaryBloc>(
        () => _i171.SummaryBloc(gh<_i8.SummaryRepository>()));
    return this;
  }
}

class _$DatabaseModule extends _i332.DatabaseModule {}

class _$ClockModule extends _i645.ClockModule {}
