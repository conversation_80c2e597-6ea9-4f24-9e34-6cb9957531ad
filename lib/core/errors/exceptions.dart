/// Represents an exception that occurs during a database operation.
class DatabaseOperationException implements Exception {
  /// The error message associated with the exception.
  final String message;

  /// Creates a new [DatabaseOperationException] with the given [message].
  const DatabaseOperationException(this.message);

  @override
  String toString() => 'DatabaseOperationException: $message';
}

