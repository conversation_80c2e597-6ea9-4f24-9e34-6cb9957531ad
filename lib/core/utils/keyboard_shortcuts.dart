import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../features/tasks/bloc/task_list_bloc.dart';
import '../../features/tasks/bloc/task_list_event.dart';
import '../../domain/models/task_model.dart';
import '../../features/calendar/bloc/calendar_bloc.dart';
import '../../features/calendar/bloc/calendar_event.dart';
import '../../features/calendar/bloc/calendar_state.dart';
import '../../features/tasks/presentation/task_editor_dialog.dart';

/// 全局键盘快捷键处理器
class KeyboardShortcuts extends StatelessWidget {
  final Widget child;

  const KeyboardShortcuts({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Shortcuts(
      shortcuts: _getShortcuts(),
      child: Actions(
        actions: _getActions(context),
        child: Focus(
          autofocus: true,
          child: child,
        ),
      ),
    );
  }

  Map<ShortcutActivator, Intent> _getShortcuts() {
    return {
      // Cmd+N: 新建任务
      const SingleActivator(
        LogicalKeyboardKey.keyN,
        meta: true,
      ): const CreateTaskIntent(),

      // Cmd+F: 搜索任务
      const SingleActivator(
        LogicalKeyboardKey.keyF,
        meta: true,
      ): const SearchTasksIntent(),

      // Cmd+T: 跳转到今天
      const SingleActivator(
        LogicalKeyboardKey.keyT,
        meta: true,
      ): const GoToTodayIntent(),

      // Esc: 关闭对话框
      const SingleActivator(
        LogicalKeyboardKey.escape,
      ): const CloseDialogIntent(),

      // Cmd+1: 切换到月视图
      const SingleActivator(
        LogicalKeyboardKey.digit1,
        meta: true,
      ): const SwitchToMonthViewIntent(),

      // Cmd+2: 切换到年视图
      const SingleActivator(
        LogicalKeyboardKey.digit2,
        meta: true,
      ): const SwitchToYearViewIntent(),

      // Cmd+3: 切换到四象限视图
      const SingleActivator(
        LogicalKeyboardKey.digit3,
        meta: true,
      ): const SwitchToQuadrantViewIntent(),

      // Calendar navigation shortcuts
      // Arrow keys for date navigation
      const SingleActivator(
        LogicalKeyboardKey.arrowLeft,
      ): const CalendarNavigationIntent(CalendarKeyAction.previousDay),

      const SingleActivator(
        LogicalKeyboardKey.arrowRight,
      ): const CalendarNavigationIntent(CalendarKeyAction.nextDay),

      const SingleActivator(
        LogicalKeyboardKey.arrowUp,
      ): const CalendarNavigationIntent(CalendarKeyAction.previousWeek),

      const SingleActivator(
        LogicalKeyboardKey.arrowDown,
      ): const CalendarNavigationIntent(CalendarKeyAction.nextWeek),

      // Page Up/Down for month navigation
      const SingleActivator(
        LogicalKeyboardKey.pageUp,
      ): const CalendarNavigationIntent(CalendarKeyAction.previousMonth),

      const SingleActivator(
        LogicalKeyboardKey.pageDown,
      ): const CalendarNavigationIntent(CalendarKeyAction.nextMonth),

      // Shift + Page Up/Down for year navigation
      const SingleActivator(
        LogicalKeyboardKey.pageUp,
        shift: true,
      ): const CalendarNavigationIntent(CalendarKeyAction.previousYear),

      const SingleActivator(
        LogicalKeyboardKey.pageDown,
        shift: true,
      ): const CalendarNavigationIntent(CalendarKeyAction.nextYear),

      // Enter key for quick task creation on selected date
      const SingleActivator(
        LogicalKeyboardKey.enter,
      ): const CalendarNavigationIntent(CalendarKeyAction.quickAddTask),

      // Space bar for date selection (same as clicking)
      const SingleActivator(
        LogicalKeyboardKey.space,
      ): const CalendarNavigationIntent(CalendarKeyAction.selectDate),
    };
  }

  Map<Type, Action<Intent>> _getActions(BuildContext context) {
    return {
      CreateTaskIntent: CreateTaskAction(context),
      SearchTasksIntent: SearchTasksAction(context),
      GoToTodayIntent: GoToTodayAction(context),
      CloseDialogIntent: CloseDialogAction(context),
      SwitchToMonthViewIntent: SwitchToMonthViewAction(context),
      SwitchToYearViewIntent: SwitchToYearViewAction(context),
      SwitchToQuadrantViewIntent: SwitchToQuadrantViewAction(context),
      CalendarNavigationIntent: CalendarNavigationAction(context),
    };
  }
}

// Intent 定义
class CreateTaskIntent extends Intent {
  const CreateTaskIntent();
}

class SearchTasksIntent extends Intent {
  const SearchTasksIntent();
}

class GoToTodayIntent extends Intent {
  const GoToTodayIntent();
}

class CloseDialogIntent extends Intent {
  const CloseDialogIntent();
}

class SwitchToMonthViewIntent extends Intent {
  const SwitchToMonthViewIntent();
}

class SwitchToYearViewIntent extends Intent {
  const SwitchToYearViewIntent();
}

class SwitchToQuadrantViewIntent extends Intent {
  const SwitchToQuadrantViewIntent();
}

class CalendarNavigationIntent extends Intent {
  final CalendarKeyAction action;

  const CalendarNavigationIntent(this.action);
}

// Action 实现
class CreateTaskAction extends Action<CreateTaskIntent> {
  final BuildContext context;

  CreateTaskAction(this.context);

  @override
  Object? invoke(CreateTaskIntent intent) {
    _showCreateTaskDialog();
    return null;
  }

  void _showCreateTaskDialog() {
    final calendarBloc = context.read<CalendarBloc>();
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => BlocProvider.value(
        value: calendarBloc,
        child: TaskEditorDialog(
          selectedDate: calendarBloc.state.selectedDate,
          onTaskSaved: (task) {
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text('任务「${task.title}」创建成功'),
                backgroundColor: Colors.green,
              ),
            );
          },
        ),
      ),
    );
  }
}

class SearchTasksAction extends Action<SearchTasksIntent> {
  final BuildContext context;

  SearchTasksAction(this.context);

  @override
  Object? invoke(SearchTasksIntent intent) {
    // Find the nearest TaskListBloc and trigger search
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    try {
      final taskListBloc = BlocProvider.of<TaskListBloc>(context);

      // Show search dialog
      showDialog(
        context: context,
        builder: (context) => SearchDialog(
          onSearch: (query) {
            taskListBloc.add(TaskListEvent.searchRequested(query));
          },
          onClear: () {
            taskListBloc.add(const TaskListEvent.searchCleared());
          },
        ),
      );
    } catch (e) {
      // Fallback if BLoC is not available
      scaffoldMessenger.showSnackBar(
        const SnackBar(
          content: Text('搜索功能暂时不可用'),
          duration: Duration(seconds: 2),
        ),
      );
    }
    return null;
  }
}

class GoToTodayAction extends Action<GoToTodayIntent> {
  final BuildContext context;

  GoToTodayAction(this.context);

  @override
  Object? invoke(GoToTodayIntent intent) {
    final now = DateTime.now();
    final calendarBloc = context.read<CalendarBloc>();
    calendarBloc.add(CalendarEvent.dateSelected(now));
    calendarBloc
        .add(CalendarEvent.monthChanged(DateTime(now.year, now.month, 1)));

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已跳转到今天'),
        duration: Duration(seconds: 1),
      ),
    );
    return null;
  }
}

class CloseDialogAction extends Action<CloseDialogIntent> {
  final BuildContext context;

  CloseDialogAction(this.context);

  @override
  Object? invoke(CloseDialogIntent intent) {
    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop();
    }
    return null;
  }
}

class SwitchToMonthViewAction extends Action<SwitchToMonthViewIntent> {
  final BuildContext context;

  SwitchToMonthViewAction(this.context);

  @override
  Object? invoke(SwitchToMonthViewIntent intent) {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    context.read<CalendarBloc>().add(
          const CalendarEvent.viewTypeChanged(CalendarViewType.month),
        );

    scaffoldMessenger.showSnackBar(
      const SnackBar(
        content: Text('已切换到月视图'),
        duration: Duration(seconds: 1),
      ),
    );
    return null;
  }
}

class SwitchToYearViewAction extends Action<SwitchToYearViewIntent> {
  final BuildContext context;

  SwitchToYearViewAction(this.context);

  @override
  Object? invoke(SwitchToYearViewIntent intent) {
    context.read<CalendarBloc>().add(
          const CalendarEvent.viewTypeChanged(CalendarViewType.year),
        );

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('已切换到年视图'),
        duration: Duration(seconds: 1),
      ),
    );
    return null;
  }
}

class SwitchToQuadrantViewAction extends Action<SwitchToQuadrantViewIntent> {
  final BuildContext context;

  SwitchToQuadrantViewAction(this.context);

  @override
  Object? invoke(SwitchToQuadrantViewIntent intent) {
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    context.read<CalendarBloc>().add(
          const CalendarEvent.viewTypeChanged(CalendarViewType.quadrant),
        );

    scaffoldMessenger.showSnackBar(
      const SnackBar(
        content: Text('已切换到四象限视图'),
        duration: Duration(seconds: 1),
      ),
    );
    return null;
  }
}

class CalendarNavigationAction extends Action<CalendarNavigationIntent> {
  final BuildContext context;

  CalendarNavigationAction(this.context);

  @override
  Object? invoke(CalendarNavigationIntent intent) {
    final calendarBloc = context.read<CalendarBloc>();
    final currentState = calendarBloc.state;

    switch (intent.action) {
      case CalendarKeyAction.previousDay:
      case CalendarKeyAction.nextDay:
      case CalendarKeyAction.previousWeek:
      case CalendarKeyAction.nextWeek:
      case CalendarKeyAction.previousMonth:
      case CalendarKeyAction.nextMonth:
      case CalendarKeyAction.previousYear:
      case CalendarKeyAction.nextYear:
      case CalendarKeyAction.goToToday:
        calendarBloc.add(CalendarEvent.keyboardNavigation(
          action: intent.action,
        ));
        break;

      case CalendarKeyAction.selectDate:
        // Space bar - re-select current date (useful for confirming selection)
        calendarBloc.add(CalendarEvent.dateSelected(currentState.selectedDate));
        break;

      case CalendarKeyAction.quickAddTask:
        // Enter key - show quick task creation dialog for selected date
        _showQuickTaskDialog(currentState.selectedDate);
        break;
    }

    return null;
  }

  void _showQuickTaskDialog(DateTime selectedDate) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) {
        String taskTitle = '';

        return AlertDialog(
          title: Text('快速添加任务 - ${_formatDate(selectedDate)}'),
          content: TextField(
            autofocus: true,
            decoration: const InputDecoration(
              hintText: '输入任务标题',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              taskTitle = value;
            },
            onSubmitted: (value) {
              if (value.trim().isNotEmpty) {
                _createQuickTask(value.trim(), selectedDate);
                Navigator.of(dialogContext).pop();
              }
            },
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                if (taskTitle.trim().isNotEmpty) {
                  _createQuickTask(taskTitle.trim(), selectedDate);
                  Navigator.of(dialogContext).pop();
                }
              },
              child: const Text('创建'),
            ),
          ],
        );
      },
    );
  }

  void _createQuickTask(String title, DateTime date) {
    context.read<CalendarBloc>().add(
          CalendarEvent.quickTaskAdded(
            title: title,
            date: date,
            priority: Priority.urgentImportant, // Default to highest priority
          ),
        );

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('任务「$title」创建成功'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月'
    ];
    return '${months[date.month - 1]}${date.day}日';
  }
}

/// Search dialog for task search functionality
class SearchDialog extends StatefulWidget {
  final Function(String) onSearch;
  final VoidCallback onClear;

  const SearchDialog({
    super.key,
    required this.onSearch,
    required this.onClear,
  });

  @override
  State<SearchDialog> createState() => _SearchDialogState();
}

class _SearchDialogState extends State<SearchDialog> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // Auto-focus the search field when dialog opens
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _performSearch() {
    final query = _controller.text.trim();
    if (query.isNotEmpty) {
      widget.onSearch(query);
      if (mounted) {
        Navigator.of(context).pop();
      }
    }
  }

  void _clearSearch() {
    _controller.clear();
    widget.onClear();
    if (mounted) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('搜索任务'),
      content: SizedBox(
        width: 400,
        child: TextField(
          controller: _controller,
          focusNode: _focusNode,
          decoration: const InputDecoration(
            hintText: '输入任务标题或内容...',
            prefixIcon: Icon(Icons.search),
            border: OutlineInputBorder(),
          ),
          onSubmitted: (_) => _performSearch(),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _clearSearch,
          child: const Text('清除'),
        ),
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _performSearch,
          child: const Text('搜索'),
        ),
      ],
    );
  }
}
