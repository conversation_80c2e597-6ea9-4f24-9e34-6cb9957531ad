import 'dart:developer' as developer;

/// Mixin that provides logging capabilities to BLoCs and other classes
mixin LoggingMixin {
  /// Log an informational message
  Future<void> logInfo({
    required String operation,
    required String message,
    Map<String, dynamic>? context,
  }) async {
    final logMessage = 'INFO [$operation]: $message';
    if (context != null) {
      developer.log(
        logMessage,
        name: runtimeType.toString(),
        level: 800, // INFO level
        error: context,
      );
    } else {
      developer.log(
        logMessage,
        name: runtimeType.toString(),
        level: 800,
      );
    }
  }

  /// Log an error message
  Future<void> logError(
    String message, {
    Object? exception,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
    bool canRetry = false,
  }) async {
    final logMessage = 'ERROR: $message${canRetry ? ' (can retry)' : ''}';
    developer.log(
      logMessage,
      name: runtimeType.toString(),
      level: 1000, // ERROR level
      error: exception,
      stackTrace: stackTrace,
    );
    
    if (context != null) {
      developer.log(
        'Context: $context',
        name: runtimeType.toString(),
        level: 1000,
      );
    }
  }

  /// Log a warning message
  Future<void> logWarning(
    String message, {
    Map<String, dynamic>? context,
  }) async {
    final logMessage = 'WARNING: $message';
    developer.log(
      logMessage,
      name: runtimeType.toString(),
      level: 900, // WARNING level
    );
    
    if (context != null) {
      developer.log(
        'Context: $context',
        name: runtimeType.toString(),
        level: 900,
      );
    }
  }

  /// Record performance metrics
  void recordPerformance(String operation, int durationMs) {
    developer.log(
      'PERFORMANCE [$operation]: ${durationMs}ms',
      name: runtimeType.toString(),
      level: 700, // DEBUG level
    );
  }

  /// Log debug information
  void logDebug(String message, {Map<String, dynamic>? context}) {
    developer.log(
      'DEBUG: $message',
      name: runtimeType.toString(),
      level: 700,
    );
    
    if (context != null) {
      developer.log(
        'Context: $context',
        name: runtimeType.toString(),
        level: 700,
      );
    }
  }
}