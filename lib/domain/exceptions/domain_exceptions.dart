/// Base class for all domain-specific exceptions
abstract class DomainException implements Exception {
  final String message;
  final String? code;
  
  const DomainException(this.message, [this.code]);
  
  @override
  String toString() => 'DomainException: $message${code != null ? ' (Code: $code)' : ''}';
}

/// Exception thrown when task validation fails
class TaskValidationException extends DomainException {
  const TaskValidationException(String message) : super(message, 'TASK_VALIDATION_ERROR');
}

/// Exception thrown when task business rules are violated
class TaskBusinessRuleException extends DomainException {
  const TaskBusinessRuleException(String message) : super(message, 'TASK_BUSINESS_RULE_ERROR');
}

/// Exception thrown when task is not found
class TaskNotFoundException extends DomainException {
  const TaskNotFoundException(String taskId) : super('Task with ID $taskId not found', 'TASK_NOT_FOUND');
}

/// Exception thrown when subtask operations fail
class SubTaskException extends DomainException {
  const SubTaskException(String message) : super(message, 'SUBTASK_ERROR');
}

/// Exception thrown when summary calculation fails
class SummaryCalculationException extends DomainException {
  const SummaryCalculationException(String message) : super(message, 'SUMMARY_CALCULATION_ERROR');
}

/// Exception thrown when date-related operations fail
class DateValidationException extends DomainException {
  const DateValidationException(String message) : super(message, 'DATE_VALIDATION_ERROR');
}