/// Repository层异常类
class RepositoryException implements Exception {
  final String message;
  final String? code;
  final Exception? cause;

  const RepositoryException(this.message, {this.code, this.cause});

  /// 数据库相关异常
  factory RepositoryException.database(String message, {Exception? cause}) {
    return RepositoryException(message, code: 'DATABASE_ERROR', cause: cause);
  }

  /// 网络相关异常
  factory RepositoryException.network(String message, {Exception? cause}) {
    return RepositoryException(message, code: 'NETWORK_ERROR', cause: cause);
  }

  /// 数据验证异常
  factory RepositoryException.validation(String message, {Exception? cause}) {
    return RepositoryException(message, code: 'VALIDATION_ERROR', cause: cause);
  }

  /// 未找到数据异常
  factory RepositoryException.notFound(String message, {Exception? cause}) {
    return RepositoryException(message, code: 'NOT_FOUND', cause: cause);
  }

  @override
  String toString() {
    return 'RepositoryException: $message${code != null ? ' (Code: $code)' : ''}';
  }
}