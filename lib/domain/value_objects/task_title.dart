import '../exceptions/domain_exceptions.dart';

/// Value object representing a task title with validation
class TaskTitle {
  static const int maxLength = 200;
  static const int minLength = 1;

  final String value;

  const TaskTitle._(this.value);

  /// Creates a TaskTitle with validation
  factory TaskTitle(String value) {
    final trimmed = value.trim();

    if (trimmed.isEmpty) {
      throw const TaskValidationException('Task title cannot be empty');
    }

    if (trimmed.length < minLength) {
      throw const TaskValidationException(
          'Task title must be at least $minLength character long');
    }

    if (trimmed.length > maxLength) {
      throw const TaskValidationException(
          'Task title cannot exceed $maxLength characters');
    }

    return TaskTitle._(trimmed);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TaskTitle &&
          runtimeType == other.runtimeType &&
          value == other.value;

  @override
  int get hashCode => value.hashCode;

  @override
  String toString() => value;
}
