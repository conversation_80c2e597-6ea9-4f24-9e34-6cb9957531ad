import '../exceptions/domain_exceptions.dart';

/// Value object representing a task ID with validation
class TaskId {
  final String value;
  
  const TaskId._(this.value);
  
  /// Creates a TaskId with validation
  factory TaskId(String value) {
    final trimmed = value.trim();
    
    if (trimmed.isEmpty) {
      throw const TaskValidationException('Task ID cannot be empty');
    }
    
    // Basic UUID format validation (can be enhanced)
    if (!_isValidUuid(trimmed)) {
      throw const TaskValidationException('Task ID must be a valid UUID format');
    }
    
    return TaskId._(trimmed);
  }
  
  /// Generates a new unique TaskId
  factory TaskId.generate() {
    // Using a simple timestamp-based ID for now
    // In production, you might want to use a proper UUID library
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return TaskId._('task_${timestamp}_$random');
  }
  
  static bool _isValidUuid(String value) {
    // Basic validation - can be enhanced with proper UUID regex
    return value.isNotEmpty && value.length >= 8;
  }
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TaskId &&
          runtimeType == other.runtimeType &&
          value == other.value;
  
  @override
  int get hashCode => value.hashCode;
  
  @override
  String toString() => value;
}