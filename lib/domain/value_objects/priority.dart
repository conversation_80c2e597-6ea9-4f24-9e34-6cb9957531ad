/// 任务优先级枚举
/// 
/// 基于艾森豪威尔矩阵的四象限分类法：
/// - 紧急重要：需要立即处理的重要任务
/// - 重要不紧急：重要但可以计划处理的任务  
/// - 紧急不重要：紧急但不重要的任务，可以委派
/// - 不紧急不重要：既不紧急也不重要的任务，可以删除
enum Priority {
  /// 紧急重要 - 第一象限
  urgentImportant('紧急重要', 1, 'urgent_important'),
  
  /// 重要不紧急 - 第二象限
  importantNotUrgent('重要不紧急', 2, 'important_not_urgent'),
  
  /// 紧急不重要 - 第三象限
  urgentNotImportant('紧急不重要', 3, 'urgent_not_important'),
  
  /// 不紧急不重要 - 第四象限
  notUrgentNotImportant('不紧急不重要', 4, 'not_urgent_not_important');

  const Priority(this.displayName, this.quadrant, this.value);

  /// 显示名称
  final String displayName;
  
  /// 象限编号 (1-4)
  final int quadrant;
  
  /// 字符串值，用于数据库存储
  final String value;

  /// 从字符串值创建Priority
  static Priority fromValue(String value) {
    return Priority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => Priority.notUrgentNotImportant,
    );
  }

  /// 获取优先级颜色
  String get colorHex {
    switch (this) {
      case Priority.urgentImportant:
        return '#FF5252'; // 红色
      case Priority.importantNotUrgent:
        return '#FF9800'; // 橙色
      case Priority.urgentNotImportant:
        return '#FFC107'; // 黄色
      case Priority.notUrgentNotImportant:
        return '#4CAF50'; // 绿色
    }
  }

  /// 获取优先级图标
  String get iconName {
    switch (this) {
      case Priority.urgentImportant:
        return 'priority_high';
      case Priority.importantNotUrgent:
        return 'schedule';
      case Priority.urgentNotImportant:
        return 'access_time';
      case Priority.notUrgentNotImportant:
        return 'low_priority';
    }
  }

  /// 获取优先级描述
  String get description {
    switch (this) {
      case Priority.urgentImportant:
        return '立即处理的重要任务';
      case Priority.importantNotUrgent:
        return '重要但可以计划处理';
      case Priority.urgentNotImportant:
        return '紧急但可以委派他人';
      case Priority.notUrgentNotImportant:
        return '可以考虑删除或推迟';
    }
  }

  /// 比较优先级高低
  bool isHigherThan(Priority other) {
    return quadrant < other.quadrant;
  }

  /// 获取所有优先级列表，按重要性排序
  static List<Priority> get sortedByImportance {
    return [
      Priority.urgentImportant,
      Priority.importantNotUrgent,
      Priority.urgentNotImportant,
      Priority.notUrgentNotImportant,
    ];
  }
}