/// Value object representing task notes with validation
class TaskNotes {
  static const int maxLength = 10000; // Reasonable limit for notes
  
  final String value;
  
  const TaskNotes._(this.value);
  
  /// Creates TaskNotes with validation
  factory TaskNotes(String value) {
    final trimmed = value.trim();
    
    if (trimmed.length > maxLength) {
      throw ArgumentError('Task notes cannot exceed $maxLength characters');
    }
    
    return TaskNotes._(trimmed);
  }
  
  /// Creates empty notes
  factory TaskNotes.empty() => const TaskNotes._('');
  
  bool get isEmpty => value.isEmpty;
  bool get isNotEmpty => value.isNotEmpty;
  
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TaskNotes &&
          runtimeType == other.runtimeType &&
          value == other.value;
  
  @override
  int get hashCode => value.hashCode;
  
  @override
  String toString() => value;
}