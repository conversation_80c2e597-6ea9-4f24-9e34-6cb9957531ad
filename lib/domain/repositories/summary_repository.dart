import '../models/summary_report_model.dart';

/// 总结报告生成的核心接口
abstract class SummaryRepository {
  /// 获取指定月份的总结报告。
  ///
  /// [year]: 年份。
  /// [month]: 月份。
  /// 返回一个包含该月统计数据的 [SummaryReport] 实体。
  Future<SummaryReport> getMonthlySummary({
    required int year, 
    required int month,
  });

  /// 获取指定年份的总结报告。
  ///
  /// [year]: 年份。
  /// 返回一个包含该年统计数据的 [SummaryReport] 实体。
  Future<SummaryReport> getYearlySummary({required int year});
}