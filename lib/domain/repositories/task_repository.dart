import '../models/task_model.dart';

/// 任务管理的核心接口
abstract class TaskRepository {
  /// 创建一个新任务。
  ///
  /// [task]: 需要被创建的任务实体。
  /// 成功时返回 Future<void>。
  /// 如果数据库操作失败，则可能抛出异常。
  Future<void> createTask(Task task);

  /// 更新一个已存在的任务。
  ///
  /// [task]: 包含更新后信息的任务实体。
  /// 成功时返回 Future<void>。
  /// 如果任务不存在或数据库操作失败，则可能抛出异常。
  Future<void> updateTask(Task task);

  /// 根据ID删除一个任务。
  ///
  /// [taskId]: 要删除的任务的唯一ID。
  /// 成功时返回 Future<void>。
  Future<void> deleteTask(String taskId);

  /// 根据ID列表批量删除任务。
  ///
  /// [taskIds]: 要删除的任务的唯一ID列表。
  /// 成功时返回 Future<void>。
  Future<void> deleteTasks(List<String> taskIds);

  /// 监听指定日期的所有任务列表。
  ///
  /// [date]: 需要查询的日期。
  /// 返回一个任务列表的Stream，当该日期的任务数据发生
  /// 任何变化（增、删、改）时，Stream会发出新的列表。
  Stream<List<Task>> watchTasksByDate(DateTime date);

  /// 监听指定月份的所有任务列表。
  ///
  /// [year]: 年份。
  /// [month]: 月份。
  /// 返回一个任务列表的Stream，当该月份的任务数据发生
  /// 任何变化（增、删、改）时，Stream会发出新的列表。
  Stream<List<Task>> watchTasksForMonth({
    required int year,
    required int month,
  });

  /// 监听指定月份中每一天的任务"负荷"。
  ///
  /// 用于在日历月视图上显示任务指示器或热力图。
  /// [year]: 年份。
  /// [month]: 月份。
  /// 返回一个Map的Stream，其中Key是日期，Value是该日的任务"负荷"分数。
  /// "负荷"分数的计算规则见 `todo_app_business_details.md`。
  Stream<Map<DateTime, int>> watchTaskLoadForMonth({
    required int year,
    required int month,
  });

  /// 获取指定日期的所有任务。
  ///
  /// [date]: 需要查询的日期。
  /// 返回一个任务列表的Future。
  Future<List<Task>> getTasksForDate(DateTime date);

  /// 获取所有任务。
  ///
  /// 返回一个任务列表的Future。
  Future<List<Task>> getAllTasks();

  /// Get a single task by ID.
  ///
  /// [taskId]: The unique ID of the task.
  /// Returns a Future<Task?> which completes with the task if found, or null.
  Future<Task?> getTaskById(String taskId);

  /// Search tasks with full-text search.
  ///
  /// [query]: The search query.
  /// Returns a list of tasks matching the query.
  Future<List<Task>> searchTasks(String query);
}
