import 'package:freezed_annotation/freezed_annotation.dart';
import '../services/task_validation_service.dart';
import '../exceptions/domain_exceptions.dart';

part 'task_model.freezed.dart';
part 'task_model.g.dart';

/// 任务优先级枚举，对应四象限分类法
enum Priority {
  @JsonValue('urgent_important')
  urgentImportant,
  @JsonValue('important_not_urgent')
  importantNotUrgent,
  @JsonValue('urgent_not_important')
  urgentNotImportant,
  @JsonValue('not_urgent_not_important')
  notUrgentNotImportant,
}

/// Priority 扩展方法
extension PriorityExtension on Priority {
  /// 获取显示名称
  String get displayName {
    switch (this) {
      case Priority.urgentImportant:
        return '紧急重要';
      case Priority.importantNotUrgent:
        return '重要不紧急';
      case Priority.urgentNotImportant:
        return '紧急不重要';
      case Priority.notUrgentNotImportant:
        return '不紧急不重要';
    }
  }

  /// 获取象限编号
  int get quadrant {
    switch (this) {
      case Priority.urgentImportant:
        return 1;
      case Priority.importantNotUrgent:
        return 2;
      case Priority.urgentNotImportant:
        return 3;
      case Priority.notUrgentNotImportant:
        return 4;
    }
  }
}

/// 任务实体
@freezed
class Task with _$Task {
  const factory Task({
    /// 任务的唯一标识符
    required String id,

    /// 任务标题
    required String title,

    /// 任务备注
    @Default('') String notes,

    /// 创建日期
    required DateTime creationDate,

    /// 截止日期
    required DateTime dueDate,

    /// 是否完成
    @Default(false) bool isCompleted,

    /// 完成日期
    DateTime? completionDate,

    /// 优先级
    @Default(Priority.urgentImportant) Priority priority,

    /// 子任务列表
    @Default([]) List<SubTask> subtasks,
  }) = _Task;

  const Task._();

  factory Task.fromJson(Map<String, dynamic> json) => _$TaskFromJson(json);

  /// Creates a new task with validation
  factory Task.create({
    required String title,
    required DateTime dueDate,
    String notes = '',
    Priority priority = Priority.urgentImportant,
    List<SubTask> subtasks = const [],
  }) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDateOnly = DateTime(dueDate.year, dueDate.month, dueDate.day);

    // 设置合理的创建日期和截止日期
    DateTime creationDate;
    DateTime normalizedDueDate;

    if (dueDateOnly.isBefore(today)) {
      // 对于过去的截止日期，创建日期设置为截止日期当天的开始时间
      creationDate = DateTime(dueDate.year, dueDate.month, dueDate.day);
      // 截止日期设置为当天的结束时间
      normalizedDueDate = DateTime(dueDate.year, dueDate.month, dueDate.day, 23, 59, 59);
    } else if (dueDateOnly.isAtSameMomentAs(today)) {
      // 对于今天的截止日期，创建日期为当前时间，截止日期为今天结束时间
      creationDate = now;
      normalizedDueDate = DateTime(dueDate.year, dueDate.month, dueDate.day, 23, 59, 59);
    } else {
      // 对于未来的截止日期，创建日期设置为当前时间
      creationDate = now;
      // 如果用户只选择了日期，设置为当天的结束时间；否则保持原时间
      if (dueDate.hour == 0 && dueDate.minute == 0 && dueDate.second == 0) {
        normalizedDueDate = DateTime(dueDate.year, dueDate.month, dueDate.day, 23, 59, 59);
      } else {
        normalizedDueDate = dueDate;
      }
    }

    // Generate task ID first
    final taskId = _generateId();

    // Fix subtask parent IDs to reference the actual task ID
    final fixedSubtasks = subtasks.map((subtask) {
      // If parentTaskId is temporary or empty, update it to the actual task ID
      if (subtask.parentTaskId == 'temp_parent' ||
          subtask.parentTaskId.trim().isEmpty) {
        return subtask.copyWith(parentTaskId: taskId);
      }
      return subtask;
    }).toList();

    final task = Task(
      id: taskId,
      title: title,
      notes: notes,
      creationDate: creationDate,
      dueDate: normalizedDueDate,
      priority: priority,
      subtasks: fixedSubtasks,
    );

    // Validate the created task
    TaskValidationService.validateTask(task);
    return task;
  }

  /// Marks the task as completed
  Task markAsCompleted() {
    if (isCompleted) {
      throw const TaskBusinessRuleException('Task is already completed');
    }

    return copyWith(
      isCompleted: true,
      completionDate: DateTime.now(),
    );
  }

  /// Marks the task as incomplete
  Task markAsIncomplete() {
    if (!isCompleted) {
      throw const TaskBusinessRuleException('Task is already incomplete');
    }

    return copyWith(
      isCompleted: false,
      completionDate: null,
    );
  }

  /// Updates the task with validation
  Task updateWith({
    String? title,
    String? notes,
    DateTime? dueDate,
    Priority? priority,
    List<SubTask>? subtasks,
  }) {
    DateTime finalDueDate = dueDate ?? this.dueDate;
    DateTime finalCreationDate = creationDate;

    // 如果新的截止日期早于创建日期，调整创建日期
    if (dueDate != null && finalDueDate.isBefore(finalCreationDate)) {
      // 将创建日期设置为截止日期当天的开始时间
      finalCreationDate = DateTime(
        finalDueDate.year,
        finalDueDate.month,
        finalDueDate.day,
      );
    }

    final updatedTask = copyWith(
      title: title ?? this.title,
      notes: notes ?? this.notes,
      dueDate: finalDueDate,
      priority: priority ?? this.priority,
      subtasks: subtasks ?? this.subtasks,
      creationDate: finalCreationDate,
    );

    // Validate the updated task
    TaskValidationService.validateTask(updatedTask);
    return updatedTask;
  }

  /// Adds a subtask to this task
  Task addSubtask(String subtaskTitle) {
    if (subtasks.length >= TaskValidationService.maxSubtasks) {
      throw const SubTaskException(
          'Cannot add more than ${TaskValidationService.maxSubtasks} subtasks');
    }

    final newSubtask = SubTask(
      id: _generateId(),
      parentTaskId: id,
      title: subtaskTitle,
    );

    return copyWith(
      subtasks: [...subtasks, newSubtask],
    );
  }

  /// Removes a subtask from this task
  Task removeSubtask(String subtaskId) {
    final updatedSubtasks = subtasks.where((st) => st.id != subtaskId).toList();
    return copyWith(subtasks: updatedSubtasks);
  }

  /// Updates a subtask in this task
  Task updateSubtask(String subtaskId, {String? title, bool? isCompleted}) {
    final updatedSubtasks = subtasks.map((st) {
      if (st.id == subtaskId) {
        return st.copyWith(
          title: title ?? st.title,
          isCompleted: isCompleted ?? st.isCompleted,
        );
      }
      return st;
    }).toList();

    return copyWith(subtasks: updatedSubtasks);
  }

  /// Updates subtasks and handles main task completion logic
  Task updateSubtasks(List<SubTask> newSubtasks) {
    // 如果任务已经完成，不需要检查子任务状态
    if (isCompleted) {
      return copyWith(subtasks: newSubtasks);
    }

    // 检查是否所有子任务都已完成
    final allSubtasksCompleted = newSubtasks.isNotEmpty &&
        newSubtasks.every((st) => st.isCompleted);

    // 如果所有子任务都完成了，自动完成主任务
    if (allSubtasksCompleted) {
      return copyWith(
        subtasks: newSubtasks,
        isCompleted: true,
        completionDate: DateTime.now(),
      );
    }

    // 否则只更新子任务
    return copyWith(subtasks: newSubtasks);
  }

  /// Checks if the task is overdue
  bool get isOverdue {
    if (isCompleted) return false;
    return DateTime.now().isAfter(dueDate);
  }

  /// Gets the completion percentage of subtasks
  double get subtaskCompletionRate {
    if (subtasks.isEmpty) return 1.0;
    final completedCount = subtasks.where((st) => st.isCompleted).length;
    return completedCount / subtasks.length;
  }

  /// Checks if all subtasks are completed
  bool get areAllSubtasksCompleted {
    return subtasks.isEmpty || subtasks.every((st) => st.isCompleted);
  }

  /// Gets the priority weight for load calculation
  int get priorityWeight {
    switch (priority) {
      case Priority.urgentImportant:
        return 4;
      case Priority.importantNotUrgent:
        return 3;
      case Priority.urgentNotImportant:
        return 2;
      case Priority.notUrgentNotImportant:
        return 1;
    }
  }

  /// Generates a unique ID for tasks and subtasks
  static String _generateId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = DateTime.now().microsecondsSinceEpoch % 100000;
    final counter = _idCounter++;
    return 'task_${timestamp}_${random}_$counter';
  }

  static int _idCounter = 0;
}

/// 子任务实体
@freezed
class SubTask with _$SubTask {
  const factory SubTask({
    /// 子任务的唯一标识符
    required String id,

    /// 父任务ID
    required String parentTaskId,

    /// 子任务标题
    required String title,

    /// 是否完成
    @Default(false) bool isCompleted,
  }) = _SubTask;

  const SubTask._();

  /// Custom constructor with validation
  factory SubTask.validated({
    required String id,
    required String parentTaskId,
    required String title,
    bool isCompleted = false,
  }) {
    // Validate parentTaskId
    if (parentTaskId.trim().isEmpty) {
      throw const SubTaskException('Subtask must have a valid parent task ID');
    }

    // Validate title
    if (title.trim().isEmpty) {
      throw const SubTaskException('Subtask title cannot be empty');
    }

    if (title.length > 200) {
      throw const SubTaskException(
          'Subtask title cannot exceed 200 characters');
    }

    // Validate id
    if (id.trim().isEmpty) {
      throw const SubTaskException('Subtask must have a valid ID');
    }

    return SubTask(
      id: id,
      parentTaskId: parentTaskId,
      title: title.trim(),
      isCompleted: isCompleted,
    );
  }

  factory SubTask.fromJson(Map<String, dynamic> json) =>
      _$SubTaskFromJson(json);

  /// Creates a new subtask with validation
  factory SubTask.create({
    required String parentTaskId,
    required String title,
  }) {
    if (title.trim().isEmpty) {
      throw const SubTaskException('Subtask title cannot be empty');
    }

    if (title.length > 200) {
      throw const SubTaskException(
          'Subtask title cannot exceed 200 characters');
    }

    if (parentTaskId.trim().isEmpty) {
      throw const SubTaskException('Subtask must have a valid parent task ID');
    }

    return SubTask(
      id: _generateSubtaskId(),
      parentTaskId: parentTaskId,
      title: title.trim(),
    );
  }

  /// Toggles the completion status of the subtask
  SubTask toggleCompletion() {
    return copyWith(isCompleted: !isCompleted);
  }

  /// Updates the subtask title with validation
  SubTask updateTitle(String newTitle) {
    if (newTitle.trim().isEmpty) {
      throw const SubTaskException('Subtask title cannot be empty');
    }

    if (newTitle.length > 200) {
      throw const SubTaskException(
          'Subtask title cannot exceed 200 characters');
    }

    return copyWith(title: newTitle.trim());
  }

  /// Generates a unique ID for subtasks using high-performance algorithm
  static String _generateSubtaskId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final microseconds = DateTime.now().microsecondsSinceEpoch;
    final counter = _subtaskIdCounter++;

    // 使用更强的哈希算法确保唯一性
    final hash = (timestamp * 31 + microseconds * 17 + counter * 13) % 999999999;
    return 'st_${timestamp}_${hash}_$counter';
  }

  static int _subtaskIdCounter = 0;
}
