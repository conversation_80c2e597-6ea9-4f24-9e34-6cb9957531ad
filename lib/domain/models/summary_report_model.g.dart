// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'summary_report_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SummaryReportImpl _$$SummaryReportImplFromJson(Map<String, dynamic> json) =>
    _$SummaryReportImpl(
      period: json['period'] as String,
      totalTasksCreated: (json['totalTasksCreated'] as num?)?.toInt() ?? 0,
      totalTasksCompleted: (json['totalTasksCompleted'] as num?)?.toInt() ?? 0,
      completionRate: (json['completionRate'] as num?)?.toDouble() ?? 0.0,
      quadrantDistribution:
          (json['quadrantDistribution'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(
                    $enumDecode(_$PriorityEnumMap, k), (e as num).toInt()),
              ) ??
              const {},
      highlights: (json['highlights'] as List<dynamic>?)
              ?.map((e) => Task.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$SummaryReportImplToJson(_$SummaryReportImpl instance) =>
    <String, dynamic>{
      'period': instance.period,
      'totalTasksCreated': instance.totalTasksCreated,
      'totalTasksCompleted': instance.totalTasksCompleted,
      'completionRate': instance.completionRate,
      'quadrantDistribution': instance.quadrantDistribution
          .map((k, e) => MapEntry(_$PriorityEnumMap[k]!, e)),
      'highlights': instance.highlights,
    };

const _$PriorityEnumMap = {
  Priority.urgentImportant: 'urgent_important',
  Priority.importantNotUrgent: 'important_not_urgent',
  Priority.urgentNotImportant: 'urgent_not_important',
  Priority.notUrgentNotImportant: 'not_urgent_not_important',
};
