import 'package:freezed_annotation/freezed_annotation.dart';
import 'task_model.dart';

part 'summary_report_model.freezed.dart';
part 'summary_report_model.g.dart';

/// 总结报告实体
@freezed
class SummaryReport with _$SummaryReport {
  const factory SummaryReport({
    /// 报告周期（如"2025年9月"或"2025年"）
    required String period,
    
    /// 在该周期内创建的任务总数
    @Default(0) int totalTasksCreated,
    
    /// 在该周期内完成的任务总数
    @Default(0) int totalTasksCompleted,
    
    /// 完成率（百分比）
    @Default(0.0) double completionRate,
    
    /// 各象限任务数量分布
    @Default({}) Map<Priority, int> quadrantDistribution,
    
    /// 高光时刻任务列表
    @Default([]) List<Task> highlights,
  }) = _SummaryReport;

  factory SummaryReport.fromJson(Map<String, dynamic> json) => _$SummaryReportFromJson(json);

  /// 创建空的总结报告
  factory SummaryReport.empty() => const SummaryReport(
    period: '',
    totalTasksCreated: 0,
    totalTasksCompleted: 0,
    completionRate: 0.0,
    quadrantDistribution: {},
    highlights: [],
  );
}