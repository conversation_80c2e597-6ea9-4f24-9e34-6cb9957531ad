// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'summary_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SummaryDataImpl _$$SummaryDataImplFromJson(Map<String, dynamic> json) =>
    _$SummaryDataImpl(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      totalTasksCreated: (json['totalTasksCreated'] as num?)?.toInt() ?? 0,
      totalTasksCompleted: (json['totalTasksCompleted'] as num?)?.toInt() ?? 0,
      completionRate: (json['completionRate'] as num?)?.toDouble() ?? 0.0,
      tasksByPriority: (json['tasksByPriority'] as Map<String, dynamic>?)?.map(
            (k, e) =>
                MapEntry($enumDecode(_$PriorityEnumMap, k), (e as num).toInt()),
          ) ??
          const {},
      completedTasksByPriority:
          (json['completedTasksByPriority'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(
                    $enumDecode(_$PriorityEnumMap, k), (e as num).toInt()),
              ) ??
              const {},
      dailyTaskCreation:
          (json['dailyTaskCreation'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(DateTime.parse(k), (e as num).toInt()),
              ) ??
              const {},
      dailyTaskCompletion:
          (json['dailyTaskCompletion'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(DateTime.parse(k), (e as num).toInt()),
              ) ??
              const {},
      averageDailyCreation:
          (json['averageDailyCreation'] as num?)?.toDouble() ?? 0.0,
      averageDailyCompletion:
          (json['averageDailyCompletion'] as num?)?.toDouble() ?? 0.0,
      mostActiveDate: json['mostActiveDate'] == null
          ? null
          : DateTime.parse(json['mostActiveDate'] as String),
      mostActiveDateTaskCount:
          (json['mostActiveDateTaskCount'] as num?)?.toInt() ?? 0,
      consecutiveCompletionDays:
          (json['consecutiveCompletionDays'] as num?)?.toInt() ?? 0,
      longestCompletionStreak:
          (json['longestCompletionStreak'] as num?)?.toInt() ?? 0,
      efficiencyTrend: (json['efficiencyTrend'] as num?)?.toDouble() ?? 0.0,
      calculationDurationMs:
          (json['calculationDurationMs'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$$SummaryDataImplToJson(_$SummaryDataImpl instance) =>
    <String, dynamic>{
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'totalTasksCreated': instance.totalTasksCreated,
      'totalTasksCompleted': instance.totalTasksCompleted,
      'completionRate': instance.completionRate,
      'tasksByPriority': instance.tasksByPriority
          .map((k, e) => MapEntry(_$PriorityEnumMap[k]!, e)),
      'completedTasksByPriority': instance.completedTasksByPriority
          .map((k, e) => MapEntry(_$PriorityEnumMap[k]!, e)),
      'dailyTaskCreation': instance.dailyTaskCreation
          .map((k, e) => MapEntry(k.toIso8601String(), e)),
      'dailyTaskCompletion': instance.dailyTaskCompletion
          .map((k, e) => MapEntry(k.toIso8601String(), e)),
      'averageDailyCreation': instance.averageDailyCreation,
      'averageDailyCompletion': instance.averageDailyCompletion,
      'mostActiveDate': instance.mostActiveDate?.toIso8601String(),
      'mostActiveDateTaskCount': instance.mostActiveDateTaskCount,
      'consecutiveCompletionDays': instance.consecutiveCompletionDays,
      'longestCompletionStreak': instance.longestCompletionStreak,
      'efficiencyTrend': instance.efficiencyTrend,
      'calculationDurationMs': instance.calculationDurationMs,
    };

const _$PriorityEnumMap = {
  Priority.urgentImportant: 'urgent_important',
  Priority.importantNotUrgent: 'important_not_urgent',
  Priority.urgentNotImportant: 'urgent_not_important',
  Priority.notUrgentNotImportant: 'not_urgent_not_important',
};

_$ProductivityTrendsImpl _$$ProductivityTrendsImplFromJson(
        Map<String, dynamic> json) =>
    _$ProductivityTrendsImpl(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      dataPoints: (json['dataPoints'] as List<dynamic>?)
              ?.map((e) => TrendDataPoint.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      overallTrend:
          $enumDecodeNullable(_$TrendDirectionEnumMap, json['overallTrend']) ??
              TrendDirection.stable,
      trendStrength: (json['trendStrength'] as num?)?.toDouble() ?? 0.0,
      predictedNextPeriodCompletion:
          (json['predictedNextPeriodCompletion'] as num?)?.toInt() ?? 0,
      confidence: (json['confidence'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$ProductivityTrendsImplToJson(
        _$ProductivityTrendsImpl instance) =>
    <String, dynamic>{
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'dataPoints': instance.dataPoints,
      'overallTrend': _$TrendDirectionEnumMap[instance.overallTrend]!,
      'trendStrength': instance.trendStrength,
      'predictedNextPeriodCompletion': instance.predictedNextPeriodCompletion,
      'confidence': instance.confidence,
    };

const _$TrendDirectionEnumMap = {
  TrendDirection.up: 'up',
  TrendDirection.down: 'down',
  TrendDirection.stable: 'stable',
  TrendDirection.fluctuating: 'fluctuating',
};

_$TrendDataPointImpl _$$TrendDataPointImplFromJson(Map<String, dynamic> json) =>
    _$TrendDataPointImpl(
      date: DateTime.parse(json['date'] as String),
      completedTasks: (json['completedTasks'] as num?)?.toInt() ?? 0,
      createdTasks: (json['createdTasks'] as num?)?.toInt() ?? 0,
      efficiencyScore: (json['efficiencyScore'] as num?)?.toDouble() ?? 0.0,
      trendChange: (json['trendChange'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$TrendDataPointImplToJson(
        _$TrendDataPointImpl instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'completedTasks': instance.completedTasks,
      'createdTasks': instance.createdTasks,
      'efficiencyScore': instance.efficiencyScore,
      'trendChange': instance.trendChange,
    };

_$QuadrantAnalysisImpl _$$QuadrantAnalysisImplFromJson(
        Map<String, dynamic> json) =>
    _$QuadrantAnalysisImpl(
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: DateTime.parse(json['endDate'] as String),
      taskCounts: (json['taskCounts'] as Map<String, dynamic>?)?.map(
            (k, e) =>
                MapEntry($enumDecode(_$PriorityEnumMap, k), (e as num).toInt()),
          ) ??
          const {},
      completionRates: (json['completionRates'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(
                $enumDecode(_$PriorityEnumMap, k), (e as num).toDouble()),
          ) ??
          const {},
      averageCompletionTime:
          (json['averageCompletionTime'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(
                    $enumDecode(_$PriorityEnumMap, k), (e as num).toDouble()),
              ) ??
              const {},
      efficiencyScores:
          (json['efficiencyScores'] as Map<String, dynamic>?)?.map(
                (k, e) => MapEntry(
                    $enumDecode(_$PriorityEnumMap, k), (e as num).toDouble()),
              ) ??
              const {},
      focusQuadrant:
          $enumDecodeNullable(_$PriorityEnumMap, json['focusQuadrant']),
      mostEfficientQuadrant:
          $enumDecodeNullable(_$PriorityEnumMap, json['mostEfficientQuadrant']),
      balanceScore: (json['balanceScore'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$QuadrantAnalysisImplToJson(
        _$QuadrantAnalysisImpl instance) =>
    <String, dynamic>{
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'taskCounts':
          instance.taskCounts.map((k, e) => MapEntry(_$PriorityEnumMap[k]!, e)),
      'completionRates': instance.completionRates
          .map((k, e) => MapEntry(_$PriorityEnumMap[k]!, e)),
      'averageCompletionTime': instance.averageCompletionTime
          .map((k, e) => MapEntry(_$PriorityEnumMap[k]!, e)),
      'efficiencyScores': instance.efficiencyScores
          .map((k, e) => MapEntry(_$PriorityEnumMap[k]!, e)),
      'focusQuadrant': _$PriorityEnumMap[instance.focusQuadrant],
      'mostEfficientQuadrant':
          _$PriorityEnumMap[instance.mostEfficientQuadrant],
      'balanceScore': instance.balanceScore,
    };

_$CompletionPatternsImpl _$$CompletionPatternsImplFromJson(
        Map<String, dynamic> json) =>
    _$CompletionPatternsImpl(
      analysisPeriod: (json['analysisPeriod'] as num?)?.toInt() ?? 90,
      dailyPatterns: (json['dailyPatterns'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(int.parse(k), (e as num).toDouble()),
          ) ??
          const {},
      weeklyPatterns: (json['weeklyPatterns'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(int.parse(k), (e as num).toDouble()),
          ) ??
          const {},
      monthlyPatterns: (json['monthlyPatterns'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(int.parse(k), (e as num).toDouble()),
          ) ??
          const {},
      bestCompletionHour: (json['bestCompletionHour'] as num?)?.toInt() ?? 0,
      bestCompletionDay: (json['bestCompletionDay'] as num?)?.toInt() ?? 0,
      mostProductiveTimeSlot: json['mostProductiveTimeSlot'] as String? ?? '',
      patternStability: (json['patternStability'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$CompletionPatternsImplToJson(
        _$CompletionPatternsImpl instance) =>
    <String, dynamic>{
      'analysisPeriod': instance.analysisPeriod,
      'dailyPatterns':
          instance.dailyPatterns.map((k, e) => MapEntry(k.toString(), e)),
      'weeklyPatterns':
          instance.weeklyPatterns.map((k, e) => MapEntry(k.toString(), e)),
      'monthlyPatterns':
          instance.monthlyPatterns.map((k, e) => MapEntry(k.toString(), e)),
      'bestCompletionHour': instance.bestCompletionHour,
      'bestCompletionDay': instance.bestCompletionDay,
      'mostProductiveTimeSlot': instance.mostProductiveTimeSlot,
      'patternStability': instance.patternStability,
    };

_$PerformanceMetricsImpl _$$PerformanceMetricsImplFromJson(
        Map<String, dynamic> json) =>
    _$PerformanceMetricsImpl(
      calculationStart: DateTime.parse(json['calculationStart'] as String),
      calculationEnd: DateTime.parse(json['calculationEnd'] as String),
      calculationDurationMs:
          (json['calculationDurationMs'] as num?)?.toInt() ?? 0,
      dataLoadDurationMs: (json['dataLoadDurationMs'] as num?)?.toInt() ?? 0,
      peakMemoryUsage: (json['peakMemoryUsage'] as num?)?.toDouble() ?? 0.0,
      peakCpuUsage: (json['peakCpuUsage'] as num?)?.toDouble() ?? 0.0,
      databaseQueryCount: (json['databaseQueryCount'] as num?)?.toInt() ?? 0,
      averageQueryResponseTime:
          (json['averageQueryResponseTime'] as num?)?.toDouble() ?? 0.0,
      cacheHitRate: (json['cacheHitRate'] as num?)?.toDouble() ?? 0.0,
      performanceScore: (json['performanceScore'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$PerformanceMetricsImplToJson(
        _$PerformanceMetricsImpl instance) =>
    <String, dynamic>{
      'calculationStart': instance.calculationStart.toIso8601String(),
      'calculationEnd': instance.calculationEnd.toIso8601String(),
      'calculationDurationMs': instance.calculationDurationMs,
      'dataLoadDurationMs': instance.dataLoadDurationMs,
      'peakMemoryUsage': instance.peakMemoryUsage,
      'peakCpuUsage': instance.peakCpuUsage,
      'databaseQueryCount': instance.databaseQueryCount,
      'averageQueryResponseTime': instance.averageQueryResponseTime,
      'cacheHitRate': instance.cacheHitRate,
      'performanceScore': instance.performanceScore,
    };

_$TaskHighlightImpl _$$TaskHighlightImplFromJson(Map<String, dynamic> json) =>
    _$TaskHighlightImpl(
      taskId: json['taskId'] as String,
      title: json['title'] as String,
      priority: $enumDecode(_$PriorityEnumMap, json['priority']),
      completionDate: DateTime.parse(json['completionDate'] as String),
      subtaskCount: (json['subtaskCount'] as num?)?.toInt() ?? 0,
      completedSubtasks: (json['completedSubtasks'] as num?)?.toInt() ?? 0,
      highlightReason: json['highlightReason'] as String? ?? '',
      highlightScore: (json['highlightScore'] as num?)?.toDouble() ?? 0.0,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
              const [],
    );

Map<String, dynamic> _$$TaskHighlightImplToJson(_$TaskHighlightImpl instance) =>
    <String, dynamic>{
      'taskId': instance.taskId,
      'title': instance.title,
      'priority': _$PriorityEnumMap[instance.priority]!,
      'completionDate': instance.completionDate.toIso8601String(),
      'subtaskCount': instance.subtaskCount,
      'completedSubtasks': instance.completedSubtasks,
      'highlightReason': instance.highlightReason,
      'highlightScore': instance.highlightScore,
      'tags': instance.tags,
    };

_$ChartDataImpl _$$ChartDataImplFromJson(Map<String, dynamic> json) =>
    _$ChartDataImpl(
      chartType: $enumDecode(_$SummaryChartTypeEnumMap, json['chartType']),
      title: json['title'] as String? ?? '',
      subtitle: json['subtitle'] as String? ?? '',
      dataPoints: (json['dataPoints'] as List<dynamic>?)
              ?.map((e) => ChartDataPoint.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
      xAxisLabels: (json['xAxisLabels'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      yAxisLabels: (json['yAxisLabels'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      chartOptions: json['chartOptions'] as Map<String, dynamic>? ?? const {},
      lastUpdated: json['lastUpdated'] == null
          ? null
          : DateTime.parse(json['lastUpdated'] as String),
    );

Map<String, dynamic> _$$ChartDataImplToJson(_$ChartDataImpl instance) =>
    <String, dynamic>{
      'chartType': _$SummaryChartTypeEnumMap[instance.chartType]!,
      'title': instance.title,
      'subtitle': instance.subtitle,
      'dataPoints': instance.dataPoints,
      'xAxisLabels': instance.xAxisLabels,
      'yAxisLabels': instance.yAxisLabels,
      'chartOptions': instance.chartOptions,
      'lastUpdated': instance.lastUpdated?.toIso8601String(),
    };

const _$SummaryChartTypeEnumMap = {
  SummaryChartType.completionTrend: 'completionTrend',
  SummaryChartType.quadrantDistribution: 'quadrantDistribution',
  SummaryChartType.dailyTaskLoad: 'dailyTaskLoad',
  SummaryChartType.priorityDistribution: 'priorityDistribution',
  SummaryChartType.completionTimeDistribution: 'completionTimeDistribution',
  SummaryChartType.efficiencyTrend: 'efficiencyTrend',
  SummaryChartType.custom: 'custom',
};

_$ChartDataPointImpl _$$ChartDataPointImplFromJson(Map<String, dynamic> json) =>
    _$ChartDataPointImpl(
      label: json['label'] as String,
      value: (json['value'] as num).toDouble(),
      color: json['color'] as String? ?? '',
      description: json['description'] as String? ?? '',
      metadata: json['metadata'] as Map<String, dynamic>? ?? const {},
    );

Map<String, dynamic> _$$ChartDataPointImplToJson(
        _$ChartDataPointImpl instance) =>
    <String, dynamic>{
      'label': instance.label,
      'value': instance.value,
      'color': instance.color,
      'description': instance.description,
      'metadata': instance.metadata,
    };

_$QuadrantMetricsImpl _$$QuadrantMetricsImplFromJson(
        Map<String, dynamic> json) =>
    _$QuadrantMetricsImpl(
      priority: $enumDecode(_$PriorityEnumMap, json['priority']),
      totalTasks: (json['totalTasks'] as num?)?.toInt() ?? 0,
      completedTasks: (json['completedTasks'] as num?)?.toInt() ?? 0,
      completionRate: (json['completionRate'] as num?)?.toDouble() ?? 0.0,
      averageCompletionTime:
          (json['averageCompletionTime'] as num?)?.toDouble() ?? 0.0,
      efficiencyScore: (json['efficiencyScore'] as num?)?.toDouble() ?? 0.0,
      trendChange: (json['trendChange'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$$QuadrantMetricsImplToJson(
        _$QuadrantMetricsImpl instance) =>
    <String, dynamic>{
      'priority': _$PriorityEnumMap[instance.priority]!,
      'totalTasks': instance.totalTasks,
      'completedTasks': instance.completedTasks,
      'completionRate': instance.completionRate,
      'averageCompletionTime': instance.averageCompletionTime,
      'efficiencyScore': instance.efficiencyScore,
      'trendChange': instance.trendChange,
    };

_$PeriodComparisonImpl _$$PeriodComparisonImplFromJson(
        Map<String, dynamic> json) =>
    _$PeriodComparisonImpl(
      period1Start: DateTime.parse(json['period1Start'] as String),
      period1End: DateTime.parse(json['period1End'] as String),
      period2Start: DateTime.parse(json['period2Start'] as String),
      period2End: DateTime.parse(json['period2End'] as String),
      period1TotalTasks: (json['period1TotalTasks'] as num?)?.toInt() ?? 0,
      period2TotalTasks: (json['period2TotalTasks'] as num?)?.toInt() ?? 0,
      period1CompletionRate:
          (json['period1CompletionRate'] as num?)?.toDouble() ?? 0.0,
      period2CompletionRate:
          (json['period2CompletionRate'] as num?)?.toDouble() ?? 0.0,
      taskCountChangePercent:
          (json['taskCountChangePercent'] as num?)?.toDouble() ?? 0.0,
      completionRateChangePercent:
          (json['completionRateChangePercent'] as num?)?.toDouble() ?? 0.0,
      efficiencyChangePercent:
          (json['efficiencyChangePercent'] as num?)?.toDouble() ?? 0.0,
      improvementSuggestions: (json['improvementSuggestions'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$PeriodComparisonImplToJson(
        _$PeriodComparisonImpl instance) =>
    <String, dynamic>{
      'period1Start': instance.period1Start.toIso8601String(),
      'period1End': instance.period1End.toIso8601String(),
      'period2Start': instance.period2Start.toIso8601String(),
      'period2End': instance.period2End.toIso8601String(),
      'period1TotalTasks': instance.period1TotalTasks,
      'period2TotalTasks': instance.period2TotalTasks,
      'period1CompletionRate': instance.period1CompletionRate,
      'period2CompletionRate': instance.period2CompletionRate,
      'taskCountChangePercent': instance.taskCountChangePercent,
      'completionRateChangePercent': instance.completionRateChangePercent,
      'efficiencyChangePercent': instance.efficiencyChangePercent,
      'improvementSuggestions': instance.improvementSuggestions,
    };
