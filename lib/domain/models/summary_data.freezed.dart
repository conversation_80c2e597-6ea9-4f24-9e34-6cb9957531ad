// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'summary_data.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SummaryData _$SummaryDataFromJson(Map<String, dynamic> json) {
  return _SummaryData.fromJson(json);
}

/// @nodoc
mixin _$SummaryData {
  /// 统计开始日期
  DateTime get startDate => throw _privateConstructorUsedError;

  /// 统计结束日期
  DateTime get endDate => throw _privateConstructorUsedError;

  /// 该时间段内创建的任务总数
  int get totalTasksCreated => throw _privateConstructorUsedError;

  /// 该时间段内完成的任务总数
  int get totalTasksCompleted => throw _privateConstructorUsedError;

  /// 任务完成率（百分比）
  double get completionRate => throw _privateConstructorUsedError;

  /// 按优先级分组的任务数量
  Map<Priority, int> get tasksByPriority => throw _privateConstructorUsedError;

  /// 按优先级分组的已完成任务数量
  Map<Priority, int> get completedTasksByPriority =>
      throw _privateConstructorUsedError;

  /// 每日任务创建数量（用于趋势分析）
  Map<DateTime, int> get dailyTaskCreation =>
      throw _privateConstructorUsedError;

  /// 每日任务完成数量（用于趋势分析）
  Map<DateTime, int> get dailyTaskCompletion =>
      throw _privateConstructorUsedError;

  /// 平均每日任务创建数量
  double get averageDailyCreation => throw _privateConstructorUsedError;

  /// 平均每日任务完成数量
  double get averageDailyCompletion => throw _privateConstructorUsedError;

  /// 最活跃的日期（任务创建最多）
  DateTime? get mostActiveDate => throw _privateConstructorUsedError;

  /// 最活跃日期的任务数量
  int get mostActiveDateTaskCount => throw _privateConstructorUsedError;

  /// 连续完成任务的天数
  int get consecutiveCompletionDays => throw _privateConstructorUsedError;

  /// 最长连续完成任务的天数
  int get longestCompletionStreak => throw _privateConstructorUsedError;

  /// 任务完成效率趋势（正数表示提升，负数表示下降）
  double get efficiencyTrend => throw _privateConstructorUsedError;

  /// 数据生成时间
  int get calculationDurationMs => throw _privateConstructorUsedError;

  /// Serializes this SummaryData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SummaryData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SummaryDataCopyWith<SummaryData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SummaryDataCopyWith<$Res> {
  factory $SummaryDataCopyWith(
          SummaryData value, $Res Function(SummaryData) then) =
      _$SummaryDataCopyWithImpl<$Res, SummaryData>;
  @useResult
  $Res call(
      {DateTime startDate,
      DateTime endDate,
      int totalTasksCreated,
      int totalTasksCompleted,
      double completionRate,
      Map<Priority, int> tasksByPriority,
      Map<Priority, int> completedTasksByPriority,
      Map<DateTime, int> dailyTaskCreation,
      Map<DateTime, int> dailyTaskCompletion,
      double averageDailyCreation,
      double averageDailyCompletion,
      DateTime? mostActiveDate,
      int mostActiveDateTaskCount,
      int consecutiveCompletionDays,
      int longestCompletionStreak,
      double efficiencyTrend,
      int calculationDurationMs});
}

/// @nodoc
class _$SummaryDataCopyWithImpl<$Res, $Val extends SummaryData>
    implements $SummaryDataCopyWith<$Res> {
  _$SummaryDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SummaryData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? totalTasksCreated = null,
    Object? totalTasksCompleted = null,
    Object? completionRate = null,
    Object? tasksByPriority = null,
    Object? completedTasksByPriority = null,
    Object? dailyTaskCreation = null,
    Object? dailyTaskCompletion = null,
    Object? averageDailyCreation = null,
    Object? averageDailyCompletion = null,
    Object? mostActiveDate = freezed,
    Object? mostActiveDateTaskCount = null,
    Object? consecutiveCompletionDays = null,
    Object? longestCompletionStreak = null,
    Object? efficiencyTrend = null,
    Object? calculationDurationMs = null,
  }) {
    return _then(_value.copyWith(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      totalTasksCreated: null == totalTasksCreated
          ? _value.totalTasksCreated
          : totalTasksCreated // ignore: cast_nullable_to_non_nullable
              as int,
      totalTasksCompleted: null == totalTasksCompleted
          ? _value.totalTasksCompleted
          : totalTasksCompleted // ignore: cast_nullable_to_non_nullable
              as int,
      completionRate: null == completionRate
          ? _value.completionRate
          : completionRate // ignore: cast_nullable_to_non_nullable
              as double,
      tasksByPriority: null == tasksByPriority
          ? _value.tasksByPriority
          : tasksByPriority // ignore: cast_nullable_to_non_nullable
              as Map<Priority, int>,
      completedTasksByPriority: null == completedTasksByPriority
          ? _value.completedTasksByPriority
          : completedTasksByPriority // ignore: cast_nullable_to_non_nullable
              as Map<Priority, int>,
      dailyTaskCreation: null == dailyTaskCreation
          ? _value.dailyTaskCreation
          : dailyTaskCreation // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      dailyTaskCompletion: null == dailyTaskCompletion
          ? _value.dailyTaskCompletion
          : dailyTaskCompletion // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      averageDailyCreation: null == averageDailyCreation
          ? _value.averageDailyCreation
          : averageDailyCreation // ignore: cast_nullable_to_non_nullable
              as double,
      averageDailyCompletion: null == averageDailyCompletion
          ? _value.averageDailyCompletion
          : averageDailyCompletion // ignore: cast_nullable_to_non_nullable
              as double,
      mostActiveDate: freezed == mostActiveDate
          ? _value.mostActiveDate
          : mostActiveDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      mostActiveDateTaskCount: null == mostActiveDateTaskCount
          ? _value.mostActiveDateTaskCount
          : mostActiveDateTaskCount // ignore: cast_nullable_to_non_nullable
              as int,
      consecutiveCompletionDays: null == consecutiveCompletionDays
          ? _value.consecutiveCompletionDays
          : consecutiveCompletionDays // ignore: cast_nullable_to_non_nullable
              as int,
      longestCompletionStreak: null == longestCompletionStreak
          ? _value.longestCompletionStreak
          : longestCompletionStreak // ignore: cast_nullable_to_non_nullable
              as int,
      efficiencyTrend: null == efficiencyTrend
          ? _value.efficiencyTrend
          : efficiencyTrend // ignore: cast_nullable_to_non_nullable
              as double,
      calculationDurationMs: null == calculationDurationMs
          ? _value.calculationDurationMs
          : calculationDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SummaryDataImplCopyWith<$Res>
    implements $SummaryDataCopyWith<$Res> {
  factory _$$SummaryDataImplCopyWith(
          _$SummaryDataImpl value, $Res Function(_$SummaryDataImpl) then) =
      __$$SummaryDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime startDate,
      DateTime endDate,
      int totalTasksCreated,
      int totalTasksCompleted,
      double completionRate,
      Map<Priority, int> tasksByPriority,
      Map<Priority, int> completedTasksByPriority,
      Map<DateTime, int> dailyTaskCreation,
      Map<DateTime, int> dailyTaskCompletion,
      double averageDailyCreation,
      double averageDailyCompletion,
      DateTime? mostActiveDate,
      int mostActiveDateTaskCount,
      int consecutiveCompletionDays,
      int longestCompletionStreak,
      double efficiencyTrend,
      int calculationDurationMs});
}

/// @nodoc
class __$$SummaryDataImplCopyWithImpl<$Res>
    extends _$SummaryDataCopyWithImpl<$Res, _$SummaryDataImpl>
    implements _$$SummaryDataImplCopyWith<$Res> {
  __$$SummaryDataImplCopyWithImpl(
      _$SummaryDataImpl _value, $Res Function(_$SummaryDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? totalTasksCreated = null,
    Object? totalTasksCompleted = null,
    Object? completionRate = null,
    Object? tasksByPriority = null,
    Object? completedTasksByPriority = null,
    Object? dailyTaskCreation = null,
    Object? dailyTaskCompletion = null,
    Object? averageDailyCreation = null,
    Object? averageDailyCompletion = null,
    Object? mostActiveDate = freezed,
    Object? mostActiveDateTaskCount = null,
    Object? consecutiveCompletionDays = null,
    Object? longestCompletionStreak = null,
    Object? efficiencyTrend = null,
    Object? calculationDurationMs = null,
  }) {
    return _then(_$SummaryDataImpl(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      totalTasksCreated: null == totalTasksCreated
          ? _value.totalTasksCreated
          : totalTasksCreated // ignore: cast_nullable_to_non_nullable
              as int,
      totalTasksCompleted: null == totalTasksCompleted
          ? _value.totalTasksCompleted
          : totalTasksCompleted // ignore: cast_nullable_to_non_nullable
              as int,
      completionRate: null == completionRate
          ? _value.completionRate
          : completionRate // ignore: cast_nullable_to_non_nullable
              as double,
      tasksByPriority: null == tasksByPriority
          ? _value._tasksByPriority
          : tasksByPriority // ignore: cast_nullable_to_non_nullable
              as Map<Priority, int>,
      completedTasksByPriority: null == completedTasksByPriority
          ? _value._completedTasksByPriority
          : completedTasksByPriority // ignore: cast_nullable_to_non_nullable
              as Map<Priority, int>,
      dailyTaskCreation: null == dailyTaskCreation
          ? _value._dailyTaskCreation
          : dailyTaskCreation // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      dailyTaskCompletion: null == dailyTaskCompletion
          ? _value._dailyTaskCompletion
          : dailyTaskCompletion // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      averageDailyCreation: null == averageDailyCreation
          ? _value.averageDailyCreation
          : averageDailyCreation // ignore: cast_nullable_to_non_nullable
              as double,
      averageDailyCompletion: null == averageDailyCompletion
          ? _value.averageDailyCompletion
          : averageDailyCompletion // ignore: cast_nullable_to_non_nullable
              as double,
      mostActiveDate: freezed == mostActiveDate
          ? _value.mostActiveDate
          : mostActiveDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      mostActiveDateTaskCount: null == mostActiveDateTaskCount
          ? _value.mostActiveDateTaskCount
          : mostActiveDateTaskCount // ignore: cast_nullable_to_non_nullable
              as int,
      consecutiveCompletionDays: null == consecutiveCompletionDays
          ? _value.consecutiveCompletionDays
          : consecutiveCompletionDays // ignore: cast_nullable_to_non_nullable
              as int,
      longestCompletionStreak: null == longestCompletionStreak
          ? _value.longestCompletionStreak
          : longestCompletionStreak // ignore: cast_nullable_to_non_nullable
              as int,
      efficiencyTrend: null == efficiencyTrend
          ? _value.efficiencyTrend
          : efficiencyTrend // ignore: cast_nullable_to_non_nullable
              as double,
      calculationDurationMs: null == calculationDurationMs
          ? _value.calculationDurationMs
          : calculationDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SummaryDataImpl implements _SummaryData {
  const _$SummaryDataImpl(
      {required this.startDate,
      required this.endDate,
      this.totalTasksCreated = 0,
      this.totalTasksCompleted = 0,
      this.completionRate = 0.0,
      final Map<Priority, int> tasksByPriority = const {},
      final Map<Priority, int> completedTasksByPriority = const {},
      final Map<DateTime, int> dailyTaskCreation = const {},
      final Map<DateTime, int> dailyTaskCompletion = const {},
      this.averageDailyCreation = 0.0,
      this.averageDailyCompletion = 0.0,
      this.mostActiveDate,
      this.mostActiveDateTaskCount = 0,
      this.consecutiveCompletionDays = 0,
      this.longestCompletionStreak = 0,
      this.efficiencyTrend = 0.0,
      this.calculationDurationMs = 0})
      : _tasksByPriority = tasksByPriority,
        _completedTasksByPriority = completedTasksByPriority,
        _dailyTaskCreation = dailyTaskCreation,
        _dailyTaskCompletion = dailyTaskCompletion;

  factory _$SummaryDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$SummaryDataImplFromJson(json);

  /// 统计开始日期
  @override
  final DateTime startDate;

  /// 统计结束日期
  @override
  final DateTime endDate;

  /// 该时间段内创建的任务总数
  @override
  @JsonKey()
  final int totalTasksCreated;

  /// 该时间段内完成的任务总数
  @override
  @JsonKey()
  final int totalTasksCompleted;

  /// 任务完成率（百分比）
  @override
  @JsonKey()
  final double completionRate;

  /// 按优先级分组的任务数量
  final Map<Priority, int> _tasksByPriority;

  /// 按优先级分组的任务数量
  @override
  @JsonKey()
  Map<Priority, int> get tasksByPriority {
    if (_tasksByPriority is EqualUnmodifiableMapView) return _tasksByPriority;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_tasksByPriority);
  }

  /// 按优先级分组的已完成任务数量
  final Map<Priority, int> _completedTasksByPriority;

  /// 按优先级分组的已完成任务数量
  @override
  @JsonKey()
  Map<Priority, int> get completedTasksByPriority {
    if (_completedTasksByPriority is EqualUnmodifiableMapView)
      return _completedTasksByPriority;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_completedTasksByPriority);
  }

  /// 每日任务创建数量（用于趋势分析）
  final Map<DateTime, int> _dailyTaskCreation;

  /// 每日任务创建数量（用于趋势分析）
  @override
  @JsonKey()
  Map<DateTime, int> get dailyTaskCreation {
    if (_dailyTaskCreation is EqualUnmodifiableMapView)
      return _dailyTaskCreation;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_dailyTaskCreation);
  }

  /// 每日任务完成数量（用于趋势分析）
  final Map<DateTime, int> _dailyTaskCompletion;

  /// 每日任务完成数量（用于趋势分析）
  @override
  @JsonKey()
  Map<DateTime, int> get dailyTaskCompletion {
    if (_dailyTaskCompletion is EqualUnmodifiableMapView)
      return _dailyTaskCompletion;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_dailyTaskCompletion);
  }

  /// 平均每日任务创建数量
  @override
  @JsonKey()
  final double averageDailyCreation;

  /// 平均每日任务完成数量
  @override
  @JsonKey()
  final double averageDailyCompletion;

  /// 最活跃的日期（任务创建最多）
  @override
  final DateTime? mostActiveDate;

  /// 最活跃日期的任务数量
  @override
  @JsonKey()
  final int mostActiveDateTaskCount;

  /// 连续完成任务的天数
  @override
  @JsonKey()
  final int consecutiveCompletionDays;

  /// 最长连续完成任务的天数
  @override
  @JsonKey()
  final int longestCompletionStreak;

  /// 任务完成效率趋势（正数表示提升，负数表示下降）
  @override
  @JsonKey()
  final double efficiencyTrend;

  /// 数据生成时间
  @override
  @JsonKey()
  final int calculationDurationMs;

  @override
  String toString() {
    return 'SummaryData(startDate: $startDate, endDate: $endDate, totalTasksCreated: $totalTasksCreated, totalTasksCompleted: $totalTasksCompleted, completionRate: $completionRate, tasksByPriority: $tasksByPriority, completedTasksByPriority: $completedTasksByPriority, dailyTaskCreation: $dailyTaskCreation, dailyTaskCompletion: $dailyTaskCompletion, averageDailyCreation: $averageDailyCreation, averageDailyCompletion: $averageDailyCompletion, mostActiveDate: $mostActiveDate, mostActiveDateTaskCount: $mostActiveDateTaskCount, consecutiveCompletionDays: $consecutiveCompletionDays, longestCompletionStreak: $longestCompletionStreak, efficiencyTrend: $efficiencyTrend, calculationDurationMs: $calculationDurationMs)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SummaryDataImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.totalTasksCreated, totalTasksCreated) ||
                other.totalTasksCreated == totalTasksCreated) &&
            (identical(other.totalTasksCompleted, totalTasksCompleted) ||
                other.totalTasksCompleted == totalTasksCompleted) &&
            (identical(other.completionRate, completionRate) ||
                other.completionRate == completionRate) &&
            const DeepCollectionEquality()
                .equals(other._tasksByPriority, _tasksByPriority) &&
            const DeepCollectionEquality().equals(
                other._completedTasksByPriority, _completedTasksByPriority) &&
            const DeepCollectionEquality()
                .equals(other._dailyTaskCreation, _dailyTaskCreation) &&
            const DeepCollectionEquality()
                .equals(other._dailyTaskCompletion, _dailyTaskCompletion) &&
            (identical(other.averageDailyCreation, averageDailyCreation) ||
                other.averageDailyCreation == averageDailyCreation) &&
            (identical(other.averageDailyCompletion, averageDailyCompletion) ||
                other.averageDailyCompletion == averageDailyCompletion) &&
            (identical(other.mostActiveDate, mostActiveDate) ||
                other.mostActiveDate == mostActiveDate) &&
            (identical(
                    other.mostActiveDateTaskCount, mostActiveDateTaskCount) ||
                other.mostActiveDateTaskCount == mostActiveDateTaskCount) &&
            (identical(other.consecutiveCompletionDays,
                    consecutiveCompletionDays) ||
                other.consecutiveCompletionDays == consecutiveCompletionDays) &&
            (identical(
                    other.longestCompletionStreak, longestCompletionStreak) ||
                other.longestCompletionStreak == longestCompletionStreak) &&
            (identical(other.efficiencyTrend, efficiencyTrend) ||
                other.efficiencyTrend == efficiencyTrend) &&
            (identical(other.calculationDurationMs, calculationDurationMs) ||
                other.calculationDurationMs == calculationDurationMs));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      startDate,
      endDate,
      totalTasksCreated,
      totalTasksCompleted,
      completionRate,
      const DeepCollectionEquality().hash(_tasksByPriority),
      const DeepCollectionEquality().hash(_completedTasksByPriority),
      const DeepCollectionEquality().hash(_dailyTaskCreation),
      const DeepCollectionEquality().hash(_dailyTaskCompletion),
      averageDailyCreation,
      averageDailyCompletion,
      mostActiveDate,
      mostActiveDateTaskCount,
      consecutiveCompletionDays,
      longestCompletionStreak,
      efficiencyTrend,
      calculationDurationMs);

  /// Create a copy of SummaryData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SummaryDataImplCopyWith<_$SummaryDataImpl> get copyWith =>
      __$$SummaryDataImplCopyWithImpl<_$SummaryDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SummaryDataImplToJson(
      this,
    );
  }
}

abstract class _SummaryData implements SummaryData {
  const factory _SummaryData(
      {required final DateTime startDate,
      required final DateTime endDate,
      final int totalTasksCreated,
      final int totalTasksCompleted,
      final double completionRate,
      final Map<Priority, int> tasksByPriority,
      final Map<Priority, int> completedTasksByPriority,
      final Map<DateTime, int> dailyTaskCreation,
      final Map<DateTime, int> dailyTaskCompletion,
      final double averageDailyCreation,
      final double averageDailyCompletion,
      final DateTime? mostActiveDate,
      final int mostActiveDateTaskCount,
      final int consecutiveCompletionDays,
      final int longestCompletionStreak,
      final double efficiencyTrend,
      final int calculationDurationMs}) = _$SummaryDataImpl;

  factory _SummaryData.fromJson(Map<String, dynamic> json) =
      _$SummaryDataImpl.fromJson;

  /// 统计开始日期
  @override
  DateTime get startDate;

  /// 统计结束日期
  @override
  DateTime get endDate;

  /// 该时间段内创建的任务总数
  @override
  int get totalTasksCreated;

  /// 该时间段内完成的任务总数
  @override
  int get totalTasksCompleted;

  /// 任务完成率（百分比）
  @override
  double get completionRate;

  /// 按优先级分组的任务数量
  @override
  Map<Priority, int> get tasksByPriority;

  /// 按优先级分组的已完成任务数量
  @override
  Map<Priority, int> get completedTasksByPriority;

  /// 每日任务创建数量（用于趋势分析）
  @override
  Map<DateTime, int> get dailyTaskCreation;

  /// 每日任务完成数量（用于趋势分析）
  @override
  Map<DateTime, int> get dailyTaskCompletion;

  /// 平均每日任务创建数量
  @override
  double get averageDailyCreation;

  /// 平均每日任务完成数量
  @override
  double get averageDailyCompletion;

  /// 最活跃的日期（任务创建最多）
  @override
  DateTime? get mostActiveDate;

  /// 最活跃日期的任务数量
  @override
  int get mostActiveDateTaskCount;

  /// 连续完成任务的天数
  @override
  int get consecutiveCompletionDays;

  /// 最长连续完成任务的天数
  @override
  int get longestCompletionStreak;

  /// 任务完成效率趋势（正数表示提升，负数表示下降）
  @override
  double get efficiencyTrend;

  /// 数据生成时间
  @override
  int get calculationDurationMs;

  /// Create a copy of SummaryData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SummaryDataImplCopyWith<_$SummaryDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ProductivityTrends _$ProductivityTrendsFromJson(Map<String, dynamic> json) {
  return _ProductivityTrends.fromJson(json);
}

/// @nodoc
mixin _$ProductivityTrends {
  /// 趋势分析开始日期
  DateTime get startDate => throw _privateConstructorUsedError;

  /// 趋势分析结束日期
  DateTime get endDate => throw _privateConstructorUsedError;

  /// 趋势数据点列表
  List<TrendDataPoint> get dataPoints => throw _privateConstructorUsedError;

  /// 整体趋势方向（上升/下降/稳定）
  TrendDirection get overallTrend => throw _privateConstructorUsedError;

  /// 趋势强度（0.0-1.0）
  double get trendStrength => throw _privateConstructorUsedError;

  /// 预测的下一个周期任务完成数量
  int get predictedNextPeriodCompletion => throw _privateConstructorUsedError;

  /// 趋势置信度（0.0-1.0）
  double get confidence => throw _privateConstructorUsedError;

  /// Serializes this ProductivityTrends to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ProductivityTrends
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ProductivityTrendsCopyWith<ProductivityTrends> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductivityTrendsCopyWith<$Res> {
  factory $ProductivityTrendsCopyWith(
          ProductivityTrends value, $Res Function(ProductivityTrends) then) =
      _$ProductivityTrendsCopyWithImpl<$Res, ProductivityTrends>;
  @useResult
  $Res call(
      {DateTime startDate,
      DateTime endDate,
      List<TrendDataPoint> dataPoints,
      TrendDirection overallTrend,
      double trendStrength,
      int predictedNextPeriodCompletion,
      double confidence});
}

/// @nodoc
class _$ProductivityTrendsCopyWithImpl<$Res, $Val extends ProductivityTrends>
    implements $ProductivityTrendsCopyWith<$Res> {
  _$ProductivityTrendsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ProductivityTrends
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? dataPoints = null,
    Object? overallTrend = null,
    Object? trendStrength = null,
    Object? predictedNextPeriodCompletion = null,
    Object? confidence = null,
  }) {
    return _then(_value.copyWith(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      dataPoints: null == dataPoints
          ? _value.dataPoints
          : dataPoints // ignore: cast_nullable_to_non_nullable
              as List<TrendDataPoint>,
      overallTrend: null == overallTrend
          ? _value.overallTrend
          : overallTrend // ignore: cast_nullable_to_non_nullable
              as TrendDirection,
      trendStrength: null == trendStrength
          ? _value.trendStrength
          : trendStrength // ignore: cast_nullable_to_non_nullable
              as double,
      predictedNextPeriodCompletion: null == predictedNextPeriodCompletion
          ? _value.predictedNextPeriodCompletion
          : predictedNextPeriodCompletion // ignore: cast_nullable_to_non_nullable
              as int,
      confidence: null == confidence
          ? _value.confidence
          : confidence // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductivityTrendsImplCopyWith<$Res>
    implements $ProductivityTrendsCopyWith<$Res> {
  factory _$$ProductivityTrendsImplCopyWith(_$ProductivityTrendsImpl value,
          $Res Function(_$ProductivityTrendsImpl) then) =
      __$$ProductivityTrendsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime startDate,
      DateTime endDate,
      List<TrendDataPoint> dataPoints,
      TrendDirection overallTrend,
      double trendStrength,
      int predictedNextPeriodCompletion,
      double confidence});
}

/// @nodoc
class __$$ProductivityTrendsImplCopyWithImpl<$Res>
    extends _$ProductivityTrendsCopyWithImpl<$Res, _$ProductivityTrendsImpl>
    implements _$$ProductivityTrendsImplCopyWith<$Res> {
  __$$ProductivityTrendsImplCopyWithImpl(_$ProductivityTrendsImpl _value,
      $Res Function(_$ProductivityTrendsImpl) _then)
      : super(_value, _then);

  /// Create a copy of ProductivityTrends
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? dataPoints = null,
    Object? overallTrend = null,
    Object? trendStrength = null,
    Object? predictedNextPeriodCompletion = null,
    Object? confidence = null,
  }) {
    return _then(_$ProductivityTrendsImpl(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      dataPoints: null == dataPoints
          ? _value._dataPoints
          : dataPoints // ignore: cast_nullable_to_non_nullable
              as List<TrendDataPoint>,
      overallTrend: null == overallTrend
          ? _value.overallTrend
          : overallTrend // ignore: cast_nullable_to_non_nullable
              as TrendDirection,
      trendStrength: null == trendStrength
          ? _value.trendStrength
          : trendStrength // ignore: cast_nullable_to_non_nullable
              as double,
      predictedNextPeriodCompletion: null == predictedNextPeriodCompletion
          ? _value.predictedNextPeriodCompletion
          : predictedNextPeriodCompletion // ignore: cast_nullable_to_non_nullable
              as int,
      confidence: null == confidence
          ? _value.confidence
          : confidence // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductivityTrendsImpl implements _ProductivityTrends {
  const _$ProductivityTrendsImpl(
      {required this.startDate,
      required this.endDate,
      final List<TrendDataPoint> dataPoints = const [],
      this.overallTrend = TrendDirection.stable,
      this.trendStrength = 0.0,
      this.predictedNextPeriodCompletion = 0,
      this.confidence = 0.0})
      : _dataPoints = dataPoints;

  factory _$ProductivityTrendsImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductivityTrendsImplFromJson(json);

  /// 趋势分析开始日期
  @override
  final DateTime startDate;

  /// 趋势分析结束日期
  @override
  final DateTime endDate;

  /// 趋势数据点列表
  final List<TrendDataPoint> _dataPoints;

  /// 趋势数据点列表
  @override
  @JsonKey()
  List<TrendDataPoint> get dataPoints {
    if (_dataPoints is EqualUnmodifiableListView) return _dataPoints;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dataPoints);
  }

  /// 整体趋势方向（上升/下降/稳定）
  @override
  @JsonKey()
  final TrendDirection overallTrend;

  /// 趋势强度（0.0-1.0）
  @override
  @JsonKey()
  final double trendStrength;

  /// 预测的下一个周期任务完成数量
  @override
  @JsonKey()
  final int predictedNextPeriodCompletion;

  /// 趋势置信度（0.0-1.0）
  @override
  @JsonKey()
  final double confidence;

  @override
  String toString() {
    return 'ProductivityTrends(startDate: $startDate, endDate: $endDate, dataPoints: $dataPoints, overallTrend: $overallTrend, trendStrength: $trendStrength, predictedNextPeriodCompletion: $predictedNextPeriodCompletion, confidence: $confidence)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductivityTrendsImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            const DeepCollectionEquality()
                .equals(other._dataPoints, _dataPoints) &&
            (identical(other.overallTrend, overallTrend) ||
                other.overallTrend == overallTrend) &&
            (identical(other.trendStrength, trendStrength) ||
                other.trendStrength == trendStrength) &&
            (identical(other.predictedNextPeriodCompletion,
                    predictedNextPeriodCompletion) ||
                other.predictedNextPeriodCompletion ==
                    predictedNextPeriodCompletion) &&
            (identical(other.confidence, confidence) ||
                other.confidence == confidence));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      startDate,
      endDate,
      const DeepCollectionEquality().hash(_dataPoints),
      overallTrend,
      trendStrength,
      predictedNextPeriodCompletion,
      confidence);

  /// Create a copy of ProductivityTrends
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductivityTrendsImplCopyWith<_$ProductivityTrendsImpl> get copyWith =>
      __$$ProductivityTrendsImplCopyWithImpl<_$ProductivityTrendsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductivityTrendsImplToJson(
      this,
    );
  }
}

abstract class _ProductivityTrends implements ProductivityTrends {
  const factory _ProductivityTrends(
      {required final DateTime startDate,
      required final DateTime endDate,
      final List<TrendDataPoint> dataPoints,
      final TrendDirection overallTrend,
      final double trendStrength,
      final int predictedNextPeriodCompletion,
      final double confidence}) = _$ProductivityTrendsImpl;

  factory _ProductivityTrends.fromJson(Map<String, dynamic> json) =
      _$ProductivityTrendsImpl.fromJson;

  /// 趋势分析开始日期
  @override
  DateTime get startDate;

  /// 趋势分析结束日期
  @override
  DateTime get endDate;

  /// 趋势数据点列表
  @override
  List<TrendDataPoint> get dataPoints;

  /// 整体趋势方向（上升/下降/稳定）
  @override
  TrendDirection get overallTrend;

  /// 趋势强度（0.0-1.0）
  @override
  double get trendStrength;

  /// 预测的下一个周期任务完成数量
  @override
  int get predictedNextPeriodCompletion;

  /// 趋势置信度（0.0-1.0）
  @override
  double get confidence;

  /// Create a copy of ProductivityTrends
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductivityTrendsImplCopyWith<_$ProductivityTrendsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TrendDataPoint _$TrendDataPointFromJson(Map<String, dynamic> json) {
  return _TrendDataPoint.fromJson(json);
}

/// @nodoc
mixin _$TrendDataPoint {
  /// 数据点日期
  DateTime get date => throw _privateConstructorUsedError;

  /// 当日任务完成数量
  int get completedTasks => throw _privateConstructorUsedError;

  /// 当日任务创建数量
  int get createdTasks => throw _privateConstructorUsedError;

  /// 当日效率分数
  double get efficiencyScore => throw _privateConstructorUsedError;

  /// 与前一日的趋势变化
  double get trendChange => throw _privateConstructorUsedError;

  /// Serializes this TrendDataPoint to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TrendDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TrendDataPointCopyWith<TrendDataPoint> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TrendDataPointCopyWith<$Res> {
  factory $TrendDataPointCopyWith(
          TrendDataPoint value, $Res Function(TrendDataPoint) then) =
      _$TrendDataPointCopyWithImpl<$Res, TrendDataPoint>;
  @useResult
  $Res call(
      {DateTime date,
      int completedTasks,
      int createdTasks,
      double efficiencyScore,
      double trendChange});
}

/// @nodoc
class _$TrendDataPointCopyWithImpl<$Res, $Val extends TrendDataPoint>
    implements $TrendDataPointCopyWith<$Res> {
  _$TrendDataPointCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TrendDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? completedTasks = null,
    Object? createdTasks = null,
    Object? efficiencyScore = null,
    Object? trendChange = null,
  }) {
    return _then(_value.copyWith(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      completedTasks: null == completedTasks
          ? _value.completedTasks
          : completedTasks // ignore: cast_nullable_to_non_nullable
              as int,
      createdTasks: null == createdTasks
          ? _value.createdTasks
          : createdTasks // ignore: cast_nullable_to_non_nullable
              as int,
      efficiencyScore: null == efficiencyScore
          ? _value.efficiencyScore
          : efficiencyScore // ignore: cast_nullable_to_non_nullable
              as double,
      trendChange: null == trendChange
          ? _value.trendChange
          : trendChange // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TrendDataPointImplCopyWith<$Res>
    implements $TrendDataPointCopyWith<$Res> {
  factory _$$TrendDataPointImplCopyWith(_$TrendDataPointImpl value,
          $Res Function(_$TrendDataPointImpl) then) =
      __$$TrendDataPointImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime date,
      int completedTasks,
      int createdTasks,
      double efficiencyScore,
      double trendChange});
}

/// @nodoc
class __$$TrendDataPointImplCopyWithImpl<$Res>
    extends _$TrendDataPointCopyWithImpl<$Res, _$TrendDataPointImpl>
    implements _$$TrendDataPointImplCopyWith<$Res> {
  __$$TrendDataPointImplCopyWithImpl(
      _$TrendDataPointImpl _value, $Res Function(_$TrendDataPointImpl) _then)
      : super(_value, _then);

  /// Create a copy of TrendDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? completedTasks = null,
    Object? createdTasks = null,
    Object? efficiencyScore = null,
    Object? trendChange = null,
  }) {
    return _then(_$TrendDataPointImpl(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      completedTasks: null == completedTasks
          ? _value.completedTasks
          : completedTasks // ignore: cast_nullable_to_non_nullable
              as int,
      createdTasks: null == createdTasks
          ? _value.createdTasks
          : createdTasks // ignore: cast_nullable_to_non_nullable
              as int,
      efficiencyScore: null == efficiencyScore
          ? _value.efficiencyScore
          : efficiencyScore // ignore: cast_nullable_to_non_nullable
              as double,
      trendChange: null == trendChange
          ? _value.trendChange
          : trendChange // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TrendDataPointImpl implements _TrendDataPoint {
  const _$TrendDataPointImpl(
      {required this.date,
      this.completedTasks = 0,
      this.createdTasks = 0,
      this.efficiencyScore = 0.0,
      this.trendChange = 0.0});

  factory _$TrendDataPointImpl.fromJson(Map<String, dynamic> json) =>
      _$$TrendDataPointImplFromJson(json);

  /// 数据点日期
  @override
  final DateTime date;

  /// 当日任务完成数量
  @override
  @JsonKey()
  final int completedTasks;

  /// 当日任务创建数量
  @override
  @JsonKey()
  final int createdTasks;

  /// 当日效率分数
  @override
  @JsonKey()
  final double efficiencyScore;

  /// 与前一日的趋势变化
  @override
  @JsonKey()
  final double trendChange;

  @override
  String toString() {
    return 'TrendDataPoint(date: $date, completedTasks: $completedTasks, createdTasks: $createdTasks, efficiencyScore: $efficiencyScore, trendChange: $trendChange)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TrendDataPointImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.completedTasks, completedTasks) ||
                other.completedTasks == completedTasks) &&
            (identical(other.createdTasks, createdTasks) ||
                other.createdTasks == createdTasks) &&
            (identical(other.efficiencyScore, efficiencyScore) ||
                other.efficiencyScore == efficiencyScore) &&
            (identical(other.trendChange, trendChange) ||
                other.trendChange == trendChange));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, date, completedTasks,
      createdTasks, efficiencyScore, trendChange);

  /// Create a copy of TrendDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TrendDataPointImplCopyWith<_$TrendDataPointImpl> get copyWith =>
      __$$TrendDataPointImplCopyWithImpl<_$TrendDataPointImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TrendDataPointImplToJson(
      this,
    );
  }
}

abstract class _TrendDataPoint implements TrendDataPoint {
  const factory _TrendDataPoint(
      {required final DateTime date,
      final int completedTasks,
      final int createdTasks,
      final double efficiencyScore,
      final double trendChange}) = _$TrendDataPointImpl;

  factory _TrendDataPoint.fromJson(Map<String, dynamic> json) =
      _$TrendDataPointImpl.fromJson;

  /// 数据点日期
  @override
  DateTime get date;

  /// 当日任务完成数量
  @override
  int get completedTasks;

  /// 当日任务创建数量
  @override
  int get createdTasks;

  /// 当日效率分数
  @override
  double get efficiencyScore;

  /// 与前一日的趋势变化
  @override
  double get trendChange;

  /// Create a copy of TrendDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TrendDataPointImplCopyWith<_$TrendDataPointImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

QuadrantAnalysis _$QuadrantAnalysisFromJson(Map<String, dynamic> json) {
  return _QuadrantAnalysis.fromJson(json);
}

/// @nodoc
mixin _$QuadrantAnalysis {
  /// 分析时间范围
  DateTime get startDate => throw _privateConstructorUsedError;
  DateTime get endDate => throw _privateConstructorUsedError;

  /// 各象限的任务数量
  Map<Priority, int> get taskCounts => throw _privateConstructorUsedError;

  /// 各象限的完成率
  Map<Priority, double> get completionRates =>
      throw _privateConstructorUsedError;

  /// 各象限的平均完成时间（小时）
  Map<Priority, double> get averageCompletionTime =>
      throw _privateConstructorUsedError;

  /// 各象限的效率分数
  Map<Priority, double> get efficiencyScores =>
      throw _privateConstructorUsedError;

  /// 最需要关注的象限（效率最低）
  Priority? get focusQuadrant => throw _privateConstructorUsedError;

  /// 最有效率的象限
  Priority? get mostEfficientQuadrant => throw _privateConstructorUsedError;

  /// 象限平衡性评分（0.0-1.0，1.0表示完全平衡）
  double get balanceScore => throw _privateConstructorUsedError;

  /// Serializes this QuadrantAnalysis to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of QuadrantAnalysis
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QuadrantAnalysisCopyWith<QuadrantAnalysis> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuadrantAnalysisCopyWith<$Res> {
  factory $QuadrantAnalysisCopyWith(
          QuadrantAnalysis value, $Res Function(QuadrantAnalysis) then) =
      _$QuadrantAnalysisCopyWithImpl<$Res, QuadrantAnalysis>;
  @useResult
  $Res call(
      {DateTime startDate,
      DateTime endDate,
      Map<Priority, int> taskCounts,
      Map<Priority, double> completionRates,
      Map<Priority, double> averageCompletionTime,
      Map<Priority, double> efficiencyScores,
      Priority? focusQuadrant,
      Priority? mostEfficientQuadrant,
      double balanceScore});
}

/// @nodoc
class _$QuadrantAnalysisCopyWithImpl<$Res, $Val extends QuadrantAnalysis>
    implements $QuadrantAnalysisCopyWith<$Res> {
  _$QuadrantAnalysisCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QuadrantAnalysis
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? taskCounts = null,
    Object? completionRates = null,
    Object? averageCompletionTime = null,
    Object? efficiencyScores = null,
    Object? focusQuadrant = freezed,
    Object? mostEfficientQuadrant = freezed,
    Object? balanceScore = null,
  }) {
    return _then(_value.copyWith(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      taskCounts: null == taskCounts
          ? _value.taskCounts
          : taskCounts // ignore: cast_nullable_to_non_nullable
              as Map<Priority, int>,
      completionRates: null == completionRates
          ? _value.completionRates
          : completionRates // ignore: cast_nullable_to_non_nullable
              as Map<Priority, double>,
      averageCompletionTime: null == averageCompletionTime
          ? _value.averageCompletionTime
          : averageCompletionTime // ignore: cast_nullable_to_non_nullable
              as Map<Priority, double>,
      efficiencyScores: null == efficiencyScores
          ? _value.efficiencyScores
          : efficiencyScores // ignore: cast_nullable_to_non_nullable
              as Map<Priority, double>,
      focusQuadrant: freezed == focusQuadrant
          ? _value.focusQuadrant
          : focusQuadrant // ignore: cast_nullable_to_non_nullable
              as Priority?,
      mostEfficientQuadrant: freezed == mostEfficientQuadrant
          ? _value.mostEfficientQuadrant
          : mostEfficientQuadrant // ignore: cast_nullable_to_non_nullable
              as Priority?,
      balanceScore: null == balanceScore
          ? _value.balanceScore
          : balanceScore // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuadrantAnalysisImplCopyWith<$Res>
    implements $QuadrantAnalysisCopyWith<$Res> {
  factory _$$QuadrantAnalysisImplCopyWith(_$QuadrantAnalysisImpl value,
          $Res Function(_$QuadrantAnalysisImpl) then) =
      __$$QuadrantAnalysisImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime startDate,
      DateTime endDate,
      Map<Priority, int> taskCounts,
      Map<Priority, double> completionRates,
      Map<Priority, double> averageCompletionTime,
      Map<Priority, double> efficiencyScores,
      Priority? focusQuadrant,
      Priority? mostEfficientQuadrant,
      double balanceScore});
}

/// @nodoc
class __$$QuadrantAnalysisImplCopyWithImpl<$Res>
    extends _$QuadrantAnalysisCopyWithImpl<$Res, _$QuadrantAnalysisImpl>
    implements _$$QuadrantAnalysisImplCopyWith<$Res> {
  __$$QuadrantAnalysisImplCopyWithImpl(_$QuadrantAnalysisImpl _value,
      $Res Function(_$QuadrantAnalysisImpl) _then)
      : super(_value, _then);

  /// Create a copy of QuadrantAnalysis
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? taskCounts = null,
    Object? completionRates = null,
    Object? averageCompletionTime = null,
    Object? efficiencyScores = null,
    Object? focusQuadrant = freezed,
    Object? mostEfficientQuadrant = freezed,
    Object? balanceScore = null,
  }) {
    return _then(_$QuadrantAnalysisImpl(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      taskCounts: null == taskCounts
          ? _value._taskCounts
          : taskCounts // ignore: cast_nullable_to_non_nullable
              as Map<Priority, int>,
      completionRates: null == completionRates
          ? _value._completionRates
          : completionRates // ignore: cast_nullable_to_non_nullable
              as Map<Priority, double>,
      averageCompletionTime: null == averageCompletionTime
          ? _value._averageCompletionTime
          : averageCompletionTime // ignore: cast_nullable_to_non_nullable
              as Map<Priority, double>,
      efficiencyScores: null == efficiencyScores
          ? _value._efficiencyScores
          : efficiencyScores // ignore: cast_nullable_to_non_nullable
              as Map<Priority, double>,
      focusQuadrant: freezed == focusQuadrant
          ? _value.focusQuadrant
          : focusQuadrant // ignore: cast_nullable_to_non_nullable
              as Priority?,
      mostEfficientQuadrant: freezed == mostEfficientQuadrant
          ? _value.mostEfficientQuadrant
          : mostEfficientQuadrant // ignore: cast_nullable_to_non_nullable
              as Priority?,
      balanceScore: null == balanceScore
          ? _value.balanceScore
          : balanceScore // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuadrantAnalysisImpl implements _QuadrantAnalysis {
  const _$QuadrantAnalysisImpl(
      {required this.startDate,
      required this.endDate,
      final Map<Priority, int> taskCounts = const {},
      final Map<Priority, double> completionRates = const {},
      final Map<Priority, double> averageCompletionTime = const {},
      final Map<Priority, double> efficiencyScores = const {},
      this.focusQuadrant,
      this.mostEfficientQuadrant,
      this.balanceScore = 0.0})
      : _taskCounts = taskCounts,
        _completionRates = completionRates,
        _averageCompletionTime = averageCompletionTime,
        _efficiencyScores = efficiencyScores;

  factory _$QuadrantAnalysisImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuadrantAnalysisImplFromJson(json);

  /// 分析时间范围
  @override
  final DateTime startDate;
  @override
  final DateTime endDate;

  /// 各象限的任务数量
  final Map<Priority, int> _taskCounts;

  /// 各象限的任务数量
  @override
  @JsonKey()
  Map<Priority, int> get taskCounts {
    if (_taskCounts is EqualUnmodifiableMapView) return _taskCounts;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_taskCounts);
  }

  /// 各象限的完成率
  final Map<Priority, double> _completionRates;

  /// 各象限的完成率
  @override
  @JsonKey()
  Map<Priority, double> get completionRates {
    if (_completionRates is EqualUnmodifiableMapView) return _completionRates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_completionRates);
  }

  /// 各象限的平均完成时间（小时）
  final Map<Priority, double> _averageCompletionTime;

  /// 各象限的平均完成时间（小时）
  @override
  @JsonKey()
  Map<Priority, double> get averageCompletionTime {
    if (_averageCompletionTime is EqualUnmodifiableMapView)
      return _averageCompletionTime;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_averageCompletionTime);
  }

  /// 各象限的效率分数
  final Map<Priority, double> _efficiencyScores;

  /// 各象限的效率分数
  @override
  @JsonKey()
  Map<Priority, double> get efficiencyScores {
    if (_efficiencyScores is EqualUnmodifiableMapView) return _efficiencyScores;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_efficiencyScores);
  }

  /// 最需要关注的象限（效率最低）
  @override
  final Priority? focusQuadrant;

  /// 最有效率的象限
  @override
  final Priority? mostEfficientQuadrant;

  /// 象限平衡性评分（0.0-1.0，1.0表示完全平衡）
  @override
  @JsonKey()
  final double balanceScore;

  @override
  String toString() {
    return 'QuadrantAnalysis(startDate: $startDate, endDate: $endDate, taskCounts: $taskCounts, completionRates: $completionRates, averageCompletionTime: $averageCompletionTime, efficiencyScores: $efficiencyScores, focusQuadrant: $focusQuadrant, mostEfficientQuadrant: $mostEfficientQuadrant, balanceScore: $balanceScore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuadrantAnalysisImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            const DeepCollectionEquality()
                .equals(other._taskCounts, _taskCounts) &&
            const DeepCollectionEquality()
                .equals(other._completionRates, _completionRates) &&
            const DeepCollectionEquality()
                .equals(other._averageCompletionTime, _averageCompletionTime) &&
            const DeepCollectionEquality()
                .equals(other._efficiencyScores, _efficiencyScores) &&
            (identical(other.focusQuadrant, focusQuadrant) ||
                other.focusQuadrant == focusQuadrant) &&
            (identical(other.mostEfficientQuadrant, mostEfficientQuadrant) ||
                other.mostEfficientQuadrant == mostEfficientQuadrant) &&
            (identical(other.balanceScore, balanceScore) ||
                other.balanceScore == balanceScore));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      startDate,
      endDate,
      const DeepCollectionEquality().hash(_taskCounts),
      const DeepCollectionEquality().hash(_completionRates),
      const DeepCollectionEquality().hash(_averageCompletionTime),
      const DeepCollectionEquality().hash(_efficiencyScores),
      focusQuadrant,
      mostEfficientQuadrant,
      balanceScore);

  /// Create a copy of QuadrantAnalysis
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QuadrantAnalysisImplCopyWith<_$QuadrantAnalysisImpl> get copyWith =>
      __$$QuadrantAnalysisImplCopyWithImpl<_$QuadrantAnalysisImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuadrantAnalysisImplToJson(
      this,
    );
  }
}

abstract class _QuadrantAnalysis implements QuadrantAnalysis {
  const factory _QuadrantAnalysis(
      {required final DateTime startDate,
      required final DateTime endDate,
      final Map<Priority, int> taskCounts,
      final Map<Priority, double> completionRates,
      final Map<Priority, double> averageCompletionTime,
      final Map<Priority, double> efficiencyScores,
      final Priority? focusQuadrant,
      final Priority? mostEfficientQuadrant,
      final double balanceScore}) = _$QuadrantAnalysisImpl;

  factory _QuadrantAnalysis.fromJson(Map<String, dynamic> json) =
      _$QuadrantAnalysisImpl.fromJson;

  /// 分析时间范围
  @override
  DateTime get startDate;
  @override
  DateTime get endDate;

  /// 各象限的任务数量
  @override
  Map<Priority, int> get taskCounts;

  /// 各象限的完成率
  @override
  Map<Priority, double> get completionRates;

  /// 各象限的平均完成时间（小时）
  @override
  Map<Priority, double> get averageCompletionTime;

  /// 各象限的效率分数
  @override
  Map<Priority, double> get efficiencyScores;

  /// 最需要关注的象限（效率最低）
  @override
  Priority? get focusQuadrant;

  /// 最有效率的象限
  @override
  Priority? get mostEfficientQuadrant;

  /// 象限平衡性评分（0.0-1.0，1.0表示完全平衡）
  @override
  double get balanceScore;

  /// Create a copy of QuadrantAnalysis
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QuadrantAnalysisImplCopyWith<_$QuadrantAnalysisImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CompletionPatterns _$CompletionPatternsFromJson(Map<String, dynamic> json) {
  return _CompletionPatterns.fromJson(json);
}

/// @nodoc
mixin _$CompletionPatterns {
  /// 分析时间范围（天数）
  int get analysisPeriod => throw _privateConstructorUsedError;

  /// 每日完成模式
  Map<int, double> get dailyPatterns =>
      throw _privateConstructorUsedError; // 0=周日, 1=周一, ..., 6=周六
  /// 每周完成模式
  Map<int, double> get weeklyPatterns =>
      throw _privateConstructorUsedError; // 1=第1周, 2=第2周, ...
  /// 每月完成模式
  Map<int, double> get monthlyPatterns =>
      throw _privateConstructorUsedError; // 1=1月, 2=2月, ..., 12=12月
  /// 最佳完成时间（小时，0-23）
  int get bestCompletionHour => throw _privateConstructorUsedError;

  /// 最佳完成日期（星期几，0-6）
  int get bestCompletionDay => throw _privateConstructorUsedError;

  /// 完成效率最高的时间段
  String get mostProductiveTimeSlot => throw _privateConstructorUsedError;

  /// 模式稳定性评分（0.0-1.0）
  double get patternStability => throw _privateConstructorUsedError;

  /// Serializes this CompletionPatterns to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CompletionPatterns
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CompletionPatternsCopyWith<CompletionPatterns> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CompletionPatternsCopyWith<$Res> {
  factory $CompletionPatternsCopyWith(
          CompletionPatterns value, $Res Function(CompletionPatterns) then) =
      _$CompletionPatternsCopyWithImpl<$Res, CompletionPatterns>;
  @useResult
  $Res call(
      {int analysisPeriod,
      Map<int, double> dailyPatterns,
      Map<int, double> weeklyPatterns,
      Map<int, double> monthlyPatterns,
      int bestCompletionHour,
      int bestCompletionDay,
      String mostProductiveTimeSlot,
      double patternStability});
}

/// @nodoc
class _$CompletionPatternsCopyWithImpl<$Res, $Val extends CompletionPatterns>
    implements $CompletionPatternsCopyWith<$Res> {
  _$CompletionPatternsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CompletionPatterns
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? analysisPeriod = null,
    Object? dailyPatterns = null,
    Object? weeklyPatterns = null,
    Object? monthlyPatterns = null,
    Object? bestCompletionHour = null,
    Object? bestCompletionDay = null,
    Object? mostProductiveTimeSlot = null,
    Object? patternStability = null,
  }) {
    return _then(_value.copyWith(
      analysisPeriod: null == analysisPeriod
          ? _value.analysisPeriod
          : analysisPeriod // ignore: cast_nullable_to_non_nullable
              as int,
      dailyPatterns: null == dailyPatterns
          ? _value.dailyPatterns
          : dailyPatterns // ignore: cast_nullable_to_non_nullable
              as Map<int, double>,
      weeklyPatterns: null == weeklyPatterns
          ? _value.weeklyPatterns
          : weeklyPatterns // ignore: cast_nullable_to_non_nullable
              as Map<int, double>,
      monthlyPatterns: null == monthlyPatterns
          ? _value.monthlyPatterns
          : monthlyPatterns // ignore: cast_nullable_to_non_nullable
              as Map<int, double>,
      bestCompletionHour: null == bestCompletionHour
          ? _value.bestCompletionHour
          : bestCompletionHour // ignore: cast_nullable_to_non_nullable
              as int,
      bestCompletionDay: null == bestCompletionDay
          ? _value.bestCompletionDay
          : bestCompletionDay // ignore: cast_nullable_to_non_nullable
              as int,
      mostProductiveTimeSlot: null == mostProductiveTimeSlot
          ? _value.mostProductiveTimeSlot
          : mostProductiveTimeSlot // ignore: cast_nullable_to_non_nullable
              as String,
      patternStability: null == patternStability
          ? _value.patternStability
          : patternStability // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CompletionPatternsImplCopyWith<$Res>
    implements $CompletionPatternsCopyWith<$Res> {
  factory _$$CompletionPatternsImplCopyWith(_$CompletionPatternsImpl value,
          $Res Function(_$CompletionPatternsImpl) then) =
      __$$CompletionPatternsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int analysisPeriod,
      Map<int, double> dailyPatterns,
      Map<int, double> weeklyPatterns,
      Map<int, double> monthlyPatterns,
      int bestCompletionHour,
      int bestCompletionDay,
      String mostProductiveTimeSlot,
      double patternStability});
}

/// @nodoc
class __$$CompletionPatternsImplCopyWithImpl<$Res>
    extends _$CompletionPatternsCopyWithImpl<$Res, _$CompletionPatternsImpl>
    implements _$$CompletionPatternsImplCopyWith<$Res> {
  __$$CompletionPatternsImplCopyWithImpl(_$CompletionPatternsImpl _value,
      $Res Function(_$CompletionPatternsImpl) _then)
      : super(_value, _then);

  /// Create a copy of CompletionPatterns
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? analysisPeriod = null,
    Object? dailyPatterns = null,
    Object? weeklyPatterns = null,
    Object? monthlyPatterns = null,
    Object? bestCompletionHour = null,
    Object? bestCompletionDay = null,
    Object? mostProductiveTimeSlot = null,
    Object? patternStability = null,
  }) {
    return _then(_$CompletionPatternsImpl(
      analysisPeriod: null == analysisPeriod
          ? _value.analysisPeriod
          : analysisPeriod // ignore: cast_nullable_to_non_nullable
              as int,
      dailyPatterns: null == dailyPatterns
          ? _value._dailyPatterns
          : dailyPatterns // ignore: cast_nullable_to_non_nullable
              as Map<int, double>,
      weeklyPatterns: null == weeklyPatterns
          ? _value._weeklyPatterns
          : weeklyPatterns // ignore: cast_nullable_to_non_nullable
              as Map<int, double>,
      monthlyPatterns: null == monthlyPatterns
          ? _value._monthlyPatterns
          : monthlyPatterns // ignore: cast_nullable_to_non_nullable
              as Map<int, double>,
      bestCompletionHour: null == bestCompletionHour
          ? _value.bestCompletionHour
          : bestCompletionHour // ignore: cast_nullable_to_non_nullable
              as int,
      bestCompletionDay: null == bestCompletionDay
          ? _value.bestCompletionDay
          : bestCompletionDay // ignore: cast_nullable_to_non_nullable
              as int,
      mostProductiveTimeSlot: null == mostProductiveTimeSlot
          ? _value.mostProductiveTimeSlot
          : mostProductiveTimeSlot // ignore: cast_nullable_to_non_nullable
              as String,
      patternStability: null == patternStability
          ? _value.patternStability
          : patternStability // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CompletionPatternsImpl implements _CompletionPatterns {
  const _$CompletionPatternsImpl(
      {this.analysisPeriod = 90,
      final Map<int, double> dailyPatterns = const {},
      final Map<int, double> weeklyPatterns = const {},
      final Map<int, double> monthlyPatterns = const {},
      this.bestCompletionHour = 0,
      this.bestCompletionDay = 0,
      this.mostProductiveTimeSlot = '',
      this.patternStability = 0.0})
      : _dailyPatterns = dailyPatterns,
        _weeklyPatterns = weeklyPatterns,
        _monthlyPatterns = monthlyPatterns;

  factory _$CompletionPatternsImpl.fromJson(Map<String, dynamic> json) =>
      _$$CompletionPatternsImplFromJson(json);

  /// 分析时间范围（天数）
  @override
  @JsonKey()
  final int analysisPeriod;

  /// 每日完成模式
  final Map<int, double> _dailyPatterns;

  /// 每日完成模式
  @override
  @JsonKey()
  Map<int, double> get dailyPatterns {
    if (_dailyPatterns is EqualUnmodifiableMapView) return _dailyPatterns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_dailyPatterns);
  }

// 0=周日, 1=周一, ..., 6=周六
  /// 每周完成模式
  final Map<int, double> _weeklyPatterns;
// 0=周日, 1=周一, ..., 6=周六
  /// 每周完成模式
  @override
  @JsonKey()
  Map<int, double> get weeklyPatterns {
    if (_weeklyPatterns is EqualUnmodifiableMapView) return _weeklyPatterns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_weeklyPatterns);
  }

// 1=第1周, 2=第2周, ...
  /// 每月完成模式
  final Map<int, double> _monthlyPatterns;
// 1=第1周, 2=第2周, ...
  /// 每月完成模式
  @override
  @JsonKey()
  Map<int, double> get monthlyPatterns {
    if (_monthlyPatterns is EqualUnmodifiableMapView) return _monthlyPatterns;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_monthlyPatterns);
  }

// 1=1月, 2=2月, ..., 12=12月
  /// 最佳完成时间（小时，0-23）
  @override
  @JsonKey()
  final int bestCompletionHour;

  /// 最佳完成日期（星期几，0-6）
  @override
  @JsonKey()
  final int bestCompletionDay;

  /// 完成效率最高的时间段
  @override
  @JsonKey()
  final String mostProductiveTimeSlot;

  /// 模式稳定性评分（0.0-1.0）
  @override
  @JsonKey()
  final double patternStability;

  @override
  String toString() {
    return 'CompletionPatterns(analysisPeriod: $analysisPeriod, dailyPatterns: $dailyPatterns, weeklyPatterns: $weeklyPatterns, monthlyPatterns: $monthlyPatterns, bestCompletionHour: $bestCompletionHour, bestCompletionDay: $bestCompletionDay, mostProductiveTimeSlot: $mostProductiveTimeSlot, patternStability: $patternStability)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompletionPatternsImpl &&
            (identical(other.analysisPeriod, analysisPeriod) ||
                other.analysisPeriod == analysisPeriod) &&
            const DeepCollectionEquality()
                .equals(other._dailyPatterns, _dailyPatterns) &&
            const DeepCollectionEquality()
                .equals(other._weeklyPatterns, _weeklyPatterns) &&
            const DeepCollectionEquality()
                .equals(other._monthlyPatterns, _monthlyPatterns) &&
            (identical(other.bestCompletionHour, bestCompletionHour) ||
                other.bestCompletionHour == bestCompletionHour) &&
            (identical(other.bestCompletionDay, bestCompletionDay) ||
                other.bestCompletionDay == bestCompletionDay) &&
            (identical(other.mostProductiveTimeSlot, mostProductiveTimeSlot) ||
                other.mostProductiveTimeSlot == mostProductiveTimeSlot) &&
            (identical(other.patternStability, patternStability) ||
                other.patternStability == patternStability));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      analysisPeriod,
      const DeepCollectionEquality().hash(_dailyPatterns),
      const DeepCollectionEquality().hash(_weeklyPatterns),
      const DeepCollectionEquality().hash(_monthlyPatterns),
      bestCompletionHour,
      bestCompletionDay,
      mostProductiveTimeSlot,
      patternStability);

  /// Create a copy of CompletionPatterns
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompletionPatternsImplCopyWith<_$CompletionPatternsImpl> get copyWith =>
      __$$CompletionPatternsImplCopyWithImpl<_$CompletionPatternsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CompletionPatternsImplToJson(
      this,
    );
  }
}

abstract class _CompletionPatterns implements CompletionPatterns {
  const factory _CompletionPatterns(
      {final int analysisPeriod,
      final Map<int, double> dailyPatterns,
      final Map<int, double> weeklyPatterns,
      final Map<int, double> monthlyPatterns,
      final int bestCompletionHour,
      final int bestCompletionDay,
      final String mostProductiveTimeSlot,
      final double patternStability}) = _$CompletionPatternsImpl;

  factory _CompletionPatterns.fromJson(Map<String, dynamic> json) =
      _$CompletionPatternsImpl.fromJson;

  /// 分析时间范围（天数）
  @override
  int get analysisPeriod;

  /// 每日完成模式
  @override
  Map<int, double> get dailyPatterns; // 0=周日, 1=周一, ..., 6=周六
  /// 每周完成模式
  @override
  Map<int, double> get weeklyPatterns; // 1=第1周, 2=第2周, ...
  /// 每月完成模式
  @override
  Map<int, double> get monthlyPatterns; // 1=1月, 2=2月, ..., 12=12月
  /// 最佳完成时间（小时，0-23）
  @override
  int get bestCompletionHour;

  /// 最佳完成日期（星期几，0-6）
  @override
  int get bestCompletionDay;

  /// 完成效率最高的时间段
  @override
  String get mostProductiveTimeSlot;

  /// 模式稳定性评分（0.0-1.0）
  @override
  double get patternStability;

  /// Create a copy of CompletionPatterns
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompletionPatternsImplCopyWith<_$CompletionPatternsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PerformanceMetrics _$PerformanceMetricsFromJson(Map<String, dynamic> json) {
  return _PerformanceMetrics.fromJson(json);
}

/// @nodoc
mixin _$PerformanceMetrics {
  /// 计算开始时间
  DateTime get calculationStart => throw _privateConstructorUsedError;

  /// 计算结束时间
  DateTime get calculationEnd => throw _privateConstructorUsedError;

  /// 计算耗时（毫秒）
  int get calculationDurationMs => throw _privateConstructorUsedError;

  /// 数据加载耗时（毫秒）
  int get dataLoadDurationMs => throw _privateConstructorUsedError;

  /// 内存使用峰值（MB）
  double get peakMemoryUsage => throw _privateConstructorUsedError;

  /// CPU使用率峰值（百分比）
  double get peakCpuUsage => throw _privateConstructorUsedError;

  /// 数据库查询次数
  int get databaseQueryCount => throw _privateConstructorUsedError;

  /// 平均查询响应时间（毫秒）
  double get averageQueryResponseTime => throw _privateConstructorUsedError;

  /// 缓存命中率（百分比）
  double get cacheHitRate => throw _privateConstructorUsedError;

  /// 性能评分（0.0-1.0）
  double get performanceScore => throw _privateConstructorUsedError;

  /// Serializes this PerformanceMetrics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PerformanceMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PerformanceMetricsCopyWith<PerformanceMetrics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PerformanceMetricsCopyWith<$Res> {
  factory $PerformanceMetricsCopyWith(
          PerformanceMetrics value, $Res Function(PerformanceMetrics) then) =
      _$PerformanceMetricsCopyWithImpl<$Res, PerformanceMetrics>;
  @useResult
  $Res call(
      {DateTime calculationStart,
      DateTime calculationEnd,
      int calculationDurationMs,
      int dataLoadDurationMs,
      double peakMemoryUsage,
      double peakCpuUsage,
      int databaseQueryCount,
      double averageQueryResponseTime,
      double cacheHitRate,
      double performanceScore});
}

/// @nodoc
class _$PerformanceMetricsCopyWithImpl<$Res, $Val extends PerformanceMetrics>
    implements $PerformanceMetricsCopyWith<$Res> {
  _$PerformanceMetricsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PerformanceMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? calculationStart = null,
    Object? calculationEnd = null,
    Object? calculationDurationMs = null,
    Object? dataLoadDurationMs = null,
    Object? peakMemoryUsage = null,
    Object? peakCpuUsage = null,
    Object? databaseQueryCount = null,
    Object? averageQueryResponseTime = null,
    Object? cacheHitRate = null,
    Object? performanceScore = null,
  }) {
    return _then(_value.copyWith(
      calculationStart: null == calculationStart
          ? _value.calculationStart
          : calculationStart // ignore: cast_nullable_to_non_nullable
              as DateTime,
      calculationEnd: null == calculationEnd
          ? _value.calculationEnd
          : calculationEnd // ignore: cast_nullable_to_non_nullable
              as DateTime,
      calculationDurationMs: null == calculationDurationMs
          ? _value.calculationDurationMs
          : calculationDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
      dataLoadDurationMs: null == dataLoadDurationMs
          ? _value.dataLoadDurationMs
          : dataLoadDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
      peakMemoryUsage: null == peakMemoryUsage
          ? _value.peakMemoryUsage
          : peakMemoryUsage // ignore: cast_nullable_to_non_nullable
              as double,
      peakCpuUsage: null == peakCpuUsage
          ? _value.peakCpuUsage
          : peakCpuUsage // ignore: cast_nullable_to_non_nullable
              as double,
      databaseQueryCount: null == databaseQueryCount
          ? _value.databaseQueryCount
          : databaseQueryCount // ignore: cast_nullable_to_non_nullable
              as int,
      averageQueryResponseTime: null == averageQueryResponseTime
          ? _value.averageQueryResponseTime
          : averageQueryResponseTime // ignore: cast_nullable_to_non_nullable
              as double,
      cacheHitRate: null == cacheHitRate
          ? _value.cacheHitRate
          : cacheHitRate // ignore: cast_nullable_to_non_nullable
              as double,
      performanceScore: null == performanceScore
          ? _value.performanceScore
          : performanceScore // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PerformanceMetricsImplCopyWith<$Res>
    implements $PerformanceMetricsCopyWith<$Res> {
  factory _$$PerformanceMetricsImplCopyWith(_$PerformanceMetricsImpl value,
          $Res Function(_$PerformanceMetricsImpl) then) =
      __$$PerformanceMetricsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime calculationStart,
      DateTime calculationEnd,
      int calculationDurationMs,
      int dataLoadDurationMs,
      double peakMemoryUsage,
      double peakCpuUsage,
      int databaseQueryCount,
      double averageQueryResponseTime,
      double cacheHitRate,
      double performanceScore});
}

/// @nodoc
class __$$PerformanceMetricsImplCopyWithImpl<$Res>
    extends _$PerformanceMetricsCopyWithImpl<$Res, _$PerformanceMetricsImpl>
    implements _$$PerformanceMetricsImplCopyWith<$Res> {
  __$$PerformanceMetricsImplCopyWithImpl(_$PerformanceMetricsImpl _value,
      $Res Function(_$PerformanceMetricsImpl) _then)
      : super(_value, _then);

  /// Create a copy of PerformanceMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? calculationStart = null,
    Object? calculationEnd = null,
    Object? calculationDurationMs = null,
    Object? dataLoadDurationMs = null,
    Object? peakMemoryUsage = null,
    Object? peakCpuUsage = null,
    Object? databaseQueryCount = null,
    Object? averageQueryResponseTime = null,
    Object? cacheHitRate = null,
    Object? performanceScore = null,
  }) {
    return _then(_$PerformanceMetricsImpl(
      calculationStart: null == calculationStart
          ? _value.calculationStart
          : calculationStart // ignore: cast_nullable_to_non_nullable
              as DateTime,
      calculationEnd: null == calculationEnd
          ? _value.calculationEnd
          : calculationEnd // ignore: cast_nullable_to_non_nullable
              as DateTime,
      calculationDurationMs: null == calculationDurationMs
          ? _value.calculationDurationMs
          : calculationDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
      dataLoadDurationMs: null == dataLoadDurationMs
          ? _value.dataLoadDurationMs
          : dataLoadDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
      peakMemoryUsage: null == peakMemoryUsage
          ? _value.peakMemoryUsage
          : peakMemoryUsage // ignore: cast_nullable_to_non_nullable
              as double,
      peakCpuUsage: null == peakCpuUsage
          ? _value.peakCpuUsage
          : peakCpuUsage // ignore: cast_nullable_to_non_nullable
              as double,
      databaseQueryCount: null == databaseQueryCount
          ? _value.databaseQueryCount
          : databaseQueryCount // ignore: cast_nullable_to_non_nullable
              as int,
      averageQueryResponseTime: null == averageQueryResponseTime
          ? _value.averageQueryResponseTime
          : averageQueryResponseTime // ignore: cast_nullable_to_non_nullable
              as double,
      cacheHitRate: null == cacheHitRate
          ? _value.cacheHitRate
          : cacheHitRate // ignore: cast_nullable_to_non_nullable
              as double,
      performanceScore: null == performanceScore
          ? _value.performanceScore
          : performanceScore // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PerformanceMetricsImpl implements _PerformanceMetrics {
  const _$PerformanceMetricsImpl(
      {required this.calculationStart,
      required this.calculationEnd,
      this.calculationDurationMs = 0,
      this.dataLoadDurationMs = 0,
      this.peakMemoryUsage = 0.0,
      this.peakCpuUsage = 0.0,
      this.databaseQueryCount = 0,
      this.averageQueryResponseTime = 0.0,
      this.cacheHitRate = 0.0,
      this.performanceScore = 0.0});

  factory _$PerformanceMetricsImpl.fromJson(Map<String, dynamic> json) =>
      _$$PerformanceMetricsImplFromJson(json);

  /// 计算开始时间
  @override
  final DateTime calculationStart;

  /// 计算结束时间
  @override
  final DateTime calculationEnd;

  /// 计算耗时（毫秒）
  @override
  @JsonKey()
  final int calculationDurationMs;

  /// 数据加载耗时（毫秒）
  @override
  @JsonKey()
  final int dataLoadDurationMs;

  /// 内存使用峰值（MB）
  @override
  @JsonKey()
  final double peakMemoryUsage;

  /// CPU使用率峰值（百分比）
  @override
  @JsonKey()
  final double peakCpuUsage;

  /// 数据库查询次数
  @override
  @JsonKey()
  final int databaseQueryCount;

  /// 平均查询响应时间（毫秒）
  @override
  @JsonKey()
  final double averageQueryResponseTime;

  /// 缓存命中率（百分比）
  @override
  @JsonKey()
  final double cacheHitRate;

  /// 性能评分（0.0-1.0）
  @override
  @JsonKey()
  final double performanceScore;

  @override
  String toString() {
    return 'PerformanceMetrics(calculationStart: $calculationStart, calculationEnd: $calculationEnd, calculationDurationMs: $calculationDurationMs, dataLoadDurationMs: $dataLoadDurationMs, peakMemoryUsage: $peakMemoryUsage, peakCpuUsage: $peakCpuUsage, databaseQueryCount: $databaseQueryCount, averageQueryResponseTime: $averageQueryResponseTime, cacheHitRate: $cacheHitRate, performanceScore: $performanceScore)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PerformanceMetricsImpl &&
            (identical(other.calculationStart, calculationStart) ||
                other.calculationStart == calculationStart) &&
            (identical(other.calculationEnd, calculationEnd) ||
                other.calculationEnd == calculationEnd) &&
            (identical(other.calculationDurationMs, calculationDurationMs) ||
                other.calculationDurationMs == calculationDurationMs) &&
            (identical(other.dataLoadDurationMs, dataLoadDurationMs) ||
                other.dataLoadDurationMs == dataLoadDurationMs) &&
            (identical(other.peakMemoryUsage, peakMemoryUsage) ||
                other.peakMemoryUsage == peakMemoryUsage) &&
            (identical(other.peakCpuUsage, peakCpuUsage) ||
                other.peakCpuUsage == peakCpuUsage) &&
            (identical(other.databaseQueryCount, databaseQueryCount) ||
                other.databaseQueryCount == databaseQueryCount) &&
            (identical(
                    other.averageQueryResponseTime, averageQueryResponseTime) ||
                other.averageQueryResponseTime == averageQueryResponseTime) &&
            (identical(other.cacheHitRate, cacheHitRate) ||
                other.cacheHitRate == cacheHitRate) &&
            (identical(other.performanceScore, performanceScore) ||
                other.performanceScore == performanceScore));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      calculationStart,
      calculationEnd,
      calculationDurationMs,
      dataLoadDurationMs,
      peakMemoryUsage,
      peakCpuUsage,
      databaseQueryCount,
      averageQueryResponseTime,
      cacheHitRate,
      performanceScore);

  /// Create a copy of PerformanceMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PerformanceMetricsImplCopyWith<_$PerformanceMetricsImpl> get copyWith =>
      __$$PerformanceMetricsImplCopyWithImpl<_$PerformanceMetricsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PerformanceMetricsImplToJson(
      this,
    );
  }
}

abstract class _PerformanceMetrics implements PerformanceMetrics {
  const factory _PerformanceMetrics(
      {required final DateTime calculationStart,
      required final DateTime calculationEnd,
      final int calculationDurationMs,
      final int dataLoadDurationMs,
      final double peakMemoryUsage,
      final double peakCpuUsage,
      final int databaseQueryCount,
      final double averageQueryResponseTime,
      final double cacheHitRate,
      final double performanceScore}) = _$PerformanceMetricsImpl;

  factory _PerformanceMetrics.fromJson(Map<String, dynamic> json) =
      _$PerformanceMetricsImpl.fromJson;

  /// 计算开始时间
  @override
  DateTime get calculationStart;

  /// 计算结束时间
  @override
  DateTime get calculationEnd;

  /// 计算耗时（毫秒）
  @override
  int get calculationDurationMs;

  /// 数据加载耗时（毫秒）
  @override
  int get dataLoadDurationMs;

  /// 内存使用峰值（MB）
  @override
  double get peakMemoryUsage;

  /// CPU使用率峰值（百分比）
  @override
  double get peakCpuUsage;

  /// 数据库查询次数
  @override
  int get databaseQueryCount;

  /// 平均查询响应时间（毫秒）
  @override
  double get averageQueryResponseTime;

  /// 缓存命中率（百分比）
  @override
  double get cacheHitRate;

  /// 性能评分（0.0-1.0）
  @override
  double get performanceScore;

  /// Create a copy of PerformanceMetrics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PerformanceMetricsImplCopyWith<_$PerformanceMetricsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

TaskHighlight _$TaskHighlightFromJson(Map<String, dynamic> json) {
  return _TaskHighlight.fromJson(json);
}

/// @nodoc
mixin _$TaskHighlight {
  /// 任务ID
  String get taskId => throw _privateConstructorUsedError;

  /// 任务标题
  String get title => throw _privateConstructorUsedError;

  /// 任务优先级
  Priority get priority => throw _privateConstructorUsedError;

  /// 完成日期
  DateTime get completionDate => throw _privateConstructorUsedError;

  /// 子任务数量
  int get subtaskCount => throw _privateConstructorUsedError;

  /// 完成子任务数量
  int get completedSubtasks => throw _privateConstructorUsedError;

  /// 高光原因描述
  String get highlightReason => throw _privateConstructorUsedError;

  /// 高光分数（用于排序）
  double get highlightScore => throw _privateConstructorUsedError;

  /// 相关标签
  List<String> get tags => throw _privateConstructorUsedError;

  /// Serializes this TaskHighlight to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of TaskHighlight
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TaskHighlightCopyWith<TaskHighlight> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskHighlightCopyWith<$Res> {
  factory $TaskHighlightCopyWith(
          TaskHighlight value, $Res Function(TaskHighlight) then) =
      _$TaskHighlightCopyWithImpl<$Res, TaskHighlight>;
  @useResult
  $Res call(
      {String taskId,
      String title,
      Priority priority,
      DateTime completionDate,
      int subtaskCount,
      int completedSubtasks,
      String highlightReason,
      double highlightScore,
      List<String> tags});
}

/// @nodoc
class _$TaskHighlightCopyWithImpl<$Res, $Val extends TaskHighlight>
    implements $TaskHighlightCopyWith<$Res> {
  _$TaskHighlightCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TaskHighlight
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = null,
    Object? title = null,
    Object? priority = null,
    Object? completionDate = null,
    Object? subtaskCount = null,
    Object? completedSubtasks = null,
    Object? highlightReason = null,
    Object? highlightScore = null,
    Object? tags = null,
  }) {
    return _then(_value.copyWith(
      taskId: null == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as Priority,
      completionDate: null == completionDate
          ? _value.completionDate
          : completionDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      subtaskCount: null == subtaskCount
          ? _value.subtaskCount
          : subtaskCount // ignore: cast_nullable_to_non_nullable
              as int,
      completedSubtasks: null == completedSubtasks
          ? _value.completedSubtasks
          : completedSubtasks // ignore: cast_nullable_to_non_nullable
              as int,
      highlightReason: null == highlightReason
          ? _value.highlightReason
          : highlightReason // ignore: cast_nullable_to_non_nullable
              as String,
      highlightScore: null == highlightScore
          ? _value.highlightScore
          : highlightScore // ignore: cast_nullable_to_non_nullable
              as double,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TaskHighlightImplCopyWith<$Res>
    implements $TaskHighlightCopyWith<$Res> {
  factory _$$TaskHighlightImplCopyWith(
          _$TaskHighlightImpl value, $Res Function(_$TaskHighlightImpl) then) =
      __$$TaskHighlightImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String taskId,
      String title,
      Priority priority,
      DateTime completionDate,
      int subtaskCount,
      int completedSubtasks,
      String highlightReason,
      double highlightScore,
      List<String> tags});
}

/// @nodoc
class __$$TaskHighlightImplCopyWithImpl<$Res>
    extends _$TaskHighlightCopyWithImpl<$Res, _$TaskHighlightImpl>
    implements _$$TaskHighlightImplCopyWith<$Res> {
  __$$TaskHighlightImplCopyWithImpl(
      _$TaskHighlightImpl _value, $Res Function(_$TaskHighlightImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskHighlight
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = null,
    Object? title = null,
    Object? priority = null,
    Object? completionDate = null,
    Object? subtaskCount = null,
    Object? completedSubtasks = null,
    Object? highlightReason = null,
    Object? highlightScore = null,
    Object? tags = null,
  }) {
    return _then(_$TaskHighlightImpl(
      taskId: null == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as Priority,
      completionDate: null == completionDate
          ? _value.completionDate
          : completionDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      subtaskCount: null == subtaskCount
          ? _value.subtaskCount
          : subtaskCount // ignore: cast_nullable_to_non_nullable
              as int,
      completedSubtasks: null == completedSubtasks
          ? _value.completedSubtasks
          : completedSubtasks // ignore: cast_nullable_to_non_nullable
              as int,
      highlightReason: null == highlightReason
          ? _value.highlightReason
          : highlightReason // ignore: cast_nullable_to_non_nullable
              as String,
      highlightScore: null == highlightScore
          ? _value.highlightScore
          : highlightScore // ignore: cast_nullable_to_non_nullable
              as double,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$TaskHighlightImpl implements _TaskHighlight {
  const _$TaskHighlightImpl(
      {required this.taskId,
      required this.title,
      required this.priority,
      required this.completionDate,
      this.subtaskCount = 0,
      this.completedSubtasks = 0,
      this.highlightReason = '',
      this.highlightScore = 0.0,
      final List<String> tags = const []})
      : _tags = tags;

  factory _$TaskHighlightImpl.fromJson(Map<String, dynamic> json) =>
      _$$TaskHighlightImplFromJson(json);

  /// 任务ID
  @override
  final String taskId;

  /// 任务标题
  @override
  final String title;

  /// 任务优先级
  @override
  final Priority priority;

  /// 完成日期
  @override
  final DateTime completionDate;

  /// 子任务数量
  @override
  @JsonKey()
  final int subtaskCount;

  /// 完成子任务数量
  @override
  @JsonKey()
  final int completedSubtasks;

  /// 高光原因描述
  @override
  @JsonKey()
  final String highlightReason;

  /// 高光分数（用于排序）
  @override
  @JsonKey()
  final double highlightScore;

  /// 相关标签
  final List<String> _tags;

  /// 相关标签
  @override
  @JsonKey()
  List<String> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  String toString() {
    return 'TaskHighlight(taskId: $taskId, title: $title, priority: $priority, completionDate: $completionDate, subtaskCount: $subtaskCount, completedSubtasks: $completedSubtasks, highlightReason: $highlightReason, highlightScore: $highlightScore, tags: $tags)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskHighlightImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.completionDate, completionDate) ||
                other.completionDate == completionDate) &&
            (identical(other.subtaskCount, subtaskCount) ||
                other.subtaskCount == subtaskCount) &&
            (identical(other.completedSubtasks, completedSubtasks) ||
                other.completedSubtasks == completedSubtasks) &&
            (identical(other.highlightReason, highlightReason) ||
                other.highlightReason == highlightReason) &&
            (identical(other.highlightScore, highlightScore) ||
                other.highlightScore == highlightScore) &&
            const DeepCollectionEquality().equals(other._tags, _tags));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      taskId,
      title,
      priority,
      completionDate,
      subtaskCount,
      completedSubtasks,
      highlightReason,
      highlightScore,
      const DeepCollectionEquality().hash(_tags));

  /// Create a copy of TaskHighlight
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskHighlightImplCopyWith<_$TaskHighlightImpl> get copyWith =>
      __$$TaskHighlightImplCopyWithImpl<_$TaskHighlightImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$TaskHighlightImplToJson(
      this,
    );
  }
}

abstract class _TaskHighlight implements TaskHighlight {
  const factory _TaskHighlight(
      {required final String taskId,
      required final String title,
      required final Priority priority,
      required final DateTime completionDate,
      final int subtaskCount,
      final int completedSubtasks,
      final String highlightReason,
      final double highlightScore,
      final List<String> tags}) = _$TaskHighlightImpl;

  factory _TaskHighlight.fromJson(Map<String, dynamic> json) =
      _$TaskHighlightImpl.fromJson;

  /// 任务ID
  @override
  String get taskId;

  /// 任务标题
  @override
  String get title;

  /// 任务优先级
  @override
  Priority get priority;

  /// 完成日期
  @override
  DateTime get completionDate;

  /// 子任务数量
  @override
  int get subtaskCount;

  /// 完成子任务数量
  @override
  int get completedSubtasks;

  /// 高光原因描述
  @override
  String get highlightReason;

  /// 高光分数（用于排序）
  @override
  double get highlightScore;

  /// 相关标签
  @override
  List<String> get tags;

  /// Create a copy of TaskHighlight
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskHighlightImplCopyWith<_$TaskHighlightImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChartData _$ChartDataFromJson(Map<String, dynamic> json) {
  return _ChartData.fromJson(json);
}

/// @nodoc
mixin _$ChartData {
  /// 图表类型
  SummaryChartType get chartType => throw _privateConstructorUsedError;

  /// 图表标题
  String get title => throw _privateConstructorUsedError;

  /// 图表副标题
  String get subtitle => throw _privateConstructorUsedError;

  /// 数据点列表
  List<ChartDataPoint> get dataPoints => throw _privateConstructorUsedError;

  /// X轴标签
  List<String> get xAxisLabels => throw _privateConstructorUsedError;

  /// Y轴标签
  List<String> get yAxisLabels => throw _privateConstructorUsedError;

  /// 图表配置选项
  Map<String, dynamic> get chartOptions => throw _privateConstructorUsedError;

  /// 数据更新时间
  DateTime? get lastUpdated => throw _privateConstructorUsedError;

  /// Serializes this ChartData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChartData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChartDataCopyWith<ChartData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChartDataCopyWith<$Res> {
  factory $ChartDataCopyWith(ChartData value, $Res Function(ChartData) then) =
      _$ChartDataCopyWithImpl<$Res, ChartData>;
  @useResult
  $Res call(
      {SummaryChartType chartType,
      String title,
      String subtitle,
      List<ChartDataPoint> dataPoints,
      List<String> xAxisLabels,
      List<String> yAxisLabels,
      Map<String, dynamic> chartOptions,
      DateTime? lastUpdated});
}

/// @nodoc
class _$ChartDataCopyWithImpl<$Res, $Val extends ChartData>
    implements $ChartDataCopyWith<$Res> {
  _$ChartDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChartData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chartType = null,
    Object? title = null,
    Object? subtitle = null,
    Object? dataPoints = null,
    Object? xAxisLabels = null,
    Object? yAxisLabels = null,
    Object? chartOptions = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(_value.copyWith(
      chartType: null == chartType
          ? _value.chartType
          : chartType // ignore: cast_nullable_to_non_nullable
              as SummaryChartType,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      subtitle: null == subtitle
          ? _value.subtitle
          : subtitle // ignore: cast_nullable_to_non_nullable
              as String,
      dataPoints: null == dataPoints
          ? _value.dataPoints
          : dataPoints // ignore: cast_nullable_to_non_nullable
              as List<ChartDataPoint>,
      xAxisLabels: null == xAxisLabels
          ? _value.xAxisLabels
          : xAxisLabels // ignore: cast_nullable_to_non_nullable
              as List<String>,
      yAxisLabels: null == yAxisLabels
          ? _value.yAxisLabels
          : yAxisLabels // ignore: cast_nullable_to_non_nullable
              as List<String>,
      chartOptions: null == chartOptions
          ? _value.chartOptions
          : chartOptions // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChartDataImplCopyWith<$Res>
    implements $ChartDataCopyWith<$Res> {
  factory _$$ChartDataImplCopyWith(
          _$ChartDataImpl value, $Res Function(_$ChartDataImpl) then) =
      __$$ChartDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SummaryChartType chartType,
      String title,
      String subtitle,
      List<ChartDataPoint> dataPoints,
      List<String> xAxisLabels,
      List<String> yAxisLabels,
      Map<String, dynamic> chartOptions,
      DateTime? lastUpdated});
}

/// @nodoc
class __$$ChartDataImplCopyWithImpl<$Res>
    extends _$ChartDataCopyWithImpl<$Res, _$ChartDataImpl>
    implements _$$ChartDataImplCopyWith<$Res> {
  __$$ChartDataImplCopyWithImpl(
      _$ChartDataImpl _value, $Res Function(_$ChartDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChartData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chartType = null,
    Object? title = null,
    Object? subtitle = null,
    Object? dataPoints = null,
    Object? xAxisLabels = null,
    Object? yAxisLabels = null,
    Object? chartOptions = null,
    Object? lastUpdated = freezed,
  }) {
    return _then(_$ChartDataImpl(
      chartType: null == chartType
          ? _value.chartType
          : chartType // ignore: cast_nullable_to_non_nullable
              as SummaryChartType,
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      subtitle: null == subtitle
          ? _value.subtitle
          : subtitle // ignore: cast_nullable_to_non_nullable
              as String,
      dataPoints: null == dataPoints
          ? _value._dataPoints
          : dataPoints // ignore: cast_nullable_to_non_nullable
              as List<ChartDataPoint>,
      xAxisLabels: null == xAxisLabels
          ? _value._xAxisLabels
          : xAxisLabels // ignore: cast_nullable_to_non_nullable
              as List<String>,
      yAxisLabels: null == yAxisLabels
          ? _value._yAxisLabels
          : yAxisLabels // ignore: cast_nullable_to_non_nullable
              as List<String>,
      chartOptions: null == chartOptions
          ? _value._chartOptions
          : chartOptions // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChartDataImpl implements _ChartData {
  const _$ChartDataImpl(
      {required this.chartType,
      this.title = '',
      this.subtitle = '',
      final List<ChartDataPoint> dataPoints = const [],
      final List<String> xAxisLabels = const [],
      final List<String> yAxisLabels = const [],
      final Map<String, dynamic> chartOptions = const {},
      this.lastUpdated})
      : _dataPoints = dataPoints,
        _xAxisLabels = xAxisLabels,
        _yAxisLabels = yAxisLabels,
        _chartOptions = chartOptions;

  factory _$ChartDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChartDataImplFromJson(json);

  /// 图表类型
  @override
  final SummaryChartType chartType;

  /// 图表标题
  @override
  @JsonKey()
  final String title;

  /// 图表副标题
  @override
  @JsonKey()
  final String subtitle;

  /// 数据点列表
  final List<ChartDataPoint> _dataPoints;

  /// 数据点列表
  @override
  @JsonKey()
  List<ChartDataPoint> get dataPoints {
    if (_dataPoints is EqualUnmodifiableListView) return _dataPoints;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_dataPoints);
  }

  /// X轴标签
  final List<String> _xAxisLabels;

  /// X轴标签
  @override
  @JsonKey()
  List<String> get xAxisLabels {
    if (_xAxisLabels is EqualUnmodifiableListView) return _xAxisLabels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_xAxisLabels);
  }

  /// Y轴标签
  final List<String> _yAxisLabels;

  /// Y轴标签
  @override
  @JsonKey()
  List<String> get yAxisLabels {
    if (_yAxisLabels is EqualUnmodifiableListView) return _yAxisLabels;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_yAxisLabels);
  }

  /// 图表配置选项
  final Map<String, dynamic> _chartOptions;

  /// 图表配置选项
  @override
  @JsonKey()
  Map<String, dynamic> get chartOptions {
    if (_chartOptions is EqualUnmodifiableMapView) return _chartOptions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_chartOptions);
  }

  /// 数据更新时间
  @override
  final DateTime? lastUpdated;

  @override
  String toString() {
    return 'ChartData(chartType: $chartType, title: $title, subtitle: $subtitle, dataPoints: $dataPoints, xAxisLabels: $xAxisLabels, yAxisLabels: $yAxisLabels, chartOptions: $chartOptions, lastUpdated: $lastUpdated)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChartDataImpl &&
            (identical(other.chartType, chartType) ||
                other.chartType == chartType) &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.subtitle, subtitle) ||
                other.subtitle == subtitle) &&
            const DeepCollectionEquality()
                .equals(other._dataPoints, _dataPoints) &&
            const DeepCollectionEquality()
                .equals(other._xAxisLabels, _xAxisLabels) &&
            const DeepCollectionEquality()
                .equals(other._yAxisLabels, _yAxisLabels) &&
            const DeepCollectionEquality()
                .equals(other._chartOptions, _chartOptions) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      chartType,
      title,
      subtitle,
      const DeepCollectionEquality().hash(_dataPoints),
      const DeepCollectionEquality().hash(_xAxisLabels),
      const DeepCollectionEquality().hash(_yAxisLabels),
      const DeepCollectionEquality().hash(_chartOptions),
      lastUpdated);

  /// Create a copy of ChartData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChartDataImplCopyWith<_$ChartDataImpl> get copyWith =>
      __$$ChartDataImplCopyWithImpl<_$ChartDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChartDataImplToJson(
      this,
    );
  }
}

abstract class _ChartData implements ChartData {
  const factory _ChartData(
      {required final SummaryChartType chartType,
      final String title,
      final String subtitle,
      final List<ChartDataPoint> dataPoints,
      final List<String> xAxisLabels,
      final List<String> yAxisLabels,
      final Map<String, dynamic> chartOptions,
      final DateTime? lastUpdated}) = _$ChartDataImpl;

  factory _ChartData.fromJson(Map<String, dynamic> json) =
      _$ChartDataImpl.fromJson;

  /// 图表类型
  @override
  SummaryChartType get chartType;

  /// 图表标题
  @override
  String get title;

  /// 图表副标题
  @override
  String get subtitle;

  /// 数据点列表
  @override
  List<ChartDataPoint> get dataPoints;

  /// X轴标签
  @override
  List<String> get xAxisLabels;

  /// Y轴标签
  @override
  List<String> get yAxisLabels;

  /// 图表配置选项
  @override
  Map<String, dynamic> get chartOptions;

  /// 数据更新时间
  @override
  DateTime? get lastUpdated;

  /// Create a copy of ChartData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChartDataImplCopyWith<_$ChartDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

ChartDataPoint _$ChartDataPointFromJson(Map<String, dynamic> json) {
  return _ChartDataPoint.fromJson(json);
}

/// @nodoc
mixin _$ChartDataPoint {
  /// 数据点标签
  String get label => throw _privateConstructorUsedError;

  /// 数据点值
  double get value => throw _privateConstructorUsedError;

  /// 数据点颜色
  String get color => throw _privateConstructorUsedError;

  /// 数据点描述
  String get description => throw _privateConstructorUsedError;

  /// 数据点元数据
  Map<String, dynamic> get metadata => throw _privateConstructorUsedError;

  /// Serializes this ChartDataPoint to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of ChartDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $ChartDataPointCopyWith<ChartDataPoint> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ChartDataPointCopyWith<$Res> {
  factory $ChartDataPointCopyWith(
          ChartDataPoint value, $Res Function(ChartDataPoint) then) =
      _$ChartDataPointCopyWithImpl<$Res, ChartDataPoint>;
  @useResult
  $Res call(
      {String label,
      double value,
      String color,
      String description,
      Map<String, dynamic> metadata});
}

/// @nodoc
class _$ChartDataPointCopyWithImpl<$Res, $Val extends ChartDataPoint>
    implements $ChartDataPointCopyWith<$Res> {
  _$ChartDataPointCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of ChartDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? label = null,
    Object? value = null,
    Object? color = null,
    Object? description = null,
    Object? metadata = null,
  }) {
    return _then(_value.copyWith(
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as double,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      metadata: null == metadata
          ? _value.metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ChartDataPointImplCopyWith<$Res>
    implements $ChartDataPointCopyWith<$Res> {
  factory _$$ChartDataPointImplCopyWith(_$ChartDataPointImpl value,
          $Res Function(_$ChartDataPointImpl) then) =
      __$$ChartDataPointImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String label,
      double value,
      String color,
      String description,
      Map<String, dynamic> metadata});
}

/// @nodoc
class __$$ChartDataPointImplCopyWithImpl<$Res>
    extends _$ChartDataPointCopyWithImpl<$Res, _$ChartDataPointImpl>
    implements _$$ChartDataPointImplCopyWith<$Res> {
  __$$ChartDataPointImplCopyWithImpl(
      _$ChartDataPointImpl _value, $Res Function(_$ChartDataPointImpl) _then)
      : super(_value, _then);

  /// Create a copy of ChartDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? label = null,
    Object? value = null,
    Object? color = null,
    Object? description = null,
    Object? metadata = null,
  }) {
    return _then(_$ChartDataPointImpl(
      label: null == label
          ? _value.label
          : label // ignore: cast_nullable_to_non_nullable
              as String,
      value: null == value
          ? _value.value
          : value // ignore: cast_nullable_to_non_nullable
              as double,
      color: null == color
          ? _value.color
          : color // ignore: cast_nullable_to_non_nullable
              as String,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      metadata: null == metadata
          ? _value._metadata
          : metadata // ignore: cast_nullable_to_non_nullable
              as Map<String, dynamic>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ChartDataPointImpl implements _ChartDataPoint {
  const _$ChartDataPointImpl(
      {required this.label,
      required this.value,
      this.color = '',
      this.description = '',
      final Map<String, dynamic> metadata = const {}})
      : _metadata = metadata;

  factory _$ChartDataPointImpl.fromJson(Map<String, dynamic> json) =>
      _$$ChartDataPointImplFromJson(json);

  /// 数据点标签
  @override
  final String label;

  /// 数据点值
  @override
  final double value;

  /// 数据点颜色
  @override
  @JsonKey()
  final String color;

  /// 数据点描述
  @override
  @JsonKey()
  final String description;

  /// 数据点元数据
  final Map<String, dynamic> _metadata;

  /// 数据点元数据
  @override
  @JsonKey()
  Map<String, dynamic> get metadata {
    if (_metadata is EqualUnmodifiableMapView) return _metadata;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_metadata);
  }

  @override
  String toString() {
    return 'ChartDataPoint(label: $label, value: $value, color: $color, description: $description, metadata: $metadata)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChartDataPointImpl &&
            (identical(other.label, label) || other.label == label) &&
            (identical(other.value, value) || other.value == value) &&
            (identical(other.color, color) || other.color == color) &&
            (identical(other.description, description) ||
                other.description == description) &&
            const DeepCollectionEquality().equals(other._metadata, _metadata));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, label, value, color, description,
      const DeepCollectionEquality().hash(_metadata));

  /// Create a copy of ChartDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChartDataPointImplCopyWith<_$ChartDataPointImpl> get copyWith =>
      __$$ChartDataPointImplCopyWithImpl<_$ChartDataPointImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ChartDataPointImplToJson(
      this,
    );
  }
}

abstract class _ChartDataPoint implements ChartDataPoint {
  const factory _ChartDataPoint(
      {required final String label,
      required final double value,
      final String color,
      final String description,
      final Map<String, dynamic> metadata}) = _$ChartDataPointImpl;

  factory _ChartDataPoint.fromJson(Map<String, dynamic> json) =
      _$ChartDataPointImpl.fromJson;

  /// 数据点标签
  @override
  String get label;

  /// 数据点值
  @override
  double get value;

  /// 数据点颜色
  @override
  String get color;

  /// 数据点描述
  @override
  String get description;

  /// 数据点元数据
  @override
  Map<String, dynamic> get metadata;

  /// Create a copy of ChartDataPoint
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChartDataPointImplCopyWith<_$ChartDataPointImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

QuadrantMetrics _$QuadrantMetricsFromJson(Map<String, dynamic> json) {
  return _QuadrantMetrics.fromJson(json);
}

/// @nodoc
mixin _$QuadrantMetrics {
  /// 象限优先级
  Priority get priority => throw _privateConstructorUsedError;

  /// 任务总数
  int get totalTasks => throw _privateConstructorUsedError;

  /// 已完成任务数
  int get completedTasks => throw _privateConstructorUsedError;

  /// 完成率
  double get completionRate => throw _privateConstructorUsedError;

  /// 平均完成时间（小时）
  double get averageCompletionTime => throw _privateConstructorUsedError;

  /// 效率分数
  double get efficiencyScore => throw _privateConstructorUsedError;

  /// 趋势变化
  double get trendChange => throw _privateConstructorUsedError;

  /// Serializes this QuadrantMetrics to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of QuadrantMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QuadrantMetricsCopyWith<QuadrantMetrics> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QuadrantMetricsCopyWith<$Res> {
  factory $QuadrantMetricsCopyWith(
          QuadrantMetrics value, $Res Function(QuadrantMetrics) then) =
      _$QuadrantMetricsCopyWithImpl<$Res, QuadrantMetrics>;
  @useResult
  $Res call(
      {Priority priority,
      int totalTasks,
      int completedTasks,
      double completionRate,
      double averageCompletionTime,
      double efficiencyScore,
      double trendChange});
}

/// @nodoc
class _$QuadrantMetricsCopyWithImpl<$Res, $Val extends QuadrantMetrics>
    implements $QuadrantMetricsCopyWith<$Res> {
  _$QuadrantMetricsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QuadrantMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? priority = null,
    Object? totalTasks = null,
    Object? completedTasks = null,
    Object? completionRate = null,
    Object? averageCompletionTime = null,
    Object? efficiencyScore = null,
    Object? trendChange = null,
  }) {
    return _then(_value.copyWith(
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as Priority,
      totalTasks: null == totalTasks
          ? _value.totalTasks
          : totalTasks // ignore: cast_nullable_to_non_nullable
              as int,
      completedTasks: null == completedTasks
          ? _value.completedTasks
          : completedTasks // ignore: cast_nullable_to_non_nullable
              as int,
      completionRate: null == completionRate
          ? _value.completionRate
          : completionRate // ignore: cast_nullable_to_non_nullable
              as double,
      averageCompletionTime: null == averageCompletionTime
          ? _value.averageCompletionTime
          : averageCompletionTime // ignore: cast_nullable_to_non_nullable
              as double,
      efficiencyScore: null == efficiencyScore
          ? _value.efficiencyScore
          : efficiencyScore // ignore: cast_nullable_to_non_nullable
              as double,
      trendChange: null == trendChange
          ? _value.trendChange
          : trendChange // ignore: cast_nullable_to_non_nullable
              as double,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QuadrantMetricsImplCopyWith<$Res>
    implements $QuadrantMetricsCopyWith<$Res> {
  factory _$$QuadrantMetricsImplCopyWith(_$QuadrantMetricsImpl value,
          $Res Function(_$QuadrantMetricsImpl) then) =
      __$$QuadrantMetricsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {Priority priority,
      int totalTasks,
      int completedTasks,
      double completionRate,
      double averageCompletionTime,
      double efficiencyScore,
      double trendChange});
}

/// @nodoc
class __$$QuadrantMetricsImplCopyWithImpl<$Res>
    extends _$QuadrantMetricsCopyWithImpl<$Res, _$QuadrantMetricsImpl>
    implements _$$QuadrantMetricsImplCopyWith<$Res> {
  __$$QuadrantMetricsImplCopyWithImpl(
      _$QuadrantMetricsImpl _value, $Res Function(_$QuadrantMetricsImpl) _then)
      : super(_value, _then);

  /// Create a copy of QuadrantMetrics
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? priority = null,
    Object? totalTasks = null,
    Object? completedTasks = null,
    Object? completionRate = null,
    Object? averageCompletionTime = null,
    Object? efficiencyScore = null,
    Object? trendChange = null,
  }) {
    return _then(_$QuadrantMetricsImpl(
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as Priority,
      totalTasks: null == totalTasks
          ? _value.totalTasks
          : totalTasks // ignore: cast_nullable_to_non_nullable
              as int,
      completedTasks: null == completedTasks
          ? _value.completedTasks
          : completedTasks // ignore: cast_nullable_to_non_nullable
              as int,
      completionRate: null == completionRate
          ? _value.completionRate
          : completionRate // ignore: cast_nullable_to_non_nullable
              as double,
      averageCompletionTime: null == averageCompletionTime
          ? _value.averageCompletionTime
          : averageCompletionTime // ignore: cast_nullable_to_non_nullable
              as double,
      efficiencyScore: null == efficiencyScore
          ? _value.efficiencyScore
          : efficiencyScore // ignore: cast_nullable_to_non_nullable
              as double,
      trendChange: null == trendChange
          ? _value.trendChange
          : trendChange // ignore: cast_nullable_to_non_nullable
              as double,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QuadrantMetricsImpl implements _QuadrantMetrics {
  const _$QuadrantMetricsImpl(
      {required this.priority,
      this.totalTasks = 0,
      this.completedTasks = 0,
      this.completionRate = 0.0,
      this.averageCompletionTime = 0.0,
      this.efficiencyScore = 0.0,
      this.trendChange = 0.0});

  factory _$QuadrantMetricsImpl.fromJson(Map<String, dynamic> json) =>
      _$$QuadrantMetricsImplFromJson(json);

  /// 象限优先级
  @override
  final Priority priority;

  /// 任务总数
  @override
  @JsonKey()
  final int totalTasks;

  /// 已完成任务数
  @override
  @JsonKey()
  final int completedTasks;

  /// 完成率
  @override
  @JsonKey()
  final double completionRate;

  /// 平均完成时间（小时）
  @override
  @JsonKey()
  final double averageCompletionTime;

  /// 效率分数
  @override
  @JsonKey()
  final double efficiencyScore;

  /// 趋势变化
  @override
  @JsonKey()
  final double trendChange;

  @override
  String toString() {
    return 'QuadrantMetrics(priority: $priority, totalTasks: $totalTasks, completedTasks: $completedTasks, completionRate: $completionRate, averageCompletionTime: $averageCompletionTime, efficiencyScore: $efficiencyScore, trendChange: $trendChange)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuadrantMetricsImpl &&
            (identical(other.priority, priority) ||
                other.priority == priority) &&
            (identical(other.totalTasks, totalTasks) ||
                other.totalTasks == totalTasks) &&
            (identical(other.completedTasks, completedTasks) ||
                other.completedTasks == completedTasks) &&
            (identical(other.completionRate, completionRate) ||
                other.completionRate == completionRate) &&
            (identical(other.averageCompletionTime, averageCompletionTime) ||
                other.averageCompletionTime == averageCompletionTime) &&
            (identical(other.efficiencyScore, efficiencyScore) ||
                other.efficiencyScore == efficiencyScore) &&
            (identical(other.trendChange, trendChange) ||
                other.trendChange == trendChange));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      priority,
      totalTasks,
      completedTasks,
      completionRate,
      averageCompletionTime,
      efficiencyScore,
      trendChange);

  /// Create a copy of QuadrantMetrics
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QuadrantMetricsImplCopyWith<_$QuadrantMetricsImpl> get copyWith =>
      __$$QuadrantMetricsImplCopyWithImpl<_$QuadrantMetricsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QuadrantMetricsImplToJson(
      this,
    );
  }
}

abstract class _QuadrantMetrics implements QuadrantMetrics {
  const factory _QuadrantMetrics(
      {required final Priority priority,
      final int totalTasks,
      final int completedTasks,
      final double completionRate,
      final double averageCompletionTime,
      final double efficiencyScore,
      final double trendChange}) = _$QuadrantMetricsImpl;

  factory _QuadrantMetrics.fromJson(Map<String, dynamic> json) =
      _$QuadrantMetricsImpl.fromJson;

  /// 象限优先级
  @override
  Priority get priority;

  /// 任务总数
  @override
  int get totalTasks;

  /// 已完成任务数
  @override
  int get completedTasks;

  /// 完成率
  @override
  double get completionRate;

  /// 平均完成时间（小时）
  @override
  double get averageCompletionTime;

  /// 效率分数
  @override
  double get efficiencyScore;

  /// 趋势变化
  @override
  double get trendChange;

  /// Create a copy of QuadrantMetrics
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QuadrantMetricsImplCopyWith<_$QuadrantMetricsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

PeriodComparison _$PeriodComparisonFromJson(Map<String, dynamic> json) {
  return _PeriodComparison.fromJson(json);
}

/// @nodoc
mixin _$PeriodComparison {
  /// 第一个期间
  DateTime get period1Start => throw _privateConstructorUsedError;
  DateTime get period1End => throw _privateConstructorUsedError;

  /// 第二个期间
  DateTime get period2Start => throw _privateConstructorUsedError;
  DateTime get period2End => throw _privateConstructorUsedError;

  /// 第一个期间的任务总数
  int get period1TotalTasks => throw _privateConstructorUsedError;

  /// 第二个期间的任务总数
  int get period2TotalTasks => throw _privateConstructorUsedError;

  /// 第一个期间的完成率
  double get period1CompletionRate => throw _privateConstructorUsedError;

  /// 第二个期间的完成率
  double get period2CompletionRate => throw _privateConstructorUsedError;

  /// 任务数量变化百分比
  double get taskCountChangePercent => throw _privateConstructorUsedError;

  /// 完成率变化百分比
  double get completionRateChangePercent => throw _privateConstructorUsedError;

  /// 效率变化百分比
  double get efficiencyChangePercent => throw _privateConstructorUsedError;

  /// 改进建议
  List<String> get improvementSuggestions => throw _privateConstructorUsedError;

  /// Serializes this PeriodComparison to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of PeriodComparison
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $PeriodComparisonCopyWith<PeriodComparison> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PeriodComparisonCopyWith<$Res> {
  factory $PeriodComparisonCopyWith(
          PeriodComparison value, $Res Function(PeriodComparison) then) =
      _$PeriodComparisonCopyWithImpl<$Res, PeriodComparison>;
  @useResult
  $Res call(
      {DateTime period1Start,
      DateTime period1End,
      DateTime period2Start,
      DateTime period2End,
      int period1TotalTasks,
      int period2TotalTasks,
      double period1CompletionRate,
      double period2CompletionRate,
      double taskCountChangePercent,
      double completionRateChangePercent,
      double efficiencyChangePercent,
      List<String> improvementSuggestions});
}

/// @nodoc
class _$PeriodComparisonCopyWithImpl<$Res, $Val extends PeriodComparison>
    implements $PeriodComparisonCopyWith<$Res> {
  _$PeriodComparisonCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of PeriodComparison
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? period1Start = null,
    Object? period1End = null,
    Object? period2Start = null,
    Object? period2End = null,
    Object? period1TotalTasks = null,
    Object? period2TotalTasks = null,
    Object? period1CompletionRate = null,
    Object? period2CompletionRate = null,
    Object? taskCountChangePercent = null,
    Object? completionRateChangePercent = null,
    Object? efficiencyChangePercent = null,
    Object? improvementSuggestions = null,
  }) {
    return _then(_value.copyWith(
      period1Start: null == period1Start
          ? _value.period1Start
          : period1Start // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period1End: null == period1End
          ? _value.period1End
          : period1End // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period2Start: null == period2Start
          ? _value.period2Start
          : period2Start // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period2End: null == period2End
          ? _value.period2End
          : period2End // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period1TotalTasks: null == period1TotalTasks
          ? _value.period1TotalTasks
          : period1TotalTasks // ignore: cast_nullable_to_non_nullable
              as int,
      period2TotalTasks: null == period2TotalTasks
          ? _value.period2TotalTasks
          : period2TotalTasks // ignore: cast_nullable_to_non_nullable
              as int,
      period1CompletionRate: null == period1CompletionRate
          ? _value.period1CompletionRate
          : period1CompletionRate // ignore: cast_nullable_to_non_nullable
              as double,
      period2CompletionRate: null == period2CompletionRate
          ? _value.period2CompletionRate
          : period2CompletionRate // ignore: cast_nullable_to_non_nullable
              as double,
      taskCountChangePercent: null == taskCountChangePercent
          ? _value.taskCountChangePercent
          : taskCountChangePercent // ignore: cast_nullable_to_non_nullable
              as double,
      completionRateChangePercent: null == completionRateChangePercent
          ? _value.completionRateChangePercent
          : completionRateChangePercent // ignore: cast_nullable_to_non_nullable
              as double,
      efficiencyChangePercent: null == efficiencyChangePercent
          ? _value.efficiencyChangePercent
          : efficiencyChangePercent // ignore: cast_nullable_to_non_nullable
              as double,
      improvementSuggestions: null == improvementSuggestions
          ? _value.improvementSuggestions
          : improvementSuggestions // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PeriodComparisonImplCopyWith<$Res>
    implements $PeriodComparisonCopyWith<$Res> {
  factory _$$PeriodComparisonImplCopyWith(_$PeriodComparisonImpl value,
          $Res Function(_$PeriodComparisonImpl) then) =
      __$$PeriodComparisonImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {DateTime period1Start,
      DateTime period1End,
      DateTime period2Start,
      DateTime period2End,
      int period1TotalTasks,
      int period2TotalTasks,
      double period1CompletionRate,
      double period2CompletionRate,
      double taskCountChangePercent,
      double completionRateChangePercent,
      double efficiencyChangePercent,
      List<String> improvementSuggestions});
}

/// @nodoc
class __$$PeriodComparisonImplCopyWithImpl<$Res>
    extends _$PeriodComparisonCopyWithImpl<$Res, _$PeriodComparisonImpl>
    implements _$$PeriodComparisonImplCopyWith<$Res> {
  __$$PeriodComparisonImplCopyWithImpl(_$PeriodComparisonImpl _value,
      $Res Function(_$PeriodComparisonImpl) _then)
      : super(_value, _then);

  /// Create a copy of PeriodComparison
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? period1Start = null,
    Object? period1End = null,
    Object? period2Start = null,
    Object? period2End = null,
    Object? period1TotalTasks = null,
    Object? period2TotalTasks = null,
    Object? period1CompletionRate = null,
    Object? period2CompletionRate = null,
    Object? taskCountChangePercent = null,
    Object? completionRateChangePercent = null,
    Object? efficiencyChangePercent = null,
    Object? improvementSuggestions = null,
  }) {
    return _then(_$PeriodComparisonImpl(
      period1Start: null == period1Start
          ? _value.period1Start
          : period1Start // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period1End: null == period1End
          ? _value.period1End
          : period1End // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period2Start: null == period2Start
          ? _value.period2Start
          : period2Start // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period2End: null == period2End
          ? _value.period2End
          : period2End // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period1TotalTasks: null == period1TotalTasks
          ? _value.period1TotalTasks
          : period1TotalTasks // ignore: cast_nullable_to_non_nullable
              as int,
      period2TotalTasks: null == period2TotalTasks
          ? _value.period2TotalTasks
          : period2TotalTasks // ignore: cast_nullable_to_non_nullable
              as int,
      period1CompletionRate: null == period1CompletionRate
          ? _value.period1CompletionRate
          : period1CompletionRate // ignore: cast_nullable_to_non_nullable
              as double,
      period2CompletionRate: null == period2CompletionRate
          ? _value.period2CompletionRate
          : period2CompletionRate // ignore: cast_nullable_to_non_nullable
              as double,
      taskCountChangePercent: null == taskCountChangePercent
          ? _value.taskCountChangePercent
          : taskCountChangePercent // ignore: cast_nullable_to_non_nullable
              as double,
      completionRateChangePercent: null == completionRateChangePercent
          ? _value.completionRateChangePercent
          : completionRateChangePercent // ignore: cast_nullable_to_non_nullable
              as double,
      efficiencyChangePercent: null == efficiencyChangePercent
          ? _value.efficiencyChangePercent
          : efficiencyChangePercent // ignore: cast_nullable_to_non_nullable
              as double,
      improvementSuggestions: null == improvementSuggestions
          ? _value._improvementSuggestions
          : improvementSuggestions // ignore: cast_nullable_to_non_nullable
              as List<String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$PeriodComparisonImpl implements _PeriodComparison {
  const _$PeriodComparisonImpl(
      {required this.period1Start,
      required this.period1End,
      required this.period2Start,
      required this.period2End,
      this.period1TotalTasks = 0,
      this.period2TotalTasks = 0,
      this.period1CompletionRate = 0.0,
      this.period2CompletionRate = 0.0,
      this.taskCountChangePercent = 0.0,
      this.completionRateChangePercent = 0.0,
      this.efficiencyChangePercent = 0.0,
      final List<String> improvementSuggestions = const []})
      : _improvementSuggestions = improvementSuggestions;

  factory _$PeriodComparisonImpl.fromJson(Map<String, dynamic> json) =>
      _$$PeriodComparisonImplFromJson(json);

  /// 第一个期间
  @override
  final DateTime period1Start;
  @override
  final DateTime period1End;

  /// 第二个期间
  @override
  final DateTime period2Start;
  @override
  final DateTime period2End;

  /// 第一个期间的任务总数
  @override
  @JsonKey()
  final int period1TotalTasks;

  /// 第二个期间的任务总数
  @override
  @JsonKey()
  final int period2TotalTasks;

  /// 第一个期间的完成率
  @override
  @JsonKey()
  final double period1CompletionRate;

  /// 第二个期间的完成率
  @override
  @JsonKey()
  final double period2CompletionRate;

  /// 任务数量变化百分比
  @override
  @JsonKey()
  final double taskCountChangePercent;

  /// 完成率变化百分比
  @override
  @JsonKey()
  final double completionRateChangePercent;

  /// 效率变化百分比
  @override
  @JsonKey()
  final double efficiencyChangePercent;

  /// 改进建议
  final List<String> _improvementSuggestions;

  /// 改进建议
  @override
  @JsonKey()
  List<String> get improvementSuggestions {
    if (_improvementSuggestions is EqualUnmodifiableListView)
      return _improvementSuggestions;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_improvementSuggestions);
  }

  @override
  String toString() {
    return 'PeriodComparison(period1Start: $period1Start, period1End: $period1End, period2Start: $period2Start, period2End: $period2End, period1TotalTasks: $period1TotalTasks, period2TotalTasks: $period2TotalTasks, period1CompletionRate: $period1CompletionRate, period2CompletionRate: $period2CompletionRate, taskCountChangePercent: $taskCountChangePercent, completionRateChangePercent: $completionRateChangePercent, efficiencyChangePercent: $efficiencyChangePercent, improvementSuggestions: $improvementSuggestions)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PeriodComparisonImpl &&
            (identical(other.period1Start, period1Start) ||
                other.period1Start == period1Start) &&
            (identical(other.period1End, period1End) ||
                other.period1End == period1End) &&
            (identical(other.period2Start, period2Start) ||
                other.period2Start == period2Start) &&
            (identical(other.period2End, period2End) ||
                other.period2End == period2End) &&
            (identical(other.period1TotalTasks, period1TotalTasks) ||
                other.period1TotalTasks == period1TotalTasks) &&
            (identical(other.period2TotalTasks, period2TotalTasks) ||
                other.period2TotalTasks == period2TotalTasks) &&
            (identical(other.period1CompletionRate, period1CompletionRate) ||
                other.period1CompletionRate == period1CompletionRate) &&
            (identical(other.period2CompletionRate, period2CompletionRate) ||
                other.period2CompletionRate == period2CompletionRate) &&
            (identical(other.taskCountChangePercent, taskCountChangePercent) ||
                other.taskCountChangePercent == taskCountChangePercent) &&
            (identical(other.completionRateChangePercent,
                    completionRateChangePercent) ||
                other.completionRateChangePercent ==
                    completionRateChangePercent) &&
            (identical(
                    other.efficiencyChangePercent, efficiencyChangePercent) ||
                other.efficiencyChangePercent == efficiencyChangePercent) &&
            const DeepCollectionEquality().equals(
                other._improvementSuggestions, _improvementSuggestions));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      period1Start,
      period1End,
      period2Start,
      period2End,
      period1TotalTasks,
      period2TotalTasks,
      period1CompletionRate,
      period2CompletionRate,
      taskCountChangePercent,
      completionRateChangePercent,
      efficiencyChangePercent,
      const DeepCollectionEquality().hash(_improvementSuggestions));

  /// Create a copy of PeriodComparison
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PeriodComparisonImplCopyWith<_$PeriodComparisonImpl> get copyWith =>
      __$$PeriodComparisonImplCopyWithImpl<_$PeriodComparisonImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$PeriodComparisonImplToJson(
      this,
    );
  }
}

abstract class _PeriodComparison implements PeriodComparison {
  const factory _PeriodComparison(
      {required final DateTime period1Start,
      required final DateTime period1End,
      required final DateTime period2Start,
      required final DateTime period2End,
      final int period1TotalTasks,
      final int period2TotalTasks,
      final double period1CompletionRate,
      final double period2CompletionRate,
      final double taskCountChangePercent,
      final double completionRateChangePercent,
      final double efficiencyChangePercent,
      final List<String> improvementSuggestions}) = _$PeriodComparisonImpl;

  factory _PeriodComparison.fromJson(Map<String, dynamic> json) =
      _$PeriodComparisonImpl.fromJson;

  /// 第一个期间
  @override
  DateTime get period1Start;
  @override
  DateTime get period1End;

  /// 第二个期间
  @override
  DateTime get period2Start;
  @override
  DateTime get period2End;

  /// 第一个期间的任务总数
  @override
  int get period1TotalTasks;

  /// 第二个期间的任务总数
  @override
  int get period2TotalTasks;

  /// 第一个期间的完成率
  @override
  double get period1CompletionRate;

  /// 第二个期间的完成率
  @override
  double get period2CompletionRate;

  /// 任务数量变化百分比
  @override
  double get taskCountChangePercent;

  /// 完成率变化百分比
  @override
  double get completionRateChangePercent;

  /// 效率变化百分比
  @override
  double get efficiencyChangePercent;

  /// 改进建议
  @override
  List<String> get improvementSuggestions;

  /// Create a copy of PeriodComparison
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PeriodComparisonImplCopyWith<_$PeriodComparisonImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
