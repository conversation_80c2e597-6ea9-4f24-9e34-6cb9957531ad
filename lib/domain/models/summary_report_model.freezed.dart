// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'summary_report_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SummaryReport _$SummaryReportFromJson(Map<String, dynamic> json) {
  return _SummaryReport.fromJson(json);
}

/// @nodoc
mixin _$SummaryReport {
  /// 报告周期（如"2025年9月"或"2025年"）
  String get period => throw _privateConstructorUsedError;

  /// 在该周期内创建的任务总数
  int get totalTasksCreated => throw _privateConstructorUsedError;

  /// 在该周期内完成的任务总数
  int get totalTasksCompleted => throw _privateConstructorUsedError;

  /// 完成率（百分比）
  double get completionRate => throw _privateConstructorUsedError;

  /// 各象限任务数量分布
  Map<Priority, int> get quadrantDistribution =>
      throw _privateConstructorUsedError;

  /// 高光时刻任务列表
  List<Task> get highlights => throw _privateConstructorUsedError;

  /// Serializes this SummaryReport to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SummaryReport
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SummaryReportCopyWith<SummaryReport> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SummaryReportCopyWith<$Res> {
  factory $SummaryReportCopyWith(
          SummaryReport value, $Res Function(SummaryReport) then) =
      _$SummaryReportCopyWithImpl<$Res, SummaryReport>;
  @useResult
  $Res call(
      {String period,
      int totalTasksCreated,
      int totalTasksCompleted,
      double completionRate,
      Map<Priority, int> quadrantDistribution,
      List<Task> highlights});
}

/// @nodoc
class _$SummaryReportCopyWithImpl<$Res, $Val extends SummaryReport>
    implements $SummaryReportCopyWith<$Res> {
  _$SummaryReportCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SummaryReport
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? period = null,
    Object? totalTasksCreated = null,
    Object? totalTasksCompleted = null,
    Object? completionRate = null,
    Object? quadrantDistribution = null,
    Object? highlights = null,
  }) {
    return _then(_value.copyWith(
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as String,
      totalTasksCreated: null == totalTasksCreated
          ? _value.totalTasksCreated
          : totalTasksCreated // ignore: cast_nullable_to_non_nullable
              as int,
      totalTasksCompleted: null == totalTasksCompleted
          ? _value.totalTasksCompleted
          : totalTasksCompleted // ignore: cast_nullable_to_non_nullable
              as int,
      completionRate: null == completionRate
          ? _value.completionRate
          : completionRate // ignore: cast_nullable_to_non_nullable
              as double,
      quadrantDistribution: null == quadrantDistribution
          ? _value.quadrantDistribution
          : quadrantDistribution // ignore: cast_nullable_to_non_nullable
              as Map<Priority, int>,
      highlights: null == highlights
          ? _value.highlights
          : highlights // ignore: cast_nullable_to_non_nullable
              as List<Task>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SummaryReportImplCopyWith<$Res>
    implements $SummaryReportCopyWith<$Res> {
  factory _$$SummaryReportImplCopyWith(
          _$SummaryReportImpl value, $Res Function(_$SummaryReportImpl) then) =
      __$$SummaryReportImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String period,
      int totalTasksCreated,
      int totalTasksCompleted,
      double completionRate,
      Map<Priority, int> quadrantDistribution,
      List<Task> highlights});
}

/// @nodoc
class __$$SummaryReportImplCopyWithImpl<$Res>
    extends _$SummaryReportCopyWithImpl<$Res, _$SummaryReportImpl>
    implements _$$SummaryReportImplCopyWith<$Res> {
  __$$SummaryReportImplCopyWithImpl(
      _$SummaryReportImpl _value, $Res Function(_$SummaryReportImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryReport
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? period = null,
    Object? totalTasksCreated = null,
    Object? totalTasksCompleted = null,
    Object? completionRate = null,
    Object? quadrantDistribution = null,
    Object? highlights = null,
  }) {
    return _then(_$SummaryReportImpl(
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as String,
      totalTasksCreated: null == totalTasksCreated
          ? _value.totalTasksCreated
          : totalTasksCreated // ignore: cast_nullable_to_non_nullable
              as int,
      totalTasksCompleted: null == totalTasksCompleted
          ? _value.totalTasksCompleted
          : totalTasksCompleted // ignore: cast_nullable_to_non_nullable
              as int,
      completionRate: null == completionRate
          ? _value.completionRate
          : completionRate // ignore: cast_nullable_to_non_nullable
              as double,
      quadrantDistribution: null == quadrantDistribution
          ? _value._quadrantDistribution
          : quadrantDistribution // ignore: cast_nullable_to_non_nullable
              as Map<Priority, int>,
      highlights: null == highlights
          ? _value._highlights
          : highlights // ignore: cast_nullable_to_non_nullable
              as List<Task>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SummaryReportImpl implements _SummaryReport {
  const _$SummaryReportImpl(
      {required this.period,
      this.totalTasksCreated = 0,
      this.totalTasksCompleted = 0,
      this.completionRate = 0.0,
      final Map<Priority, int> quadrantDistribution = const {},
      final List<Task> highlights = const []})
      : _quadrantDistribution = quadrantDistribution,
        _highlights = highlights;

  factory _$SummaryReportImpl.fromJson(Map<String, dynamic> json) =>
      _$$SummaryReportImplFromJson(json);

  /// 报告周期（如"2025年9月"或"2025年"）
  @override
  final String period;

  /// 在该周期内创建的任务总数
  @override
  @JsonKey()
  final int totalTasksCreated;

  /// 在该周期内完成的任务总数
  @override
  @JsonKey()
  final int totalTasksCompleted;

  /// 完成率（百分比）
  @override
  @JsonKey()
  final double completionRate;

  /// 各象限任务数量分布
  final Map<Priority, int> _quadrantDistribution;

  /// 各象限任务数量分布
  @override
  @JsonKey()
  Map<Priority, int> get quadrantDistribution {
    if (_quadrantDistribution is EqualUnmodifiableMapView)
      return _quadrantDistribution;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_quadrantDistribution);
  }

  /// 高光时刻任务列表
  final List<Task> _highlights;

  /// 高光时刻任务列表
  @override
  @JsonKey()
  List<Task> get highlights {
    if (_highlights is EqualUnmodifiableListView) return _highlights;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_highlights);
  }

  @override
  String toString() {
    return 'SummaryReport(period: $period, totalTasksCreated: $totalTasksCreated, totalTasksCompleted: $totalTasksCompleted, completionRate: $completionRate, quadrantDistribution: $quadrantDistribution, highlights: $highlights)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SummaryReportImpl &&
            (identical(other.period, period) || other.period == period) &&
            (identical(other.totalTasksCreated, totalTasksCreated) ||
                other.totalTasksCreated == totalTasksCreated) &&
            (identical(other.totalTasksCompleted, totalTasksCompleted) ||
                other.totalTasksCompleted == totalTasksCompleted) &&
            (identical(other.completionRate, completionRate) ||
                other.completionRate == completionRate) &&
            const DeepCollectionEquality()
                .equals(other._quadrantDistribution, _quadrantDistribution) &&
            const DeepCollectionEquality()
                .equals(other._highlights, _highlights));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      period,
      totalTasksCreated,
      totalTasksCompleted,
      completionRate,
      const DeepCollectionEquality().hash(_quadrantDistribution),
      const DeepCollectionEquality().hash(_highlights));

  /// Create a copy of SummaryReport
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SummaryReportImplCopyWith<_$SummaryReportImpl> get copyWith =>
      __$$SummaryReportImplCopyWithImpl<_$SummaryReportImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SummaryReportImplToJson(
      this,
    );
  }
}

abstract class _SummaryReport implements SummaryReport {
  const factory _SummaryReport(
      {required final String period,
      final int totalTasksCreated,
      final int totalTasksCompleted,
      final double completionRate,
      final Map<Priority, int> quadrantDistribution,
      final List<Task> highlights}) = _$SummaryReportImpl;

  factory _SummaryReport.fromJson(Map<String, dynamic> json) =
      _$SummaryReportImpl.fromJson;

  /// 报告周期（如"2025年9月"或"2025年"）
  @override
  String get period;

  /// 在该周期内创建的任务总数
  @override
  int get totalTasksCreated;

  /// 在该周期内完成的任务总数
  @override
  int get totalTasksCompleted;

  /// 完成率（百分比）
  @override
  double get completionRate;

  /// 各象限任务数量分布
  @override
  Map<Priority, int> get quadrantDistribution;

  /// 高光时刻任务列表
  @override
  List<Task> get highlights;

  /// Create a copy of SummaryReport
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SummaryReportImplCopyWith<_$SummaryReportImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
