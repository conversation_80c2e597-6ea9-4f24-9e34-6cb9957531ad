// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$TaskImpl _$$TaskImplFromJson(Map<String, dynamic> json) => _$TaskImpl(
      id: json['id'] as String,
      title: json['title'] as String,
      notes: json['notes'] as String? ?? '',
      creationDate: DateTime.parse(json['creationDate'] as String),
      dueDate: DateTime.parse(json['dueDate'] as String),
      isCompleted: json['isCompleted'] as bool? ?? false,
      completionDate: json['completionDate'] == null
          ? null
          : DateTime.parse(json['completionDate'] as String),
      priority: $enumDecodeNullable(_$PriorityEnumMap, json['priority']) ??
          Priority.urgentImportant,
      subtasks: (json['subtasks'] as List<dynamic>?)
              ?.map((e) => SubTask.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$$TaskImplToJson(_$TaskImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'notes': instance.notes,
      'creationDate': instance.creationDate.toIso8601String(),
      'dueDate': instance.dueDate.toIso8601String(),
      'isCompleted': instance.isCompleted,
      'completionDate': instance.completionDate?.toIso8601String(),
      'priority': _$PriorityEnumMap[instance.priority]!,
      'subtasks': instance.subtasks,
    };

const _$PriorityEnumMap = {
  Priority.urgentImportant: 'urgent_important',
  Priority.importantNotUrgent: 'important_not_urgent',
  Priority.urgentNotImportant: 'urgent_not_important',
  Priority.notUrgentNotImportant: 'not_urgent_not_important',
};

_$SubTaskImpl _$$SubTaskImplFromJson(Map<String, dynamic> json) =>
    _$SubTaskImpl(
      id: json['id'] as String,
      parentTaskId: json['parentTaskId'] as String,
      title: json['title'] as String,
      isCompleted: json['isCompleted'] as bool? ?? false,
    );

Map<String, dynamic> _$$SubTaskImplToJson(_$SubTaskImpl instance) =>
    <String, dynamic>{
      'id': instance.id,
      'parentTaskId': instance.parentTaskId,
      'title': instance.title,
      'isCompleted': instance.isCompleted,
    };
