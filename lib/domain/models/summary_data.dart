import 'package:freezed_annotation/freezed_annotation.dart';
import 'task_model.dart';

part 'summary_data.freezed.dart';
part 'summary_data.g.dart';

/// 总结数据容器
/// 
/// 包含指定时间范围内的任务统计和分析数据
@freezed
class SummaryData with _$SummaryData {
  const factory SummaryData({
    /// 统计开始日期
    required DateTime startDate,
    
    /// 统计结束日期
    required DateTime endDate,
    
    /// 该时间段内创建的任务总数
    @Default(0) int totalTasksCreated,
    
    /// 该时间段内完成的任务总数
    @Default(0) int totalTasksCompleted,
    
    /// 任务完成率（百分比）
    @Default(0.0) double completionRate,
    
    /// 按优先级分组的任务数量
    @Default({}) Map<Priority, int> tasksByPriority,
    
    /// 按优先级分组的已完成任务数量
    @Default({}) Map<Priority, int> completedTasksByPriority,
    
    /// 每日任务创建数量（用于趋势分析）
    @Default({}) Map<DateTime, int> dailyTaskCreation,
    
    /// 每日任务完成数量（用于趋势分析）
    @Default({}) Map<DateTime, int> dailyTaskCompletion,
    
    /// 平均每日任务创建数量
    @Default(0.0) double averageDailyCreation,
    
    /// 平均每日任务完成数量
    @Default(0.0) double averageDailyCompletion,
    
    /// 最活跃的日期（任务创建最多）
    DateTime? mostActiveDate,
    
    /// 最活跃日期的任务数量
    @Default(0) int mostActiveDateTaskCount,
    
    /// 连续完成任务的天数
    @Default(0) int consecutiveCompletionDays,
    
    /// 最长连续完成任务的天数
    @Default(0) int longestCompletionStreak,
    
    /// 任务完成效率趋势（正数表示提升，负数表示下降）
    @Default(0.0) double efficiencyTrend,
    
    /// 数据生成时间
    @Default(0) int calculationDurationMs,
  }) = _SummaryData;

  factory SummaryData.fromJson(Map<String, dynamic> json) => _$SummaryDataFromJson(json);
}

/// 生产力趋势数据
@freezed
class ProductivityTrends with _$ProductivityTrends {
  const factory ProductivityTrends({
    /// 趋势分析开始日期
    required DateTime startDate,
    
    /// 趋势分析结束日期
    required DateTime endDate,
    
    /// 趋势数据点列表
    @Default([]) List<TrendDataPoint> dataPoints,
    
    /// 整体趋势方向（上升/下降/稳定）
    @Default(TrendDirection.stable) TrendDirection overallTrend,
    
    /// 趋势强度（0.0-1.0）
    @Default(0.0) double trendStrength,
    
    /// 预测的下一个周期任务完成数量
    @Default(0) int predictedNextPeriodCompletion,
    
    /// 趋势置信度（0.0-1.0）
    @Default(0.0) double confidence,
  }) = _ProductivityTrends;

  factory ProductivityTrends.fromJson(Map<String, dynamic> json) => _$ProductivityTrendsFromJson(json);
}

/// 趋势数据点
@freezed
class TrendDataPoint with _$TrendDataPoint {
  const factory TrendDataPoint({
    /// 数据点日期
    required DateTime date,
    
    /// 当日任务完成数量
    @Default(0) int completedTasks,
    
    /// 当日任务创建数量
    @Default(0) int createdTasks,
    
    /// 当日效率分数
    @Default(0.0) double efficiencyScore,
    
    /// 与前一日的趋势变化
    @Default(0.0) double trendChange,
  }) = _TrendDataPoint;

  factory TrendDataPoint.fromJson(Map<String, dynamic> json) => _$TrendDataPointFromJson(json);
}

/// 趋势方向枚举
enum TrendDirection {
  /// 上升趋势
  up,
  
  /// 下降趋势
  down,
  
  /// 稳定趋势
  stable,
  
  /// 波动趋势
  fluctuating,
}

/// 象限分析数据
@freezed
class QuadrantAnalysis with _$QuadrantAnalysis {
  const factory QuadrantAnalysis({
    /// 分析时间范围
    required DateTime startDate,
    required DateTime endDate,
    
    /// 各象限的任务数量
    @Default({}) Map<Priority, int> taskCounts,
    
    /// 各象限的完成率
    @Default({}) Map<Priority, double> completionRates,
    
    /// 各象限的平均完成时间（小时）
    @Default({}) Map<Priority, double> averageCompletionTime,
    
    /// 各象限的效率分数
    @Default({}) Map<Priority, double> efficiencyScores,
    
    /// 最需要关注的象限（效率最低）
    Priority? focusQuadrant,
    
    /// 最有效率的象限
    Priority? mostEfficientQuadrant,
    
    /// 象限平衡性评分（0.0-1.0，1.0表示完全平衡）
    @Default(0.0) double balanceScore,
  }) = _QuadrantAnalysis;

  factory QuadrantAnalysis.fromJson(Map<String, dynamic> json) => _$QuadrantAnalysisFromJson(json);
}

/// 完成模式分析
@freezed
class CompletionPatterns with _$CompletionPatterns {
  const factory CompletionPatterns({
    /// 分析时间范围（天数）
    @Default(90) int analysisPeriod,
    
    /// 每日完成模式
    @Default({}) Map<int, double> dailyPatterns, // 0=周日, 1=周一, ..., 6=周六
    
    /// 每周完成模式
    @Default({}) Map<int, double> weeklyPatterns, // 1=第1周, 2=第2周, ...
    
    /// 每月完成模式
    @Default({}) Map<int, double> monthlyPatterns, // 1=1月, 2=2月, ..., 12=12月
    
    /// 最佳完成时间（小时，0-23）
    @Default(0) int bestCompletionHour,
    
    /// 最佳完成日期（星期几，0-6）
    @Default(0) int bestCompletionDay,
    
    /// 完成效率最高的时间段
    @Default('') String mostProductiveTimeSlot,
    
    /// 模式稳定性评分（0.0-1.0）
    @Default(0.0) double patternStability,
  }) = _CompletionPatterns;

  factory CompletionPatterns.fromJson(Map<String, dynamic> json) => _$CompletionPatternsFromJson(json);
}

/// 性能指标
@freezed
class PerformanceMetrics with _$PerformanceMetrics {
  const factory PerformanceMetrics({
    /// 计算开始时间
    required DateTime calculationStart,
    
    /// 计算结束时间
    required DateTime calculationEnd,
    
    /// 计算耗时（毫秒）
    @Default(0) int calculationDurationMs,
    
    /// 数据加载耗时（毫秒）
    @Default(0) int dataLoadDurationMs,
    
    /// 内存使用峰值（MB）
    @Default(0.0) double peakMemoryUsage,
    
    /// CPU使用率峰值（百分比）
    @Default(0.0) double peakCpuUsage,
    
    /// 数据库查询次数
    @Default(0) int databaseQueryCount,
    
    /// 平均查询响应时间（毫秒）
    @Default(0.0) double averageQueryResponseTime,
    
    /// 缓存命中率（百分比）
    @Default(0.0) double cacheHitRate,
    
    /// 性能评分（0.0-1.0）
    @Default(0.0) double performanceScore,
  }) = _PerformanceMetrics;

  factory PerformanceMetrics.fromJson(Map<String, dynamic> json) => _$PerformanceMetricsFromJson(json);
}

/// 任务高光时刻
@freezed
class TaskHighlight with _$TaskHighlight {
  const factory TaskHighlight({
    /// 任务ID
    required String taskId,
    
    /// 任务标题
    required String title,
    
    /// 任务优先级
    required Priority priority,
    
    /// 完成日期
    required DateTime completionDate,
    
    /// 子任务数量
    @Default(0) int subtaskCount,
    
    /// 完成子任务数量
    @Default(0) int completedSubtasks,
    
    /// 高光原因描述
    @Default('') String highlightReason,
    
    /// 高光分数（用于排序）
    @Default(0.0) double highlightScore,
    
    /// 相关标签
    @Default([]) List<String> tags,
  }) = _TaskHighlight;

  factory TaskHighlight.fromJson(Map<String, dynamic> json) => _$TaskHighlightFromJson(json);
}

/// 图表数据
@freezed
class ChartData with _$ChartData {
  const factory ChartData({
    /// 图表类型
    required SummaryChartType chartType,
    
    /// 图表标题
    @Default('') String title,
    
    /// 图表副标题
    @Default('') String subtitle,
    
    /// 数据点列表
    @Default([]) List<ChartDataPoint> dataPoints,
    
    /// X轴标签
    @Default([]) List<String> xAxisLabels,
    
    /// Y轴标签
    @Default([]) List<String> yAxisLabels,
    
    /// 图表配置选项
    @Default({}) Map<String, dynamic> chartOptions,
    
    /// 数据更新时间
    DateTime? lastUpdated,
  }) = _ChartData;

  factory ChartData.fromJson(Map<String, dynamic> json) => _$ChartDataFromJson(json);
}

/// 图表数据点
@freezed
class ChartDataPoint with _$ChartDataPoint {
  const factory ChartDataPoint({
    /// 数据点标签
    required String label,
    
    /// 数据点值
    required double value,
    
    /// 数据点颜色
    @Default('') String color,
    
    /// 数据点描述
    @Default('') String description,
    
    /// 数据点元数据
    @Default({}) Map<String, dynamic> metadata,
  }) = _ChartDataPoint;

  factory ChartDataPoint.fromJson(Map<String, dynamic> json) => _$ChartDataPointFromJson(json);
}

/// 象限指标
@freezed
class QuadrantMetrics with _$QuadrantMetrics {
  const factory QuadrantMetrics({
    /// 象限优先级
    required Priority priority,
    
    /// 任务总数
    @Default(0) int totalTasks,
    
    /// 已完成任务数
    @Default(0) int completedTasks,
    
    /// 完成率
    @Default(0.0) double completionRate,
    
    /// 平均完成时间（小时）
    @Default(0.0) double averageCompletionTime,
    
    /// 效率分数
    @Default(0.0) double efficiencyScore,
    
    /// 趋势变化
    @Default(0.0) double trendChange,
  }) = _QuadrantMetrics;

  factory QuadrantMetrics.fromJson(Map<String, dynamic> json) => _$QuadrantMetricsFromJson(json);
}

/// 期间比较数据
@freezed
class PeriodComparison with _$PeriodComparison {
  const factory PeriodComparison({
    /// 第一个期间
    required DateTime period1Start,
    required DateTime period1End,
    
    /// 第二个期间
    required DateTime period2Start,
    required DateTime period2End,
    
    /// 第一个期间的任务总数
    @Default(0) int period1TotalTasks,
    
    /// 第二个期间的任务总数
    @Default(0) int period2TotalTasks,
    
    /// 第一个期间的完成率
    @Default(0.0) double period1CompletionRate,
    
    /// 第二个期间的完成率
    @Default(0.0) double period2CompletionRate,
    
    /// 任务数量变化百分比
    @Default(0.0) double taskCountChangePercent,
    
    /// 完成率变化百分比
    @Default(0.0) double completionRateChangePercent,
    
    /// 效率变化百分比
    @Default(0.0) double efficiencyChangePercent,
    
    /// 改进建议
    @Default([]) List<String> improvementSuggestions,
  }) = _PeriodComparison;

  factory PeriodComparison.fromJson(Map<String, dynamic> json) => _$PeriodComparisonFromJson(json);
}

/// 总结图表类型枚举
enum SummaryChartType {
  /// 任务完成趋势图
  completionTrend,
  
  /// 象限分布饼图
  quadrantDistribution,
  
  /// 每日任务负荷图
  dailyTaskLoad,
  
  /// 优先级分布柱状图
  priorityDistribution,
  
  /// 完成时间分布图
  completionTimeDistribution,
  
  /// 效率趋势图
  efficiencyTrend,
  
  /// 自定义图表
  custom,
}

/// 总结期间枚举
enum SummaryPeriod {
  /// 日
  daily,
  
  /// 周
  weekly,
  
  /// 月
  monthly,
  
  /// 季度
  quarterly,
  
  /// 年
  yearly,
  
  /// 自定义
  custom,
}

/// 总结导出格式枚举
enum SummaryExportFormat {
  /// JSON格式
  json,
  
  /// CSV格式
  csv,
  
  /// PDF格式
  pdf,
  
  /// Excel格式
  excel,
  
  /// 纯文本格式
  text,
}
