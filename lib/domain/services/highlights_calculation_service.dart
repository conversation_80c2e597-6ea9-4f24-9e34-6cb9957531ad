import '../models/task_model.dart';

/// Service for calculating task highlights for summary reports
class HighlightsCalculationService {
  /// Maximum number of highlights to return
  static const int maxHighlights = 5;
  
  /// Calculates highlights from completed tasks
  /// 
  /// Highlights are selected based on:
  /// 1. Priority weight (higher priority = more important)
  /// 2. Number of subtasks (more subtasks = more complex)
  /// 3. Completion recency (more recent = more relevant)
  static List<Task> calculateHighlights(List<Task> completedTasks) {
    if (completedTasks.isEmpty) return [];
    
    // Filter only completed tasks and sort by highlight score
    final scoredTasks = completedTasks
        .where((task) => task.isCompleted && task.completionDate != null)
        .map((task) => _TaskWithScore(task, _calculateHighlightScore(task)))
        .toList();
    
    // Sort by score (descending) and take top highlights
    scoredTasks.sort((a, b) => b.score.compareTo(a.score));
    
    return scoredTasks
        .take(maxHighlights)
        .map((scored) => scored.task)
        .toList();
  }
  
  /// Calculates highlight score for a task
  static double _calculateHighlightScore(Task task) {
    if (!task.isCompleted || task.completionDate == null) return 0.0;
    
    double score = 0.0;
    
    // Priority weight (40% of score)
    score += _getPriorityScore(task.priority) * 0.4;
    
    // Complexity based on subtasks (30% of score)
    score += _getComplexityScore(task.subtasks) * 0.3;
    
    // Recency bonus (30% of score)
    score += _getRecencyScore(task.completionDate!) * 0.3;
    
    return score;
  }
  
  /// Gets priority score (0.0 to 1.0)
  static double _getPriorityScore(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return 1.0;
      case Priority.importantNotUrgent:
        return 0.8;
      case Priority.urgentNotImportant:
        return 0.6;
      case Priority.notUrgentNotImportant:
        return 0.4;
    }
  }
  
  /// Gets complexity score based on subtasks (0.0 to 1.0)
  static double _getComplexityScore(List<SubTask> subtasks) {
    if (subtasks.isEmpty) return 0.2; // Base score for simple tasks
    
    // Score increases with number of subtasks, capped at 1.0
    final subtaskScore = (subtasks.length / 10.0).clamp(0.0, 0.8);
    
    // Bonus for completed subtasks
    final completedSubtasks = subtasks.where((st) => st.isCompleted).length;
    final completionBonus = subtasks.isNotEmpty 
        ? (completedSubtasks / subtasks.length) * 0.2 
        : 0.0;
    
    return (subtaskScore + completionBonus + 0.2).clamp(0.0, 1.0);
  }
  
  /// Gets recency score (0.0 to 1.0)
  static double _getRecencyScore(DateTime completionDate) {
    final now = DateTime.now();
    final daysSinceCompletion = now.difference(completionDate).inDays;
    
    // Recent completions get higher scores
    if (daysSinceCompletion <= 1) return 1.0;
    if (daysSinceCompletion <= 7) return 0.8;
    if (daysSinceCompletion <= 30) return 0.6;
    if (daysSinceCompletion <= 90) return 0.4;
    
    return 0.2; // Older completions get lower scores
  }
  
  /// Filters highlights by date range
  static List<Task> filterHighlightsByDateRange(
    List<Task> highlights,
    DateTime startDate,
    DateTime endDate,
  ) {
    return highlights
        .where((task) => 
            task.completionDate != null &&
            task.completionDate!.isAfter(startDate.subtract(const Duration(days: 1))) &&
            task.completionDate!.isBefore(endDate.add(const Duration(days: 1))))
        .toList();
  }
}

/// Helper class for pairing tasks with their highlight scores
class _TaskWithScore {
  final Task task;
  final double score;
  
  const _TaskWithScore(this.task, this.score);
}