import '../models/task_model.dart';

/// 高性能子任务管理器
/// 使用优化算法处理子任务的所有操作
class SubtaskManager {
  /// 私有构造函数，使用单例模式
  SubtaskManager._();
  static final SubtaskManager _instance = SubtaskManager._();
  static SubtaskManager get instance => _instance;

  /// 高性能子任务状态切换
  /// 使用O(1)时间复杂度的哈希查找
  Task toggleSubtaskCompletion({
    required Task task,
    required String subtaskId,
    required bool isCompleted,
  }) {
    // 使用Map进行O(1)查找，而不是O(n)遍历
    final subtaskMap = <String, SubTask>{};
    for (final subtask in task.subtasks) {
      subtaskMap[subtask.id] = subtask;
    }

    // 验证子任务存在
    if (!subtaskMap.containsKey(subtaskId)) {
      throw ArgumentError('Subtask with ID $subtaskId not found');
    }

    // 更新目标子任务
    final updatedSubtask = subtaskMap[subtaskId]!.copyWith(
      isCompleted: isCompleted,
    );
    subtaskMap[subtaskId] = updatedSubtask;

    // 重建子任务列表，保持原有顺序
    final updatedSubtasks = task.subtasks.map((original) {
      return subtaskMap[original.id]!;
    }).toList();

    // 检查主任务完成状态
    return _updateTaskCompletionStatus(task, updatedSubtasks);
  }

  /// 批量更新子任务状态
  /// 用于处理多个子任务同时更新的场景
  Task batchUpdateSubtasks({
    required Task task,
    required Map<String, bool> subtaskUpdates,
  }) {
    // 使用Map进行高效查找
    final subtaskMap = <String, SubTask>{};
    for (final subtask in task.subtasks) {
      subtaskMap[subtask.id] = subtask;
    }

    // 批量更新
    for (final entry in subtaskUpdates.entries) {
      final subtaskId = entry.key;
      final isCompleted = entry.value;

      if (subtaskMap.containsKey(subtaskId)) {
        subtaskMap[subtaskId] = subtaskMap[subtaskId]!.copyWith(
          isCompleted: isCompleted,
        );
      }
    }

    // 重建子任务列表
    final updatedSubtasks = task.subtasks.map((original) {
      return subtaskMap[original.id]!;
    }).toList();

    return _updateTaskCompletionStatus(task, updatedSubtasks);
  }

  /// 添加子任务
  Task addSubtask({
    required Task task,
    required String title,
  }) {
    if (title.trim().isEmpty) {
      throw ArgumentError('Subtask title cannot be empty');
    }

    if (task.subtasks.length >= 50) { // 限制子任务数量
      throw ArgumentError('Cannot add more than 50 subtasks');
    }

    final newSubtask = SubTask.create(
      parentTaskId: task.id,
      title: title.trim(),
    );

    final updatedSubtasks = [...task.subtasks, newSubtask];
    return task.copyWith(subtasks: updatedSubtasks);
  }

  /// 删除子任务
  Task removeSubtask({
    required Task task,
    required String subtaskId,
  }) {
    final updatedSubtasks = task.subtasks
        .where((subtask) => subtask.id != subtaskId)
        .toList();

    return _updateTaskCompletionStatus(task, updatedSubtasks);
  }

  /// 更新子任务标题
  Task updateSubtaskTitle({
    required Task task,
    required String subtaskId,
    required String newTitle,
  }) {
    if (newTitle.trim().isEmpty) {
      throw ArgumentError('Subtask title cannot be empty');
    }

    final updatedSubtasks = task.subtasks.map((subtask) {
      if (subtask.id == subtaskId) {
        return subtask.copyWith(title: newTitle.trim());
      }
      return subtask;
    }).toList();

    return task.copyWith(subtasks: updatedSubtasks);
  }

  /// 获取子任务完成统计
  SubtaskStats getSubtaskStats(Task task) {
    final total = task.subtasks.length;
    final completed = task.subtasks.where((s) => s.isCompleted).length;
    final pending = total - completed;
    final completionRate = total > 0 ? (completed / total) * 100 : 0.0;

    return SubtaskStats(
      total: total,
      completed: completed,
      pending: pending,
      completionRate: completionRate,
    );
  }

  /// 私有方法：更新主任务完成状态
  Task _updateTaskCompletionStatus(Task task, List<SubTask> updatedSubtasks) {
    // 如果没有子任务，保持原状态
    if (updatedSubtasks.isEmpty) {
      return task.copyWith(subtasks: updatedSubtasks);
    }

    // 检查是否所有子任务都已完成
    final allCompleted = updatedSubtasks.every((s) => s.isCompleted);
    final anyCompleted = updatedSubtasks.any((s) => s.isCompleted);

    // 智能主任务状态管理
    if (allCompleted && !task.isCompleted) {
      // 所有子任务完成，自动完成主任务
      return task.copyWith(
        subtasks: updatedSubtasks,
        isCompleted: true,
        completionDate: DateTime.now(),
      );
    } else if (!anyCompleted && task.isCompleted) {
      // 所有子任务都未完成，且主任务已完成，取消主任务完成状态
      return task.copyWith(
        subtasks: updatedSubtasks,
        isCompleted: false,
        completionDate: null,
      );
    } else {
      // 其他情况，只更新子任务
      return task.copyWith(subtasks: updatedSubtasks);
    }
  }
}

/// 子任务统计信息
class SubtaskStats {
  final int total;
  final int completed;
  final int pending;
  final double completionRate;

  const SubtaskStats({
    required this.total,
    required this.completed,
    required this.pending,
    required this.completionRate,
  });

  @override
  String toString() {
    return 'SubtaskStats(total: $total, completed: $completed, pending: $pending, rate: ${completionRate.toStringAsFixed(1)}%)';
  }
}
