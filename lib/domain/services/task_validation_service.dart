import '../exceptions/domain_exceptions.dart';
import '../models/task_model.dart';

/// Service for validating task business rules
class TaskValidationService {
  static const int maxSubtasks = 50;
  static const int maxFutureDays = 3650; // ~10 years

  /// Validates a task according to business rules
  static void validateTask(Task task) {
    _validateTitle(task.title);
    _validateDates(task.creationDate, task.dueDate, task.completionDate);
    _validateSubtasks(task.subtasks);
    _validateCompletion(task.isCompleted, task.completionDate);
  }

  /// Validates task title
  static void _validateTitle(String title) {
    final trimmed = title.trim();

    if (trimmed.isEmpty) {
      throw const TaskValidationException('Task title cannot be empty');
    }

    if (trimmed.length > 200) {
      throw const TaskValidationException(
          'Task title cannot exceed 200 characters');
    }
  }

  /// Validates task dates
  static void _validateDates(
      DateTime creationDate, DateTime dueDate, DateTime? completionDate) {
    final now = DateTime.now();

    // Creation date cannot be in the future
    if (creationDate.isAfter(now)) {
      throw const DateValidationException(
          'Task creation date cannot be in the future');
    }

    // Due date cannot be more than 10 years in the future
    final maxFutureDate = now.add(const Duration(days: maxFutureDays));
    if (dueDate.isAfter(maxFutureDate)) {
      throw const DateValidationException(
          'Task due date cannot be more than ${maxFutureDays ~/ 365} years in the future');
    }

    // Due date should be after creation date, unless it's a past due task
    // For past due tasks, we allow due date to be before creation date
    if (dueDate.isBefore(creationDate) && dueDate.isAfter(now)) {
      throw const DateValidationException(
          'Task due date cannot be before creation date');
    }

    // If completed, completion date should be valid
    if (completionDate != null) {
      if (completionDate.isBefore(creationDate)) {
        throw const DateValidationException(
            'Task completion date cannot be before creation date');
      }

      if (completionDate.isAfter(now)) {
        throw const DateValidationException(
            'Task completion date cannot be in the future');
      }
    }
  }

  /// Validates subtasks
  static void _validateSubtasks(List<SubTask> subtasks) {
    if (subtasks.length > maxSubtasks) {
      throw const SubTaskException(
          'Task cannot have more than $maxSubtasks subtasks');
    }

    // Check for duplicate subtask IDs
    final ids = subtasks.map((st) => st.id).toSet();
    if (ids.length != subtasks.length) {
      throw const SubTaskException('Subtasks cannot have duplicate IDs');
    }

    // Validate each subtask
    for (final subtask in subtasks) {
      _validateSubtask(subtask);
    }
  }

  /// Validates a single subtask
  static void _validateSubtask(SubTask subtask) {
    if (subtask.title.trim().isEmpty) {
      throw const SubTaskException('Subtask title cannot be empty');
    }

    if (subtask.title.length > 200) {
      throw const SubTaskException(
          'Subtask title cannot exceed 200 characters');
    }

    if (subtask.id.trim().isEmpty) {
      throw const SubTaskException('Subtask ID cannot be empty');
    }

    if (subtask.parentTaskId.trim().isEmpty) {
      throw const SubTaskException('Subtask must have a valid parent task ID');
    }
  }

  /// Validates task completion state
  static void _validateCompletion(bool isCompleted, DateTime? completionDate) {
    if (isCompleted && completionDate == null) {
      throw const TaskValidationException(
          'Completed task must have a completion date');
    }

    if (!isCompleted && completionDate != null) {
      throw const TaskValidationException(
          'Incomplete task cannot have a completion date');
    }
  }
}
