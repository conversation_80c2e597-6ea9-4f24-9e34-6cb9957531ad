import '../models/task_model.dart';

/// Service for calculating task load for calendar heatmap
class TaskLoadCalculationService {
  /// Weight values for each priority level
  static const Map<Priority, int> _priorityWeights = {
    Priority.urgentImportant: 4,      // Quadrant 1
    Priority.importantNotUrgent: 3,   // Quadrant 2
    Priority.urgentNotImportant: 2,   // Quadrant 3
    Priority.notUrgentNotImportant: 1, // Quadrant 4
  };
  
  /// Calculates the task load for a specific date
  /// 
  /// Task load is calculated by summing the weights of all incomplete tasks
  /// due on the given date. Completed tasks don't contribute to the load.
  static int calculateDailyTaskLoad(List<Task> tasks) {
    return tasks
        .where((task) => !task.isCompleted)
        .map((task) => _priorityWeights[task.priority] ?? 1)
        .fold(0, (sum, weight) => sum + weight);
  }
  
  /// Calculates task load for multiple dates
  /// 
  /// Returns a map where keys are dates and values are the calculated loads
  static Map<DateTime, int> calculateTaskLoadForDates(Map<DateTime, List<Task>> tasksByDate) {
    final result = <DateTime, int>{};
    
    for (final entry in tasksByDate.entries) {
      result[entry.key] = calculateDailyTaskLoad(entry.value);
    }
    
    return result;
  }
  
  /// Calculates the maximum task load from a collection of daily loads
  /// 
  /// Used for normalizing heatmap intensity
  static int calculateMaxLoad(Map<DateTime, int> dailyLoads) {
    if (dailyLoads.isEmpty) return 0;
    return dailyLoads.values.reduce((max, load) => load > max ? load : max);
  }
  
  /// Calculates normalized opacity for heatmap visualization
  /// 
  /// Returns a value between 0.0 and 1.0 based on the load relative to maxLoad
  static double calculateHeatmapOpacity(int load, int maxLoad) {
    if (maxLoad == 0) return 0.0;
    return (load / maxLoad).clamp(0.0, 1.0);
  }
  
  /// Gets the weight for a specific priority
  static int getPriorityWeight(Priority priority) {
    return _priorityWeights[priority] ?? 1;
  }
  
  /// Calculates completion rate for a list of tasks
  static double calculateCompletionRate(List<Task> tasks) {
    if (tasks.isEmpty) return 0.0;
    
    final completedCount = tasks.where((task) => task.isCompleted).length;
    return completedCount / tasks.length;
  }
  
  /// Calculates quadrant distribution for analytics
  static Map<Priority, int> calculateQuadrantDistribution(List<Task> tasks) {
    final distribution = <Priority, int>{
      Priority.urgentImportant: 0,
      Priority.importantNotUrgent: 0,
      Priority.urgentNotImportant: 0,
      Priority.notUrgentNotImportant: 0,
    };
    
    for (final task in tasks) {
      distribution[task.priority] = (distribution[task.priority] ?? 0) + 1;
    }
    
    return distribution;
  }
}