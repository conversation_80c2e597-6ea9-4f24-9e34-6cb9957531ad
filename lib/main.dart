import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'app/router.dart';
import 'app/theme.dart';
import 'core/di/injection.dart';
import 'features/tasks/bloc/task_list_bloc.dart';
import 'features/tasks/bloc/task_list_event.dart';
import 'features/calendar/bloc/calendar_bloc.dart';
import 'features/calendar/bloc/calendar_event.dart';

// Import all implementations to ensure they are registered

void main() async {
  // 确保Flutter绑定完全初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 等待一个微任务，确保绑定完全准备就绪
  await Future.delayed(Duration.zero);

  // 配置依赖注入
  configureDependencies();

  runApp(const MyToDoApp());
}

class MyToDoApp extends StatelessWidget {
  const MyToDoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        // 全局TaskListBloc - 保持状态跨页面导航
        BlocProvider<TaskListBloc>(
          create: (context) {
            final now = DateTime.now();
            return getIt<TaskListBloc>()
              ..add(TaskListEvent.monthSubscriptionRequested(
                year: now.year,
                month: now.month,
              ));
          },
        ),
        // 全局CalendarBloc - 保持日历状态
        BlocProvider<CalendarBloc>(
          create: (context) =>
              getIt<CalendarBloc>()..add(const CalendarEvent.started()),
        ),
      ],
      child: MaterialApp.router(
        title: 'My ToDo Space',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        routerConfig: appRouter,
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('zh', 'CN'),
          Locale('en', 'US'),
        ],
        locale: const Locale('zh', 'CN'),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}
