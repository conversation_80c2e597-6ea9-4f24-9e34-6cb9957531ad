import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/di/injection.dart';
import '../../../core/utils/keyboard_shortcuts.dart';
import '../../tasks/bloc/task_list_bloc.dart';
import '../../tasks/bloc/task_list_event.dart';
import '../../tasks/bloc/task_list_state.dart';
import '../../tasks/presentation/task_list_panel.dart';
import '../bloc/calendar_bloc.dart';
import '../bloc/calendar_event.dart';
import '../bloc/calendar_state.dart';
import 'widgets/calendar_month_view.dart';
import 'widgets/calendar_year_view.dart';
import 'widgets/calendar_quadrant_view.dart';
import 'widgets/calendar_toolbar.dart';
import 'widgets/sidebar.dart';
import '../../../app/theme.dart';
import '../../summary/presentation/widgets/summary_split_view.dart';

class CalendarPage extends StatelessWidget {
  const CalendarPage({super.key});

  @override
  Widget build(BuildContext context) {
    // 使用全局BLoC提供者，避免重新创建状态
    // 这样从summary页面返回时不会丢失状态
    return const CalendarView();
  }
}

class CalendarView extends StatefulWidget {
  const CalendarView({super.key});

  @override
  State<CalendarView> createState() => _CalendarViewState();
}

class _CalendarViewState extends State<CalendarView> with RouteAware {
  // 年视图下的侧边栏折叠状态（默认隐藏）
  bool _yearSidebarExpanded = false;

  void _toggleYearSidebar() {
    setState(() {
      _yearSidebarExpanded = !_yearSidebarExpanded;
    });
  }
  @override
  void initState() {
    super.initState();
    // 当页面初始化时，确保任务订阅是活跃的
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _ensureTaskSubscription();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 监听路由变化，当返回到此页面时恢复状态
    final route = ModalRoute.of(context);
    if (route is PageRoute) {
      // 当从其他页面返回时，确保任务订阅正常
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _ensureTaskSubscription();
      });
    }
  }

  void _ensureTaskSubscription() {
    final calendarBloc = context.read<CalendarBloc>();
    final taskListBloc = context.read<TaskListBloc>();
    final calendarState = calendarBloc.state;
    // final taskListState = taskListBloc.state; // not used here

    // 确保根据当前视图类型始终订阅到正确的数据源（返回本页时防止任务数据陈旧）
    if (calendarState.viewType == CalendarViewType.quadrant) {
      taskListBloc.add(
        TaskListEvent.subscriptionRequested(calendarState.selectedDate),
      );
    } else {
      taskListBloc.add(
        TaskListEvent.monthSubscriptionRequested(
          year: calendarState.selectedDate.year,
          month: calendarState.selectedDate.month,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardShortcuts(
      child: Scaffold(
        body: Row(
          children: [
            // 侧边栏（年视图：可折叠；其它视图：固定显示）
            BlocBuilder<CalendarBloc, CalendarState>(
              builder: (context, state) {
                if (state.viewType == CalendarViewType.year) {
                  return AnimatedSwitcher(
                    duration: const Duration(milliseconds: 180),
                    switchInCurve: Curves.easeOut,
                    switchOutCurve: Curves.easeIn,
                    child: _yearSidebarExpanded
                        ? const Sidebar()
                        : const SizedBox.shrink(),
                  );
                }
                return const Sidebar();
              },
            ),

            // 主内容区域
            Expanded(
              child: Column(
                children: [
                  // 顶部工具栏（年视图/四象限/总结 视图隐藏，释放内容空间）
                  BlocBuilder<CalendarBloc, CalendarState>(
                    builder: (context, state) {
                      final hideToolbar = state.viewType == CalendarViewType.year ||
                          state.viewType == CalendarViewType.quadrant ||
                          state.viewType == CalendarViewType.summary;
                      return hideToolbar ? const SizedBox.shrink() : const CalendarToolbar();
                    },
                  ),
                  Expanded(
                    child: BlocBuilder<CalendarBloc, CalendarState>(
                      builder: (context, state) {
                        // 四象限视图：内容 + 底部状态栏（统计）
                        if (state.viewType == CalendarViewType.quadrant) {
                          return BlocListener<CalendarBloc, CalendarState>(
                            listener: (context, calendarState) {
                              // 当选中日期变化时，更新任务列表
                              context.read<TaskListBloc>().add(
                                    TaskListEvent.subscriptionRequested(
                                        calendarState.selectedDate),
                                  );
                            },
                            child: Column(
                              children: const [
                                Expanded(child: CalendarQuadrantView()),
                                // 将统计移至底部状态栏
                                QuadrantStatusBar(),
                              ],
                            ),
                          );
                        }

                        // 合并总结视图：右侧内容区内左右并排显示月/年总结
                        if (state.viewType == CalendarViewType.summary) {
                          return const SummarySplitView();
                        }

                        // 年视图占满整个区域（无右侧面板）
                        if (state.viewType == CalendarViewType.year) {
                          return BlocListener<CalendarBloc, CalendarState>(
                            listener: (context, calendarState) {
                              // 年视图订阅整个月的任务（基于选中日期所在月）
                              context.read<TaskListBloc>().add(
                                    TaskListEvent.monthSubscriptionRequested(
                                      year: calendarState.selectedDate.year,
                                      month: calendarState.selectedDate.month,
                                    ),
                                  );
                            },
                            child: Stack(
                              children: [
                                // 年视图主体
                                const CalendarYearView(),
                                // 年视图左侧折叠按钮（始终显示）
                                Positioned(
                                  left: AppTheme.spacing3,
                                  top: 0,
                                  bottom: 0,
                                  child: Align(
                                    alignment: Alignment.centerLeft,
                                    child: _SidebarToggleButton(
                                      expanded: _yearSidebarExpanded,
                                      onToggle: _toggleYearSidebar,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }

                        // 月视图使用分栏布局（包含任务面板）
                        return Row(
                          children: [
                            Expanded(
                              flex: 2,
                              child: BlocListener<CalendarBloc, CalendarState>(
                                listener: (context, calendarState) {
                                  // 月视图订阅整个月的任务
                                  context.read<TaskListBloc>().add(
                                        TaskListEvent.monthSubscriptionRequested(
                                          year: calendarState.selectedDate.year,
                                          month: calendarState.selectedDate.month,
                                        ),
                                      );
                                },
                                child: const CalendarMonthView(),
                              ),
                            ),
                            const Expanded(
                              flex: 1,
                              child: TaskListPanel(),
                            ),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}


class _SidebarToggleButton extends StatelessWidget {
  final bool expanded;
  final VoidCallback onToggle;
  const _SidebarToggleButton({required this.expanded, required this.onToggle});

  @override
  Widget build(BuildContext context) {
    final icon = expanded ? Icons.chevron_left : Icons.chevron_right;
    return Tooltip(
      message: expanded ? '收起侧边栏' : '展开侧边栏',
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(AppTheme.radiusXl),
          onTap: onToggle,
          child: Container(
            width: AppTheme.spacing8, // 32
            height: AppTheme.spacing14, // 56
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppTheme.radiusXl),
              border: Border.all(color: AppTheme.borderLight, width: 1),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.slate600.withOpacity(0.12),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            alignment: Alignment.center,
            child: Icon(
              icon,
              size: 20,
              color: AppTheme.primaryBlue,
            ),
          ),
        ),
      ),
    );
  }
}
