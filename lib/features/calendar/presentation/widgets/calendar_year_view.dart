import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../bloc/calendar_bloc.dart';
import '../../bloc/calendar_event.dart';
import '../../bloc/calendar_state.dart';
import '../../../tasks/bloc/task_list_bloc.dart';
import '../../../tasks/bloc/task_list_state.dart';
import '../../../../domain/models/task_model.dart';
import '../../../../app/theme.dart';
import 'calendar_quadrant_view.dart';

class CalendarYearView extends StatefulWidget {
  const CalendarYearView({super.key});

  @override
  State<CalendarYearView> createState() => _CalendarYearViewState();
}

class _CalendarYearViewState extends State<CalendarYearView> {
  int? _expandedMonth; // 1-12; null means full year grid

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarBloc, CalendarState>(
      builder: (context, calendarState) {
        return BlocBuilder<TaskListBloc, TaskListState>(
          builder: (context, taskState) {
            if (_expandedMonth != null) {
              final monthDate = DateTime(
                calendarState.displayMonthDate.year,
                _expandedMonth!,
                1,
              );
              return _buildExpandedMonth(context, monthDate, taskState);
            }
            return _buildYearGrid(context, calendarState, taskState);
          },
        );
      },
    );
  }

  Widget _buildYearGrid(BuildContext context, CalendarState calendarState,
      TaskListState taskState) {
    final currentYear = calendarState.displayMonthDate.year;

    // 使用LayoutBuilder根据可用空间自适应为3x4或4x3，并计算精确的childAspectRatio，
    // 确保12个月完整显示且无截断（禁止滚动）。
    return LayoutBuilder(
      builder: (context, constraints) {
        const padding = AppTheme.spacing4;
        const spacing = AppTheme.spacing4;
        final maxW = constraints.maxWidth;
        final maxH = constraints.maxHeight;

        // 宽屏优先4列，否则3列
        final crossAxisCount = (maxW > maxH) ? 4 : 3;
        final rows = (12 / crossAxisCount).ceil();

        // 计算每个单元格的理想宽高
        final totalHorizontalGaps = spacing * (crossAxisCount - 1);
        final totalVerticalGaps = spacing * (rows - 1);
        final gridWidth = maxW - padding * 2;
        final gridHeight = maxH - padding * 2;
        final itemWidth = (gridWidth - totalHorizontalGaps) / crossAxisCount;
        final itemHeight = (gridHeight - totalVerticalGaps) / rows;
        final aspectRatio = (itemWidth > 0 && itemHeight > 0)
            ? itemWidth / itemHeight
            : 1.2;

        return Padding(
          padding: const EdgeInsets.all(padding),
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            // 让GridView占据可用空间，避免shrinkWrap导致高度按内容计算而被裁剪
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: crossAxisCount,
              childAspectRatio: aspectRatio,
              crossAxisSpacing: spacing,
              mainAxisSpacing: spacing,
            ),
            itemCount: 12,
            itemBuilder: (context, index) {
              final month = index + 1;
              final monthDate = DateTime(currentYear, month, 1);
              final isCurrentMonth = month == DateTime.now().month &&
                  currentYear == DateTime.now().year;

              return _buildMonthCell(
                context,
                monthDate,
                month,
                isCurrentMonth,
                taskState.tasks,
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildYearHeader(BuildContext context, int year) {
    return Container(
      padding: const EdgeInsets.all(20.0),
      child: Row(
        children: [
          // 上一年按钮
          IconButton(
            onPressed: () {
              final calendarBloc = context.read<CalendarBloc>();
              final newYear = year - 1;
              final newDate = DateTime(newYear, 1, 1);
              calendarBloc.add(CalendarEvent.monthChanged(newDate));
            },
            icon: const Icon(Icons.chevron_left, size: 28),
            tooltip: '上一年',
          ),

          const SizedBox(width: 20),

          // 年份标题
          Expanded(
            child: Text(
              '$year年',
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    fontSize: 32,
                  ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(width: 20),

          // 下一年按钮
          IconButton(
            onPressed: () {
              final calendarBloc = context.read<CalendarBloc>();
              final newYear = year + 1;
              final newDate = DateTime(newYear, 1, 1);
              calendarBloc.add(CalendarEvent.monthChanged(newDate));
            },
            icon: const Icon(Icons.chevron_right, size: 28),
            tooltip: '下一年',
          ),
        ],
      ),
    );
  }

  Widget _buildMonthCell(
    BuildContext context,
    DateTime monthDate,
    int month,
    bool isCurrentMonth,
    List<Task> allTasks,
  ) {
    final monthTasks = _getTasksForMonth(allTasks, monthDate.year, month);

    return GestureDetector(
      onTap: () => setState(() => _expandedMonth = month),
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.cardLight,
          borderRadius: BorderRadius.circular(AppTheme.radiusXl),
          border: Border.all(
            color: isCurrentMonth ? AppTheme.primaryBlue : AppTheme.borderLight,
            width: isCurrentMonth ? 2.0 : 1.0,
          ),
          boxShadow: [
            BoxShadow(
              color: AppTheme.slate600.withAlpha(26),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // 月份标题
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing3),
              decoration: BoxDecoration(
                color: isCurrentMonth ? AppTheme.primaryBlue : AppTheme.slate50,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppTheme.radiusXl),
                  topRight: Radius.circular(AppTheme.radiusXl),
                ),
              ),
              child: Text(
                '$month月',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isCurrentMonth ? Colors.white : AppTheme.textSecondary,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // 月份内容
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppTheme.spacing3),
                child: Column(
                  children: [
                    // 任务统计
                    _buildMonthStats(monthTasks),

                    const SizedBox(height: AppTheme.spacing3),

                    // 热力图
                    Expanded(
                      child: _buildMonthHeatmap(monthDate, monthTasks),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }


  Widget _buildExpandedMonth(
    BuildContext context,
    DateTime monthDate,
    TaskListState taskState,
  ) {
    final month = monthDate.month;
    final isCurrentMonth = month == DateTime.now().month &&
        monthDate.year == DateTime.now().year;
    final monthTasks = _getTasksForMonth(taskState.tasks, monthDate.year, month);

    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.all(AppTheme.spacing5),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 简洁的月份标题
              Container(
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing3),
                decoration: BoxDecoration(
                  color: isCurrentMonth ? AppTheme.primaryBlue : AppTheme.slate100,
                  borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                ),
                child: Text(
                  '$month月',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isCurrentMonth ? Colors.white : AppTheme.textPrimary,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: AppTheme.spacing4),

              // 任务统计
              _buildMonthStats(monthTasks),

              const SizedBox(height: AppTheme.spacing3),

              // 热力图（可点选日期）
              Expanded(
                child: _buildMonthHeatmap(monthDate, monthTasks),
              ),
            ],
          ),
        ),

        // 返回年视图按钮
        Positioned(
          top: AppTheme.spacing4,
          left: AppTheme.spacing4,
          child: OutlinedButton.icon(
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.primaryBlue,
              side: const BorderSide(color: AppTheme.primaryBlue, width: 1),
              backgroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing3,
                vertical: AppTheme.spacing2,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              ),
            ),
            onPressed: () => setState(() => _expandedMonth = null),
            icon: const Icon(Icons.arrow_back, size: 16, color: AppTheme.primaryBlue),
            label: const Text('返回年视图'),
          ),
        ),
      ],
    );
  }


  Widget _buildMonthStats(List<Task> monthTasks) {
    final totalTasks = monthTasks.length;
    final completedTasks = monthTasks.where((task) => task.isCompleted).length;
    final completionRate =
        totalTasks > 0 ? (completedTasks / totalTasks * 100).round() : 0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildStatItem('总任务', '$totalTasks', AppTheme.infoColor),
        _buildStatItem('已完成', '$completedTasks', AppTheme.successColor),
        _buildStatItem('完成率', '$completionRate%', AppTheme.warningColor),
      ],
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: AppTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildMonthHeatmap(DateTime monthDate, List<Task> monthTasks) {
    final daysInMonth = DateTime(monthDate.year, monthDate.month + 1, 0).day;
    final firstDayOfMonth = DateTime(monthDate.year, monthDate.month, 1);
    final firstWeekday = firstDayOfMonth.weekday % 7; // 0 = 周日

    return Column(
      children: [
        // 星期标题
        Row(
          children: ['日', '一', '二', '三', '四', '五', '六'].map((day) {
            return Expanded(
              child: Text(
                day,
                style: TextStyle(
                  fontSize: 12,
                  color: AppTheme.textSecondary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            );
          }).toList(),
        ),

        const SizedBox(height: AppTheme.spacing1),

        // 日期网格
        Expanded(
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: 1.0,
              crossAxisSpacing: AppTheme.spacing1,
              mainAxisSpacing: AppTheme.spacing1,
            ),
            itemCount: 42, // 6周 x 7天
            itemBuilder: (context, index) {
              final week = index ~/ 7;
              final weekday = index % 7;

              // 计算实际日期
              final dayOffset = week * 7 + weekday - firstWeekday;
              final day = dayOffset + 1;

              if (day < 1 || day > daysInMonth) {
                return const SizedBox.shrink(); // 空单元格
              }

              final date = DateTime(monthDate.year, monthDate.month, day);
              final tasksForDate = _getTasksForDate(monthTasks, date);
              final taskLoad = _calculateDailyTaskLoad(tasksForDate);
              final isToday = _isSameDate(date, DateTime.now());

              return _buildDateCell(context, date, day, tasksForDate, taskLoad, isToday);
            },
          ),
        ),
      ],
    );
  }

  // 构建年视图中的日期格子
  Widget _buildDateCell(BuildContext context, DateTime date, int day, List<Task> tasks, int taskLoad, bool isToday) {
    final hasHighPriority = tasks.any((task) => task.priority == Priority.urgentImportant);
    final hasMediumPriority = tasks.any((task) => task.priority == Priority.importantNotUrgent);
    final hasLowPriority = tasks.any((task) => task.priority == Priority.urgentNotImportant);

    // 根据任务负荷和优先级计算背景颜色
    Color backgroundColor;
    if (taskLoad == 0) {
      backgroundColor = AppTheme.slate50;
    } else if (hasHighPriority) {
      backgroundColor = AppTheme.urgentImportantColor.withOpacity(0.15);
    } else if (hasMediumPriority) {
      backgroundColor = AppTheme.importantNotUrgentColor.withOpacity(0.15);
    } else if (hasLowPriority) {
      backgroundColor = AppTheme.successColor.withOpacity(0.15);
    } else {
      backgroundColor = AppTheme.primaryBlue.withOpacity(0.15);
    }

    return GestureDetector(
      onTap: () {
        //
        if (_expandedMonth != null) {
          _showDateQuadrantPopup(context, date);
        } else {
          context.read<CalendarBloc>().add(CalendarEvent.dateSelected(date));
        }
      },
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(AppTheme.radiusSm),
          border: isToday ? Border.all(color: AppTheme.primaryBlue, width: 1.5) : null,
        ),
        child: Stack(
          children: [
            // 日期数字
            Positioned(
              top: 1,
              left: 2,
              child: Text(
                '$day',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: isToday ? FontWeight.w700 : FontWeight.w500,
                  color: isToday ? AppTheme.primaryBlue : AppTheme.textSecondary,
                ),
              ),
            ),

            // 任务数量指示器
            if (tasks.isNotEmpty)
              Positioned(
                bottom: 1,
                right: 1,
                child: Container(
                  constraints: const BoxConstraints(minWidth: 10),
                  padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 0.5),
                  decoration: BoxDecoration(
                    color: _getTaskCountColorForYear(tasks),
                    borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                  ),
                  child: Text(
                    '${tasks.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w700,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 获取年视图任务数量指示器颜色
  Color _getTaskCountColorForYear(List<Task> tasks) {
    final urgentImportantCount = tasks.where((task) => task.priority == Priority.urgentImportant).length;
    final importantNotUrgentCount = tasks.where((task) => task.priority == Priority.importantNotUrgent).length;
    final urgentNotImportantCount = tasks.where((task) => task.priority == Priority.urgentNotImportant).length;

    if (urgentImportantCount > 0) return AppTheme.urgentImportantColor;
    if (importantNotUrgentCount > 0) return AppTheme.importantNotUrgentColor;
    if (urgentNotImportantCount > 0) return AppTheme.urgentNotImportantColor;
    return AppTheme.notUrgentNotImportantColor;
  }

  List<Task> _getTasksForMonth(List<Task> allTasks, int year, int month) {
    return allTasks.where((task) {
      return task.dueDate.year == year && task.dueDate.month == month;
    }).toList();
  }

  List<Task> _getTasksForDate(List<Task> monthTasks, DateTime date) {
    return monthTasks.where((task) {
      final taskDate =
          DateTime(task.dueDate.year, task.dueDate.month, task.dueDate.day);
      final targetDate = DateTime(date.year, date.month, date.day);
      return taskDate.isAtSameMomentAs(targetDate);
    }).toList();
  }

  int _calculateDailyTaskLoad(List<Task> tasks) {
    int load = 0;
    for (final task in tasks) {
      switch (task.priority) {
        case Priority.urgentImportant:
          load += 3;
          break;
        case Priority.importantNotUrgent:
          load += 2;
          break;
        case Priority.urgentNotImportant:
          load += 1;
          break;
        case Priority.notUrgentNotImportant:
          load += 1;
          break;
      }
    }
    return load;
  }

  bool _isSameDate(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  //
  void _showDateQuadrantPopup(BuildContext context, DateTime date) {
    final calendarBloc = context.read<CalendarBloc>();
    final previous = calendarBloc.state.selectedDate;
    //
    calendarBloc.add(CalendarEvent.dateSelected(date));

    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: AppTheme.slate600.withOpacity(0.15),
      builder: (dialogContext) {
        return Dialog(
          insetPadding: const EdgeInsets.all(AppTheme.spacing6),
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.radiusXl),
            side: const BorderSide(color: AppTheme.borderLight, width: 1),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppTheme.radiusXl),
            child: Stack(
              children: [
                // 动态计算可用尺寸，使用尽可能大的对话框空间，避免因固定高度导致的溢出
                LayoutBuilder(
                  builder: (innerContext, boxConstraints) {
                    final mediaSize = MediaQuery.of(dialogContext).size;
                    // 与 Dialog 的 insetPadding 保持一致，预留安全边距
                    final inset = AppTheme.spacing6 * 2;
                    // 设定在当前窗口内的最大可用宽高，并给出合理的下限，防止过小
                    final maxW = (mediaSize.width - inset).clamp(600.0, mediaSize.width);
                    final maxH = (mediaSize.height - inset).clamp(520.0, mediaSize.height);
                    return SizedBox(
                      width: maxW,
                      height: maxH,
                      // 只读态：不改变原有交互语义
                      child: const CalendarQuadrantView(),
                    );
                  },
                ),
                // 
                const Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: QuadrantStatusBar(),
                ),
                Positioned(
                  right: AppTheme.spacing2.toDouble(),
                  top: AppTheme.spacing2.toDouble(),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => Navigator.of(dialogContext).pop(),
                      borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                      child: Container(
                        padding: const EdgeInsets.all(AppTheme.spacing1),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(color: AppTheme.borderLight, width: 1),
                          borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                          boxShadow: [
                            BoxShadow(
                              color: AppTheme.slate600.withOpacity(0.08),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(Icons.close, size: 16, color: AppTheme.textSecondary),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    ).then((_) {
      if (mounted) {
        //
        calendarBloc.add(CalendarEvent.dateSelected(previous));
      }
    });
  }

  void _onMonthSelected(BuildContext context, DateTime monthDate) {
    context.read<CalendarBloc>().add(CalendarEvent.monthChanged(monthDate));
    // 切换到月视图
    context
        .read<CalendarBloc>()
        .add(const CalendarEvent.viewTypeChanged(CalendarViewType.month));
  }
}
