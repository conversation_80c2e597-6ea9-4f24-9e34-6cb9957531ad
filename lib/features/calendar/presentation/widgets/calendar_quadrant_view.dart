import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../bloc/calendar_bloc.dart';

import '../../bloc/calendar_state.dart';
import '../../../tasks/bloc/task_list_event.dart';
import '../../../tasks/bloc/task_list_bloc.dart';
import '../../../tasks/bloc/task_list_state.dart';
import '../../../../app/theme.dart';
import '../../../../domain/models/task_model.dart';
import '../../../tasks/presentation/task_editor_dialog.dart';
import '../../../tasks/presentation/widgets/task_detail_dialog.dart';

/// 🎯 高性能缓存管理器
class _QuadrantViewCache {
  static final Map<String, Map<Priority, List<Task>>> _taskCache = {};
  static final Map<String, Map<Priority, int>> _statsCache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);
  static const int _maxCacheSize = 50;

  /// 生成缓存键
  static String generateCacheKey(DateTime date, List<Task> tasks) {
    final dateKey = '${date.year}-${date.month}-${date.day}';
    final tasksHash = tasks.map((t) => '${t.id}_${t.isCompleted}').join('|').hashCode;
    return '${dateKey}_$tasksHash';
  }

  /// 获取缓存的任务分组
  static Map<Priority, List<Task>>? getCachedTasks(String key) {
    if (!_isCacheValid(key)) return null;
    return _taskCache[key];
  }

  /// 获取缓存的统计数据
  static Map<Priority, int>? getCachedStats(String key) {
    if (!_isCacheValid(key)) return null;
    return _statsCache[key];
  }

  /// 设置缓存
  static void setCache(String key, Map<Priority, List<Task>> tasks, Map<Priority, int> stats) {
    _cleanupCache();
    _taskCache[key] = Map.from(tasks);
    _statsCache[key] = Map.from(stats);
    _cacheTimestamps[key] = DateTime.now();
  }

  /// 检查缓存是否有效
  static bool _isCacheValid(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return false;
    return DateTime.now().difference(timestamp) < _cacheExpiry;
  }

  /// 清理过期缓存
  static void _cleanupCache() {
    if (_taskCache.length <= _maxCacheSize) return;

    final now = DateTime.now();
    final expiredKeys = _cacheTimestamps.entries
        .where((entry) => now.difference(entry.value) > _cacheExpiry)
        .map((entry) => entry.key)
        .toList();

    for (final key in expiredKeys) {
      _taskCache.remove(key);
      _statsCache.remove(key);
      _cacheTimestamps.remove(key);
    }

    // 如果还是太多，移除最旧的
    if (_taskCache.length > _maxCacheSize) {
      final oldestKeys = _cacheTimestamps.entries
          .toList()
          ..sort((a, b) => a.value.compareTo(b.value));

      final keysToRemove = oldestKeys.take(_taskCache.length - _maxCacheSize);
      for (final entry in keysToRemove) {
        _taskCache.remove(entry.key);
        _statsCache.remove(entry.key);
        _cacheTimestamps.remove(entry.key);
      }
    }
  }

  /// 清除所有缓存
  static void clearAll() {
    _taskCache.clear();
    _statsCache.clear();
    _cacheTimestamps.clear();
  }
}

class CalendarQuadrantView extends StatelessWidget {
  const CalendarQuadrantView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarBloc, CalendarState>(
      builder: (context, calendarState) {
        return BlocBuilder<TaskListBloc, TaskListState>(
          builder: (context, taskState) {
            return _buildQuadrantGrid(context, taskState);
          },
        );
      },
    );
  }

  Widget _buildQuadrantGrid(BuildContext context, TaskListState taskState) {
    return BlocBuilder<CalendarBloc, CalendarState>(
      builder: (context, calendarState) {
        // 🎯 高性能日期和象限过滤算法 - 单次遍历实现
        final selectedDate = calendarState.selectedDate;
        final quadrantTasks = _filterAndGroupTasksByDateAndPriority(
          taskState.tasks,
          selectedDate,
        );

        // 从分组结果中提取各象限任务
        final urgentImportantTasks = quadrantTasks[Priority.urgentImportant] ?? [];
        final importantNotUrgentTasks = quadrantTasks[Priority.importantNotUrgent] ?? [];
        final urgentNotImportantTasks = quadrantTasks[Priority.urgentNotImportant] ?? [];
        final notUrgentNotImportantTasks = quadrantTasks[Priority.notUrgentNotImportant] ?? [];

        return Column(
      children: [
        // 标题
        _buildHeader(context),

        // 四象限网格
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(AppTheme.spacing4),
            child: Column(
              children: [
                // 第一行：重要且紧急 + 重要但不紧急
                Expanded(
                  child: Row(
                    children: [
                      // 第一象限：重要且紧急
                      Expanded(
                        child: _buildQuadrant(
                          context,
                          '重要且紧急',
                          'Do First',
                          AppTheme.urgentImportantColor,
                          urgentImportantTasks,
                          Priority.urgentImportant,
                        ),
                      ),

                      const SizedBox(width: AppTheme.spacing4),

                      // 第二象限：重要但不紧急
                      Expanded(
                        child: _buildQuadrant(
                          context,
                          '重要但不紧急',
                          'Schedule',
                          AppTheme.importantNotUrgentColor,
                          importantNotUrgentTasks,
                          Priority.importantNotUrgent,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: AppTheme.spacing4),

                // 第二行：紧急但不重要 + 不重要且不紧急
                Expanded(
                  child: Row(
                    children: [
                      // 第三象限：紧急但不重要
                      Expanded(
                        child: _buildQuadrant(
                          context,
                          '紧急但不重要',
                          'Delegate',
                          AppTheme.urgentNotImportantColor,
                          urgentNotImportantTasks,
                          Priority.urgentNotImportant,
                        ),
                      ),

                      const SizedBox(width: AppTheme.spacing4),

                      // 第四象限：不重要且不紧急
                      Expanded(
                        child: _buildQuadrant(
                          context,
                          '不重要且不紧急',
                          'Eliminate',
                          AppTheme.notUrgentNotImportantColor,
                          notUrgentNotImportantTasks,
                          Priority.notUrgentNotImportant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    // 应用户要求：移除标题与顶部统计，释放内容空间
    return const SizedBox.shrink();
  }

  Widget _buildStats(BuildContext context) {
    return BlocBuilder<CalendarBloc, CalendarState>(
      builder: (context, calendarState) {
        return BlocBuilder<TaskListBloc, TaskListState>(
          builder: (context, taskState) {
            // 🎯 高性能日期过滤和象限统计 - 复用过滤逻辑
            final selectedDate = calendarState.selectedDate;
            final quadrantTasks = _filterAndGroupTasksByDateAndPriority(
              taskState.tasks,
              selectedDate,
            );

            // 🎯 优先使用缓存的统计数据
            final cacheKey = _QuadrantViewCache.generateCacheKey(selectedDate, taskState.tasks);
            var quadrantStats = _QuadrantViewCache.getCachedStats(cacheKey);

            // 如果缓存未命中，从分组结果计算统计
            if (quadrantStats == null) {
              quadrantStats = <Priority, int>{
                Priority.urgentImportant: quadrantTasks[Priority.urgentImportant]!.length,
                Priority.importantNotUrgent: quadrantTasks[Priority.importantNotUrgent]!.length,
                Priority.urgentNotImportant: quadrantTasks[Priority.urgentNotImportant]!.length,
                Priority.notUrgentNotImportant: quadrantTasks[Priority.notUrgentNotImportant]!.length,
              };
            }

            return Column(
              children: [
                // 日期标题
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacing4,
                    vertical: AppTheme.spacing2,
                  ),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                  ),
                  child: Text(
                    _formatSelectedDate(selectedDate),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryBlue,
                    ),
                  ),
                ),
                const SizedBox(height: AppTheme.spacing4),

                // 象限统计卡片
                Row(
                  children: [
                    Expanded(
                      child: _buildQuadrantStatCard(
                        '重要紧急',
                        quadrantStats[Priority.urgentImportant] ?? 0,
                        AppTheme.urgentImportantColor,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacing3),
                    Expanded(
                      child: _buildQuadrantStatCard(
                        '重要不紧急',
                        quadrantStats[Priority.importantNotUrgent] ?? 0,
                        AppTheme.importantNotUrgentColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacing3),
                Row(
                  children: [
                    Expanded(
                      child: _buildQuadrantStatCard(
                        '紧急不重要',
                        quadrantStats[Priority.urgentNotImportant] ?? 0,
                        AppTheme.urgentNotImportantColor,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacing3),
                    Expanded(
                      child: _buildQuadrantStatCard(
                        '不重要不紧急',
                        quadrantStats[Priority.notUrgentNotImportant] ?? 0,
                        AppTheme.notUrgentNotImportantColor,
                      ),
                    ),
                  ],
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// 🎯 高性能日期和象限过滤算法 - 带缓存的单次遍历实现
  ///
  /// 时间复杂度: O(1) 缓存命中, O(n) 缓存未命中
  /// 空间复杂度: O(n) - 最坏情况下所有任务都在同一天
  Map<Priority, List<Task>> _filterAndGroupTasksByDateAndPriority(
    List<Task> allTasks,
    DateTime selectedDate,
  ) {
    // 🎯 检查缓存
    final cacheKey = _QuadrantViewCache.generateCacheKey(selectedDate, allTasks);
    final cachedTasks = _QuadrantViewCache.getCachedTasks(cacheKey);
    if (cachedTasks != null) {
      return cachedTasks;
    }

    // 🎯 缓存未命中，执行高性能计算
    final result = <Priority, List<Task>>{
      Priority.urgentImportant: [],
      Priority.importantNotUrgent: [],
      Priority.urgentNotImportant: [],
      Priority.notUrgentNotImportant: [],
    };

    // 预计算选中日期的年月日，避免重复计算
    final selectedYear = selectedDate.year;
    final selectedMonth = selectedDate.month;
    final selectedDay = selectedDate.day;

    // 单次遍历，同时进行日期过滤和象限分组
    for (final task in allTasks) {
      final taskDate = task.dueDate ?? task.creationDate;

      // 高效日期比较 - 避免创建新的DateTime对象
      if (taskDate.year == selectedYear &&
          taskDate.month == selectedMonth &&
          taskDate.day == selectedDay) {
        result[task.priority]!.add(task);
      }
    }

    // 🎯 计算统计数据并缓存
    final stats = <Priority, int>{
      Priority.urgentImportant: result[Priority.urgentImportant]!.length,
      Priority.importantNotUrgent: result[Priority.importantNotUrgent]!.length,
      Priority.urgentNotImportant: result[Priority.urgentNotImportant]!.length,
      Priority.notUrgentNotImportant: result[Priority.notUrgentNotImportant]!.length,
    };

    _QuadrantViewCache.setCache(cacheKey, result, stats);
    return result;
  }

  /// 🎯 高性能象限统计计算
  Map<Priority, int> _calculateQuadrantStats(List<Task> tasks) {
    final stats = <Priority, int>{
      Priority.urgentImportant: 0,
      Priority.importantNotUrgent: 0,
      Priority.urgentNotImportant: 0,
      Priority.notUrgentNotImportant: 0,
    };

    // 单次遍历计算所有象限统计
    for (final task in tasks) {
      stats[task.priority] = (stats[task.priority] ?? 0) + 1;
    }

    return stats;
  }

  /// 格式化选中日期显示
  String _formatSelectedDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(date.year, date.month, date.day);

    if (selectedDay == today) {
      return '今日任务';
    } else if (selectedDay == today.add(const Duration(days: 1))) {
      return '明日任务';
    } else if (selectedDay == today.subtract(const Duration(days: 1))) {
      return '昨日任务';
    } else {
      return '${date.month}月${date.day}日';
    }
  }

  /// 象限统计卡片
  Widget _buildQuadrantStatCard(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing3,
        vertical: AppTheme.spacing2_5,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Text(
            '$count',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          const SizedBox(height: AppTheme.spacing1),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: color.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildQuadrant(
    BuildContext context,
    String title,
    String subtitle,
    Color color,
    List<Task> tasks,
    Priority priority,
  ) {
    return Container(
      margin: const EdgeInsets.all(AppTheme.spacing2),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.radius2xl),
        border: Border.all(
          color: AppTheme.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 已按需移除：象限顶部彩色标题与计数头部，释放内容空间

          // 任务列表
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(AppTheme.spacing3),
              child: tasks.isEmpty
                  ? _buildEmptyState(color)
                  : _buildTaskList(context, tasks, priority),
            ),
          ),

          // 添加任务按钮
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(AppTheme.spacing3),
            child: ElevatedButton.icon(
              onPressed: () => _showCreateTaskDialog(context, priority),
              icon: Icon(Icons.add, color: color),
              label: Text(
                '添加任务',
                style: TextStyle(color: color),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: color.withAlpha(26),
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(Color color) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.task_alt,
            size: 48,
            color: color.withAlpha(128),
          ),
          const SizedBox(height: 8),
          Text(
            '暂无任务',
            style: TextStyle(
              color: color.withAlpha(179),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskList(
      BuildContext context, List<Task> tasks, Priority priority) {
    return ListView.builder(
      itemCount: tasks.length,
      itemBuilder: (context, index) {
        final task = tasks[index];
        return _buildTaskItem(context, task, priority);
      },
    );
  }

  Widget _buildTaskItem(BuildContext context, Task task, Priority priority) {
    final color = _getPriorityColor(priority);

    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacing2),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(AppTheme.radiusXl),
        border: Border.all(
          color: AppTheme.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        onTap: () => _showTaskDetailDialog(context, task),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacing3,
          vertical: AppTheme.spacing1,
        ),
        leading: Transform.scale(
          scale: 0.9,
          child: Checkbox(
            value: task.isCompleted,
            onChanged: (value) {
              context.read<TaskListBloc>().add(
                    TaskListEvent.completionToggled(
                      taskId: task.id,
                      isCompleted: value ?? false,
                    ),
                  );
            },
            activeColor: color,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(AppTheme.radiusSm),
            ),
          ),
        ),
        title: Tooltip(
          message: task.title,
          child: Text(
            task.title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              decoration: task.isCompleted ? TextDecoration.lineThrough : null,
              fontWeight: FontWeight.w500,
              color: task.isCompleted ? AppTheme.textMuted : AppTheme.textPrimary,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (task.notes?.isNotEmpty == true) ...[
              const SizedBox(height: AppTheme.spacing1),
              Text(
                task.notes!,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
            const SizedBox(height: AppTheme.spacing1),
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  size: 12,
                  color: AppTheme.textMuted,
                ),
                const SizedBox(width: AppTheme.spacing1),
                Text(
                  DateFormat('M月d日').format(task.dueDate),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.textMuted,
                    fontSize: 11,
                  ),
                ),
                if (task.subtasks.isNotEmpty) ...[
                  const SizedBox(width: AppTheme.spacing2),
                  Icon(
                    Icons.checklist,
                    size: 12,
                    color: AppTheme.textMuted,
                  ),
                  const SizedBox(width: AppTheme.spacing1),
                  Text(
                    '${task.subtasks.where((s) => s.isCompleted).length}/${task.subtasks.length}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textMuted,
                      fontSize: 11,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: Icon(Icons.more_vert, color: color),
          onSelected: (value) {
            switch (value) {
              case 'edit':
                _showEditTaskDialog(context, task);
                break;
              case 'delete':
                _showDeleteTaskDialog(context, task);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: AppTheme.errorColor),
                  SizedBox(width: 8),
                  Text('删除', style: TextStyle(color: AppTheme.errorColor)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return AppTheme.urgentImportantColor;
      case Priority.importantNotUrgent:
        return AppTheme.urgentNotImportantColor;
      case Priority.urgentNotImportant:
        return AppTheme.importantNotUrgentColor;
      case Priority.notUrgentNotImportant:
        return AppTheme.notUrgentNotImportantColor;
    }
  }

  void _showCreateTaskDialog(BuildContext context, Priority priority) {
    showDialog(
      context: context,
      builder: (dialogContext) => TaskEditorDialog(
        initialPriority: priority,
        onTaskSaved: (task) {
          context.read<TaskListBloc>().add(TaskListEvent.taskCreated(task));
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('任务 "${task.title}" 已创建'),
              backgroundColor: AppTheme.successColor,
            ),
          );
        },
      ),
    );
  }

  void _showEditTaskDialog(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (dialogContext) => TaskEditorDialog(
        existingTask: task,
        onTaskSaved: (updatedTask) {
          context
              .read<TaskListBloc>()
              .add(TaskListEvent.taskUpdated(updatedTask));
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('任务 "${updatedTask.title}" 已更新'),
              backgroundColor: AppTheme.primaryBlue,
            ),
          );
        },
      ),
    );
  }

  void _showDeleteTaskDialog(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除任务 "${task.title}" 吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              context
                  .read<TaskListBloc>()
                  .add(TaskListEvent.taskDeleted(taskId: task.id));
              Navigator.of(dialogContext).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('任务 "${task.title}" 已删除'),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            },
            style: TextButton.styleFrom(foregroundColor: AppTheme.errorColor),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showTaskDetailDialog(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<TaskListBloc>(),
        child: TaskDetailDialog(
          task: task,
          onTaskUpdated: () {
            // 不需要手动刷新，BLoC会自动处理
            // 避免使用context，因为Widget可能已经被卸载
          },
          onTaskDeleted: () {
            // 任务已删除，无需额外操作
          },
        ),
      ),
    );
  }

  IconData _getPriorityIcon(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return Icons.warning;
      case Priority.importantNotUrgent:
        return Icons.star;
      case Priority.urgentNotImportant:
        return Icons.flash_on;
      case Priority.notUrgentNotImportant:
        return Icons.low_priority;
    }
  }
}

/// 底部状态栏：显示所选日期的四象限统计（替代顶部大标题与统计）
class QuadrantStatusBar extends StatelessWidget {
  const QuadrantStatusBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing4,
        vertical: AppTheme.spacing2,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: const Border(
          top: BorderSide(color: AppTheme.borderLight, width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 6,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BlocBuilder<CalendarBloc, CalendarState>(
        builder: (context, calendarState) {
          return BlocBuilder<TaskListBloc, TaskListState>(
            builder: (context, taskState) {
              final selectedDate = calendarState.selectedDate;
              // 计算象限分布
              final grouped = _QuadrantStatusUtils.groupByPriorityForDate(
                taskState.tasks,
                selectedDate,
              );
              return Row(
                children: [
                  // 日期标题
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppTheme.spacing3,
                      vertical: AppTheme.spacing1,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryBlue.withOpacity(0.08),
                      borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                    ),
                    child: Text(
                      _QuadrantStatusUtils.formatSelectedDate(selectedDate),
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppTheme.primaryBlue,
                          ),
                    ),
                  ),
                  const Spacer(),
                  _StatPill(
                    label: '重要紧急',
                    count: grouped[Priority.urgentImportant] ?? 0,
                    color: AppTheme.urgentImportantColor,
                  ),
                  _StatPill(
                    label: '重要不紧急',
                    count: grouped[Priority.importantNotUrgent] ?? 0,
                    color: AppTheme.importantNotUrgentColor,
                  ),
                  _StatPill(
                    label: '紧急不重要',
                    count: grouped[Priority.urgentNotImportant] ?? 0,
                    color: AppTheme.urgentNotImportantColor,
                  ),
                  _StatPill(
                    label: '不重要不紧急',
                    count: grouped[Priority.notUrgentNotImportant] ?? 0,
                    color: AppTheme.notUrgentNotImportantColor,
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}

class _StatPill extends StatelessWidget {
  final String label;
  final int count;
  final Color color;
  const _StatPill({required this.label, required this.count, required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: AppTheme.spacing3),
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing2,
        vertical: AppTheme.spacing1,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.08),
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
        border: Border.all(color: color.withOpacity(0.25), width: 1),
      ),
      child: Row(
        children: [
          Text(
            '$count',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          const SizedBox(width: AppTheme.spacing1),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              color: color.withOpacity(0.9),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

/// 实用工具：计算一天内各优先级数量 & 日期标题格式化
class _QuadrantStatusUtils {
  static Map<Priority, int> groupByPriorityForDate(List<Task> all, DateTime date) {
    final result = <Priority, int>{
      Priority.urgentImportant: 0,
      Priority.importantNotUrgent: 0,
      Priority.urgentNotImportant: 0,
      Priority.notUrgentNotImportant: 0,
    };
    final y = date.year, m = date.month, d = date.day;
    for (final t in all) {
      final td = t.dueDate ?? t.creationDate;
      if (td.year == y && td.month == m && td.day == d) {
        result[t.priority] = (result[t.priority] ?? 0) + 1;
      }
    }
    return result;
  }

  static String formatSelectedDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dd = DateTime(date.year, date.month, date.day);
    if (dd == today) return '今日任务';
    if (dd == today.add(const Duration(days: 1))) return '明日任务';
    if (dd == today.subtract(const Duration(days: 1))) return '昨日任务';
    return '${date.month}月${date.day}日';
  }
}

