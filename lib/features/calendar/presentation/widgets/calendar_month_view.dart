import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../bloc/calendar_bloc.dart';
import '../../bloc/calendar_event.dart';
import '../../bloc/calendar_state.dart';
import '../../../tasks/bloc/task_list_bloc.dart';
import '../../../tasks/bloc/task_list_state.dart';
import '../../../../domain/models/task_model.dart';
import '../../../../app/theme.dart';

class CalendarMonthView extends StatefulWidget {
  const CalendarMonthView({super.key});

  @override
  State<CalendarMonthView> createState() => _CalendarMonthViewState();
}

class _CalendarMonthViewState extends State<CalendarMonthView> {
  // 高性能状态管理：使用优化的数据结构
  final Map<String, bool> _expandedDates = <String, bool>{};

  // 性能优化：缓存日期键以避免重复字符串拼接
  final Map<DateTime, String> _dateKeyCache = <DateTime, String>{};

  // 获取优化的日期键
  String _getDateKey(DateTime date) {
    return _dateKeyCache.putIfAbsent(
      date,
      () => '${date.year}-${date.month}-${date.day}',
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarBloc, CalendarState>(
      builder: (context, calendarState) {
        return BlocBuilder<TaskListBloc, TaskListState>(
          builder: (context, taskState) {
            return _buildMonthGrid(context, calendarState, taskState);
          },
        );
      },
    );
  }

  Widget _buildMonthGrid(BuildContext context, CalendarState calendarState, TaskListState taskState) {
    final displayMonth = calendarState.displayMonthDate;
    final selectedDate = calendarState.selectedDate;

    // 计算月份的第一天和最后一天
    final firstDayOfMonth = DateTime(displayMonth.year, displayMonth.month, 1);
    final lastDayOfMonth = DateTime(displayMonth.year, displayMonth.month + 1, 0);

    // 计算日历网格需要显示的第一天（包括上个月的日期）
    final firstDayOfGrid = firstDayOfMonth.subtract(Duration(days: firstDayOfMonth.weekday % 7));

    // 计算日历网格需要显示的最后一天（包括下个月的日期）
    final lastDayOfGrid = lastDayOfMonth.add(Duration(days: (7 - lastDayOfMonth.weekday) % 7));

    // 计算总的天数
    final totalDays = lastDayOfGrid.difference(firstDayOfGrid).inDays + 1;

    return Column(
      children: [
        // 星期标题行
        _buildWeekdayHeader(),

        // 日历网格
        Expanded(
          child: GridView.builder(
            padding: const EdgeInsets.all(8.0),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: 0.75, // 调整为更高的单元格以容纳任务列表
              crossAxisSpacing: 1.0,
              mainAxisSpacing: 1.0,
            ),
            itemCount: totalDays,
            itemBuilder: (context, index) {
              final currentDate = firstDayOfGrid.add(Duration(days: index));
              final isCurrentMonth = currentDate.month == displayMonth.month;
              final isSelected = _isSameDate(currentDate, selectedDate);
              final isToday = _isSameDate(currentDate, DateTime.now());

              // 获取该日期的任务
              final tasksForDate = _getTasksForDate(taskState.tasks, currentDate);

              return _buildDateCell(
                context,
                currentDate,
                isCurrentMonth,
                isSelected,
                isToday,
                tasksForDate,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildWeekdayHeader() {
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];

    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(
          bottom: BorderSide(color: AppTheme.borderLight),
        ),
      ),
      child: Row(
        children: weekdays.map((day) {
          return Expanded(
            child: Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(color: AppTheme.borderLight),
                ),
              ),
              child: Text(
                day,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildDateCell(
    BuildContext context,
    DateTime date,
    bool isCurrentMonth,
    bool isSelected,
    bool isToday,
    List<Task> tasks,
  ) {
    // 性能优化：使用缓存的日期键
    final dateKey = _getDateKey(date);
    final isExpanded = _expandedDates[dateKey] ?? false;

    // 性能优化：预计算常用值 - 新的显示策略
    const maxVisibleTasks = 3; // 正常状态显示3条任务 + 1条more按钮 = 4条
    const maxExpandedTasks = 5; // 展开状态最多显示5条任务
    final taskCount = tasks.length;
    final hasMoreTasks = taskCount > maxVisibleTasks && !isExpanded;
    final double bottomReserved = (hasMoreTasks || (isExpanded && taskCount > maxVisibleTasks)) ? 22 : 0;


    // 高效的任务列表切片 - 优化展开逻辑
    final visibleTasks = isExpanded
        ? (taskCount <= maxExpandedTasks ? tasks : tasks.sublist(0, maxExpandedTasks))
        : (taskCount <= maxVisibleTasks ? tasks : tasks.sublist(0, maxVisibleTasks));

    return GestureDetector(
      onTap: () => _onDateSelected(context, date),
      onDoubleTap: () => _onDateDoubleTapped(context, date),
      child: Container(
        margin: const EdgeInsets.all(1.0),
        decoration: BoxDecoration(
          color: _getDateCellColor(isCurrentMonth, isSelected, isToday, tasks),
          borderRadius: BorderRadius.circular(6.0),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.transparent,
            width: isSelected ? 2.0 : 1.0,
          ),
          boxShadow: isSelected || isToday ? [
            BoxShadow(
              color: (isSelected ? Colors.blue : Colors.grey).withOpacity(0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 头部区域：日期数字和任务数量
                Container(
                  height: 24,
                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  child: FittedBox(
                    fit: BoxFit.scaleDown,
                    alignment: Alignment.centerLeft,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // 日期数字
                        Text(
                          '${date.day}',
                          style: TextStyle(
                            fontSize: isToday ? 13 : 12,
                            fontWeight: isToday ? FontWeight.w700 : FontWeight.w500,
                            color: _getDateTextColor(isCurrentMonth, isSelected, isToday),
                            height: 1.0,
                          ),
                        ),

                        // 任务数量指示器 - 完美居中对齐
                        if (tasks.isNotEmpty)
                          Container(
                            width: 18, // 固定宽度确保圆形
                            height: 18, // 固定高度确保圆形
                            decoration: BoxDecoration(
                              color: _getTaskCountColor(tasks),
                              shape: BoxShape.circle, // 使用圆形确保完美居中
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.15),
                                  blurRadius: 2,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Center( // 使用Center确保完美居中
                              child: Text(
                                '${tasks.length}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10, // 稍微增大字号提升可读性
                                  fontWeight: FontWeight.w700,
                                  height: 1.0,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),

                // 任务列表区域 - 防溢出实现
                if (tasks.isNotEmpty)
                  Flexible(
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(
                        AppTheme.spacing1,
                        0,
                        AppTheme.spacing1,
                        bottomReserved,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 任务列表 - 使用 Flexible 而非 Expanded 避免溢出
                          Flexible(
                            child: isExpanded && tasks.length > maxExpandedTasks
                                ? _buildScrollableTaskList(visibleTasks)
                                : _buildStaticTaskList(visibleTasks),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),

            // 今天标记
            // 底部操作条：固定在日期单元格底部
            if (hasMoreTasks)
              Positioned(
                left: 4,
                right: 4,
                bottom: 4,
                child: _buildMoreButton(dateKey, taskCount - maxVisibleTasks),
              ),
            if (isExpanded && taskCount > maxVisibleTasks)
              Positioned(
                left: 4,
                right: 4,
                bottom: 4,
                child: _buildCollapseButton(dateKey),
              ),

            if (isToday)
              Positioned(
                bottom: 1,
                right: 1,
                child: Container(
                  width: 4,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 高性能展开切换方法
  void _toggleDateExpansion(String dateKey) {
    setState(() {
      final currentState = _expandedDates[dateKey] ?? false;
      _expandedDates[dateKey] = !currentState;
    });
  }

  // 高性能任务项构建方法
  Widget _buildTaskItem(Task task) {
    final priorityColor = _getPriorityColor(task.priority);
    final isCompleted = task.isCompleted;

    // 性能优化：预计算样式值
    final backgroundColor = isCompleted
        ? AppTheme.slate50
        : priorityColor.withOpacity(0.08);
    final borderColor = isCompleted
        ? AppTheme.borderLight
        : priorityColor.withOpacity(0.2);
    final textColor = isCompleted
        ? AppTheme.textMuted
        : AppTheme.textPrimary;

    return Container(
      height: 16, // 固定高度避免溢出
      margin: const EdgeInsets.only(bottom: AppTheme.spacing1),
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing1,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
        border: Border.all(color: borderColor, width: 0.5),
      ),
      child: Row(
        children: [
          // 优先级指示器点
          Container(
            width: 4,
            height: 4,
            decoration: BoxDecoration(
              color: isCompleted ? AppTheme.textMuted : priorityColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppTheme.spacing1),
          // 优化的任务标题 - 增大字体提升可读性
          Expanded(
            child: Text(
              task.title,
              style: TextStyle(
                fontSize: 10, // 增大字号：8->10
                fontWeight: FontWeight.w600, // 增强字重提升可读性
                color: textColor,
                decoration: isCompleted ? TextDecoration.lineThrough : null,
                height: 1.3, // 稍微增加行高
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Color _getDateCellColor(bool isCurrentMonth, bool isSelected, bool isToday, List<Task> tasks) {
    if (isSelected) return AppTheme.primaryBlue.withOpacity(0.1);
    if (isToday) return AppTheme.primaryBlue.withOpacity(0.15);
    if (tasks.isNotEmpty && isCurrentMonth) {
      // 根据任务优先级调整背景色
      final hasHighPriority = tasks.any((task) => task.priority == Priority.urgentImportant);
      final hasMediumPriority = tasks.any((task) => task.priority == Priority.importantNotUrgent);

      if (hasHighPriority) return AppTheme.urgentImportantColor.withOpacity(0.05);
      if (hasMediumPriority) return AppTheme.importantNotUrgentColor.withOpacity(0.05);
      return AppTheme.primaryBlue.withOpacity(0.03);
    }
    if (isCurrentMonth) return AppTheme.cardLight;
    return AppTheme.slate50;
  }

  Color _getDateTextColor(bool isCurrentMonth, bool isSelected, bool isToday) {
    if (isSelected) return AppTheme.primaryBlue;
    if (isToday) return AppTheme.primaryBlue;
    if (isCurrentMonth) return AppTheme.textPrimary;
    return AppTheme.textTertiary;
  }

  // 获取任务数量指示器颜色
  Color _getTaskCountColor(List<Task> tasks) {
    final urgentImportantCount = tasks.where((task) => task.priority == Priority.urgentImportant).length;
    final importantNotUrgentCount = tasks.where((task) => task.priority == Priority.importantNotUrgent).length;
    final urgentNotImportantCount = tasks.where((task) => task.priority == Priority.urgentNotImportant).length;

    if (urgentImportantCount > 0) return AppTheme.urgentImportantColor;
    if (importantNotUrgentCount > 0) return AppTheme.importantNotUrgentColor;
    if (urgentNotImportantCount > 0) return AppTheme.urgentNotImportantColor;
    return AppTheme.notUrgentNotImportantColor;
  }

  // 构建优先级指示器条
  Widget _buildPriorityIndicatorBar(List<Task> tasks) {
    final priorityGroups = <Priority, int>{};
    for (final task in tasks) {
      priorityGroups[task.priority] = (priorityGroups[task.priority] ?? 0) + 1;
    }

    if (priorityGroups.isEmpty) return const SizedBox.shrink();

    final segments = <Widget>[];
    final totalTasks = tasks.length;

    // 按优先级顺序添加段
    final priorityOrder = [
      Priority.urgentImportant,
      Priority.importantNotUrgent,
      Priority.urgentNotImportant,
      Priority.notUrgentNotImportant,
    ];

    for (final priority in priorityOrder) {
      final count = priorityGroups[priority] ?? 0;
      if (count > 0) {
        final flex = (count * 100 / totalTasks).round();
        segments.add(
          Expanded(
            flex: flex,
            child: Container(
              height: 3,
              decoration: BoxDecoration(
                color: _getPriorityColor(priority),
                borderRadius: segments.isEmpty
                  ? const BorderRadius.only(
                      topLeft: Radius.circular(1.5),
                      bottomLeft: Radius.circular(1.5),
                    )
                  : null,
              ),
            ),
          ),
        );
      }
    }

    return Container(
      height: 2,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(1),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 0.5,
            offset: const Offset(0, 0.5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(1),
        child: Row(children: segments),
      ),
    );
  }

  // 获取优先级颜色
  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return AppTheme.urgentImportantColor;
      case Priority.importantNotUrgent:
        return AppTheme.importantNotUrgentColor;
      case Priority.urgentNotImportant:
        return AppTheme.urgentNotImportantColor;
      case Priority.notUrgentNotImportant:
        return AppTheme.notUrgentNotImportantColor;
    }
  }

  // 检查是否有已完成的任务
  bool _hasCompletedTasks(List<Task> tasks) {
    return tasks.any((task) => task.isCompleted);
  }

  List<Task> _getTasksForDate(List<Task> allTasks, DateTime date) {
    return allTasks.where((task) {
      final taskDate = DateTime(task.dueDate.year, task.dueDate.month, task.dueDate.day);
      final targetDate = DateTime(date.year, date.month, date.day);
      return taskDate.isAtSameMomentAs(targetDate);
    }).toList();
  }

  bool _isSameDate(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  void _onDateSelected(BuildContext context, DateTime date) {
    context.read<CalendarBloc>().add(CalendarEvent.dateSelected(date));
  }

  void _onDateDoubleTapped(BuildContext context, DateTime date) {
    // 双击创建新任务
    _showCreateTaskDialog(context, date);
  }

  void _showCreateTaskDialog(BuildContext context, DateTime date) {
    // TODO: 实现任务创建对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('在 ${DateFormat('yyyy年M月d日').format(date)} 创建任务'),
        content: const Text('任务创建功能即将实现'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 构建可滚动任务列表 - 高性能滚动实现
  Widget _buildScrollableTaskList(List<Task> tasks) {
    return SingleChildScrollView(
      physics: const BouncingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: tasks.map((task) => _buildTaskItem(task)).toList(),
      ),
    );
  }

  /// 构建静态任务列表 - 防溢出实现
  Widget _buildStaticTaskList(List<Task> tasks) {
    return SingleChildScrollView(
      physics: const NeverScrollableScrollPhysics(), // 禁用滚动，但允许内容裁剪
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: tasks.map((task) => _buildTaskItem(task)).toList(),
      ),
    );
  }

  /// 构建"更多"按钮 - 高性能点击响应
  Widget _buildMoreButton(String dateKey, int moreCount) {
    return GestureDetector(
      onTap: () => _toggleDateExpansion(dateKey),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing1),
        margin: const EdgeInsets.only(top: AppTheme.spacing1),
        decoration: BoxDecoration(
          color: AppTheme.primaryBlue.withOpacity(0.05),
          borderRadius: BorderRadius.circular(AppTheme.radiusSm),
          border: Border.all(
            color: AppTheme.primaryBlue.withOpacity(0.2),
            width: 0.5,
          ),
        ),
        child: Text(
          '+$moreCount',
          style: const TextStyle(
            fontSize: 10, // 稍微增大字号提升可读性
            color: AppTheme.primaryBlue,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  /// 构建收起按钮 - 高性能动画响应
  Widget _buildCollapseButton(String dateKey) {
    return GestureDetector(
      onTap: () => _toggleDateExpansion(dateKey),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing1),
        margin: const EdgeInsets.only(top: AppTheme.spacing1),
        decoration: BoxDecoration(
          color: AppTheme.slate100,
          borderRadius: BorderRadius.circular(AppTheme.radiusSm),
          border: Border.all(
            color: AppTheme.borderLight,
            width: 0.5,
          ),
        ),
        child: const Icon(
          Icons.keyboard_arrow_up,
          size: 14, // 稍微增大图标提升可见性
          color: AppTheme.textSecondary,
        ),
      ),
    );
  }
}