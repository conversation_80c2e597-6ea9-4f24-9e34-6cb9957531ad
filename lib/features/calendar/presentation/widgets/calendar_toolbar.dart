import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../app/theme.dart';
import '../../../tasks/bloc/task_list_bloc.dart';
import '../../../tasks/bloc/task_list_event.dart';

import '../../../tasks/presentation/task_editor_dialog.dart';
import '../../bloc/calendar_bloc.dart';
import '../../bloc/calendar_event.dart';
import '../../bloc/calendar_state.dart';
import '../../../data_management/presentation/database_management_dialog.dart';

class CalendarToolbar extends StatefulWidget {
  const CalendarToolbar({super.key});

  @override
  State<CalendarToolbar> createState() => _CalendarToolbarState();
}

class _CalendarToolbarState extends State<CalendarToolbar> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing4, vertical: AppTheme.spacing2),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.borderLight),
        ),
      ),
      child: Row(
        children: [
          // 月份/年份切换按钮
          IconButton(
            onPressed: () {
              final calendarBloc = context.read<CalendarBloc>();
              final currentMonth = calendarBloc.state.displayMonthDate;
              final previousMonth =
                  DateTime(currentMonth.year, currentMonth.month - 1, 1);
              calendarBloc.add(CalendarEvent.monthChanged(previousMonth));
            },
            icon: const Icon(Icons.chevron_left),
            tooltip: '上个月',
          ),
          IconButton(
            onPressed: () {
              final calendarBloc = context.read<CalendarBloc>();
              final currentMonth = calendarBloc.state.displayMonthDate;
              final nextMonth =
                  DateTime(currentMonth.year, currentMonth.month + 1, 1);
              calendarBloc.add(CalendarEvent.monthChanged(nextMonth));
            },
            icon: const Icon(Icons.chevron_right),
            tooltip: '下个月',
          ),

          const SizedBox(width: AppTheme.spacing4),

          // 月份年份标题
          Flexible(
            child: BlocBuilder<CalendarBloc, CalendarState>(
              builder: (context, state) {
                final formatter = DateFormat('yyyy年 M月');
                return InkWell(
                  onTap: () => _showDatePicker(context, state.displayMonthDate),
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: AppTheme.spacing2, vertical: AppTheme.spacing1),
                    child: Text(
                      formatter.format(state.displayMonthDate),
                      style:
                          Theme.of(context).textTheme.headlineSmall?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                );
              },
            ),
          ),

          const SizedBox(width: AppTheme.spacing4),

          // 今天按钮
          TextButton.icon(
            onPressed: () {
              final now = DateTime.now();
              final calendarBloc = context.read<CalendarBloc>();
              final taskListBloc = context.read<TaskListBloc>();
              calendarBloc.add(CalendarEvent.dateSelected(now));
              calendarBloc.add(
                  CalendarEvent.monthChanged(DateTime(now.year, now.month, 1)));
              //
              taskListBloc.add(TaskListEvent.monthSubscriptionRequested(
                year: now.year,
                month: now.month,
              ));
            },
            icon: const Icon(Icons.today),
            label: const Text('今天'),
          ),

          const Spacer(),

          // 搜索框
          Flexible(
            child: SizedBox(
              width: 200,
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: '搜索任务',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _performSearch('');
                          },
                        )
                      : null,
                  isDense: true,
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: AppTheme.spacing3, vertical: AppTheme.spacing2),
                ),
                onChanged: _performSearch,
                onSubmitted: _performSearch,
              ),
            ),
          ),

          const SizedBox(width: AppTheme.spacing4),

          // 新建任务按钮
          ElevatedButton.icon(
            onPressed: () => _showCreateTaskDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('新建任务'),
          ),

          const SizedBox(width: AppTheme.spacing2),
          // 数据库操作
          OutlinedButton.icon(
            onPressed: () => _onDatabasePressed(context),
            icon: const Icon(Icons.storage),
            label: const Text('数据库'),
          ),
          const SizedBox(width: AppTheme.spacing2),


          // 更多选项按钮
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            tooltip: '更多选项',
            onSelected: (value) => _handleMoreOptions(context, value),
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'import',
                child: Row(
                  children: [
                    Icon(Icons.file_upload),
                    SizedBox(width: AppTheme.spacing2),
                    Text('导入任务'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.file_download),
                    SizedBox(width: AppTheme.spacing2),
                    Text('导出任务'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: AppTheme.spacing2),
                    Text('设置'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'help',
                child: Row(
                  children: [
                    Icon(Icons.help),
                    SizedBox(width: AppTheme.spacing2),
                    Text('帮助'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showDatePicker(BuildContext context, DateTime currentDate) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: currentDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (picked != null && context.mounted) {
      final calendarBloc = context.read<CalendarBloc>();
      calendarBloc.add(CalendarEvent.dateSelected(picked));
      calendarBloc.add(
          CalendarEvent.monthChanged(DateTime(picked.year, picked.month, 1)));
    }
  }

  void _showCreateTaskDialog(BuildContext context) {
    final calendarState = context.read<CalendarBloc>().state;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => MultiBlocProvider(
        providers: [
          BlocProvider.value(value: context.read<CalendarBloc>()),
          BlocProvider.value(value: context.read<TaskListBloc>()),
        ],
        child: TaskEditorDialog(
          selectedDate: calendarState.selectedDate,
          onTaskSaved: (task) {
            // 任务已保存，不需要额外操作，因为BLoC已经处理了
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('任务「${task.title}」创建成功'),
                backgroundColor: AppTheme.successColor,
              ),
            );
          },
        ),
      ),
    );
  }

  void _performSearch(String query) {
    setState(() {}); // 触发重建以更新清除按钮

    // TODO: 实现实际的搜索逻辑
    // 这里可以添加搜索事件到TaskListBloc
    // context.read<TaskListBloc>().add(TaskListEvent.searchQueryChanged(query));

    if (query.isNotEmpty) {}
  }

  void _onDatabasePressed(BuildContext context) {
    debugPrint('[TOOLBAR] Click: open DatabaseManagementDialog');
    showDialog(
      context: context,
      barrierDismissible: _busyDialogBarrierDismissible(),
      builder: (ctx) => const DatabaseManagementDialog(),
    );
  }

  // Dialog barrier can be dismissed when no blocking tasks are running.
  // Kept simple here; DatabaseManagementDialog already controls its own internal busy state.
  bool _busyDialogBarrierDismissible() => true;

  void _handleMoreOptions(BuildContext context, String value) {
    switch (value) {
      case 'import':
        _showImportDialog(context);
        break;
      case 'export':
        _showExportDialog(context);
        break;
      case 'settings':
        _showSettingsDialog(context);
        break;
      case 'help':
        _showHelpDialog(context);
        break;
    }
  }

  void _showImportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导入任务'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('支持导入格式：'),
            SizedBox(height: AppTheme.spacing2),
            Text('• CSV 文件'),
            Text('• JSON 文件'),
            Text('• iCal 文件'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现文件选择和导入逻辑
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('导入功能开发中...')),
              );
            },
            child: const Text('选择文件'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导出任务'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('选择导出格式：'),
            SizedBox(height: AppTheme.spacing4),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    Icon(Icons.file_copy),
                    SizedBox(height: AppTheme.spacing1),
                    Text('CSV'),
                  ],
                ),
                Column(
                  children: [
                    Icon(Icons.code),
                    SizedBox(height: AppTheme.spacing1),
                    Text('JSON'),
                  ],
                ),
                Column(
                  children: [
                    Icon(Icons.calendar_today),
                    SizedBox(height: AppTheme.spacing1),
                    Text('iCal'),
                  ],
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();
              // TODO: 实现导出逻辑
              scaffoldMessenger.showSnackBar(
                const SnackBar(content: Text('导出功能开发中...')),
              );
            },
            child: const Text('导出'),
          ),
        ],
      ),
    );
  }

  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('设置'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('应用设置', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: AppTheme.spacing2),
            Text('• 主题设置'),
            Text('• 语言设置'),
            Text('• 通知设置'),
            Text('• 数据备份'),
            SizedBox(height: AppTheme.spacing4),
            Text('任务设置', style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: AppTheme.spacing2),
            Text('• 默认优先级'),
            Text('• 提醒设置'),
            Text('• 视图设置'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              final navigator = Navigator.of(context);
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              navigator.pop();
              // TODO: 实现设置页面
              scaffoldMessenger.showSnackBar(
                const SnackBar(content: Text('设置页面开发中...')),
              );
            },
            child: const Text('打开设置'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('帮助'),
        content: const SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('快捷键', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: AppTheme.spacing2),
              Text('• Cmd+N: 新建任务'),
              Text('• Cmd+F: 搜索任务'),
              Text('• Cmd+D: 删除选中任务'),
              Text('• Cmd+E: 编辑选中任务'),
              Text('• Space: 切换任务完成状态'),
              SizedBox(height: AppTheme.spacing4),
              Text('操作指南', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: AppTheme.spacing2),
              Text('• 单击日期选择日期'),
              Text('• 双击任务快速编辑'),
              Text('• 右键任务显示菜单'),
              Text('• 拖拽任务改变优先级'),
              SizedBox(height: AppTheme.spacing4),
              Text('四象限说明', style: TextStyle(fontWeight: FontWeight.bold)),
              SizedBox(height: 8),
              Text('• 重要且紧急: 立即处理'),
              Text('• 重要但不紧急: 计划处理'),
              Text('• 不重要但紧急: 委派处理'),
              Text('• 不重要不紧急: 有时间处理'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }
}
