import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../../../app/theme.dart';
import '../../../../core/di/injection.dart';
import '../../../summary/bloc/summary_bloc.dart';
import '../../../summary/bloc/summary_event.dart';
import '../../../summary/bloc/summary_state.dart';
import '../../bloc/calendar_bloc.dart';
import '../../bloc/calendar_event.dart';
import '../../bloc/calendar_state.dart';

/// 侧边栏组件，包含迷你日历和导航选项
class Sidebar extends StatelessWidget {
  const Sidebar({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 280,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          right: BorderSide(color: AppTheme.borderLight),
        ),
      ),
      child: Column(
        children: [
          // 应用标题
          Container(
            height: 60,
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing4),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: AppTheme.borderLight),
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryBlue,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.calendar_today,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
                const SizedBox(width: AppTheme.spacing3),
                Flexible(
                  child: Text(
                    'My ToDo Space',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // 迷你日历
          const Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(AppTheme.spacing4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MiniCalendar(),
                  SizedBox(height: AppTheme.spacing6),
                  ViewSwitcher(),
                  SizedBox(height: AppTheme.spacing6),
                  SummaryNavigation(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 迷你日历组件
class MiniCalendar extends StatelessWidget {
  const MiniCalendar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarBloc, CalendarState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 月份导航
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  DateFormat('yyyy年 M月').format(state.displayMonthDate),
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                Row(
                  children: [
                    IconButton(
                      onPressed: () {
                        final currentMonth = state.displayMonthDate;
                        final previousMonth = DateTime(
                            currentMonth.year, currentMonth.month - 1, 1);
                        context
                            .read<CalendarBloc>()
                            .add(CalendarEvent.monthChanged(previousMonth));
                      },
                      icon: const Icon(Icons.chevron_left, size: 20),
                      constraints:
                          const BoxConstraints(minWidth: 32, minHeight: 32),
                      padding: EdgeInsets.zero,
                    ),
                    IconButton(
                      onPressed: () {
                        final currentMonth = state.displayMonthDate;
                        final nextMonth = DateTime(
                            currentMonth.year, currentMonth.month + 1, 1);
                        context
                            .read<CalendarBloc>()
                            .add(CalendarEvent.monthChanged(nextMonth));
                      },
                      icon: const Icon(Icons.chevron_right, size: 20),
                      constraints:
                          const BoxConstraints(minWidth: 32, minHeight: 32),
                      padding: EdgeInsets.zero,
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: AppTheme.spacing3),

            // 星期标题
            Row(
              children: ['日', '一', '二', '三', '四', '五', '六']
                  .map((weekday) => Expanded(
                        child: Center(
                          child: Text(
                            weekday,
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onSurfaceVariant,
                                      fontWeight: FontWeight.w500,
                                    ),
                          ),
                        ),
                      ))
                  .toList(),
            ),

            const SizedBox(height: AppTheme.spacing2),

            // 日历网格
            MiniCalendarGrid(
              displayMonth: state.displayMonthDate,
              selectedDate: state.selectedDate,
              taskLoadByDate: state.taskLoadByDate,
            ),
          ],
        );
      },
    );
  }
}

/// 迷你日历网格
class MiniCalendarGrid extends StatelessWidget {
  final DateTime displayMonth;
  final DateTime selectedDate;
  final Map<DateTime, int> taskLoadByDate;

  const MiniCalendarGrid({
    super.key,
    required this.displayMonth,
    required this.selectedDate,
    required this.taskLoadByDate,
  });

  @override
  Widget build(BuildContext context) {
    final firstDayOfMonth = DateTime(displayMonth.year, displayMonth.month, 1);
    // Calculate grid layout for mini calendar
    final startDate = firstDayOfMonth.subtract(
      Duration(days: firstDayOfMonth.weekday % 7),
    );

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        crossAxisSpacing: 2,
        mainAxisSpacing: 2,
        childAspectRatio: 1.0,
      ),
      itemCount: 42, // 6周 x 7天
      itemBuilder: (context, index) {
        final currentDate = startDate.add(Duration(days: index));
        final dateKey =
            DateTime(currentDate.year, currentDate.month, currentDate.day);

        final isCurrentMonth = currentDate.month == displayMonth.month;
        final isToday = _isSameDay(currentDate, DateTime.now());
        final isSelected = _isSameDay(currentDate, selectedDate);
        final taskLoad = taskLoadByDate[dateKey] ?? 0;

        return MiniDayCell(
          date: currentDate,
          isCurrentMonth: isCurrentMonth,
          isToday: isToday,
          isSelected: isSelected,
          taskLoad: taskLoad,
        );
      },
    );
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }
}

/// 迷你日历日期单元格
class MiniDayCell extends StatelessWidget {
  final DateTime date;
  final bool isCurrentMonth;
  final bool isToday;
  final bool isSelected;
  final int taskLoad;

  const MiniDayCell({
    super.key,
    required this.date,
    required this.isCurrentMonth,
    required this.isToday,
    required this.isSelected,
    required this.taskLoad,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.read<CalendarBloc>().add(CalendarEvent.dateSelected(date));
      },
      child: Container(
        decoration: BoxDecoration(
          color: _getCellBackgroundColor(context),
          borderRadius: BorderRadius.circular(4),
          border: isSelected
              ? Border.all(color: AppTheme.primaryBlue, width: 1.5)
              : null,
        ),
        child: Stack(
          children: [
            // 日期数字
            Center(
              child: Text(
                '${date.day}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: _getTextColor(context),
                      fontWeight: isToday || isSelected
                          ? FontWeight.w600
                          : FontWeight.w400,
                    ),
              ),
            ),

            // 任务指示器
            if (taskLoad > 0)
              Positioned(
                bottom: 2,
                right: 2,
                child: Container(
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryBlue.withAlpha(
                      (255 * (taskLoad / 10).clamp(0.3, 1.0)).round(),
                    ), // accent dot opacity 30–100% per design
                    shape: BoxShape.circle,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getCellBackgroundColor(BuildContext context) {
    if (isSelected) {
      return AppTheme.primaryBlue.withAlpha(26); // ~10% accent tint
    }
    if (isToday) {
      return AppTheme.primaryBlue.withAlpha(13); // ~5% accent tint
    }
    return Colors.transparent;
  }

  Color _getTextColor(BuildContext context) {
    if (isSelected || isToday) {
      return AppTheme.primaryBlue;
    }
    if (!isCurrentMonth) {
      return Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(102); // ~40% dimmed text
    }
    return Theme.of(context).colorScheme.onSurface;
  }
}

/// 视图切换器
class ViewSwitcher extends StatelessWidget {
  const ViewSwitcher({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<CalendarBloc, CalendarState>(
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '视图',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
            const SizedBox(height: AppTheme.spacing3),
            _ViewOption(
              icon: Icons.calendar_view_month,
              title: '月视图',
              isSelected: state.viewType == CalendarViewType.month,
              onTap: () {
                context.read<CalendarBloc>().add(
                      const CalendarEvent.viewTypeChanged(
                          CalendarViewType.month),
                    );
              },
            ),
            _ViewOption(
              icon: Icons.calendar_view_week,
              title: '年视图',
              isSelected: state.viewType == CalendarViewType.year,
              onTap: () {
                context.read<CalendarBloc>().add(
                      const CalendarEvent.viewTypeChanged(
                          CalendarViewType.year),
                    );
              },
            ),
            _ViewOption(
              icon: Icons.grid_view,
              title: '四象限视图',
              isSelected: state.viewType == CalendarViewType.quadrant,
              onTap: () {
                context.read<CalendarBloc>().add(
                      const CalendarEvent.viewTypeChanged(
                          CalendarViewType.quadrant),
                    );
              },
            ),
          ],
        );
      },
    );
  }
}

/// 视图选项组件
class _ViewOption extends StatelessWidget {
  final IconData icon;
  final String title;
  final bool isSelected;
  final VoidCallback onTap;

  const _ViewOption({
    required this.icon,
    required this.title,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing3, vertical: AppTheme.spacing2),
        margin: const EdgeInsets.only(bottom: AppTheme.spacing1),
        decoration: BoxDecoration(
          color: isSelected
              ? AppTheme.primaryBlue.withAlpha(26)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected
                  ? AppTheme.primaryBlue
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: AppTheme.spacing3),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? AppTheme.primaryBlue
                        : Theme.of(context).colorScheme.onSurface,
                    fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                  ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 总结导航
class SummaryNavigation extends StatelessWidget {
  const SummaryNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '总结报告',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
        ),
        const SizedBox(height: AppTheme.spacing3),
        InkWell(
          onTap: () {
            context.read<CalendarBloc>().add(
              const CalendarEvent.viewTypeChanged(CalendarViewType.summary),
            );
          },
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(AppTheme.spacing3),
            margin: const EdgeInsets.only(bottom: AppTheme.spacing2),
            decoration: BoxDecoration(
              border: Border.all(color: AppTheme.borderLight),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryBlue.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.analytics,
                    size: 18,
                    color: AppTheme.primaryBlue,
                  ),
                ),
                const SizedBox(width: AppTheme.spacing3),
                const Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '本月/年总结',
                        style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
                      ),
                      SizedBox(height: AppTheme.spacing0_5),
                      Text(
                        '同时查看月/年任务统计',
                        style: TextStyle(fontSize: 12, color: AppTheme.textSecondary),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

/// 总结选项组件（带数据预览）
class _SummaryOptionWithPreview extends StatefulWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final bool isMonthly;
  final VoidCallback onTap;

  const _SummaryOptionWithPreview({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.isMonthly,
    required this.onTap,
  });

  @override
  State<_SummaryOptionWithPreview> createState() =>
      _SummaryOptionWithPreviewState();
}

class _SummaryOptionWithPreviewState extends State<_SummaryOptionWithPreview> {
  @override
  void initState() {
    super.initState();
    // 加载预览数据
    final now = DateTime.now();
    if (widget.isMonthly) {
      context.read<SummaryBloc>().add(
            SummaryEvent.monthlyRequested(year: now.year, month: now.month),
          );
    } else {
      context.read<SummaryBloc>().add(
            SummaryEvent.yearlyRequested(year: now.year),
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacing3),
        margin: const EdgeInsets.only(bottom: AppTheme.spacing2),
        decoration: BoxDecoration(
          border: Border.all(
            color: AppTheme.borderLight,
          ),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryBlue.withAlpha(26), // ~10% accent tint
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    widget.icon,
                    size: 18,
                    color: AppTheme.primaryBlue,
                  ),
                ),
                const SizedBox(width: AppTheme.spacing3),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.title,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                      const SizedBox(height: AppTheme.spacing0_5),
                      Text(
                        widget.subtitle,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // 数据预览
            const SizedBox(height: AppTheme.spacing2),
            BlocBuilder<SummaryBloc, SummaryState>(
              builder: (context, state) {
                return _buildPreviewContent(context, state);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreviewContent(BuildContext context, SummaryState state) {
    switch (state.status) {
      case SummaryStatus.loading:
        return SizedBox(
          height: 20,
          child: Row(
            children: [
              SizedBox(
                width: 12,
                height: 12,
                child: CircularProgressIndicator(
                  strokeWidth: 1.5,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(width: AppTheme.spacing2),
              Text(
                '加载中...',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ),
        );

      case SummaryStatus.success:
        final report = state.report!;
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: AppTheme.spacing2, vertical: AppTheme.spacing1),
          decoration: BoxDecoration(
            color: Theme.of(context)
                .colorScheme
                .surfaceContainerHighest
                .withAlpha(77), // ~30% overlay
            borderRadius: BorderRadius.circular(4),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _PreviewStat(
                label: '总计',
                value: '${report.totalTasksCreated}',
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              _PreviewStat(
                label: '完成',
                value: '${report.totalTasksCompleted}',
                color: AppTheme.successColor,
              ),
              _PreviewStat(
                label: '完成率',
                value: '${(report.completionRate * 100).toInt()}%',
                color: AppTheme.primaryBlue,
              ),
            ],
          ),
        );

      case SummaryStatus.failure:
        return SizedBox(
          height: 20,
          child: Row(
            children: [
              Icon(
                Icons.error_outline,
                size: 12,
                color: Theme.of(context).colorScheme.error,
              ),
              const SizedBox(width: AppTheme.spacing1),
              Text(
                '加载失败',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
              ),
            ],
          ),
        );

      default:
        return const SizedBox(height: AppTheme.spacing5);
    }
  }
}

/// 预览统计数据组件
class _PreviewStat extends StatelessWidget {
  final String label;
  final String value;
  final Color color;

  const _PreviewStat({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          value,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: color,
              ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontSize: 10,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
        ),
      ],
    );
  }
}
