import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../domain/models/task_model.dart';
import 'calendar_state.dart';

part 'calendar_event.freezed.dart';

@freezed
abstract class CalendarEvent with _$CalendarEvent {
  /// Initialize calendar or refresh data
  const factory CalendarEvent.started() = _Started;
  
  /// User selected a date on the calendar
  const factory CalendarEvent.dateSelected(DateTime date) = _DateSelected;
  
  /// Navigate to previous/next month
  const factory CalendarEvent.monthChanged(DateTime newMonthDate) = _MonthChanged;
  
  /// Navigate to previous/next year
  const factory CalendarEvent.yearChanged(int year) = _YearChanged;
  
  /// Navigate to today's date
  const factory CalendarEvent.todayNavigated() = _TodayNavigated;
  
  /// Navigate to specific date
  const factory CalendarEvent.dateNavigated(DateTime date) = _DateNavigated;

  /// Quick task creation from calendar double-click
  const factory CalendarEvent.quickTaskAdded({
    required String title,
    required DateTime date,
    @Default(Priority.urgentImportant) Priority priority,
  }) = _QuickTaskAdded;
  
  /// Bulk quick task creation
  const factory CalendarEvent.bulkQuickTasksAdded({
    required List<String> titles,
    required DateTime date,
    @Default(Priority.urgentImportant) Priority priority,
  }) = _BulkQuickTasksAdded;

  /// Change calendar view type
  const factory CalendarEvent.viewTypeChanged(CalendarViewType viewType) = _ViewTypeChanged;
  
  /// Toggle between month and year view
  const factory CalendarEvent.viewToggled() = _ViewToggled;

  /// Task dropped on calendar date (drag and drop)
  const factory CalendarEvent.taskDropped({
    required String taskId,
    required DateTime newDate,
  }) = _TaskDropped;
  
  /// Multiple tasks dropped on calendar date
  const factory CalendarEvent.multipleTasksDropped({
    required List<String> taskIds,
    required DateTime newDate,
  }) = _MultipleTasksDropped;

  /// Refresh calendar data
  const factory CalendarEvent.refreshRequested() = _RefreshRequested;
  
  /// Load task load data for specific month
  const factory CalendarEvent.taskLoadRequested({
    required int year,
    required int month,
  }) = _TaskLoadRequested;
  
  /// Load task load data for entire year
  const factory CalendarEvent.yearTaskLoadRequested(int year) = _YearTaskLoadRequested;

  /// Filter calendar by priority
  const factory CalendarEvent.priorityFilterChanged(Priority? priority) = _PriorityFilterChanged;
  
  /// Filter calendar by completion status
  const factory CalendarEvent.completionFilterChanged(bool? isCompleted) = _CompletionFilterChanged;
  
  /// Clear all filters
  const factory CalendarEvent.filtersCleared() = _FiltersCleared;

  /// Show/hide weekends
  const factory CalendarEvent.weekendsToggled(bool showWeekends) = _WeekendsToggled;
  
  /// Change first day of week
  const factory CalendarEvent.firstDayOfWeekChanged(int firstDay) = _FirstDayOfWeekChanged;
  
  /// Toggle task preview on hover
  const factory CalendarEvent.taskPreviewToggled(bool enabled) = _TaskPreviewToggled;

  /// Keyboard navigation
  const factory CalendarEvent.keyboardNavigation({
    required CalendarKeyAction action,
    DateTime? targetDate,
  }) = _KeyboardNavigation;

  /// Retry failed operation
  const factory CalendarEvent.retryRequested() = _RetryRequested;
  
  /// Clear error state
  const factory CalendarEvent.errorCleared() = _ErrorCleared;

  /// (Internal) Task load data updated
  const factory CalendarEvent.taskLoadUpdated(Map<DateTime, int> taskLoadByDate) = _TaskLoadUpdated;
  
  /// (Internal) Year task load data updated
  const factory CalendarEvent.yearTaskLoadUpdated(Map<DateTime, int> yearTaskLoadByDate) = _YearTaskLoadUpdated;
  
  /// (Internal) Error occurred
  const factory CalendarEvent.errorOccurred({
    required String message,
    Exception? exception,
  }) = _ErrorOccurred;
}

/// Keyboard navigation actions for calendar
enum CalendarKeyAction {
  previousDay,
  nextDay,
  previousWeek,
  nextWeek,
  previousMonth,
  nextMonth,
  previousYear,
  nextYear,
  goToToday,
  selectDate,
  quickAddTask,
}