import 'package:clock/clock.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../domain/models/task_model.dart';

part 'calendar_state.freezed.dart';

enum CalendarStatus {
  initial,
  loading,
  success,
  failure,
  refreshing,
  creatingTask,
  updatingTask,
  loadingTaskLoad,
}

enum CalendarViewType {
  month,
  year,
  quadrant,
  summary,
  week,
  day,
}

enum CalendarOperation {
  none,
  loadingData,
  creatingQuickTask,
  droppingTask,
  changingView,
  navigating,
  filtering,
  refreshing,
}

@freezed
abstract class CalendarState with _$CalendarState {
  const factory CalendarState({
    required CalendarStatus status,
    required CalendarOperation currentOperation,
    required CalendarViewType viewType,
    required DateTime selectedDate,
    required DateTime displayMonthDate,
    required int displayYear,

    // Task load data
    required Map<DateTime, int> taskLoadByDate,
    required Map<DateTime, int> yearTaskLoadByDate,
    @Default(0) int maxTaskLoad,
    @Default(0) int totalTasksInMonth,
    @Default(0) int completedTasksInMonth,

    // Filtering
    Priority? priorityFilter,
    bool? completionFilter,
    @Default(false) bool hasActiveFilters,

    // Calendar settings
    @Default(true) bool showWeekends,
    @Default(1) int firstDayOfWeek, // 1 = Monday, 7 = Sunday
    @Default(true) bool taskPreviewEnabled,
    @Default(false) bool showTaskCount,
    @Default(false) bool showCompletionRate,

    // Navigation state
    @Default(false) bool canNavigatePrevious,
    @Default(false) bool canNavigateNext,
    DateTime? minDate,
    DateTime? maxDate,

    // Loading states
    @Default({}) Map<DateTime, bool> dateLoadingStates,
    @Default([]) List<String> creatingTaskIds,
    @Default([]) List<String> updatingTaskIds,

    // Error handling
    String? errorMessage,
    Exception? lastException,
    @Default(false) bool canRetry,

    // Performance metrics
    DateTime? lastUpdated,
    @Default(0) int loadDurationMs,
    @Default(0) int taskLoadCalculationMs,
  }) = _CalendarState;

  factory CalendarState.initial() {
    final now = clock.now();
    final displayMonth = DateTime(now.year, now.month, 1);

    return CalendarState(
      status: CalendarStatus.initial,
      currentOperation: CalendarOperation.none,
      viewType: CalendarViewType.month,
      selectedDate: now,
      displayMonthDate: displayMonth,
      displayYear: now.year,
      taskLoadByDate: {},
      yearTaskLoadByDate: {},
      canNavigatePrevious: true,
      canNavigateNext: true,
    );
  }
}

extension CalendarStateX on CalendarState {
  /// Check if currently loading
  bool get isLoading =>
      status == CalendarStatus.loading ||
      status == CalendarStatus.refreshing ||
      status == CalendarStatus.creatingTask ||
      status == CalendarStatus.updatingTask ||
      status == CalendarStatus.loadingTaskLoad;

  /// Check if has error
  bool get hasError => status == CalendarStatus.failure;

  /// Check if has task load data
  bool get hasTaskLoadData => taskLoadByDate.isNotEmpty;

  /// Check if has year task load data
  bool get hasYearTaskLoadData => yearTaskLoadByDate.isNotEmpty;

  /// Get completion rate for current month
  double get monthCompletionRate => totalTasksInMonth > 0
      ? (completedTasksInMonth / totalTasksInMonth) * 100
      : 0.0;

  /// Get task load for specific date
  int getTaskLoadForDate(DateTime date) {
    final dateKey = DateTime(date.year, date.month, date.day);
    return taskLoadByDate[dateKey] ?? 0;
  }

  /// Get year task load for specific date
  int getYearTaskLoadForDate(DateTime date) {
    final dateKey = DateTime(date.year, date.month, date.day);
    return yearTaskLoadByDate[dateKey] ?? 0;
  }

  /// Check if date is selected
  bool isDateSelected(DateTime date) {
    return selectedDate.year == date.year &&
        selectedDate.month == date.month &&
        selectedDate.day == date.day;
  }

  /// Check if date is today
  bool isToday(DateTime date) {
    final today = clock.now();
    return date.year == today.year &&
        date.month == today.month &&
        date.day == today.day;
  }

  /// Check if date is in current display month
  bool isInDisplayMonth(DateTime date) {
    return date.year == displayMonthDate.year &&
        date.month == displayMonthDate.month;
  }

  /// Check if date is weekend
  bool isWeekend(DateTime date) {
    return date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;
  }

  /// Check if date should be shown (considering weekend setting)
  bool shouldShowDate(DateTime date) {
    if (!showWeekends && isWeekend(date)) {
      return false;
    }
    return true;
  }

  /// Get dates for current month view
  List<DateTime> get monthViewDates {
    final firstDay = displayMonthDate;

    // Calculate first date to show (might be from previous month)
    final firstDateToShow = firstDay
        .subtract(Duration(days: (firstDay.weekday - firstDayOfWeek) % 7));

    // Calculate last date to show (might be from next month)
    const daysToShow = 42; // 6 weeks * 7 days
    final dates = <DateTime>[];

    for (int i = 0; i < daysToShow; i++) {
      final date = firstDateToShow.add(Duration(days: i));
      if (shouldShowDate(date)) {
        dates.add(date);
      }
    }

    return dates;
  }

  /// Get months for year view
  List<DateTime> get yearViewMonths {
    final months = <DateTime>[];
    for (int month = 1; month <= 12; month++) {
      months.add(DateTime(displayYear, month, 1));
    }
    return months;
  }

  /// Get task load intensity (0.0 to 1.0) for date
  double getTaskLoadIntensity(DateTime date) {
    if (maxTaskLoad == 0) return 0.0;

    final load = viewType == CalendarViewType.year
        ? getYearTaskLoadForDate(date)
        : getTaskLoadForDate(date);

    return (load / maxTaskLoad).clamp(0.0, 1.0);
  }

  /// Check if specific date is loading
  bool isDateLoading(DateTime date) {
    final dateKey = DateTime(date.year, date.month, date.day);
    return dateLoadingStates[dateKey] ?? false;
  }

  /// Get navigation boundaries
  DateTime get earliestNavigableDate => minDate ?? DateTime(1900);
  DateTime get latestNavigableDate => maxDate ?? DateTime(2100);

  /// Check if can navigate to previous period
  bool get canGoToPrevious {
    switch (viewType) {
      case CalendarViewType.month:
        final prevMonth =
            DateTime(displayMonthDate.year, displayMonthDate.month - 1, 1);
        return prevMonth.isAfter(earliestNavigableDate) ||
            prevMonth.isAtSameMomentAs(earliestNavigableDate);
      case CalendarViewType.year:
        return displayYear > earliestNavigableDate.year;
      default:
        return canNavigatePrevious;
    }
  }

  /// Check if can navigate to next period
  bool get canGoToNext {
    switch (viewType) {
      case CalendarViewType.month:
        final nextMonth =
            DateTime(displayMonthDate.year, displayMonthDate.month + 1, 1);
        return nextMonth.isBefore(latestNavigableDate) ||
            nextMonth.isAtSameMomentAs(latestNavigableDate);
      case CalendarViewType.year:
        return displayYear < latestNavigableDate.year;
      default:
        return canNavigateNext;
    }
  }
}
