// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'calendar_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CalendarState {
  CalendarStatus get status => throw _privateConstructorUsedError;
  CalendarOperation get currentOperation => throw _privateConstructorUsedError;
  CalendarViewType get viewType => throw _privateConstructorUsedError;
  DateTime get selectedDate => throw _privateConstructorUsedError;
  DateTime get displayMonthDate => throw _privateConstructorUsedError;
  int get displayYear => throw _privateConstructorUsedError; // Task load data
  Map<DateTime, int> get taskLoadByDate => throw _privateConstructorUsedError;
  Map<DateTime, int> get yearTaskLoadByDate =>
      throw _privateConstructorUsedError;
  int get maxTaskLoad => throw _privateConstructorUsedError;
  int get totalTasksInMonth => throw _privateConstructorUsedError;
  int get completedTasksInMonth =>
      throw _privateConstructorUsedError; // Filtering
  Priority? get priorityFilter => throw _privateConstructorUsedError;
  bool? get completionFilter => throw _privateConstructorUsedError;
  bool get hasActiveFilters =>
      throw _privateConstructorUsedError; // Calendar settings
  bool get showWeekends => throw _privateConstructorUsedError;
  int get firstDayOfWeek =>
      throw _privateConstructorUsedError; // 1 = Monday, 7 = Sunday
  bool get taskPreviewEnabled => throw _privateConstructorUsedError;
  bool get showTaskCount => throw _privateConstructorUsedError;
  bool get showCompletionRate =>
      throw _privateConstructorUsedError; // Navigation state
  bool get canNavigatePrevious => throw _privateConstructorUsedError;
  bool get canNavigateNext => throw _privateConstructorUsedError;
  DateTime? get minDate => throw _privateConstructorUsedError;
  DateTime? get maxDate => throw _privateConstructorUsedError; // Loading states
  Map<DateTime, bool> get dateLoadingStates =>
      throw _privateConstructorUsedError;
  List<String> get creatingTaskIds => throw _privateConstructorUsedError;
  List<String> get updatingTaskIds =>
      throw _privateConstructorUsedError; // Error handling
  String? get errorMessage => throw _privateConstructorUsedError;
  Exception? get lastException => throw _privateConstructorUsedError;
  bool get canRetry =>
      throw _privateConstructorUsedError; // Performance metrics
  DateTime? get lastUpdated => throw _privateConstructorUsedError;
  int get loadDurationMs => throw _privateConstructorUsedError;
  int get taskLoadCalculationMs => throw _privateConstructorUsedError;

  /// Create a copy of CalendarState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CalendarStateCopyWith<CalendarState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CalendarStateCopyWith<$Res> {
  factory $CalendarStateCopyWith(
          CalendarState value, $Res Function(CalendarState) then) =
      _$CalendarStateCopyWithImpl<$Res, CalendarState>;
  @useResult
  $Res call(
      {CalendarStatus status,
      CalendarOperation currentOperation,
      CalendarViewType viewType,
      DateTime selectedDate,
      DateTime displayMonthDate,
      int displayYear,
      Map<DateTime, int> taskLoadByDate,
      Map<DateTime, int> yearTaskLoadByDate,
      int maxTaskLoad,
      int totalTasksInMonth,
      int completedTasksInMonth,
      Priority? priorityFilter,
      bool? completionFilter,
      bool hasActiveFilters,
      bool showWeekends,
      int firstDayOfWeek,
      bool taskPreviewEnabled,
      bool showTaskCount,
      bool showCompletionRate,
      bool canNavigatePrevious,
      bool canNavigateNext,
      DateTime? minDate,
      DateTime? maxDate,
      Map<DateTime, bool> dateLoadingStates,
      List<String> creatingTaskIds,
      List<String> updatingTaskIds,
      String? errorMessage,
      Exception? lastException,
      bool canRetry,
      DateTime? lastUpdated,
      int loadDurationMs,
      int taskLoadCalculationMs});
}

/// @nodoc
class _$CalendarStateCopyWithImpl<$Res, $Val extends CalendarState>
    implements $CalendarStateCopyWith<$Res> {
  _$CalendarStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CalendarState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? currentOperation = null,
    Object? viewType = null,
    Object? selectedDate = null,
    Object? displayMonthDate = null,
    Object? displayYear = null,
    Object? taskLoadByDate = null,
    Object? yearTaskLoadByDate = null,
    Object? maxTaskLoad = null,
    Object? totalTasksInMonth = null,
    Object? completedTasksInMonth = null,
    Object? priorityFilter = freezed,
    Object? completionFilter = freezed,
    Object? hasActiveFilters = null,
    Object? showWeekends = null,
    Object? firstDayOfWeek = null,
    Object? taskPreviewEnabled = null,
    Object? showTaskCount = null,
    Object? showCompletionRate = null,
    Object? canNavigatePrevious = null,
    Object? canNavigateNext = null,
    Object? minDate = freezed,
    Object? maxDate = freezed,
    Object? dateLoadingStates = null,
    Object? creatingTaskIds = null,
    Object? updatingTaskIds = null,
    Object? errorMessage = freezed,
    Object? lastException = freezed,
    Object? canRetry = null,
    Object? lastUpdated = freezed,
    Object? loadDurationMs = null,
    Object? taskLoadCalculationMs = null,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CalendarStatus,
      currentOperation: null == currentOperation
          ? _value.currentOperation
          : currentOperation // ignore: cast_nullable_to_non_nullable
              as CalendarOperation,
      viewType: null == viewType
          ? _value.viewType
          : viewType // ignore: cast_nullable_to_non_nullable
              as CalendarViewType,
      selectedDate: null == selectedDate
          ? _value.selectedDate
          : selectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      displayMonthDate: null == displayMonthDate
          ? _value.displayMonthDate
          : displayMonthDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      displayYear: null == displayYear
          ? _value.displayYear
          : displayYear // ignore: cast_nullable_to_non_nullable
              as int,
      taskLoadByDate: null == taskLoadByDate
          ? _value.taskLoadByDate
          : taskLoadByDate // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      yearTaskLoadByDate: null == yearTaskLoadByDate
          ? _value.yearTaskLoadByDate
          : yearTaskLoadByDate // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      maxTaskLoad: null == maxTaskLoad
          ? _value.maxTaskLoad
          : maxTaskLoad // ignore: cast_nullable_to_non_nullable
              as int,
      totalTasksInMonth: null == totalTasksInMonth
          ? _value.totalTasksInMonth
          : totalTasksInMonth // ignore: cast_nullable_to_non_nullable
              as int,
      completedTasksInMonth: null == completedTasksInMonth
          ? _value.completedTasksInMonth
          : completedTasksInMonth // ignore: cast_nullable_to_non_nullable
              as int,
      priorityFilter: freezed == priorityFilter
          ? _value.priorityFilter
          : priorityFilter // ignore: cast_nullable_to_non_nullable
              as Priority?,
      completionFilter: freezed == completionFilter
          ? _value.completionFilter
          : completionFilter // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasActiveFilters: null == hasActiveFilters
          ? _value.hasActiveFilters
          : hasActiveFilters // ignore: cast_nullable_to_non_nullable
              as bool,
      showWeekends: null == showWeekends
          ? _value.showWeekends
          : showWeekends // ignore: cast_nullable_to_non_nullable
              as bool,
      firstDayOfWeek: null == firstDayOfWeek
          ? _value.firstDayOfWeek
          : firstDayOfWeek // ignore: cast_nullable_to_non_nullable
              as int,
      taskPreviewEnabled: null == taskPreviewEnabled
          ? _value.taskPreviewEnabled
          : taskPreviewEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      showTaskCount: null == showTaskCount
          ? _value.showTaskCount
          : showTaskCount // ignore: cast_nullable_to_non_nullable
              as bool,
      showCompletionRate: null == showCompletionRate
          ? _value.showCompletionRate
          : showCompletionRate // ignore: cast_nullable_to_non_nullable
              as bool,
      canNavigatePrevious: null == canNavigatePrevious
          ? _value.canNavigatePrevious
          : canNavigatePrevious // ignore: cast_nullable_to_non_nullable
              as bool,
      canNavigateNext: null == canNavigateNext
          ? _value.canNavigateNext
          : canNavigateNext // ignore: cast_nullable_to_non_nullable
              as bool,
      minDate: freezed == minDate
          ? _value.minDate
          : minDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      maxDate: freezed == maxDate
          ? _value.maxDate
          : maxDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateLoadingStates: null == dateLoadingStates
          ? _value.dateLoadingStates
          : dateLoadingStates // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, bool>,
      creatingTaskIds: null == creatingTaskIds
          ? _value.creatingTaskIds
          : creatingTaskIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      updatingTaskIds: null == updatingTaskIds
          ? _value.updatingTaskIds
          : updatingTaskIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      lastException: freezed == lastException
          ? _value.lastException
          : lastException // ignore: cast_nullable_to_non_nullable
              as Exception?,
      canRetry: null == canRetry
          ? _value.canRetry
          : canRetry // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      loadDurationMs: null == loadDurationMs
          ? _value.loadDurationMs
          : loadDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
      taskLoadCalculationMs: null == taskLoadCalculationMs
          ? _value.taskLoadCalculationMs
          : taskLoadCalculationMs // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CalendarStateImplCopyWith<$Res>
    implements $CalendarStateCopyWith<$Res> {
  factory _$$CalendarStateImplCopyWith(
          _$CalendarStateImpl value, $Res Function(_$CalendarStateImpl) then) =
      __$$CalendarStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {CalendarStatus status,
      CalendarOperation currentOperation,
      CalendarViewType viewType,
      DateTime selectedDate,
      DateTime displayMonthDate,
      int displayYear,
      Map<DateTime, int> taskLoadByDate,
      Map<DateTime, int> yearTaskLoadByDate,
      int maxTaskLoad,
      int totalTasksInMonth,
      int completedTasksInMonth,
      Priority? priorityFilter,
      bool? completionFilter,
      bool hasActiveFilters,
      bool showWeekends,
      int firstDayOfWeek,
      bool taskPreviewEnabled,
      bool showTaskCount,
      bool showCompletionRate,
      bool canNavigatePrevious,
      bool canNavigateNext,
      DateTime? minDate,
      DateTime? maxDate,
      Map<DateTime, bool> dateLoadingStates,
      List<String> creatingTaskIds,
      List<String> updatingTaskIds,
      String? errorMessage,
      Exception? lastException,
      bool canRetry,
      DateTime? lastUpdated,
      int loadDurationMs,
      int taskLoadCalculationMs});
}

/// @nodoc
class __$$CalendarStateImplCopyWithImpl<$Res>
    extends _$CalendarStateCopyWithImpl<$Res, _$CalendarStateImpl>
    implements _$$CalendarStateImplCopyWith<$Res> {
  __$$CalendarStateImplCopyWithImpl(
      _$CalendarStateImpl _value, $Res Function(_$CalendarStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? currentOperation = null,
    Object? viewType = null,
    Object? selectedDate = null,
    Object? displayMonthDate = null,
    Object? displayYear = null,
    Object? taskLoadByDate = null,
    Object? yearTaskLoadByDate = null,
    Object? maxTaskLoad = null,
    Object? totalTasksInMonth = null,
    Object? completedTasksInMonth = null,
    Object? priorityFilter = freezed,
    Object? completionFilter = freezed,
    Object? hasActiveFilters = null,
    Object? showWeekends = null,
    Object? firstDayOfWeek = null,
    Object? taskPreviewEnabled = null,
    Object? showTaskCount = null,
    Object? showCompletionRate = null,
    Object? canNavigatePrevious = null,
    Object? canNavigateNext = null,
    Object? minDate = freezed,
    Object? maxDate = freezed,
    Object? dateLoadingStates = null,
    Object? creatingTaskIds = null,
    Object? updatingTaskIds = null,
    Object? errorMessage = freezed,
    Object? lastException = freezed,
    Object? canRetry = null,
    Object? lastUpdated = freezed,
    Object? loadDurationMs = null,
    Object? taskLoadCalculationMs = null,
  }) {
    return _then(_$CalendarStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as CalendarStatus,
      currentOperation: null == currentOperation
          ? _value.currentOperation
          : currentOperation // ignore: cast_nullable_to_non_nullable
              as CalendarOperation,
      viewType: null == viewType
          ? _value.viewType
          : viewType // ignore: cast_nullable_to_non_nullable
              as CalendarViewType,
      selectedDate: null == selectedDate
          ? _value.selectedDate
          : selectedDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      displayMonthDate: null == displayMonthDate
          ? _value.displayMonthDate
          : displayMonthDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      displayYear: null == displayYear
          ? _value.displayYear
          : displayYear // ignore: cast_nullable_to_non_nullable
              as int,
      taskLoadByDate: null == taskLoadByDate
          ? _value._taskLoadByDate
          : taskLoadByDate // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      yearTaskLoadByDate: null == yearTaskLoadByDate
          ? _value._yearTaskLoadByDate
          : yearTaskLoadByDate // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
      maxTaskLoad: null == maxTaskLoad
          ? _value.maxTaskLoad
          : maxTaskLoad // ignore: cast_nullable_to_non_nullable
              as int,
      totalTasksInMonth: null == totalTasksInMonth
          ? _value.totalTasksInMonth
          : totalTasksInMonth // ignore: cast_nullable_to_non_nullable
              as int,
      completedTasksInMonth: null == completedTasksInMonth
          ? _value.completedTasksInMonth
          : completedTasksInMonth // ignore: cast_nullable_to_non_nullable
              as int,
      priorityFilter: freezed == priorityFilter
          ? _value.priorityFilter
          : priorityFilter // ignore: cast_nullable_to_non_nullable
              as Priority?,
      completionFilter: freezed == completionFilter
          ? _value.completionFilter
          : completionFilter // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasActiveFilters: null == hasActiveFilters
          ? _value.hasActiveFilters
          : hasActiveFilters // ignore: cast_nullable_to_non_nullable
              as bool,
      showWeekends: null == showWeekends
          ? _value.showWeekends
          : showWeekends // ignore: cast_nullable_to_non_nullable
              as bool,
      firstDayOfWeek: null == firstDayOfWeek
          ? _value.firstDayOfWeek
          : firstDayOfWeek // ignore: cast_nullable_to_non_nullable
              as int,
      taskPreviewEnabled: null == taskPreviewEnabled
          ? _value.taskPreviewEnabled
          : taskPreviewEnabled // ignore: cast_nullable_to_non_nullable
              as bool,
      showTaskCount: null == showTaskCount
          ? _value.showTaskCount
          : showTaskCount // ignore: cast_nullable_to_non_nullable
              as bool,
      showCompletionRate: null == showCompletionRate
          ? _value.showCompletionRate
          : showCompletionRate // ignore: cast_nullable_to_non_nullable
              as bool,
      canNavigatePrevious: null == canNavigatePrevious
          ? _value.canNavigatePrevious
          : canNavigatePrevious // ignore: cast_nullable_to_non_nullable
              as bool,
      canNavigateNext: null == canNavigateNext
          ? _value.canNavigateNext
          : canNavigateNext // ignore: cast_nullable_to_non_nullable
              as bool,
      minDate: freezed == minDate
          ? _value.minDate
          : minDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      maxDate: freezed == maxDate
          ? _value.maxDate
          : maxDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      dateLoadingStates: null == dateLoadingStates
          ? _value._dateLoadingStates
          : dateLoadingStates // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, bool>,
      creatingTaskIds: null == creatingTaskIds
          ? _value._creatingTaskIds
          : creatingTaskIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      updatingTaskIds: null == updatingTaskIds
          ? _value._updatingTaskIds
          : updatingTaskIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      lastException: freezed == lastException
          ? _value.lastException
          : lastException // ignore: cast_nullable_to_non_nullable
              as Exception?,
      canRetry: null == canRetry
          ? _value.canRetry
          : canRetry // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      loadDurationMs: null == loadDurationMs
          ? _value.loadDurationMs
          : loadDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
      taskLoadCalculationMs: null == taskLoadCalculationMs
          ? _value.taskLoadCalculationMs
          : taskLoadCalculationMs // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$CalendarStateImpl implements _CalendarState {
  const _$CalendarStateImpl(
      {required this.status,
      required this.currentOperation,
      required this.viewType,
      required this.selectedDate,
      required this.displayMonthDate,
      required this.displayYear,
      required final Map<DateTime, int> taskLoadByDate,
      required final Map<DateTime, int> yearTaskLoadByDate,
      this.maxTaskLoad = 0,
      this.totalTasksInMonth = 0,
      this.completedTasksInMonth = 0,
      this.priorityFilter,
      this.completionFilter,
      this.hasActiveFilters = false,
      this.showWeekends = true,
      this.firstDayOfWeek = 1,
      this.taskPreviewEnabled = true,
      this.showTaskCount = false,
      this.showCompletionRate = false,
      this.canNavigatePrevious = false,
      this.canNavigateNext = false,
      this.minDate,
      this.maxDate,
      final Map<DateTime, bool> dateLoadingStates = const {},
      final List<String> creatingTaskIds = const [],
      final List<String> updatingTaskIds = const [],
      this.errorMessage,
      this.lastException,
      this.canRetry = false,
      this.lastUpdated,
      this.loadDurationMs = 0,
      this.taskLoadCalculationMs = 0})
      : _taskLoadByDate = taskLoadByDate,
        _yearTaskLoadByDate = yearTaskLoadByDate,
        _dateLoadingStates = dateLoadingStates,
        _creatingTaskIds = creatingTaskIds,
        _updatingTaskIds = updatingTaskIds;

  @override
  final CalendarStatus status;
  @override
  final CalendarOperation currentOperation;
  @override
  final CalendarViewType viewType;
  @override
  final DateTime selectedDate;
  @override
  final DateTime displayMonthDate;
  @override
  final int displayYear;
// Task load data
  final Map<DateTime, int> _taskLoadByDate;
// Task load data
  @override
  Map<DateTime, int> get taskLoadByDate {
    if (_taskLoadByDate is EqualUnmodifiableMapView) return _taskLoadByDate;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_taskLoadByDate);
  }

  final Map<DateTime, int> _yearTaskLoadByDate;
  @override
  Map<DateTime, int> get yearTaskLoadByDate {
    if (_yearTaskLoadByDate is EqualUnmodifiableMapView)
      return _yearTaskLoadByDate;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_yearTaskLoadByDate);
  }

  @override
  @JsonKey()
  final int maxTaskLoad;
  @override
  @JsonKey()
  final int totalTasksInMonth;
  @override
  @JsonKey()
  final int completedTasksInMonth;
// Filtering
  @override
  final Priority? priorityFilter;
  @override
  final bool? completionFilter;
  @override
  @JsonKey()
  final bool hasActiveFilters;
// Calendar settings
  @override
  @JsonKey()
  final bool showWeekends;
  @override
  @JsonKey()
  final int firstDayOfWeek;
// 1 = Monday, 7 = Sunday
  @override
  @JsonKey()
  final bool taskPreviewEnabled;
  @override
  @JsonKey()
  final bool showTaskCount;
  @override
  @JsonKey()
  final bool showCompletionRate;
// Navigation state
  @override
  @JsonKey()
  final bool canNavigatePrevious;
  @override
  @JsonKey()
  final bool canNavigateNext;
  @override
  final DateTime? minDate;
  @override
  final DateTime? maxDate;
// Loading states
  final Map<DateTime, bool> _dateLoadingStates;
// Loading states
  @override
  @JsonKey()
  Map<DateTime, bool> get dateLoadingStates {
    if (_dateLoadingStates is EqualUnmodifiableMapView)
      return _dateLoadingStates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_dateLoadingStates);
  }

  final List<String> _creatingTaskIds;
  @override
  @JsonKey()
  List<String> get creatingTaskIds {
    if (_creatingTaskIds is EqualUnmodifiableListView) return _creatingTaskIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_creatingTaskIds);
  }

  final List<String> _updatingTaskIds;
  @override
  @JsonKey()
  List<String> get updatingTaskIds {
    if (_updatingTaskIds is EqualUnmodifiableListView) return _updatingTaskIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_updatingTaskIds);
  }

// Error handling
  @override
  final String? errorMessage;
  @override
  final Exception? lastException;
  @override
  @JsonKey()
  final bool canRetry;
// Performance metrics
  @override
  final DateTime? lastUpdated;
  @override
  @JsonKey()
  final int loadDurationMs;
  @override
  @JsonKey()
  final int taskLoadCalculationMs;

  @override
  String toString() {
    return 'CalendarState(status: $status, currentOperation: $currentOperation, viewType: $viewType, selectedDate: $selectedDate, displayMonthDate: $displayMonthDate, displayYear: $displayYear, taskLoadByDate: $taskLoadByDate, yearTaskLoadByDate: $yearTaskLoadByDate, maxTaskLoad: $maxTaskLoad, totalTasksInMonth: $totalTasksInMonth, completedTasksInMonth: $completedTasksInMonth, priorityFilter: $priorityFilter, completionFilter: $completionFilter, hasActiveFilters: $hasActiveFilters, showWeekends: $showWeekends, firstDayOfWeek: $firstDayOfWeek, taskPreviewEnabled: $taskPreviewEnabled, showTaskCount: $showTaskCount, showCompletionRate: $showCompletionRate, canNavigatePrevious: $canNavigatePrevious, canNavigateNext: $canNavigateNext, minDate: $minDate, maxDate: $maxDate, dateLoadingStates: $dateLoadingStates, creatingTaskIds: $creatingTaskIds, updatingTaskIds: $updatingTaskIds, errorMessage: $errorMessage, lastException: $lastException, canRetry: $canRetry, lastUpdated: $lastUpdated, loadDurationMs: $loadDurationMs, taskLoadCalculationMs: $taskLoadCalculationMs)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CalendarStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.currentOperation, currentOperation) ||
                other.currentOperation == currentOperation) &&
            (identical(other.viewType, viewType) ||
                other.viewType == viewType) &&
            (identical(other.selectedDate, selectedDate) ||
                other.selectedDate == selectedDate) &&
            (identical(other.displayMonthDate, displayMonthDate) ||
                other.displayMonthDate == displayMonthDate) &&
            (identical(other.displayYear, displayYear) ||
                other.displayYear == displayYear) &&
            const DeepCollectionEquality()
                .equals(other._taskLoadByDate, _taskLoadByDate) &&
            const DeepCollectionEquality()
                .equals(other._yearTaskLoadByDate, _yearTaskLoadByDate) &&
            (identical(other.maxTaskLoad, maxTaskLoad) ||
                other.maxTaskLoad == maxTaskLoad) &&
            (identical(other.totalTasksInMonth, totalTasksInMonth) ||
                other.totalTasksInMonth == totalTasksInMonth) &&
            (identical(other.completedTasksInMonth, completedTasksInMonth) ||
                other.completedTasksInMonth == completedTasksInMonth) &&
            (identical(other.priorityFilter, priorityFilter) ||
                other.priorityFilter == priorityFilter) &&
            (identical(other.completionFilter, completionFilter) ||
                other.completionFilter == completionFilter) &&
            (identical(other.hasActiveFilters, hasActiveFilters) ||
                other.hasActiveFilters == hasActiveFilters) &&
            (identical(other.showWeekends, showWeekends) ||
                other.showWeekends == showWeekends) &&
            (identical(other.firstDayOfWeek, firstDayOfWeek) ||
                other.firstDayOfWeek == firstDayOfWeek) &&
            (identical(other.taskPreviewEnabled, taskPreviewEnabled) ||
                other.taskPreviewEnabled == taskPreviewEnabled) &&
            (identical(other.showTaskCount, showTaskCount) ||
                other.showTaskCount == showTaskCount) &&
            (identical(other.showCompletionRate, showCompletionRate) ||
                other.showCompletionRate == showCompletionRate) &&
            (identical(other.canNavigatePrevious, canNavigatePrevious) ||
                other.canNavigatePrevious == canNavigatePrevious) &&
            (identical(other.canNavigateNext, canNavigateNext) ||
                other.canNavigateNext == canNavigateNext) &&
            (identical(other.minDate, minDate) || other.minDate == minDate) &&
            (identical(other.maxDate, maxDate) || other.maxDate == maxDate) &&
            const DeepCollectionEquality()
                .equals(other._dateLoadingStates, _dateLoadingStates) &&
            const DeepCollectionEquality()
                .equals(other._creatingTaskIds, _creatingTaskIds) &&
            const DeepCollectionEquality()
                .equals(other._updatingTaskIds, _updatingTaskIds) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.lastException, lastException) ||
                other.lastException == lastException) &&
            (identical(other.canRetry, canRetry) ||
                other.canRetry == canRetry) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.loadDurationMs, loadDurationMs) ||
                other.loadDurationMs == loadDurationMs) &&
            (identical(other.taskLoadCalculationMs, taskLoadCalculationMs) ||
                other.taskLoadCalculationMs == taskLoadCalculationMs));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        status,
        currentOperation,
        viewType,
        selectedDate,
        displayMonthDate,
        displayYear,
        const DeepCollectionEquality().hash(_taskLoadByDate),
        const DeepCollectionEquality().hash(_yearTaskLoadByDate),
        maxTaskLoad,
        totalTasksInMonth,
        completedTasksInMonth,
        priorityFilter,
        completionFilter,
        hasActiveFilters,
        showWeekends,
        firstDayOfWeek,
        taskPreviewEnabled,
        showTaskCount,
        showCompletionRate,
        canNavigatePrevious,
        canNavigateNext,
        minDate,
        maxDate,
        const DeepCollectionEquality().hash(_dateLoadingStates),
        const DeepCollectionEquality().hash(_creatingTaskIds),
        const DeepCollectionEquality().hash(_updatingTaskIds),
        errorMessage,
        lastException,
        canRetry,
        lastUpdated,
        loadDurationMs,
        taskLoadCalculationMs
      ]);

  /// Create a copy of CalendarState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CalendarStateImplCopyWith<_$CalendarStateImpl> get copyWith =>
      __$$CalendarStateImplCopyWithImpl<_$CalendarStateImpl>(this, _$identity);
}

abstract class _CalendarState implements CalendarState {
  const factory _CalendarState(
      {required final CalendarStatus status,
      required final CalendarOperation currentOperation,
      required final CalendarViewType viewType,
      required final DateTime selectedDate,
      required final DateTime displayMonthDate,
      required final int displayYear,
      required final Map<DateTime, int> taskLoadByDate,
      required final Map<DateTime, int> yearTaskLoadByDate,
      final int maxTaskLoad,
      final int totalTasksInMonth,
      final int completedTasksInMonth,
      final Priority? priorityFilter,
      final bool? completionFilter,
      final bool hasActiveFilters,
      final bool showWeekends,
      final int firstDayOfWeek,
      final bool taskPreviewEnabled,
      final bool showTaskCount,
      final bool showCompletionRate,
      final bool canNavigatePrevious,
      final bool canNavigateNext,
      final DateTime? minDate,
      final DateTime? maxDate,
      final Map<DateTime, bool> dateLoadingStates,
      final List<String> creatingTaskIds,
      final List<String> updatingTaskIds,
      final String? errorMessage,
      final Exception? lastException,
      final bool canRetry,
      final DateTime? lastUpdated,
      final int loadDurationMs,
      final int taskLoadCalculationMs}) = _$CalendarStateImpl;

  @override
  CalendarStatus get status;
  @override
  CalendarOperation get currentOperation;
  @override
  CalendarViewType get viewType;
  @override
  DateTime get selectedDate;
  @override
  DateTime get displayMonthDate;
  @override
  int get displayYear; // Task load data
  @override
  Map<DateTime, int> get taskLoadByDate;
  @override
  Map<DateTime, int> get yearTaskLoadByDate;
  @override
  int get maxTaskLoad;
  @override
  int get totalTasksInMonth;
  @override
  int get completedTasksInMonth; // Filtering
  @override
  Priority? get priorityFilter;
  @override
  bool? get completionFilter;
  @override
  bool get hasActiveFilters; // Calendar settings
  @override
  bool get showWeekends;
  @override
  int get firstDayOfWeek; // 1 = Monday, 7 = Sunday
  @override
  bool get taskPreviewEnabled;
  @override
  bool get showTaskCount;
  @override
  bool get showCompletionRate; // Navigation state
  @override
  bool get canNavigatePrevious;
  @override
  bool get canNavigateNext;
  @override
  DateTime? get minDate;
  @override
  DateTime? get maxDate; // Loading states
  @override
  Map<DateTime, bool> get dateLoadingStates;
  @override
  List<String> get creatingTaskIds;
  @override
  List<String> get updatingTaskIds; // Error handling
  @override
  String? get errorMessage;
  @override
  Exception? get lastException;
  @override
  bool get canRetry; // Performance metrics
  @override
  DateTime? get lastUpdated;
  @override
  int get loadDurationMs;
  @override
  int get taskLoadCalculationMs;

  /// Create a copy of CalendarState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CalendarStateImplCopyWith<_$CalendarStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
