import 'dart:async';
import 'dart:math';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../../../domain/domain.dart';
import '../../../data/error_handling/error_handling_initializer.dart';
import 'calendar_event.dart';
import 'calendar_state.dart';

/// CalendarBloc manages calendar state and operations for the todo application.
/// 
/// This BLoC handles:
/// - Month/year navigation functionality (Requirements 2.1, 2.4)
/// - Quick task creation from calendar double-click (Requirement 2.2)
/// - Task load data management for heatmap visualization (Requirement 2.4)
/// - Date selection and view switching logic (Requirements 2.1, 2.3)
/// - Drag and drop task operations (Requirement 2.5)
/// - Calendar filtering and preferences
/// - Performance optimization with caching
/// - Comprehensive error handling and logging
@injectable
class CalendarBloc extends Bloc<CalendarEvent, CalendarState> with ErrorHandlingMixin {
  final TaskRepository _taskRepository;
  StreamSubscription? _taskLoadSubscription;
  StreamSubscription? _yearTaskLoadSubscription;
  
  // Cache for performance optimization
  final Map<String, Map<DateTime, int>> _taskLoadCache = {};
  static const Duration _cacheExpiry = Duration(minutes: 3);
  final Map<String, DateTime> _cacheTimestamps = {};

  CalendarBloc({required TaskRepository taskRepository}) 
    : _taskRepository = taskRepository,
      super(CalendarState.initial()) {
    // Register specific event handlers using the correct CalendarEvent types
    on<CalendarEvent>((event, emit) async {
      event.when(
        started: () => _onStarted(emit),
        dateSelected: (date) => _onDateSelected(date, emit),
        monthChanged: (date) => _onMonthChanged(date, emit),
        yearChanged: (year) => _onYearChanged(year, emit),
        todayNavigated: () => _onTodayNavigated(emit),
        dateNavigated: (date) => _onDateNavigated(date, emit),
        quickTaskAdded: (title, date, priority) => _onQuickTaskAdded(title, date, priority, emit),
        bulkQuickTasksAdded: (titles, date, priority) => _onBulkQuickTasksAdded(titles, date, priority, emit),
        viewTypeChanged: (viewType) => _onViewTypeChanged(viewType, emit),
        viewToggled: () => _onViewToggled(emit),
        taskDropped: (taskId, newDate) => _onTaskDropped(taskId, newDate, emit),
        multipleTasksDropped: (taskIds, newDate) => _onMultipleTasksDropped(taskIds, newDate, emit),
        refreshRequested: () => _onRefreshRequested(emit),
        taskLoadRequested: (year, month) => _onTaskLoadRequested(year, month, emit),
        yearTaskLoadRequested: (year) => _onYearTaskLoadRequested(year, emit),
        priorityFilterChanged: (priority) => _onPriorityFilterChanged(priority, emit),
        completionFilterChanged: (isCompleted) => _onCompletionFilterChanged(isCompleted, emit),
        filtersCleared: () => _onFiltersCleared(emit),
        weekendsToggled: (showWeekends) => _onWeekendsToggled(showWeekends, emit),
        firstDayOfWeekChanged: (firstDay) => _onFirstDayOfWeekChanged(firstDay, emit),
        taskPreviewToggled: (enabled) => _onTaskPreviewToggled(enabled, emit),
        keyboardNavigation: (action, targetDate) => _onKeyboardNavigation(action, targetDate, emit),
        retryRequested: () => _onRetryRequested(emit),
        errorCleared: () => _onErrorCleared(emit),
        taskLoadUpdated: (taskLoadByDate) => _onTaskLoadUpdated(taskLoadByDate, emit),
        yearTaskLoadUpdated: (yearTaskLoadByDate) => _onYearTaskLoadUpdated(yearTaskLoadByDate, emit),
        errorOccurred: (message, exception) => _onErrorOccurred(message, exception, emit),
      );
    });
  }

  // ==================== EVENT HANDLERS ====================

  Future<void> _onStarted(Emitter<CalendarState> emit) async {
    final startTime = DateTime.now();
    
    emit(state.copyWith(
      status: CalendarStatus.loading,
      currentOperation: CalendarOperation.loadingData,
    ));

    try {
      await logInfo(
        operation: 'calendarStarted',
        message: 'Initializing calendar',
        context: {
          'view_type': state.viewType.name,
          'selected_date': state.selectedDate.toIso8601String(),
        },
      );

      // Load initial task load data
      final taskLoadData = await _loadTaskLoadData();
      
      final duration = DateTime.now().difference(startTime);
      
      // 检查emit是否仍然有效
      if (!emit.isDone) {
        emit(state.copyWith(
          status: CalendarStatus.success,
          currentOperation: CalendarOperation.none,
          lastUpdated: DateTime.now(),
          loadDurationMs: duration.inMilliseconds,
          taskLoadByDate: taskLoadData,
        ));
      }
      
      recordPerformance('calendarStarted', duration.inMilliseconds);
      
    } catch (error, stackTrace) {
      await logError(
        operation: 'calendarStarted',
        exception: error is Exception ? error : Exception(error.toString()),
        stackTrace: stackTrace,
      );
      
      // 检查emit是否仍然有效
      if (!emit.isDone) {
        emit(state.copyWith(
          status: CalendarStatus.failure,
          currentOperation: CalendarOperation.none,
          errorMessage: 'Failed to initialize calendar: ${error.toString()}',
          lastException: error is Exception ? error : Exception(error.toString()),
          canRetry: true,
        ));
      }
    }
  }

  Future<void> _onDateSelected(DateTime date, Emitter<CalendarState> emit) async {
    await logInfo(
      operation: 'dateSelected',
      message: 'Date selected',
      context: {
        'selected_date': date.toIso8601String(),
        'previous_date': state.selectedDate.toIso8601String(),
      },
    );

    emit(state.copyWith(
      selectedDate: date,
      currentOperation: CalendarOperation.none,
    ));
    
    // If selected date is not in current display month, navigate to that month
    if (date.year != state.displayMonthDate.year ||
        date.month != state.displayMonthDate.month) {
      add(CalendarEvent.monthChanged(DateTime(date.year, date.month, 1)));
    }
  }

  Future<void> _onMonthChanged(DateTime newMonthDate, Emitter<CalendarState> emit) async {
    final startTime = DateTime.now();
    
    // Validate navigation boundaries
    if (!_canNavigateToMonth(newMonthDate)) {
      await logInfo(
        operation: 'monthChanged.blocked',
        message: 'Navigation blocked due to boundaries',
        context: {
          'requested_month': newMonthDate.toIso8601String(),
          'min_date': state.minDate?.toIso8601String(),
          'max_date': state.maxDate?.toIso8601String(),
        },
      );
      return;
    }
    
    if (!emit.isDone) {
      emit(state.copyWith(
        status: CalendarStatus.loading,
        currentOperation: CalendarOperation.navigating,
        displayMonthDate: newMonthDate,
      ));
    }

    try {
      await logInfo(
        operation: 'monthChanged',
        message: 'Navigating to new month',
        context: {
          'new_month': newMonthDate.toIso8601String(),
          'previous_month': state.displayMonthDate.toIso8601String(),
        },
      );

      // Load task load data for new month
      final taskLoadData = await _loadTaskLoadForMonth(newMonthDate.year, newMonthDate.month);
      
      final duration = DateTime.now().difference(startTime);
      
      if (!emit.isDone) {
        emit(state.copyWith(
          status: CalendarStatus.success,
          currentOperation: CalendarOperation.none,
          lastUpdated: DateTime.now(),
          loadDurationMs: duration.inMilliseconds,
          taskLoadByDate: taskLoadData,
        ));
      }
      
      recordPerformance('monthChanged', duration.inMilliseconds);
      
    } catch (error, stackTrace) {
      await logError(
        operation: 'monthChanged',
        exception: error is Exception ? error : Exception(error.toString()),
        stackTrace: stackTrace,
      );
      
      if (!emit.isDone) {
        emit(state.copyWith(
          status: CalendarStatus.failure,
          currentOperation: CalendarOperation.none,
          errorMessage: 'Failed to navigate to month: ${error.toString()}',
          lastException: error is Exception ? error : Exception(error.toString()),
          canRetry: true,
        ));
      }
    }
  }

  Future<void> _onYearChanged(int year, Emitter<CalendarState> emit) async {
    final startTime = DateTime.now();
    
    // Validate navigation boundaries
    if (!_canNavigateToYear(year)) {
      await logInfo(
        operation: 'yearChanged.blocked',
        message: 'Navigation blocked due to boundaries',
        context: {
          'requested_year': year,
          'min_year': state.minDate?.year,
          'max_year': state.maxDate?.year,
        },
      );
      return;
    }
    
    emit(state.copyWith(
      status: CalendarStatus.loading,
      currentOperation: CalendarOperation.navigating,
      displayYear: year,
    ));

    try {
      await logInfo(
        operation: 'yearChanged',
        message: 'Navigating to new year',
        context: {
          'new_year': year,
          'previous_year': state.displayYear,
        },
      );

      // Load task load data for entire year
      await _loadTaskLoadForYear(year);
      
      final duration = DateTime.now().difference(startTime);
      
      emit(state.copyWith(
        status: CalendarStatus.success,
        currentOperation: CalendarOperation.none,
        lastUpdated: DateTime.now(),
        loadDurationMs: duration.inMilliseconds,
      ));
      
      recordPerformance('yearChanged', duration.inMilliseconds);
      
    } catch (error, stackTrace) {
      await logError(
        operation: 'yearChanged',
        exception: error is Exception ? error : Exception(error.toString()),
        stackTrace: stackTrace,
        context: {'new_year': year},
      );
      
      emit(state.copyWith(
        status: CalendarStatus.failure,
        currentOperation: CalendarOperation.none,
        errorMessage: 'Failed to load year data: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        canRetry: true,
      ));
    }
  }

  Future<void> _onTodayNavigated(Emitter<CalendarState> emit) async {
    final today = DateTime.now();
    
    await logInfo(
      operation: 'todayNavigated',
      message: 'Navigating to today',
      context: {'today': today.toIso8601String()},
    );

    // Select today and navigate to current month if needed
    add(CalendarEvent.dateSelected(today));
    
    if (state.viewType == CalendarViewType.year) {
      add(CalendarEvent.yearChanged(today.year));
    } else {
      add(CalendarEvent.monthChanged(DateTime(today.year, today.month, 1)));
    }
  }

  Future<void> _onDateNavigated(DateTime date, Emitter<CalendarState> emit) async {
    await logInfo(
      operation: 'dateNavigated',
      message: 'Navigating to specific date',
      context: {'target_date': date.toIso8601String()},
    );

    // Select the date and navigate to appropriate view
    add(CalendarEvent.dateSelected(date));
    
    if (state.viewType == CalendarViewType.year) {
      add(CalendarEvent.yearChanged(date.year));
    } else {
      add(CalendarEvent.monthChanged(DateTime(date.year, date.month, 1)));
    }
  }

  Future<void> _onQuickTaskAdded(
    String title,
    DateTime date,
    Priority priority,
    Emitter<CalendarState> emit,
  ) async {
    // Validate input
    if (title.trim().isEmpty) {
      emit(state.copyWith(
        status: CalendarStatus.failure,
        errorMessage: 'Task title cannot be empty',
        canRetry: false,
      ));
      return;
    }

    final startTime = DateTime.now();
    final taskId = DateTime.now().millisecondsSinceEpoch.toString();
    
    // Add to creating list
    final updatedCreatingIds = List<String>.from(state.creatingTaskIds);
    updatedCreatingIds.add(taskId);
    
    emit(state.copyWith(
      status: CalendarStatus.creatingTask,
      currentOperation: CalendarOperation.creatingQuickTask,
      creatingTaskIds: updatedCreatingIds,
    ));

    try {
      // 计算合适的创建日期
      final now = DateTime.now();
      DateTime creationDate;
      if (date.isBefore(now)) {
        // 对于过去的截止日期，创建日期设置为截止日期当天的开始时间
        creationDate = DateTime(date.year, date.month, date.day);
      } else {
        // 对于未来的截止日期，创建日期设置为当前时间
        creationDate = now;
      }
      
      final newTask = Task(
        id: taskId,
        title: title.trim(),
        notes: '',
        creationDate: creationDate,
        dueDate: date,
        isCompleted: false,
        priority: priority,
        subtasks: [],
      );

      await _taskRepository.createTask(newTask);
      
      await logInfo(
        operation: 'quickTaskAdded',
        message: 'Quick task created successfully',
        context: {
          'task_id': taskId,
          'title': title,
          'date': date.toIso8601String(),
          'priority': priority.name,
        },
      );
      
      final duration = DateTime.now().difference(startTime);
      recordPerformance('quickTaskAdded', duration.inMilliseconds);
      
    } catch (error, stackTrace) {
      await logError(
        operation: 'quickTaskAdded',
        exception: error is Exception ? error : Exception(error.toString()),
        stackTrace: stackTrace,
        context: {
          'title': title,
          'date': date.toIso8601String(),
          'priority': priority.name,
        },
      );
      
      emit(state.copyWith(
        status: CalendarStatus.failure,
        errorMessage: 'Failed to create quick task: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        canRetry: true,
      ));
    } finally {
      // Remove from creating list
      final clearedCreatingIds = List<String>.from(state.creatingTaskIds);
      clearedCreatingIds.remove(taskId);
      
      emit(state.copyWith(
        status: CalendarStatus.success,
        currentOperation: CalendarOperation.none,
        creatingTaskIds: clearedCreatingIds,
      ));
    }
  }

  Future<void> _onBulkQuickTasksAdded(
    List<String> titles,
    DateTime date,
    Priority priority,
    Emitter<CalendarState> emit,
  ) async {
    // Validate input
    final validTitles = titles.where((title) => title.trim().isNotEmpty).toList();
    if (validTitles.isEmpty) {
      emit(state.copyWith(
        status: CalendarStatus.failure,
        errorMessage: 'No valid task titles provided',
        canRetry: false,
      ));
      return;
    }

    final startTime = DateTime.now();
    
    emit(state.copyWith(
      status: CalendarStatus.creatingTask,
      currentOperation: CalendarOperation.creatingQuickTask,
    ));

    try {
      final tasks = validTitles.asMap().entries.map((entry) => Task(
        id: '${DateTime.now().millisecondsSinceEpoch}_${entry.key}',
        title: entry.value.trim(),
        notes: '',
        creationDate: DateTime.now(),
        dueDate: date,
        isCompleted: false,
        priority: priority,
        subtasks: [],
      )).toList();

      // Create tasks individually (batch creation could be added to repository interface in future)
      for (final task in tasks) {
        await _taskRepository.createTask(task);
      }
      
      await logInfo(
        operation: 'bulkQuickTasksAdded',
        message: 'Bulk quick tasks created successfully',
        context: {
          'task_count': validTitles.length,
          'original_count': titles.length,
          'date': date.toIso8601String(),
          'priority': priority.name,
        },
      );
      
      final duration = DateTime.now().difference(startTime);
      recordPerformance('bulkQuickTasksAdded', duration.inMilliseconds);
      
    } catch (error, stackTrace) {
      await logError(
        operation: 'bulkQuickTasksAdded',
        exception: error is Exception ? error : Exception(error.toString()),
        stackTrace: stackTrace,
        context: {
          'task_count': validTitles.length,
          'date': date.toIso8601String(),
          'priority': priority.name,
        },
      );
      
      emit(state.copyWith(
        status: CalendarStatus.failure,
        errorMessage: 'Failed to create bulk tasks: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        canRetry: true,
      ));
    } finally {
      emit(state.copyWith(
        status: CalendarStatus.success,
        currentOperation: CalendarOperation.none,
      ));
    }
  }

  Future<void> _onViewTypeChanged(CalendarViewType viewType, Emitter<CalendarState> emit) async {
    await logInfo(
      operation: 'viewTypeChanged',
      message: 'Calendar view type changed',
      context: {
        'new_view': viewType.name,
        'previous_view': state.viewType.name,
      },
    );

    emit(state.copyWith(
      viewType: viewType,
      currentOperation: CalendarOperation.changingView,
    ));

    // Load appropriate data for new view
    if (viewType == CalendarViewType.year) {
      add(CalendarEvent.yearTaskLoadRequested(state.displayYear));
    } else {
      add(CalendarEvent.taskLoadRequested(
        year: state.displayMonthDate.year,
        month: state.displayMonthDate.month,
      ));
    }

    emit(state.copyWith(currentOperation: CalendarOperation.none));
  }

  Future<void> _onViewToggled(Emitter<CalendarState> emit) async {
    final newViewType = state.viewType == CalendarViewType.month 
        ? CalendarViewType.year 
        : CalendarViewType.month;
    
    add(CalendarEvent.viewTypeChanged(newViewType));
  }

  Future<void> _onTaskDropped(
    String taskId,
    DateTime newDate,
    Emitter<CalendarState> emit,
  ) async {
    final startTime = DateTime.now();
    
    // Add to updating list
    final updatedUpdatingIds = List<String>.from(state.updatingTaskIds);
    updatedUpdatingIds.add(taskId);
    
    emit(state.copyWith(
      status: CalendarStatus.updatingTask,
      currentOperation: CalendarOperation.droppingTask,
      updatingTaskIds: updatedUpdatingIds,
    ));

    try {
      await logInfo(
        operation: 'taskDropped',
        message: 'Task dropped on calendar date',
        context: {
          'task_id': taskId,
          'new_date': newDate.toIso8601String(),
        },
      );
      
      // Find the task by ID and update its due date
      final taskToUpdate = await _findTaskById(taskId);
      
      // Update the task with new due date
      final updatedTask = taskToUpdate.copyWith(dueDate: newDate);
      await _taskRepository.updateTask(updatedTask);
      
      final duration = DateTime.now().difference(startTime);
      recordPerformance('taskDropped', duration.inMilliseconds);
      
    } catch (error, stackTrace) {
      await logError(
        operation: 'taskDropped',
        exception: error is Exception ? error : Exception(error.toString()),
        stackTrace: stackTrace,
        context: {
          'task_id': taskId,
          'new_date': newDate.toIso8601String(),
        },
      );
      
      // Check if emit is still valid before emitting error state
      if (!emit.isDone) {
        emit(state.copyWith(
          status: CalendarStatus.failure,
          errorMessage: 'Failed to move task: ${error.toString()}',
          lastException: error is Exception ? error : Exception(error.toString()),
          canRetry: true,
          updatingTaskIds: state.updatingTaskIds.where((id) => id != taskId).toList(),
        ));
      }
    }
  }

  Future<void> _onMultipleTasksDropped(
    List<String> taskIds,
    DateTime newDate,
    Emitter<CalendarState> emit,
  ) async {
    final startTime = DateTime.now();
    
    emit(state.copyWith(
      status: CalendarStatus.updatingTask,
      currentOperation: CalendarOperation.droppingTask,
      updatingTaskIds: taskIds,
    ));

    try {
      // Update all tasks to new date
      for (final taskId in taskIds) {
        try {
          final taskToUpdate = await _findTaskById(taskId);
          
          // Update the task with new due date
          final updatedTask = taskToUpdate.copyWith(dueDate: newDate);
          await _taskRepository.updateTask(updatedTask);
        } catch (taskError) {
          // Log individual task update failure but continue with others
          await logError(
            operation: 'multipleTasksDropped.individualTask',
            exception: taskError is Exception ? taskError : Exception(taskError.toString()),
            context: {
              'task_id': taskId,
              'new_date': newDate.toIso8601String(),
            },
          );
        }
      }
      
      await logInfo(
        operation: 'multipleTasksDropped',
        message: 'Multiple tasks dropped on calendar date',
        context: {
          'task_count': taskIds.length,
          'new_date': newDate.toIso8601String(),
        },
      );
      
      final duration = DateTime.now().difference(startTime);
      recordPerformance('multipleTasksDropped', duration.inMilliseconds);
      
    } catch (error, stackTrace) {
      await logError(
        operation: 'multipleTasksDropped',
        exception: error is Exception ? error : Exception(error.toString()),
        stackTrace: stackTrace,
        context: {
          'task_count': taskIds.length,
          'new_date': newDate.toIso8601String(),
        },
      );
      
      // Check if emit is still valid before emitting error state
      if (!emit.isDone) {
        emit(state.copyWith(
          status: CalendarStatus.failure,
          errorMessage: 'Failed to move tasks: ${error.toString()}',
          lastException: error is Exception ? error : Exception(error.toString()),
          canRetry: true,
          updatingTaskIds: [],
        ));
      }
    }
  }

  Future<void> _onRefreshRequested(Emitter<CalendarState> emit) async {
    emit(state.copyWith(
      status: CalendarStatus.refreshing,
      currentOperation: CalendarOperation.refreshing,
    ));

    // Clear cache to force fresh data
    _clearCache();
    
    // Reload current view data
    if (state.viewType == CalendarViewType.year) {
      add(CalendarEvent.yearTaskLoadRequested(state.displayYear));
    } else {
      add(CalendarEvent.taskLoadRequested(
        year: state.displayMonthDate.year,
        month: state.displayMonthDate.month,
      ));
    }
  }

  Future<void> _onTaskLoadRequested(
    int year,
    int month,
    Emitter<CalendarState> emit,
  ) async {
    await _loadTaskLoadForMonth(year, month);
  }

  Future<void> _onYearTaskLoadRequested(int year, Emitter<CalendarState> emit) async {
    await _loadTaskLoadForYear(year);
  }

  void _onPriorityFilterChanged(Priority? priority, Emitter<CalendarState> emit) {
    emit(state.copyWith(
      priorityFilter: priority,
      hasActiveFilters: priority != null || state.completionFilter != null,
      currentOperation: CalendarOperation.filtering,
    ));

    // Apply filter to task load data by reloading with current filters
    _applyFiltersToTaskLoad();
    emit(state.copyWith(currentOperation: CalendarOperation.none));
  }

  void _onCompletionFilterChanged(bool? isCompleted, Emitter<CalendarState> emit) {
    emit(state.copyWith(
      completionFilter: isCompleted,
      hasActiveFilters: state.priorityFilter != null || isCompleted != null,
      currentOperation: CalendarOperation.filtering,
    ));

    // Apply filter to task load data by reloading with current filters
    _applyFiltersToTaskLoad();
    emit(state.copyWith(currentOperation: CalendarOperation.none));
  }

  void _onFiltersCleared(Emitter<CalendarState> emit) {
    emit(state.copyWith(
      priorityFilter: null,
      completionFilter: null,
      hasActiveFilters: false,
      currentOperation: CalendarOperation.filtering,
    ));

    // Clear filters from task load data by reloading without filters
    _applyFiltersToTaskLoad();
    emit(state.copyWith(currentOperation: CalendarOperation.none));
  }

  void _onWeekendsToggled(bool showWeekends, Emitter<CalendarState> emit) {
    emit(state.copyWith(showWeekends: showWeekends));
  }

  void _onFirstDayOfWeekChanged(int firstDay, Emitter<CalendarState> emit) {
    if (firstDay >= 1 && firstDay <= 7) {
      emit(state.copyWith(firstDayOfWeek: firstDay));
    }
  }

  void _onTaskPreviewToggled(bool enabled, Emitter<CalendarState> emit) {
    emit(state.copyWith(taskPreviewEnabled: enabled));
  }

  Future<void> _onKeyboardNavigation(
    CalendarKeyAction action,
    DateTime? targetDate,
    Emitter<CalendarState> emit,
  ) async {
    switch (action) {
      case CalendarKeyAction.previousDay:
        final prevDay = state.selectedDate.subtract(const Duration(days: 1));
        add(CalendarEvent.dateSelected(prevDay));
        break;
      case CalendarKeyAction.nextDay:
        final nextDay = state.selectedDate.add(const Duration(days: 1));
        add(CalendarEvent.dateSelected(nextDay));
        break;
      case CalendarKeyAction.previousWeek:
        final prevWeek = state.selectedDate.subtract(const Duration(days: 7));
        add(CalendarEvent.dateSelected(prevWeek));
        break;
      case CalendarKeyAction.nextWeek:
        final nextWeek = state.selectedDate.add(const Duration(days: 7));
        add(CalendarEvent.dateSelected(nextWeek));
        break;
      case CalendarKeyAction.previousMonth:
        final prevMonth = DateTime(
          state.displayMonthDate.year,
          state.displayMonthDate.month - 1,
          1,
        );
        add(CalendarEvent.monthChanged(prevMonth));
        break;
      case CalendarKeyAction.nextMonth:
        final nextMonth = DateTime(
          state.displayMonthDate.year,
          state.displayMonthDate.month + 1,
          1,
        );
        add(CalendarEvent.monthChanged(nextMonth));
        break;
      case CalendarKeyAction.previousYear:
        add(CalendarEvent.yearChanged(state.displayYear - 1));
        break;
      case CalendarKeyAction.nextYear:
        add(CalendarEvent.yearChanged(state.displayYear + 1));
        break;
      case CalendarKeyAction.goToToday:
        add(CalendarEvent.todayNavigated());
        break;
      case CalendarKeyAction.selectDate:
        if (targetDate != null) {
          add(CalendarEvent.dateSelected(targetDate));
        }
        break;
      case CalendarKeyAction.quickAddTask:
        // This would typically trigger a UI dialog
        // The actual task creation happens via quickTaskAdded event
        break;
    }
  }

  Future<void> _onRetryRequested(Emitter<CalendarState> emit) async {
    // Clear error state and retry the last operation
    emit(state.copyWith(
      status: CalendarStatus.loading,
      errorMessage: null,
      lastException: null,
      canRetry: false,
    ));
    
    // Retry loading data
    add(CalendarEvent.started());
  }

  void _onErrorCleared(Emitter<CalendarState> emit) {
    emit(state.copyWith(
      status: CalendarStatus.success,
      errorMessage: null,
      lastException: null,
      canRetry: false,
    ));
  }

  void _onTaskLoadUpdated(
    Map<DateTime, int> taskLoadByDate,
    Emitter<CalendarState> emit,
  ) {
    // Apply filters to the task load data
    final filteredTaskLoad = _applyFiltersToTaskLoadData(taskLoadByDate);
    
    final maxLoad = filteredTaskLoad.values.isEmpty ? 0 : filteredTaskLoad.values.reduce(max);
    final totalTasks = filteredTaskLoad.values.fold(0, (sum, load) => sum + load);
    
    emit(state.copyWith(
      taskLoadByDate: filteredTaskLoad,
      maxTaskLoad: maxLoad,
      totalTasksInMonth: totalTasks,
      lastUpdated: DateTime.now(),
    ));
  }

  void _onYearTaskLoadUpdated(
    Map<DateTime, int> yearTaskLoadByDate,
    Emitter<CalendarState> emit,
  ) {
    // Apply filters to the year task load data
    final filteredYearTaskLoad = _applyFiltersToTaskLoadData(yearTaskLoadByDate);
    
    final maxLoad = filteredYearTaskLoad.values.isEmpty ? 0 : filteredYearTaskLoad.values.reduce(max);
    
    emit(state.copyWith(
      yearTaskLoadByDate: filteredYearTaskLoad,
      maxTaskLoad: maxLoad,
      lastUpdated: DateTime.now(),
    ));
  }

  void _onErrorOccurred(
    String message,
    Exception? exception,
    Emitter<CalendarState> emit,
  ) {
    emit(state.copyWith(
      status: CalendarStatus.failure,
      currentOperation: CalendarOperation.none,
      errorMessage: message,
      lastException: exception,
      canRetry: true,
    ));
  }

  // ==================== HELPER METHODS ====================

  Future<Map<DateTime, int>> _loadTaskLoadData() async {
    try {
      if (state.viewType == CalendarViewType.year) {
        return await _loadTaskLoadForYear(state.displayYear);
      } else {
        return await _loadTaskLoadForMonth(
          state.displayMonthDate.year,
          state.displayMonthDate.month,
        );
      }
    } catch (error) {
      // Log error but don't fail the entire initialization
      await logError(
        operation: 'loadTaskLoadData',
        exception: error is Exception ? error : Exception(error.toString()),
        context: {
          'view_type': state.viewType.name,
          'display_year': state.displayYear,
          'display_month': state.displayMonthDate.month,
        },
      );
      return {};
    }
  }

  Future<Map<DateTime, int>> _loadTaskLoadForMonth(int year, int month) async {
    final cacheKey = '${year}_$month';
    
    // Check cache first
    if (_isCacheValid(cacheKey)) {
      final cachedData = _taskLoadCache[cacheKey]!;
      return cachedData;
    }

    try {
      await _taskLoadSubscription?.cancel();
      
      // For testing, use first value from stream
      final taskLoadMap = await _taskRepository
          .watchTaskLoadForMonth(year: year, month: month)
          .first;
      
      // Update cache
      _updateCache(cacheKey, taskLoadMap);
      
      // Also calculate and update completed tasks count for the month
      _calculateCompletedTasksForMonth(year, month);
      
      return taskLoadMap;
      
    } catch (error) {
      if (!isClosed) {
        add(CalendarEvent.errorOccurred(
          message: 'Failed to subscribe to task data: ${error.toString()}',
          exception: error is Exception ? error : Exception(error.toString()),
        ));
      }
      return {};
    }
  }

  Future<Map<DateTime, int>> _loadTaskLoadForYear(int year) async {
    final cacheKey = 'year_$year';
    
    // Check cache first
    if (_isCacheValid(cacheKey)) {
      final cachedData = _taskLoadCache[cacheKey]!;
      return cachedData;
    }

    try {
      await _yearTaskLoadSubscription?.cancel();
      
      // Load task load for entire year (aggregate monthly data)
      final yearTaskLoad = <DateTime, int>{};
      
      for (int month = 1; month <= 12; month++) {
        final monthTaskLoad = await _taskRepository
            .watchTaskLoadForMonth(year: year, month: month)
            .first;
        
        yearTaskLoad.addAll(monthTaskLoad);
      }
      
      // Update cache
      _updateCache(cacheKey, yearTaskLoad);
      
      return yearTaskLoad;
    } catch (error) {
      if (!isClosed) {
        add(CalendarEvent.errorOccurred(
          message: 'Failed to load year task data: ${error.toString()}',
          exception: error is Exception ? error : Exception(error.toString()),
        ));
      }
      return {};
    }
  }

  void _updateCache(String key, Map<DateTime, int> data) {
    _taskLoadCache[key] = data;
    _cacheTimestamps[key] = DateTime.now();
  }

  bool _isCacheValid(String key) {
    if (!_taskLoadCache.containsKey(key) || !_cacheTimestamps.containsKey(key)) {
      return false;
    }
    
    final cacheTime = _cacheTimestamps[key]!;
    return DateTime.now().difference(cacheTime) < _cacheExpiry;
  }

  void _clearCache() {
    _taskLoadCache.clear();
    _cacheTimestamps.clear();
  }

  /// Apply current filters to task load data by reloading
  void _applyFiltersToTaskLoad() {
    // Clear cache to force reload with filters
    _clearCache();
    
    // Reload current view data with filters applied
    if (state.viewType == CalendarViewType.year) {
      add(CalendarEvent.yearTaskLoadRequested(state.displayYear));
    } else {
      add(CalendarEvent.taskLoadRequested(
        year: state.displayMonthDate.year,
        month: state.displayMonthDate.month,
      ));
    }
  }

  /// Filter task load data based on current filters
  /// Note: This is a client-side filtering approach since the repository
  /// doesn't currently support filtered task load queries
  Map<DateTime, int> _applyFiltersToTaskLoadData(Map<DateTime, int> originalData) {
    // If no filters are active, return original data
    if (!state.hasActiveFilters) {
      return originalData;
    }

    // For now, we return the original data since we don't have access to
    // individual task details in the task load data structure.
    // In a real implementation, you might want to:
    // 1. Add filtered task load methods to the repository
    // 2. Or fetch individual tasks and calculate load client-side
    // 3. Or modify the task load calculation to support filters
    
    return originalData;
  }

  /// Helper method to find a task by ID across multiple dates
  /// This is a workaround until we have a direct getTaskById method
  Future<Task> _findTaskById(String taskId) async {
    // Try to find the task in recent dates around the selected date
    final searchDates = [
      state.selectedDate,
      state.selectedDate.subtract(const Duration(days: 1)),
      state.selectedDate.add(const Duration(days: 1)),
      DateTime.now(),
    ];

    for (final date in searchDates) {
      try {
        final tasks = await _taskRepository
            .watchTasksByDate(date)
            .first
            .timeout(const Duration(seconds: 2));
        
        final foundTask = tasks.where((task) => task.id == taskId).isNotEmpty 
            ? tasks.where((task) => task.id == taskId).first 
            : null;
        if (foundTask != null) {
          return foundTask;
        }
      } catch (e) {
        // Continue searching in other dates
        continue;
      }
    }
    
    throw Exception('Task with ID $taskId not found');
  }

  /// Check if navigation to a specific month is allowed
  bool _canNavigateToMonth(DateTime monthDate) {
    if (state.minDate != null && monthDate.isBefore(state.minDate!)) {
      return false;
    }
    if (state.maxDate != null && monthDate.isAfter(state.maxDate!)) {
      return false;
    }
    return true;
  }

  /// Check if navigation to a specific year is allowed
  bool _canNavigateToYear(int year) {
    if (state.minDate != null && year < state.minDate!.year) {
      return false;
    }
    if (state.maxDate != null && year > state.maxDate!.year) {
      return false;
    }
    return true;
  }

  /// Calculate completed tasks count for a specific month
  /// This is an approximation since we don't have direct access to completed task counts
  /// in the task load data structure
  Future<void> _calculateCompletedTasksForMonth(int year, int month) async {
    try {
      // This is a simplified calculation - in a real implementation,
      // you might want to add a separate method to the repository
      // to get completed task counts, or modify the task load calculation
      // to include completion information
      
      // For now, we'll estimate based on the assumption that completed tasks
      // contribute less to the task load, but this is not accurate
      // A proper implementation would require repository changes
      
      // Placeholder: set to 0 for now
      // In a real implementation, you'd query completed tasks for the month
      const completedCount = 0;
      
      // This would be updated in the state when we have the actual data
      // emit(state.copyWith(completedTasksInMonth: completedCount));
      
    } catch (error) {
      // Log error but don't fail the main operation
      await logError(
        operation: 'calculateCompletedTasksForMonth',
        exception: error is Exception ? error : Exception(error.toString()),
        context: {
          'year': year,
          'month': month,
        },
      );
    }
  }

  /// Update task load data directly without triggering events
  void _updateTaskLoadData(Map<DateTime, int> taskLoadData) {
    // This method should not be called directly from event handlers
    // Use the event-based approach instead
  }

  /// Update year task load data directly without triggering events
  void _updateYearTaskLoadData(Map<DateTime, int> yearTaskLoadData) {
    // This method should not be called directly from event handlers
    // Use the event-based approach instead
  }

  @override
  Future<void> close() {
    _taskLoadSubscription?.cancel();
    _yearTaskLoadSubscription?.cancel();
    _clearCache();
    return super.close();
  }
}