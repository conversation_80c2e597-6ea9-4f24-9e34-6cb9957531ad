// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'calendar_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CalendarEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CalendarEventCopyWith<$Res> {
  factory $CalendarEventCopyWith(
          CalendarEvent value, $Res Function(CalendarEvent) then) =
      _$CalendarEventCopyWithImpl<$Res, CalendarEvent>;
}

/// @nodoc
class _$CalendarEventCopyWithImpl<$Res, $Val extends CalendarEvent>
    implements $CalendarEventCopyWith<$Res> {
  _$CalendarEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$StartedImplCopyWith<$Res> {
  factory _$$StartedImplCopyWith(
          _$StartedImpl value, $Res Function(_$StartedImpl) then) =
      __$$StartedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$StartedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$StartedImpl>
    implements _$$StartedImplCopyWith<$Res> {
  __$$StartedImplCopyWithImpl(
      _$StartedImpl _value, $Res Function(_$StartedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$StartedImpl implements _Started {
  const _$StartedImpl();

  @override
  String toString() {
    return 'CalendarEvent.started()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$StartedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return started();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return started?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return started(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return started?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (started != null) {
      return started(this);
    }
    return orElse();
  }
}

abstract class _Started implements CalendarEvent {
  const factory _Started() = _$StartedImpl;
}

/// @nodoc
abstract class _$$DateSelectedImplCopyWith<$Res> {
  factory _$$DateSelectedImplCopyWith(
          _$DateSelectedImpl value, $Res Function(_$DateSelectedImpl) then) =
      __$$DateSelectedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class __$$DateSelectedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$DateSelectedImpl>
    implements _$$DateSelectedImplCopyWith<$Res> {
  __$$DateSelectedImplCopyWithImpl(
      _$DateSelectedImpl _value, $Res Function(_$DateSelectedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
  }) {
    return _then(_$DateSelectedImpl(
      null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$DateSelectedImpl implements _DateSelected {
  const _$DateSelectedImpl(this.date);

  @override
  final DateTime date;

  @override
  String toString() {
    return 'CalendarEvent.dateSelected(date: $date)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DateSelectedImpl &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DateSelectedImplCopyWith<_$DateSelectedImpl> get copyWith =>
      __$$DateSelectedImplCopyWithImpl<_$DateSelectedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return dateSelected(date);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return dateSelected?.call(date);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (dateSelected != null) {
      return dateSelected(date);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return dateSelected(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return dateSelected?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (dateSelected != null) {
      return dateSelected(this);
    }
    return orElse();
  }
}

abstract class _DateSelected implements CalendarEvent {
  const factory _DateSelected(final DateTime date) = _$DateSelectedImpl;

  DateTime get date;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DateSelectedImplCopyWith<_$DateSelectedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MonthChangedImplCopyWith<$Res> {
  factory _$$MonthChangedImplCopyWith(
          _$MonthChangedImpl value, $Res Function(_$MonthChangedImpl) then) =
      __$$MonthChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime newMonthDate});
}

/// @nodoc
class __$$MonthChangedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$MonthChangedImpl>
    implements _$$MonthChangedImplCopyWith<$Res> {
  __$$MonthChangedImplCopyWithImpl(
      _$MonthChangedImpl _value, $Res Function(_$MonthChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? newMonthDate = null,
  }) {
    return _then(_$MonthChangedImpl(
      null == newMonthDate
          ? _value.newMonthDate
          : newMonthDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$MonthChangedImpl implements _MonthChanged {
  const _$MonthChangedImpl(this.newMonthDate);

  @override
  final DateTime newMonthDate;

  @override
  String toString() {
    return 'CalendarEvent.monthChanged(newMonthDate: $newMonthDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonthChangedImpl &&
            (identical(other.newMonthDate, newMonthDate) ||
                other.newMonthDate == newMonthDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, newMonthDate);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MonthChangedImplCopyWith<_$MonthChangedImpl> get copyWith =>
      __$$MonthChangedImplCopyWithImpl<_$MonthChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return monthChanged(newMonthDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return monthChanged?.call(newMonthDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (monthChanged != null) {
      return monthChanged(newMonthDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return monthChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return monthChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (monthChanged != null) {
      return monthChanged(this);
    }
    return orElse();
  }
}

abstract class _MonthChanged implements CalendarEvent {
  const factory _MonthChanged(final DateTime newMonthDate) = _$MonthChangedImpl;

  DateTime get newMonthDate;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MonthChangedImplCopyWith<_$MonthChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$YearChangedImplCopyWith<$Res> {
  factory _$$YearChangedImplCopyWith(
          _$YearChangedImpl value, $Res Function(_$YearChangedImpl) then) =
      __$$YearChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int year});
}

/// @nodoc
class __$$YearChangedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$YearChangedImpl>
    implements _$$YearChangedImplCopyWith<$Res> {
  __$$YearChangedImplCopyWithImpl(
      _$YearChangedImpl _value, $Res Function(_$YearChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = null,
  }) {
    return _then(_$YearChangedImpl(
      null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$YearChangedImpl implements _YearChanged {
  const _$YearChangedImpl(this.year);

  @override
  final int year;

  @override
  String toString() {
    return 'CalendarEvent.yearChanged(year: $year)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$YearChangedImpl &&
            (identical(other.year, year) || other.year == year));
  }

  @override
  int get hashCode => Object.hash(runtimeType, year);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$YearChangedImplCopyWith<_$YearChangedImpl> get copyWith =>
      __$$YearChangedImplCopyWithImpl<_$YearChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return yearChanged(year);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return yearChanged?.call(year);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (yearChanged != null) {
      return yearChanged(year);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return yearChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return yearChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (yearChanged != null) {
      return yearChanged(this);
    }
    return orElse();
  }
}

abstract class _YearChanged implements CalendarEvent {
  const factory _YearChanged(final int year) = _$YearChangedImpl;

  int get year;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$YearChangedImplCopyWith<_$YearChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TodayNavigatedImplCopyWith<$Res> {
  factory _$$TodayNavigatedImplCopyWith(_$TodayNavigatedImpl value,
          $Res Function(_$TodayNavigatedImpl) then) =
      __$$TodayNavigatedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$TodayNavigatedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$TodayNavigatedImpl>
    implements _$$TodayNavigatedImplCopyWith<$Res> {
  __$$TodayNavigatedImplCopyWithImpl(
      _$TodayNavigatedImpl _value, $Res Function(_$TodayNavigatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$TodayNavigatedImpl implements _TodayNavigated {
  const _$TodayNavigatedImpl();

  @override
  String toString() {
    return 'CalendarEvent.todayNavigated()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$TodayNavigatedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return todayNavigated();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return todayNavigated?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (todayNavigated != null) {
      return todayNavigated();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return todayNavigated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return todayNavigated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (todayNavigated != null) {
      return todayNavigated(this);
    }
    return orElse();
  }
}

abstract class _TodayNavigated implements CalendarEvent {
  const factory _TodayNavigated() = _$TodayNavigatedImpl;
}

/// @nodoc
abstract class _$$DateNavigatedImplCopyWith<$Res> {
  factory _$$DateNavigatedImplCopyWith(
          _$DateNavigatedImpl value, $Res Function(_$DateNavigatedImpl) then) =
      __$$DateNavigatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class __$$DateNavigatedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$DateNavigatedImpl>
    implements _$$DateNavigatedImplCopyWith<$Res> {
  __$$DateNavigatedImplCopyWithImpl(
      _$DateNavigatedImpl _value, $Res Function(_$DateNavigatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
  }) {
    return _then(_$DateNavigatedImpl(
      null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$DateNavigatedImpl implements _DateNavigated {
  const _$DateNavigatedImpl(this.date);

  @override
  final DateTime date;

  @override
  String toString() {
    return 'CalendarEvent.dateNavigated(date: $date)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DateNavigatedImpl &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DateNavigatedImplCopyWith<_$DateNavigatedImpl> get copyWith =>
      __$$DateNavigatedImplCopyWithImpl<_$DateNavigatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return dateNavigated(date);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return dateNavigated?.call(date);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (dateNavigated != null) {
      return dateNavigated(date);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return dateNavigated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return dateNavigated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (dateNavigated != null) {
      return dateNavigated(this);
    }
    return orElse();
  }
}

abstract class _DateNavigated implements CalendarEvent {
  const factory _DateNavigated(final DateTime date) = _$DateNavigatedImpl;

  DateTime get date;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DateNavigatedImplCopyWith<_$DateNavigatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$QuickTaskAddedImplCopyWith<$Res> {
  factory _$$QuickTaskAddedImplCopyWith(_$QuickTaskAddedImpl value,
          $Res Function(_$QuickTaskAddedImpl) then) =
      __$$QuickTaskAddedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String title, DateTime date, Priority priority});
}

/// @nodoc
class __$$QuickTaskAddedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$QuickTaskAddedImpl>
    implements _$$QuickTaskAddedImplCopyWith<$Res> {
  __$$QuickTaskAddedImplCopyWithImpl(
      _$QuickTaskAddedImpl _value, $Res Function(_$QuickTaskAddedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = null,
    Object? date = null,
    Object? priority = null,
  }) {
    return _then(_$QuickTaskAddedImpl(
      title: null == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as Priority,
    ));
  }
}

/// @nodoc

class _$QuickTaskAddedImpl implements _QuickTaskAdded {
  const _$QuickTaskAddedImpl(
      {required this.title,
      required this.date,
      this.priority = Priority.urgentImportant});

  @override
  final String title;
  @override
  final DateTime date;
  @override
  @JsonKey()
  final Priority priority;

  @override
  String toString() {
    return 'CalendarEvent.quickTaskAdded(title: $title, date: $date, priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuickTaskAddedImpl &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.priority, priority) ||
                other.priority == priority));
  }

  @override
  int get hashCode => Object.hash(runtimeType, title, date, priority);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QuickTaskAddedImplCopyWith<_$QuickTaskAddedImpl> get copyWith =>
      __$$QuickTaskAddedImplCopyWithImpl<_$QuickTaskAddedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return quickTaskAdded(title, date, priority);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return quickTaskAdded?.call(title, date, priority);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (quickTaskAdded != null) {
      return quickTaskAdded(title, date, priority);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return quickTaskAdded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return quickTaskAdded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (quickTaskAdded != null) {
      return quickTaskAdded(this);
    }
    return orElse();
  }
}

abstract class _QuickTaskAdded implements CalendarEvent {
  const factory _QuickTaskAdded(
      {required final String title,
      required final DateTime date,
      final Priority priority}) = _$QuickTaskAddedImpl;

  String get title;
  DateTime get date;
  Priority get priority;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QuickTaskAddedImplCopyWith<_$QuickTaskAddedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BulkQuickTasksAddedImplCopyWith<$Res> {
  factory _$$BulkQuickTasksAddedImplCopyWith(_$BulkQuickTasksAddedImpl value,
          $Res Function(_$BulkQuickTasksAddedImpl) then) =
      __$$BulkQuickTasksAddedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> titles, DateTime date, Priority priority});
}

/// @nodoc
class __$$BulkQuickTasksAddedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$BulkQuickTasksAddedImpl>
    implements _$$BulkQuickTasksAddedImplCopyWith<$Res> {
  __$$BulkQuickTasksAddedImplCopyWithImpl(_$BulkQuickTasksAddedImpl _value,
      $Res Function(_$BulkQuickTasksAddedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? titles = null,
    Object? date = null,
    Object? priority = null,
  }) {
    return _then(_$BulkQuickTasksAddedImpl(
      titles: null == titles
          ? _value._titles
          : titles // ignore: cast_nullable_to_non_nullable
              as List<String>,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      priority: null == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as Priority,
    ));
  }
}

/// @nodoc

class _$BulkQuickTasksAddedImpl implements _BulkQuickTasksAdded {
  const _$BulkQuickTasksAddedImpl(
      {required final List<String> titles,
      required this.date,
      this.priority = Priority.urgentImportant})
      : _titles = titles;

  final List<String> _titles;
  @override
  List<String> get titles {
    if (_titles is EqualUnmodifiableListView) return _titles;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_titles);
  }

  @override
  final DateTime date;
  @override
  @JsonKey()
  final Priority priority;

  @override
  String toString() {
    return 'CalendarEvent.bulkQuickTasksAdded(titles: $titles, date: $date, priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BulkQuickTasksAddedImpl &&
            const DeepCollectionEquality().equals(other._titles, _titles) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.priority, priority) ||
                other.priority == priority));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_titles), date, priority);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BulkQuickTasksAddedImplCopyWith<_$BulkQuickTasksAddedImpl> get copyWith =>
      __$$BulkQuickTasksAddedImplCopyWithImpl<_$BulkQuickTasksAddedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return bulkQuickTasksAdded(titles, date, priority);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return bulkQuickTasksAdded?.call(titles, date, priority);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (bulkQuickTasksAdded != null) {
      return bulkQuickTasksAdded(titles, date, priority);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return bulkQuickTasksAdded(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return bulkQuickTasksAdded?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (bulkQuickTasksAdded != null) {
      return bulkQuickTasksAdded(this);
    }
    return orElse();
  }
}

abstract class _BulkQuickTasksAdded implements CalendarEvent {
  const factory _BulkQuickTasksAdded(
      {required final List<String> titles,
      required final DateTime date,
      final Priority priority}) = _$BulkQuickTasksAddedImpl;

  List<String> get titles;
  DateTime get date;
  Priority get priority;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BulkQuickTasksAddedImplCopyWith<_$BulkQuickTasksAddedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ViewTypeChangedImplCopyWith<$Res> {
  factory _$$ViewTypeChangedImplCopyWith(_$ViewTypeChangedImpl value,
          $Res Function(_$ViewTypeChangedImpl) then) =
      __$$ViewTypeChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CalendarViewType viewType});
}

/// @nodoc
class __$$ViewTypeChangedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$ViewTypeChangedImpl>
    implements _$$ViewTypeChangedImplCopyWith<$Res> {
  __$$ViewTypeChangedImplCopyWithImpl(
      _$ViewTypeChangedImpl _value, $Res Function(_$ViewTypeChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? viewType = null,
  }) {
    return _then(_$ViewTypeChangedImpl(
      null == viewType
          ? _value.viewType
          : viewType // ignore: cast_nullable_to_non_nullable
              as CalendarViewType,
    ));
  }
}

/// @nodoc

class _$ViewTypeChangedImpl implements _ViewTypeChanged {
  const _$ViewTypeChangedImpl(this.viewType);

  @override
  final CalendarViewType viewType;

  @override
  String toString() {
    return 'CalendarEvent.viewTypeChanged(viewType: $viewType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ViewTypeChangedImpl &&
            (identical(other.viewType, viewType) ||
                other.viewType == viewType));
  }

  @override
  int get hashCode => Object.hash(runtimeType, viewType);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ViewTypeChangedImplCopyWith<_$ViewTypeChangedImpl> get copyWith =>
      __$$ViewTypeChangedImplCopyWithImpl<_$ViewTypeChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return viewTypeChanged(viewType);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return viewTypeChanged?.call(viewType);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (viewTypeChanged != null) {
      return viewTypeChanged(viewType);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return viewTypeChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return viewTypeChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (viewTypeChanged != null) {
      return viewTypeChanged(this);
    }
    return orElse();
  }
}

abstract class _ViewTypeChanged implements CalendarEvent {
  const factory _ViewTypeChanged(final CalendarViewType viewType) =
      _$ViewTypeChangedImpl;

  CalendarViewType get viewType;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ViewTypeChangedImplCopyWith<_$ViewTypeChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ViewToggledImplCopyWith<$Res> {
  factory _$$ViewToggledImplCopyWith(
          _$ViewToggledImpl value, $Res Function(_$ViewToggledImpl) then) =
      __$$ViewToggledImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ViewToggledImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$ViewToggledImpl>
    implements _$$ViewToggledImplCopyWith<$Res> {
  __$$ViewToggledImplCopyWithImpl(
      _$ViewToggledImpl _value, $Res Function(_$ViewToggledImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ViewToggledImpl implements _ViewToggled {
  const _$ViewToggledImpl();

  @override
  String toString() {
    return 'CalendarEvent.viewToggled()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ViewToggledImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return viewToggled();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return viewToggled?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (viewToggled != null) {
      return viewToggled();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return viewToggled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return viewToggled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (viewToggled != null) {
      return viewToggled(this);
    }
    return orElse();
  }
}

abstract class _ViewToggled implements CalendarEvent {
  const factory _ViewToggled() = _$ViewToggledImpl;
}

/// @nodoc
abstract class _$$TaskDroppedImplCopyWith<$Res> {
  factory _$$TaskDroppedImplCopyWith(
          _$TaskDroppedImpl value, $Res Function(_$TaskDroppedImpl) then) =
      __$$TaskDroppedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String taskId, DateTime newDate});
}

/// @nodoc
class __$$TaskDroppedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$TaskDroppedImpl>
    implements _$$TaskDroppedImplCopyWith<$Res> {
  __$$TaskDroppedImplCopyWithImpl(
      _$TaskDroppedImpl _value, $Res Function(_$TaskDroppedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = null,
    Object? newDate = null,
  }) {
    return _then(_$TaskDroppedImpl(
      taskId: null == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      newDate: null == newDate
          ? _value.newDate
          : newDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$TaskDroppedImpl implements _TaskDropped {
  const _$TaskDroppedImpl({required this.taskId, required this.newDate});

  @override
  final String taskId;
  @override
  final DateTime newDate;

  @override
  String toString() {
    return 'CalendarEvent.taskDropped(taskId: $taskId, newDate: $newDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskDroppedImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.newDate, newDate) || other.newDate == newDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, taskId, newDate);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskDroppedImplCopyWith<_$TaskDroppedImpl> get copyWith =>
      __$$TaskDroppedImplCopyWithImpl<_$TaskDroppedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return taskDropped(taskId, newDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return taskDropped?.call(taskId, newDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskDropped != null) {
      return taskDropped(taskId, newDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return taskDropped(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return taskDropped?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskDropped != null) {
      return taskDropped(this);
    }
    return orElse();
  }
}

abstract class _TaskDropped implements CalendarEvent {
  const factory _TaskDropped(
      {required final String taskId,
      required final DateTime newDate}) = _$TaskDroppedImpl;

  String get taskId;
  DateTime get newDate;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskDroppedImplCopyWith<_$TaskDroppedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MultipleTasksDroppedImplCopyWith<$Res> {
  factory _$$MultipleTasksDroppedImplCopyWith(_$MultipleTasksDroppedImpl value,
          $Res Function(_$MultipleTasksDroppedImpl) then) =
      __$$MultipleTasksDroppedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> taskIds, DateTime newDate});
}

/// @nodoc
class __$$MultipleTasksDroppedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$MultipleTasksDroppedImpl>
    implements _$$MultipleTasksDroppedImplCopyWith<$Res> {
  __$$MultipleTasksDroppedImplCopyWithImpl(_$MultipleTasksDroppedImpl _value,
      $Res Function(_$MultipleTasksDroppedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskIds = null,
    Object? newDate = null,
  }) {
    return _then(_$MultipleTasksDroppedImpl(
      taskIds: null == taskIds
          ? _value._taskIds
          : taskIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      newDate: null == newDate
          ? _value.newDate
          : newDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$MultipleTasksDroppedImpl implements _MultipleTasksDropped {
  const _$MultipleTasksDroppedImpl(
      {required final List<String> taskIds, required this.newDate})
      : _taskIds = taskIds;

  final List<String> _taskIds;
  @override
  List<String> get taskIds {
    if (_taskIds is EqualUnmodifiableListView) return _taskIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_taskIds);
  }

  @override
  final DateTime newDate;

  @override
  String toString() {
    return 'CalendarEvent.multipleTasksDropped(taskIds: $taskIds, newDate: $newDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MultipleTasksDroppedImpl &&
            const DeepCollectionEquality().equals(other._taskIds, _taskIds) &&
            (identical(other.newDate, newDate) || other.newDate == newDate));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_taskIds), newDate);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MultipleTasksDroppedImplCopyWith<_$MultipleTasksDroppedImpl>
      get copyWith =>
          __$$MultipleTasksDroppedImplCopyWithImpl<_$MultipleTasksDroppedImpl>(
              this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return multipleTasksDropped(taskIds, newDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return multipleTasksDropped?.call(taskIds, newDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (multipleTasksDropped != null) {
      return multipleTasksDropped(taskIds, newDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return multipleTasksDropped(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return multipleTasksDropped?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (multipleTasksDropped != null) {
      return multipleTasksDropped(this);
    }
    return orElse();
  }
}

abstract class _MultipleTasksDropped implements CalendarEvent {
  const factory _MultipleTasksDropped(
      {required final List<String> taskIds,
      required final DateTime newDate}) = _$MultipleTasksDroppedImpl;

  List<String> get taskIds;
  DateTime get newDate;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MultipleTasksDroppedImplCopyWith<_$MultipleTasksDroppedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RefreshRequestedImplCopyWith<$Res> {
  factory _$$RefreshRequestedImplCopyWith(_$RefreshRequestedImpl value,
          $Res Function(_$RefreshRequestedImpl) then) =
      __$$RefreshRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RefreshRequestedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$RefreshRequestedImpl>
    implements _$$RefreshRequestedImplCopyWith<$Res> {
  __$$RefreshRequestedImplCopyWithImpl(_$RefreshRequestedImpl _value,
      $Res Function(_$RefreshRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RefreshRequestedImpl implements _RefreshRequested {
  const _$RefreshRequestedImpl();

  @override
  String toString() {
    return 'CalendarEvent.refreshRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RefreshRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return refreshRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return refreshRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (refreshRequested != null) {
      return refreshRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return refreshRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return refreshRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (refreshRequested != null) {
      return refreshRequested(this);
    }
    return orElse();
  }
}

abstract class _RefreshRequested implements CalendarEvent {
  const factory _RefreshRequested() = _$RefreshRequestedImpl;
}

/// @nodoc
abstract class _$$TaskLoadRequestedImplCopyWith<$Res> {
  factory _$$TaskLoadRequestedImplCopyWith(_$TaskLoadRequestedImpl value,
          $Res Function(_$TaskLoadRequestedImpl) then) =
      __$$TaskLoadRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int year, int month});
}

/// @nodoc
class __$$TaskLoadRequestedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$TaskLoadRequestedImpl>
    implements _$$TaskLoadRequestedImplCopyWith<$Res> {
  __$$TaskLoadRequestedImplCopyWithImpl(_$TaskLoadRequestedImpl _value,
      $Res Function(_$TaskLoadRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = null,
    Object? month = null,
  }) {
    return _then(_$TaskLoadRequestedImpl(
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      month: null == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$TaskLoadRequestedImpl implements _TaskLoadRequested {
  const _$TaskLoadRequestedImpl({required this.year, required this.month});

  @override
  final int year;
  @override
  final int month;

  @override
  String toString() {
    return 'CalendarEvent.taskLoadRequested(year: $year, month: $month)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskLoadRequestedImpl &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.month, month) || other.month == month));
  }

  @override
  int get hashCode => Object.hash(runtimeType, year, month);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskLoadRequestedImplCopyWith<_$TaskLoadRequestedImpl> get copyWith =>
      __$$TaskLoadRequestedImplCopyWithImpl<_$TaskLoadRequestedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return taskLoadRequested(year, month);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return taskLoadRequested?.call(year, month);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskLoadRequested != null) {
      return taskLoadRequested(year, month);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return taskLoadRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return taskLoadRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskLoadRequested != null) {
      return taskLoadRequested(this);
    }
    return orElse();
  }
}

abstract class _TaskLoadRequested implements CalendarEvent {
  const factory _TaskLoadRequested(
      {required final int year,
      required final int month}) = _$TaskLoadRequestedImpl;

  int get year;
  int get month;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskLoadRequestedImplCopyWith<_$TaskLoadRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$YearTaskLoadRequestedImplCopyWith<$Res> {
  factory _$$YearTaskLoadRequestedImplCopyWith(
          _$YearTaskLoadRequestedImpl value,
          $Res Function(_$YearTaskLoadRequestedImpl) then) =
      __$$YearTaskLoadRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int year});
}

/// @nodoc
class __$$YearTaskLoadRequestedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$YearTaskLoadRequestedImpl>
    implements _$$YearTaskLoadRequestedImplCopyWith<$Res> {
  __$$YearTaskLoadRequestedImplCopyWithImpl(_$YearTaskLoadRequestedImpl _value,
      $Res Function(_$YearTaskLoadRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = null,
  }) {
    return _then(_$YearTaskLoadRequestedImpl(
      null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$YearTaskLoadRequestedImpl implements _YearTaskLoadRequested {
  const _$YearTaskLoadRequestedImpl(this.year);

  @override
  final int year;

  @override
  String toString() {
    return 'CalendarEvent.yearTaskLoadRequested(year: $year)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$YearTaskLoadRequestedImpl &&
            (identical(other.year, year) || other.year == year));
  }

  @override
  int get hashCode => Object.hash(runtimeType, year);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$YearTaskLoadRequestedImplCopyWith<_$YearTaskLoadRequestedImpl>
      get copyWith => __$$YearTaskLoadRequestedImplCopyWithImpl<
          _$YearTaskLoadRequestedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return yearTaskLoadRequested(year);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return yearTaskLoadRequested?.call(year);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (yearTaskLoadRequested != null) {
      return yearTaskLoadRequested(year);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return yearTaskLoadRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return yearTaskLoadRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (yearTaskLoadRequested != null) {
      return yearTaskLoadRequested(this);
    }
    return orElse();
  }
}

abstract class _YearTaskLoadRequested implements CalendarEvent {
  const factory _YearTaskLoadRequested(final int year) =
      _$YearTaskLoadRequestedImpl;

  int get year;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$YearTaskLoadRequestedImplCopyWith<_$YearTaskLoadRequestedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PriorityFilterChangedImplCopyWith<$Res> {
  factory _$$PriorityFilterChangedImplCopyWith(
          _$PriorityFilterChangedImpl value,
          $Res Function(_$PriorityFilterChangedImpl) then) =
      __$$PriorityFilterChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Priority? priority});
}

/// @nodoc
class __$$PriorityFilterChangedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$PriorityFilterChangedImpl>
    implements _$$PriorityFilterChangedImplCopyWith<$Res> {
  __$$PriorityFilterChangedImplCopyWithImpl(_$PriorityFilterChangedImpl _value,
      $Res Function(_$PriorityFilterChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? priority = freezed,
  }) {
    return _then(_$PriorityFilterChangedImpl(
      freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as Priority?,
    ));
  }
}

/// @nodoc

class _$PriorityFilterChangedImpl implements _PriorityFilterChanged {
  const _$PriorityFilterChangedImpl(this.priority);

  @override
  final Priority? priority;

  @override
  String toString() {
    return 'CalendarEvent.priorityFilterChanged(priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PriorityFilterChangedImpl &&
            (identical(other.priority, priority) ||
                other.priority == priority));
  }

  @override
  int get hashCode => Object.hash(runtimeType, priority);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PriorityFilterChangedImplCopyWith<_$PriorityFilterChangedImpl>
      get copyWith => __$$PriorityFilterChangedImplCopyWithImpl<
          _$PriorityFilterChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return priorityFilterChanged(priority);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return priorityFilterChanged?.call(priority);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (priorityFilterChanged != null) {
      return priorityFilterChanged(priority);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return priorityFilterChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return priorityFilterChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (priorityFilterChanged != null) {
      return priorityFilterChanged(this);
    }
    return orElse();
  }
}

abstract class _PriorityFilterChanged implements CalendarEvent {
  const factory _PriorityFilterChanged(final Priority? priority) =
      _$PriorityFilterChangedImpl;

  Priority? get priority;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PriorityFilterChangedImplCopyWith<_$PriorityFilterChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CompletionFilterChangedImplCopyWith<$Res> {
  factory _$$CompletionFilterChangedImplCopyWith(
          _$CompletionFilterChangedImpl value,
          $Res Function(_$CompletionFilterChangedImpl) then) =
      __$$CompletionFilterChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool? isCompleted});
}

/// @nodoc
class __$$CompletionFilterChangedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$CompletionFilterChangedImpl>
    implements _$$CompletionFilterChangedImplCopyWith<$Res> {
  __$$CompletionFilterChangedImplCopyWithImpl(
      _$CompletionFilterChangedImpl _value,
      $Res Function(_$CompletionFilterChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCompleted = freezed,
  }) {
    return _then(_$CompletionFilterChangedImpl(
      freezed == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$CompletionFilterChangedImpl implements _CompletionFilterChanged {
  const _$CompletionFilterChangedImpl(this.isCompleted);

  @override
  final bool? isCompleted;

  @override
  String toString() {
    return 'CalendarEvent.completionFilterChanged(isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompletionFilterChangedImpl &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isCompleted);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompletionFilterChangedImplCopyWith<_$CompletionFilterChangedImpl>
      get copyWith => __$$CompletionFilterChangedImplCopyWithImpl<
          _$CompletionFilterChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return completionFilterChanged(isCompleted);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return completionFilterChanged?.call(isCompleted);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (completionFilterChanged != null) {
      return completionFilterChanged(isCompleted);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return completionFilterChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return completionFilterChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (completionFilterChanged != null) {
      return completionFilterChanged(this);
    }
    return orElse();
  }
}

abstract class _CompletionFilterChanged implements CalendarEvent {
  const factory _CompletionFilterChanged(final bool? isCompleted) =
      _$CompletionFilterChangedImpl;

  bool? get isCompleted;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompletionFilterChangedImplCopyWith<_$CompletionFilterChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FiltersClearedImplCopyWith<$Res> {
  factory _$$FiltersClearedImplCopyWith(_$FiltersClearedImpl value,
          $Res Function(_$FiltersClearedImpl) then) =
      __$$FiltersClearedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FiltersClearedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$FiltersClearedImpl>
    implements _$$FiltersClearedImplCopyWith<$Res> {
  __$$FiltersClearedImplCopyWithImpl(
      _$FiltersClearedImpl _value, $Res Function(_$FiltersClearedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FiltersClearedImpl implements _FiltersCleared {
  const _$FiltersClearedImpl();

  @override
  String toString() {
    return 'CalendarEvent.filtersCleared()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FiltersClearedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return filtersCleared();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return filtersCleared?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (filtersCleared != null) {
      return filtersCleared();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return filtersCleared(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return filtersCleared?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (filtersCleared != null) {
      return filtersCleared(this);
    }
    return orElse();
  }
}

abstract class _FiltersCleared implements CalendarEvent {
  const factory _FiltersCleared() = _$FiltersClearedImpl;
}

/// @nodoc
abstract class _$$WeekendsToggledImplCopyWith<$Res> {
  factory _$$WeekendsToggledImplCopyWith(_$WeekendsToggledImpl value,
          $Res Function(_$WeekendsToggledImpl) then) =
      __$$WeekendsToggledImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool showWeekends});
}

/// @nodoc
class __$$WeekendsToggledImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$WeekendsToggledImpl>
    implements _$$WeekendsToggledImplCopyWith<$Res> {
  __$$WeekendsToggledImplCopyWithImpl(
      _$WeekendsToggledImpl _value, $Res Function(_$WeekendsToggledImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? showWeekends = null,
  }) {
    return _then(_$WeekendsToggledImpl(
      null == showWeekends
          ? _value.showWeekends
          : showWeekends // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$WeekendsToggledImpl implements _WeekendsToggled {
  const _$WeekendsToggledImpl(this.showWeekends);

  @override
  final bool showWeekends;

  @override
  String toString() {
    return 'CalendarEvent.weekendsToggled(showWeekends: $showWeekends)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$WeekendsToggledImpl &&
            (identical(other.showWeekends, showWeekends) ||
                other.showWeekends == showWeekends));
  }

  @override
  int get hashCode => Object.hash(runtimeType, showWeekends);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$WeekendsToggledImplCopyWith<_$WeekendsToggledImpl> get copyWith =>
      __$$WeekendsToggledImplCopyWithImpl<_$WeekendsToggledImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return weekendsToggled(showWeekends);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return weekendsToggled?.call(showWeekends);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (weekendsToggled != null) {
      return weekendsToggled(showWeekends);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return weekendsToggled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return weekendsToggled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (weekendsToggled != null) {
      return weekendsToggled(this);
    }
    return orElse();
  }
}

abstract class _WeekendsToggled implements CalendarEvent {
  const factory _WeekendsToggled(final bool showWeekends) =
      _$WeekendsToggledImpl;

  bool get showWeekends;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$WeekendsToggledImplCopyWith<_$WeekendsToggledImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FirstDayOfWeekChangedImplCopyWith<$Res> {
  factory _$$FirstDayOfWeekChangedImplCopyWith(
          _$FirstDayOfWeekChangedImpl value,
          $Res Function(_$FirstDayOfWeekChangedImpl) then) =
      __$$FirstDayOfWeekChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int firstDay});
}

/// @nodoc
class __$$FirstDayOfWeekChangedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$FirstDayOfWeekChangedImpl>
    implements _$$FirstDayOfWeekChangedImplCopyWith<$Res> {
  __$$FirstDayOfWeekChangedImplCopyWithImpl(_$FirstDayOfWeekChangedImpl _value,
      $Res Function(_$FirstDayOfWeekChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? firstDay = null,
  }) {
    return _then(_$FirstDayOfWeekChangedImpl(
      null == firstDay
          ? _value.firstDay
          : firstDay // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$FirstDayOfWeekChangedImpl implements _FirstDayOfWeekChanged {
  const _$FirstDayOfWeekChangedImpl(this.firstDay);

  @override
  final int firstDay;

  @override
  String toString() {
    return 'CalendarEvent.firstDayOfWeekChanged(firstDay: $firstDay)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FirstDayOfWeekChangedImpl &&
            (identical(other.firstDay, firstDay) ||
                other.firstDay == firstDay));
  }

  @override
  int get hashCode => Object.hash(runtimeType, firstDay);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FirstDayOfWeekChangedImplCopyWith<_$FirstDayOfWeekChangedImpl>
      get copyWith => __$$FirstDayOfWeekChangedImplCopyWithImpl<
          _$FirstDayOfWeekChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return firstDayOfWeekChanged(firstDay);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return firstDayOfWeekChanged?.call(firstDay);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (firstDayOfWeekChanged != null) {
      return firstDayOfWeekChanged(firstDay);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return firstDayOfWeekChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return firstDayOfWeekChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (firstDayOfWeekChanged != null) {
      return firstDayOfWeekChanged(this);
    }
    return orElse();
  }
}

abstract class _FirstDayOfWeekChanged implements CalendarEvent {
  const factory _FirstDayOfWeekChanged(final int firstDay) =
      _$FirstDayOfWeekChangedImpl;

  int get firstDay;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FirstDayOfWeekChangedImplCopyWith<_$FirstDayOfWeekChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TaskPreviewToggledImplCopyWith<$Res> {
  factory _$$TaskPreviewToggledImplCopyWith(_$TaskPreviewToggledImpl value,
          $Res Function(_$TaskPreviewToggledImpl) then) =
      __$$TaskPreviewToggledImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool enabled});
}

/// @nodoc
class __$$TaskPreviewToggledImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$TaskPreviewToggledImpl>
    implements _$$TaskPreviewToggledImplCopyWith<$Res> {
  __$$TaskPreviewToggledImplCopyWithImpl(_$TaskPreviewToggledImpl _value,
      $Res Function(_$TaskPreviewToggledImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? enabled = null,
  }) {
    return _then(_$TaskPreviewToggledImpl(
      null == enabled
          ? _value.enabled
          : enabled // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$TaskPreviewToggledImpl implements _TaskPreviewToggled {
  const _$TaskPreviewToggledImpl(this.enabled);

  @override
  final bool enabled;

  @override
  String toString() {
    return 'CalendarEvent.taskPreviewToggled(enabled: $enabled)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskPreviewToggledImpl &&
            (identical(other.enabled, enabled) || other.enabled == enabled));
  }

  @override
  int get hashCode => Object.hash(runtimeType, enabled);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskPreviewToggledImplCopyWith<_$TaskPreviewToggledImpl> get copyWith =>
      __$$TaskPreviewToggledImplCopyWithImpl<_$TaskPreviewToggledImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return taskPreviewToggled(enabled);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return taskPreviewToggled?.call(enabled);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskPreviewToggled != null) {
      return taskPreviewToggled(enabled);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return taskPreviewToggled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return taskPreviewToggled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskPreviewToggled != null) {
      return taskPreviewToggled(this);
    }
    return orElse();
  }
}

abstract class _TaskPreviewToggled implements CalendarEvent {
  const factory _TaskPreviewToggled(final bool enabled) =
      _$TaskPreviewToggledImpl;

  bool get enabled;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskPreviewToggledImplCopyWith<_$TaskPreviewToggledImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$KeyboardNavigationImplCopyWith<$Res> {
  factory _$$KeyboardNavigationImplCopyWith(_$KeyboardNavigationImpl value,
          $Res Function(_$KeyboardNavigationImpl) then) =
      __$$KeyboardNavigationImplCopyWithImpl<$Res>;
  @useResult
  $Res call({CalendarKeyAction action, DateTime? targetDate});
}

/// @nodoc
class __$$KeyboardNavigationImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$KeyboardNavigationImpl>
    implements _$$KeyboardNavigationImplCopyWith<$Res> {
  __$$KeyboardNavigationImplCopyWithImpl(_$KeyboardNavigationImpl _value,
      $Res Function(_$KeyboardNavigationImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? action = null,
    Object? targetDate = freezed,
  }) {
    return _then(_$KeyboardNavigationImpl(
      action: null == action
          ? _value.action
          : action // ignore: cast_nullable_to_non_nullable
              as CalendarKeyAction,
      targetDate: freezed == targetDate
          ? _value.targetDate
          : targetDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
    ));
  }
}

/// @nodoc

class _$KeyboardNavigationImpl implements _KeyboardNavigation {
  const _$KeyboardNavigationImpl({required this.action, this.targetDate});

  @override
  final CalendarKeyAction action;
  @override
  final DateTime? targetDate;

  @override
  String toString() {
    return 'CalendarEvent.keyboardNavigation(action: $action, targetDate: $targetDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$KeyboardNavigationImpl &&
            (identical(other.action, action) || other.action == action) &&
            (identical(other.targetDate, targetDate) ||
                other.targetDate == targetDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, action, targetDate);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$KeyboardNavigationImplCopyWith<_$KeyboardNavigationImpl> get copyWith =>
      __$$KeyboardNavigationImplCopyWithImpl<_$KeyboardNavigationImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return keyboardNavigation(action, targetDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return keyboardNavigation?.call(action, targetDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (keyboardNavigation != null) {
      return keyboardNavigation(action, targetDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return keyboardNavigation(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return keyboardNavigation?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (keyboardNavigation != null) {
      return keyboardNavigation(this);
    }
    return orElse();
  }
}

abstract class _KeyboardNavigation implements CalendarEvent {
  const factory _KeyboardNavigation(
      {required final CalendarKeyAction action,
      final DateTime? targetDate}) = _$KeyboardNavigationImpl;

  CalendarKeyAction get action;
  DateTime? get targetDate;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$KeyboardNavigationImplCopyWith<_$KeyboardNavigationImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RetryRequestedImplCopyWith<$Res> {
  factory _$$RetryRequestedImplCopyWith(_$RetryRequestedImpl value,
          $Res Function(_$RetryRequestedImpl) then) =
      __$$RetryRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RetryRequestedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$RetryRequestedImpl>
    implements _$$RetryRequestedImplCopyWith<$Res> {
  __$$RetryRequestedImplCopyWithImpl(
      _$RetryRequestedImpl _value, $Res Function(_$RetryRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RetryRequestedImpl implements _RetryRequested {
  const _$RetryRequestedImpl();

  @override
  String toString() {
    return 'CalendarEvent.retryRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RetryRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return retryRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return retryRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (retryRequested != null) {
      return retryRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return retryRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return retryRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (retryRequested != null) {
      return retryRequested(this);
    }
    return orElse();
  }
}

abstract class _RetryRequested implements CalendarEvent {
  const factory _RetryRequested() = _$RetryRequestedImpl;
}

/// @nodoc
abstract class _$$ErrorClearedImplCopyWith<$Res> {
  factory _$$ErrorClearedImplCopyWith(
          _$ErrorClearedImpl value, $Res Function(_$ErrorClearedImpl) then) =
      __$$ErrorClearedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ErrorClearedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$ErrorClearedImpl>
    implements _$$ErrorClearedImplCopyWith<$Res> {
  __$$ErrorClearedImplCopyWithImpl(
      _$ErrorClearedImpl _value, $Res Function(_$ErrorClearedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ErrorClearedImpl implements _ErrorCleared {
  const _$ErrorClearedImpl();

  @override
  String toString() {
    return 'CalendarEvent.errorCleared()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ErrorClearedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return errorCleared();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return errorCleared?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorCleared != null) {
      return errorCleared();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return errorCleared(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return errorCleared?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorCleared != null) {
      return errorCleared(this);
    }
    return orElse();
  }
}

abstract class _ErrorCleared implements CalendarEvent {
  const factory _ErrorCleared() = _$ErrorClearedImpl;
}

/// @nodoc
abstract class _$$TaskLoadUpdatedImplCopyWith<$Res> {
  factory _$$TaskLoadUpdatedImplCopyWith(_$TaskLoadUpdatedImpl value,
          $Res Function(_$TaskLoadUpdatedImpl) then) =
      __$$TaskLoadUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<DateTime, int> taskLoadByDate});
}

/// @nodoc
class __$$TaskLoadUpdatedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$TaskLoadUpdatedImpl>
    implements _$$TaskLoadUpdatedImplCopyWith<$Res> {
  __$$TaskLoadUpdatedImplCopyWithImpl(
      _$TaskLoadUpdatedImpl _value, $Res Function(_$TaskLoadUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskLoadByDate = null,
  }) {
    return _then(_$TaskLoadUpdatedImpl(
      null == taskLoadByDate
          ? _value._taskLoadByDate
          : taskLoadByDate // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
    ));
  }
}

/// @nodoc

class _$TaskLoadUpdatedImpl implements _TaskLoadUpdated {
  const _$TaskLoadUpdatedImpl(final Map<DateTime, int> taskLoadByDate)
      : _taskLoadByDate = taskLoadByDate;

  final Map<DateTime, int> _taskLoadByDate;
  @override
  Map<DateTime, int> get taskLoadByDate {
    if (_taskLoadByDate is EqualUnmodifiableMapView) return _taskLoadByDate;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_taskLoadByDate);
  }

  @override
  String toString() {
    return 'CalendarEvent.taskLoadUpdated(taskLoadByDate: $taskLoadByDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskLoadUpdatedImpl &&
            const DeepCollectionEquality()
                .equals(other._taskLoadByDate, _taskLoadByDate));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_taskLoadByDate));

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskLoadUpdatedImplCopyWith<_$TaskLoadUpdatedImpl> get copyWith =>
      __$$TaskLoadUpdatedImplCopyWithImpl<_$TaskLoadUpdatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return taskLoadUpdated(taskLoadByDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return taskLoadUpdated?.call(taskLoadByDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskLoadUpdated != null) {
      return taskLoadUpdated(taskLoadByDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return taskLoadUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return taskLoadUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskLoadUpdated != null) {
      return taskLoadUpdated(this);
    }
    return orElse();
  }
}

abstract class _TaskLoadUpdated implements CalendarEvent {
  const factory _TaskLoadUpdated(final Map<DateTime, int> taskLoadByDate) =
      _$TaskLoadUpdatedImpl;

  Map<DateTime, int> get taskLoadByDate;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskLoadUpdatedImplCopyWith<_$TaskLoadUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$YearTaskLoadUpdatedImplCopyWith<$Res> {
  factory _$$YearTaskLoadUpdatedImplCopyWith(_$YearTaskLoadUpdatedImpl value,
          $Res Function(_$YearTaskLoadUpdatedImpl) then) =
      __$$YearTaskLoadUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Map<DateTime, int> yearTaskLoadByDate});
}

/// @nodoc
class __$$YearTaskLoadUpdatedImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$YearTaskLoadUpdatedImpl>
    implements _$$YearTaskLoadUpdatedImplCopyWith<$Res> {
  __$$YearTaskLoadUpdatedImplCopyWithImpl(_$YearTaskLoadUpdatedImpl _value,
      $Res Function(_$YearTaskLoadUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? yearTaskLoadByDate = null,
  }) {
    return _then(_$YearTaskLoadUpdatedImpl(
      null == yearTaskLoadByDate
          ? _value._yearTaskLoadByDate
          : yearTaskLoadByDate // ignore: cast_nullable_to_non_nullable
              as Map<DateTime, int>,
    ));
  }
}

/// @nodoc

class _$YearTaskLoadUpdatedImpl implements _YearTaskLoadUpdated {
  const _$YearTaskLoadUpdatedImpl(final Map<DateTime, int> yearTaskLoadByDate)
      : _yearTaskLoadByDate = yearTaskLoadByDate;

  final Map<DateTime, int> _yearTaskLoadByDate;
  @override
  Map<DateTime, int> get yearTaskLoadByDate {
    if (_yearTaskLoadByDate is EqualUnmodifiableMapView)
      return _yearTaskLoadByDate;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_yearTaskLoadByDate);
  }

  @override
  String toString() {
    return 'CalendarEvent.yearTaskLoadUpdated(yearTaskLoadByDate: $yearTaskLoadByDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$YearTaskLoadUpdatedImpl &&
            const DeepCollectionEquality()
                .equals(other._yearTaskLoadByDate, _yearTaskLoadByDate));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_yearTaskLoadByDate));

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$YearTaskLoadUpdatedImplCopyWith<_$YearTaskLoadUpdatedImpl> get copyWith =>
      __$$YearTaskLoadUpdatedImplCopyWithImpl<_$YearTaskLoadUpdatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return yearTaskLoadUpdated(yearTaskLoadByDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return yearTaskLoadUpdated?.call(yearTaskLoadByDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (yearTaskLoadUpdated != null) {
      return yearTaskLoadUpdated(yearTaskLoadByDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return yearTaskLoadUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return yearTaskLoadUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (yearTaskLoadUpdated != null) {
      return yearTaskLoadUpdated(this);
    }
    return orElse();
  }
}

abstract class _YearTaskLoadUpdated implements CalendarEvent {
  const factory _YearTaskLoadUpdated(
      final Map<DateTime, int> yearTaskLoadByDate) = _$YearTaskLoadUpdatedImpl;

  Map<DateTime, int> get yearTaskLoadByDate;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$YearTaskLoadUpdatedImplCopyWith<_$YearTaskLoadUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorOccurredImplCopyWith<$Res> {
  factory _$$ErrorOccurredImplCopyWith(
          _$ErrorOccurredImpl value, $Res Function(_$ErrorOccurredImpl) then) =
      __$$ErrorOccurredImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, Exception? exception});
}

/// @nodoc
class __$$ErrorOccurredImplCopyWithImpl<$Res>
    extends _$CalendarEventCopyWithImpl<$Res, _$ErrorOccurredImpl>
    implements _$$ErrorOccurredImplCopyWith<$Res> {
  __$$ErrorOccurredImplCopyWithImpl(
      _$ErrorOccurredImpl _value, $Res Function(_$ErrorOccurredImpl) _then)
      : super(_value, _then);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? exception = freezed,
  }) {
    return _then(_$ErrorOccurredImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      exception: freezed == exception
          ? _value.exception
          : exception // ignore: cast_nullable_to_non_nullable
              as Exception?,
    ));
  }
}

/// @nodoc

class _$ErrorOccurredImpl implements _ErrorOccurred {
  const _$ErrorOccurredImpl({required this.message, this.exception});

  @override
  final String message;
  @override
  final Exception? exception;

  @override
  String toString() {
    return 'CalendarEvent.errorOccurred(message: $message, exception: $exception)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorOccurredImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.exception, exception) ||
                other.exception == exception));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, exception);

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorOccurredImplCopyWith<_$ErrorOccurredImpl> get copyWith =>
      __$$ErrorOccurredImplCopyWithImpl<_$ErrorOccurredImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() started,
    required TResult Function(DateTime date) dateSelected,
    required TResult Function(DateTime newMonthDate) monthChanged,
    required TResult Function(int year) yearChanged,
    required TResult Function() todayNavigated,
    required TResult Function(DateTime date) dateNavigated,
    required TResult Function(String title, DateTime date, Priority priority)
        quickTaskAdded,
    required TResult Function(
            List<String> titles, DateTime date, Priority priority)
        bulkQuickTasksAdded,
    required TResult Function(CalendarViewType viewType) viewTypeChanged,
    required TResult Function() viewToggled,
    required TResult Function(String taskId, DateTime newDate) taskDropped,
    required TResult Function(List<String> taskIds, DateTime newDate)
        multipleTasksDropped,
    required TResult Function() refreshRequested,
    required TResult Function(int year, int month) taskLoadRequested,
    required TResult Function(int year) yearTaskLoadRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function(bool showWeekends) weekendsToggled,
    required TResult Function(int firstDay) firstDayOfWeekChanged,
    required TResult Function(bool enabled) taskPreviewToggled,
    required TResult Function(CalendarKeyAction action, DateTime? targetDate)
        keyboardNavigation,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(Map<DateTime, int> taskLoadByDate)
        taskLoadUpdated,
    required TResult Function(Map<DateTime, int> yearTaskLoadByDate)
        yearTaskLoadUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return errorOccurred(message, exception);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? started,
    TResult? Function(DateTime date)? dateSelected,
    TResult? Function(DateTime newMonthDate)? monthChanged,
    TResult? Function(int year)? yearChanged,
    TResult? Function()? todayNavigated,
    TResult? Function(DateTime date)? dateNavigated,
    TResult? Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult? Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult? Function(CalendarViewType viewType)? viewTypeChanged,
    TResult? Function()? viewToggled,
    TResult? Function(String taskId, DateTime newDate)? taskDropped,
    TResult? Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult? Function()? refreshRequested,
    TResult? Function(int year, int month)? taskLoadRequested,
    TResult? Function(int year)? yearTaskLoadRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function(bool showWeekends)? weekendsToggled,
    TResult? Function(int firstDay)? firstDayOfWeekChanged,
    TResult? Function(bool enabled)? taskPreviewToggled,
    TResult? Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult? Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return errorOccurred?.call(message, exception);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? started,
    TResult Function(DateTime date)? dateSelected,
    TResult Function(DateTime newMonthDate)? monthChanged,
    TResult Function(int year)? yearChanged,
    TResult Function()? todayNavigated,
    TResult Function(DateTime date)? dateNavigated,
    TResult Function(String title, DateTime date, Priority priority)?
        quickTaskAdded,
    TResult Function(List<String> titles, DateTime date, Priority priority)?
        bulkQuickTasksAdded,
    TResult Function(CalendarViewType viewType)? viewTypeChanged,
    TResult Function()? viewToggled,
    TResult Function(String taskId, DateTime newDate)? taskDropped,
    TResult Function(List<String> taskIds, DateTime newDate)?
        multipleTasksDropped,
    TResult Function()? refreshRequested,
    TResult Function(int year, int month)? taskLoadRequested,
    TResult Function(int year)? yearTaskLoadRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function(bool showWeekends)? weekendsToggled,
    TResult Function(int firstDay)? firstDayOfWeekChanged,
    TResult Function(bool enabled)? taskPreviewToggled,
    TResult Function(CalendarKeyAction action, DateTime? targetDate)?
        keyboardNavigation,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(Map<DateTime, int> taskLoadByDate)? taskLoadUpdated,
    TResult Function(Map<DateTime, int> yearTaskLoadByDate)?
        yearTaskLoadUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorOccurred != null) {
      return errorOccurred(message, exception);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_Started value) started,
    required TResult Function(_DateSelected value) dateSelected,
    required TResult Function(_MonthChanged value) monthChanged,
    required TResult Function(_YearChanged value) yearChanged,
    required TResult Function(_TodayNavigated value) todayNavigated,
    required TResult Function(_DateNavigated value) dateNavigated,
    required TResult Function(_QuickTaskAdded value) quickTaskAdded,
    required TResult Function(_BulkQuickTasksAdded value) bulkQuickTasksAdded,
    required TResult Function(_ViewTypeChanged value) viewTypeChanged,
    required TResult Function(_ViewToggled value) viewToggled,
    required TResult Function(_TaskDropped value) taskDropped,
    required TResult Function(_MultipleTasksDropped value) multipleTasksDropped,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_TaskLoadRequested value) taskLoadRequested,
    required TResult Function(_YearTaskLoadRequested value)
        yearTaskLoadRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_WeekendsToggled value) weekendsToggled,
    required TResult Function(_FirstDayOfWeekChanged value)
        firstDayOfWeekChanged,
    required TResult Function(_TaskPreviewToggled value) taskPreviewToggled,
    required TResult Function(_KeyboardNavigation value) keyboardNavigation,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TaskLoadUpdated value) taskLoadUpdated,
    required TResult Function(_YearTaskLoadUpdated value) yearTaskLoadUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return errorOccurred(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_Started value)? started,
    TResult? Function(_DateSelected value)? dateSelected,
    TResult? Function(_MonthChanged value)? monthChanged,
    TResult? Function(_YearChanged value)? yearChanged,
    TResult? Function(_TodayNavigated value)? todayNavigated,
    TResult? Function(_DateNavigated value)? dateNavigated,
    TResult? Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult? Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult? Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult? Function(_ViewToggled value)? viewToggled,
    TResult? Function(_TaskDropped value)? taskDropped,
    TResult? Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult? Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_WeekendsToggled value)? weekendsToggled,
    TResult? Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult? Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult? Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult? Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return errorOccurred?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_Started value)? started,
    TResult Function(_DateSelected value)? dateSelected,
    TResult Function(_MonthChanged value)? monthChanged,
    TResult Function(_YearChanged value)? yearChanged,
    TResult Function(_TodayNavigated value)? todayNavigated,
    TResult Function(_DateNavigated value)? dateNavigated,
    TResult Function(_QuickTaskAdded value)? quickTaskAdded,
    TResult Function(_BulkQuickTasksAdded value)? bulkQuickTasksAdded,
    TResult Function(_ViewTypeChanged value)? viewTypeChanged,
    TResult Function(_ViewToggled value)? viewToggled,
    TResult Function(_TaskDropped value)? taskDropped,
    TResult Function(_MultipleTasksDropped value)? multipleTasksDropped,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_TaskLoadRequested value)? taskLoadRequested,
    TResult Function(_YearTaskLoadRequested value)? yearTaskLoadRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_WeekendsToggled value)? weekendsToggled,
    TResult Function(_FirstDayOfWeekChanged value)? firstDayOfWeekChanged,
    TResult Function(_TaskPreviewToggled value)? taskPreviewToggled,
    TResult Function(_KeyboardNavigation value)? keyboardNavigation,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TaskLoadUpdated value)? taskLoadUpdated,
    TResult Function(_YearTaskLoadUpdated value)? yearTaskLoadUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorOccurred != null) {
      return errorOccurred(this);
    }
    return orElse();
  }
}

abstract class _ErrorOccurred implements CalendarEvent {
  const factory _ErrorOccurred(
      {required final String message,
      final Exception? exception}) = _$ErrorOccurredImpl;

  String get message;
  Exception? get exception;

  /// Create a copy of CalendarEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorOccurredImplCopyWith<_$ErrorOccurredImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
