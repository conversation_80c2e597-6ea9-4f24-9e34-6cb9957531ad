import 'package:intl/intl.dart';
import '../../../domain/models/task_model.dart';

class MarkdownExporter {
  const MarkdownExporter();

  String exportTasks({
    required List<Task> tasks,
    required DateTime start,
    required DateTime end,
    String title = '任务导出',
  }) {
    final buffer = StringBuffer();
    final dateFmt = DateFormat('yyyy-MM-dd');

    // Header
    buffer.writeln('# $title');
    buffer.writeln('> 导出范围：${dateFmt.format(start)} 至 ${dateFmt.format(end)}');
    buffer.writeln('> 导出时间：${DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now())}');
    buffer.writeln('');

    if (tasks.isEmpty) {
      buffer.writeln('（所选日期范围内没有任务）');
      return buffer.toString();
    }

    // Sort and group by due date (date only)
    final sorted = [...tasks]
      ..sort((a, b) => a.dueDate.compareTo(b.dueDate));

    DateTime? currentDate;
    for (final task in sorted) {
      final taskDate = DateTime(task.dueDate.year, task.dueDate.month, task.dueDate.day);
      if (currentDate == null || taskDate != currentDate) {
        // New date section
        currentDate = taskDate;
        buffer.writeln('');
        buffer.writeln('## ${dateFmt.format(currentDate)}');
      }

      final completedMark = task.isCompleted ? '✅' : '🔲';
      final priorityLabel = task.priority.displayName;
      buffer.writeln('- $completedMark **${_md(task.title)}**  \\\n  截止：${DateFormat('MM-dd HH:mm').format(task.dueDate)} · 优先级：$priorityLabel');

      if (task.notes.trim().isNotEmpty) {
        buffer.writeln('  > ${_md(task.notes)}');
      }

      if (task.subtasks.isNotEmpty) {
        for (final st in task.subtasks) {
          final stMark = st.isCompleted ? '✔' : '◻';
          buffer.writeln('  - $stMark ${_md(st.title)}');
        }
      }
    }

    return buffer.toString();
  }

  String _md(String input) {
    // Minimal escaping for markdown special chars
    return input
        .replaceAll('\\', r'\\')
        .replaceAll('*', r'\*')
        .replaceAll('_', r'\_')
        .replaceAll('`', r'\`')
        .replaceAll('#', r'\#');
  }
}

