import 'package:file_selector/file_selector.dart' as fs;

/// Abstraction over file_selector to simplify testing and platform differences
abstract class FilePickerService {
  Future<String?> pickDatabaseImportPath();
  Future<String?> pickDatabaseExportPath({String suggestedName});
  Future<String?> pickMarkdownExportPath({String suggestedName});
}

class FilePickerServiceImpl implements FilePickerService {
  const FilePickerServiceImpl();

  static const _dbTypeGroup = fs.XTypeGroup(
    label: 'SQLite Database',
    extensions: ['db'],
  );


  @override
  Future<String?> pickDatabaseImportPath() async {
    print('[FILE-PICKER] openFile: picking .db file');
    try {
      final file = await fs.openFile(acceptedTypeGroups: const [_dbTypeGroup]);
      final path = file?.path;
      print('[FILE-PICKER] result: $path');
      return path;
    } catch (e) {
      print('[FILE-PICKER] openFile error: $e');
      return null;
    }
  }

  @override
  Future<String?> pickDatabaseExportPath({String suggestedName = 'mytodospace.db'}) async {
    print('[FILE-PICKER] getSaveLocation: picking export path');
    try {
      final location = await fs.getSaveLocation(suggestedName: suggestedName);
      final path = location?.path;
      print('[FILE-PICKER] export path: $path');
      return path;
    } catch (e) {
      print('[FILE-PICKER] getSaveLocation error: $e');
      return null;
    }
  }

  @override
  Future<String?> pickMarkdownExportPath({String suggestedName = 'tasks.md'}) async {
    print('[FILE-PICKER] getSaveLocation: picking markdown export path');
    try {
      final location = await fs.getSaveLocation(suggestedName: suggestedName);
      final path = location?.path;
      print('[FILE-PICKER] markdown export path: $path');
      return path;
    } catch (e) {
      print('[FILE-PICKER] getSaveLocation error: $e');
      return null;
    }
  }
}

