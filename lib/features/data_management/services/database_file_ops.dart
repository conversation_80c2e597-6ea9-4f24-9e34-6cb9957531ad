import '../../../data/datasources/database_config.dart';

/// Abstraction over DatabaseConfig static file operations to ease testing
abstract class DatabaseFileOps {
  Future<String> getDatabasePath();
  Future<void> backupDatabase(String backupPath);
  Future<void> restoreDatabase(String sourcePath);
  Future<void> deleteDatabase();
}

class DefaultDatabaseFileOps implements DatabaseFileOps {
  const DefaultDatabaseFileOps();

  @override
  Future<void> backupDatabase(String backupPath) {
    return DatabaseConfig.backupDatabase(backupPath);
  }

  @override
  Future<void> deleteDatabase() {
    return DatabaseConfig.deleteDatabase();
  }

  @override
  Future<String> getDatabasePath() {
    return DatabaseConfig.getDatabasePath();
  }

  @override
  Future<void> restoreDatabase(String sourcePath) {
    return DatabaseConfig.restoreDatabase(sourcePath);
  }
}

