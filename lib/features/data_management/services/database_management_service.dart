import 'dart:io';

import 'package:flutter/material.dart';

import '../../../core/di/injection.dart';
import '../../../data/datasources/local_database.dart';
import '../../../data/repositories/mappers/task_mapper.dart';
import '../../../domain/models/task_model.dart';
import 'database_file_ops.dart';
import 'markdown_exporter.dart';

/// Abstraction to allow resetting the database instance in DI
abstract class DatabaseReinitializer {
  Future<void> reinitialize(LocalDatabase oldDb);
}

class GetItDatabaseReinitializer implements DatabaseReinitializer {
  const GetItDatabaseReinitializer();

  @override
  Future<void> reinitialize(LocalDatabase oldDb) async {
    // Close old connection
    await oldDb.close();

    // Replace the LocalDatabase singleton in get_it
    if (getIt.isRegistered<LocalDatabase>()) {
      await getIt.unregister<LocalDatabase>();
    }
    getIt.registerLazySingleton<LocalDatabase>(() => LocalDatabase());

    // Force open connection to apply migrations/indexes lazily
    // ignore: unused_local_variable
    final db = getIt<LocalDatabase>();
  }
}

class DatabaseManagementService {
  final LocalDatabase _database;
  final DatabaseFileOps _fileOps;
  final DatabaseReinitializer _reinitializer;
  final MarkdownExporter _markdownExporter;

  DatabaseManagementService({
    LocalDatabase? database,
    DatabaseFileOps? fileOps,
    DatabaseReinitializer? reinitializer,
    MarkdownExporter? markdownExporter,
  })  : _database = database ?? getIt<LocalDatabase>(),
        _fileOps = fileOps ?? const DefaultDatabaseFileOps(),
        _reinitializer = reinitializer ?? const GetItDatabaseReinitializer(),
        _markdownExporter = markdownExporter ?? const MarkdownExporter();

  /// Reset database by deleting db files first, then reinitializing a fresh connection
  Future<void> resetDatabase() async {
    debugPrint('[DB-SERVICE] resetDatabase START');
    // Ensure existing connection is closed before touching files
    try { await _database.close(); } catch (e) { debugPrint('[DB-SERVICE] close ignored: $e'); }
    if (getIt.isRegistered<LocalDatabase>()) {
      await getIt.unregister<LocalDatabase>();
      debugPrint('[DB-SERVICE] LocalDatabase unregistered');
    }
    // Delete DB files and create a brand new connection
    await _fileOps.deleteDatabase();
    debugPrint('[DB-SERVICE] database files deleted');
    getIt.registerLazySingleton<LocalDatabase>(() => LocalDatabase());
    // Force open
    // ignore: unused_local_variable
    final _ = getIt<LocalDatabase>();
    debugPrint('[DB-SERVICE] resetDatabase DONE');
  }

  /// Export database to the specified path
  Future<void> exportDatabaseTo(String destinationPath) async {
    debugPrint('[DB-SERVICE] exportDatabaseTo START -> $destinationPath');
    // Ensure destination directory exists
    final destFile = File(destinationPath);
    await destFile.parent.create(recursive: true);
    await _fileOps.backupDatabase(destinationPath);
    debugPrint('[DB-SERVICE] exportDatabaseTo DONE -> $destinationPath');
  }

  /// Import database from selected backup; destructive: replaces existing DB
  Future<void> importDatabaseFrom(String sourcePath) async {
    debugPrint('[DB-SERVICE] importDatabaseFrom START <- $sourcePath');
    // Close & unregister current connection to release file lock
    try { await _database.close(); } catch (e) { debugPrint('[DB-SERVICE] close ignored: $e'); }
    if (getIt.isRegistered<LocalDatabase>()) {
      await getIt.unregister<LocalDatabase>();
      debugPrint('[DB-SERVICE] LocalDatabase unregistered');
    }

    // Remove current DB files and restore from backup
    await _fileOps.deleteDatabase();
    debugPrint('[DB-SERVICE] database files deleted before restore');
    await _fileOps.restoreDatabase(sourcePath);
    debugPrint('[DB-SERVICE] database restored from $sourcePath');

    // Register a fresh connection pointing to the restored DB
    getIt.registerLazySingleton<LocalDatabase>(() => LocalDatabase());
    // Force open
    // ignore: unused_local_variable
    final _ = getIt<LocalDatabase>();
    debugPrint('[DB-SERVICE] importDatabaseFrom DONE');
  }

  /// Get domain tasks with subtasks due in [start, end)
  Future<List<Task>> getTasksDueInRange(DateTime start, DateTime end) async {
    // Fetch TaskData
    final taskData = await _database.getTasksDueInRange(start, end);
    // Map to domain with subtasks
    final tasks = await TaskMapper.fromTaskDataList(taskData, _database);
    return tasks;
  }

  /// Export tasks in range to markdown at [destinationPath]
  Future<void> exportMarkdownTo({
    required DateTime start,
    required DateTime end,
    required String destinationPath,
    String title = '\u4efb\u52a1\u5bfc\u51fa',
  }) async {
    debugPrint('[DB-SERVICE] exportMarkdownTo START -> $destinationPath, range=$start..$end');
    final tasks = await getTasksDueInRange(start, end);
    debugPrint('[DB-SERVICE] exportMarkdownTo tasks count: ${tasks.length}');
    final md = _markdownExporter.exportTasks(tasks: tasks, start: start, end: end, title: title);

    final file = File(destinationPath);
    await file.parent.create(recursive: true);
    await file.writeAsString(md);
    debugPrint('[DB-SERVICE] exportMarkdownTo DONE -> $destinationPath');
  }
}

