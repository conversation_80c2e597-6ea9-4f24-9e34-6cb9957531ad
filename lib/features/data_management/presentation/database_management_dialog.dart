import 'package:flutter/material.dart';
import '../../../app/theme.dart';
import '../../data_management/services/database_management_service.dart';
import '../../data_management/services/file_picker_service.dart';

class DatabaseManagementDialog extends StatefulWidget {
  const DatabaseManagementDialog({super.key});

  @override
  State<DatabaseManagementDialog> createState() => _DatabaseManagementDialogState();
}

enum _BusyOp { none, reset, exportDb, importDb, exportMd }

class _DatabaseManagementDialogState extends State<DatabaseManagementDialog> {
  final _service = DatabaseManagementService();
  final FilePickerService _filePicker = const FilePickerServiceImpl();

  _BusyOp _busy = _BusyOp.none;
  bool _interacting = false; // debounces pickers/confirm dialogs
  DateTimeRange? _mdRange;

  @override
  void initState() {
    super.initState();
    final now = DateTime.now();
    final start = DateTime(now.year, now.month, now.day);
    final end = start.add(const Duration(days: 1));
    _mdRange = DateTimeRange(start: start, end: end);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.all(AppTheme.spacing6),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(AppTheme.radius2xl)),
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 680),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.spacing6),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const Icon(Icons.storage, size: 24, color: AppTheme.textSecondary),
                    const SizedBox(width: AppTheme.spacing2),
                    Text('数据库管理', style: Theme.of(context).textTheme.headlineSmall),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: _busy == _BusyOp.none ? () => Navigator.of(context).pop() : null,
                      tooltip: '关闭',
                    ),
                  ],
                ),
                const SizedBox(height: AppTheme.spacing2),
                Text('在此执行数据库初始化、导出/导入、和 Markdown 导出操作。请谨慎执行破坏性操作。',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondary)),

                if (_busy != _BusyOp.none) ...[
                  const SizedBox(height: AppTheme.spacing4),
                  const LinearProgressIndicator(minHeight: 2),
                ] else if (_interacting) ...[
                  const SizedBox(height: AppTheme.spacing4),
                  Row(children: const [
                    SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2)),
                    SizedBox(width: 8),
                    Text('正在打开系统文件对话框…')
                  ]),
                ],

                const SizedBox(height: AppTheme.spacing6),

                _buildResetSection(context),
                const SizedBox(height: AppTheme.spacing6),
                _buildDbTransferSection(context),
                const SizedBox(height: AppTheme.spacing6),
                _buildMarkdownSection(context),

                const SizedBox(height: AppTheme.spacing6),
                Align(
                  alignment: Alignment.centerRight,
                  child: TextButton(
                    onPressed: _busy == _BusyOp.none ? () => Navigator.of(context).pop() : null,
                    child: const Text('关闭'),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(IconData icon, String title, String subtitle) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: AppTheme.textSecondary),
        const SizedBox(width: AppTheme.spacing2),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(title, style: Theme.of(context).textTheme.titleLarge),
              const SizedBox(height: AppTheme.spacing1),
              Text(subtitle, style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondary)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildResetSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(Icons.warning_amber_rounded, 'DB 初始化', '清空所有应用数据并重新创建数据库（不可恢复）'),
        const SizedBox(height: AppTheme.spacing3),
        Row(
          children: [
            FilledButton.icon(
              onPressed: (_busy == _BusyOp.none && !_interacting) ? _confirmAndReset : null,
              style: FilledButton.styleFrom(backgroundColor: AppTheme.errorColor),
              icon: const Icon(Icons.warning, color: Colors.white),
              label: const Text('DB初始化'),
            ),
            const SizedBox(width: AppTheme.spacing2),
            Text('此操作将永久删除所有数据。', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: AppTheme.textSecondary)),
          ],
        ),
      ],
    );
  }

  Widget _buildDbTransferSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(Icons.file_present_outlined, '数据库导出 / 导入', '完整导出或导入 SQLite 数据库文件'),
        const SizedBox(height: AppTheme.spacing3),
        Wrap(
          spacing: AppTheme.spacing2,
          runSpacing: AppTheme.spacing2,
          children: [
            OutlinedButton(
              onPressed: (_busy == _BusyOp.none && !_interacting) ? _handleExportDb : null,
              child: const Text('DB文件导出'),
            ),
            OutlinedButton(
              onPressed: (_busy == _BusyOp.none && !_interacting) ? _confirmAndImportDb : null,
              child: const Text('DB文件导入'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMarkdownSection(BuildContext context) {
    final rangeText = _mdRangeText();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(Icons.article_outlined, 'Markdown 导出', '导出所选日期范围内的任务为 Markdown 文件'),
        const SizedBox(height: AppTheme.spacing3),
        Row(
          children: [
            Expanded(
              child: TextField(
                readOnly: true,
                decoration: InputDecoration(
                  labelText: '日期范围',
                  hintText: '请选择日期范围',
                ),
                controller: TextEditingController(text: rangeText),
                onTap: (_busy == _BusyOp.none && !_interacting) ? _pickDateRange : null,
              ),
            ),
            const SizedBox(width: AppTheme.spacing2),
            OutlinedButton.icon(
              onPressed: (_busy == _BusyOp.none && !_interacting) ? _pickDateRange : null,
              icon: const Icon(Icons.date_range),
              label: const Text('选择日期'),
            ),
            const SizedBox(width: AppTheme.spacing2),
            FilledButton(
              onPressed: (_busy == _BusyOp.none && !_interacting) ? _handleExportMarkdown : null,
              child: const Text('md文件导出'),
            ),
          ],
        ),
      ],
    );
  }

  Future<void> _confirmAndReset() async {
    debugPrint('[DB-MODAL] Click: Reset DB');
    final ok = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('确认初始化数据库'),
        content: const Text('此操作将清空所有应用数据且不可恢复，是否继续？'),
        actions: [
          TextButton(onPressed: () => Navigator.of(ctx).pop(false), child: const Text('取消')),
          FilledButton(
            style: FilledButton.styleFrom(backgroundColor: AppTheme.errorColor),
            onPressed: () => Navigator.of(ctx).pop(true),
            child: const Text('确认删除'),
          ),
        ],
      ),
    );
    debugPrint('[DB-MODAL] Reset confirm result: $ok');
    if (ok != true) return;

    await _runBusy(_BusyOp.reset, () async {
      debugPrint('[DB-MODAL] Reset START');
      await _service.resetDatabase();
      debugPrint('[DB-MODAL] Reset DONE');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text('数据库已初始化'), backgroundColor: AppTheme.successColor,
        ));
      }
    });
  }

  Future<void> _handleExportDb() async {
    if (_interacting || _busy != _BusyOp.none) return;
    setState(() => _interacting = true);
    try {
      debugPrint('[DB-MODAL] Click: Export DB');
      final dest = await _filePicker.pickDatabaseExportPath();
      debugPrint('[DB-MODAL] Export DB path: $dest');
      if (dest == null) return;

      await _runBusy(_BusyOp.exportDb, () async {
        debugPrint('[DB-MODAL] Export DB START to $dest');
        await _service.exportDatabaseTo(dest);
        debugPrint('[DB-MODAL] Export DB DONE to $dest');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('数据库导出成功'), backgroundColor: AppTheme.successColor,
          ));
        }
      });
    } finally {
      if (mounted) setState(() => _interacting = false);
    }
  }

  Future<void> _confirmAndImportDb() async {
    if (_interacting || _busy != _BusyOp.none) return;
    setState(() => _interacting = true);
    try {
      debugPrint('[DB-MODAL] Click: Import DB');
      final src = await _filePicker.pickDatabaseImportPath();
      debugPrint('[DB-MODAL] Import DB source: $src');
      if (src == null) return; // user canceled

      final ok = await showDialog<bool>(
        context: context,
        builder: (ctx) => AlertDialog(
          title: const Text('确认导入数据库'),
          content: const Text('导入将覆盖并清空现有数据库，且不可撤销。是否继续？'),
          actions: [
            TextButton(onPressed: () => Navigator.of(ctx).pop(false), child: const Text('取消')),
            FilledButton(
              style: FilledButton.styleFrom(backgroundColor: AppTheme.errorColor),
              onPressed: () => Navigator.of(ctx).pop(true),
              child: const Text('确认导入'),
            ),
          ],
        ),
      );
      debugPrint('[DB-MODAL] Import confirm result: $ok');
      if (ok != true) return;

      await _runBusy(_BusyOp.importDb, () async {
        debugPrint('[DB-MODAL] Import DB START from $src');
        await _service.importDatabaseFrom(src);
        debugPrint('[DB-MODAL] Import DB DONE from $src');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('数据库导入成功'), backgroundColor: AppTheme.successColor,
          ));
        }
      });
    } finally {
      if (mounted) setState(() => _interacting = false);
    }
  }

  Future<void> _handleExportMarkdown() async {
    if (_interacting || _busy != _BusyOp.none) return;
    setState(() => _interacting = true);
    try {
      debugPrint('[DB-MODAL] Click: Export Markdown');
      final range = _mdRange;
      if (range == null) {
        debugPrint('[DB-MODAL] Export Markdown aborted: no range');
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
          content: Text('请选择日期范围'), backgroundColor: AppTheme.errorColor,
        ));
        return;
      }

      final suggested = 'tasks_${_fmt(range.start)}_${_fmt(range.end.subtract(const Duration(days: 1)))}.md';
      final savePath = await _filePicker.pickMarkdownExportPath(suggestedName: suggested);
      debugPrint('[DB-MODAL) Markdown save path: $savePath');
      if (savePath == null) return; // user canceled

      await _runBusy(_BusyOp.exportMd, () async {
        debugPrint('[DB-MODAL] Export Markdown START to $savePath, range: ${_fmt(range.start)} ~ ${_fmt(range.end.subtract(const Duration(days: 1)))}');
        await _service.exportMarkdownTo(start: range.start, end: range.end, destinationPath: savePath, title: '任务导出');
        debugPrint('[DB-MODAL] Export Markdown DONE to $savePath');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
            content: Text('Markdown 导出成功'), backgroundColor: AppTheme.successColor,
          ));
        }
      });
    } finally {
      if (mounted) setState(() => _interacting = false);
    }
  }

  Future<void> _pickDateRange() async {
    debugPrint('[DB-MODAL] Click: Pick Date Range');
    final initial = _mdRange ?? DateTimeRange(start: DateTime.now(), end: DateTime.now().add(const Duration(days: 1)));
    final picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
      initialDateRange: initial,
    );
    debugPrint('[DB-MODAL] Date range picked: ${picked == null ? 'null' : _fmt(picked.start)} ~ ${picked == null ? '' : _fmt(picked.end.subtract(const Duration(days: 1)))}');
    if (picked != null) {
      setState(() => _mdRange = picked);
    }
  }

  Future<void> _runBusy(_BusyOp op, Future<void> Function() action) async {
    debugPrint('[DB-MODAL] RUN BUSY START: \\u003d${op.toString()}');
    try {
      setState(() => _busy = op);
      await action();
      debugPrint('[DB-MODAL] RUN BUSY DONE: \\u003d${op.toString()}');
    } catch (e, st) {
      debugPrint('[DB-MODAL] RUN BUSY ERROR: op=${op.toString()} err=$e');
      debugPrint(st.toString());
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(
          content: Text('操作失败：$e'), backgroundColor: AppTheme.errorColor,
        ));
      }
    } finally {
      if (mounted) setState(() => _busy = _BusyOp.none);
    }
  }

  String _mdRangeText() {
    if (_mdRange == null) return '';
    final s = _fmt(_mdRange!.start);
    final e = _fmt(_mdRange!.end.subtract(const Duration(days: 1)));
    return '$s 至 $e';
  }

  String _fmt(DateTime d) {
    return '${d.year.toString().padLeft(4, '0')}-${d.month.toString().padLeft(2, '0')}-${d.day.toString().padLeft(2, '0')}';
  }
}

