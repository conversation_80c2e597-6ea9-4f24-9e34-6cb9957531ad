import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../domain/models/task_model.dart';
import '../../bloc/task_list_bloc.dart';
import '../../bloc/task_list_event.dart';
import '../../bloc/task_list_state.dart';

class TaskFilterBar extends StatelessWidget {
  const TaskFilterBar({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TaskListBloc, TaskListState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border(
              bottom: BorderSide(
                color: Colors.grey[200]!,
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              // 优先级过滤器
              _buildPriorityFilter(context, state),

              const SizedBox(width: 16),

              // 完成状态过滤器
              _buildCompletionFilter(context, state),

              const SizedBox(width: 16),

              // 日期过滤器
              _buildDateFilter(context, state),

              const Spacer(),

              // 排序下拉菜单
              _buildSortDropdown(context, state),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPriorityFilter(BuildContext context, TaskListState state) {
    return PopupMenuButton<Priority?>(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.filter_list,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 6),
            Text(
              _getPriorityFilterText(state.priorityFilter),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: Colors.grey[600],
            ),
          ],
        ),
      ),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: null,
          child: Row(
            children: [
              Icon(Icons.clear, size: 16),
              SizedBox(width: 8),
              Text('全部优先级'),
            ],
          ),
        ),
        ...Priority.values.map((priority) {
          final priorityInfo = _getPriorityInfo(priority);
          return PopupMenuItem(
            value: priority,
            child: Row(
              children: [
                Icon(
                  priorityInfo.icon,
                  size: 16,
                  color: priorityInfo.color,
                ),
                const SizedBox(width: 8),
                Text(priorityInfo.title),
              ],
            ),
          );
        }),
      ],
      onSelected: (priority) {
        context.read<TaskListBloc>().add(
              TaskListEvent.filterByPriority(priority),
            );
      },
    );
  }

  Widget _buildCompletionFilter(BuildContext context, TaskListState state) {
    return PopupMenuButton<bool?>(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle_outline,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 6),
            Text(
              _getCompletionFilterText(state.completionFilter),
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: Colors.grey[600],
            ),
          ],
        ),
      ),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: null,
          child: Row(
            children: [
              Icon(Icons.clear, size: 16),
              SizedBox(width: 8),
              Text('全部状态'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: false,
          child: Row(
            children: [
              Icon(Icons.radio_button_unchecked,
                  size: 16, color: Colors.orange),
              SizedBox(width: 8),
              Text('未完成'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: true,
          child: Row(
            children: [
              Icon(Icons.check_circle, size: 16, color: Colors.green),
              SizedBox(width: 8),
              Text('已完成'),
            ],
          ),
        ),
      ],
      onSelected: (isCompleted) {
        context.read<TaskListBloc>().add(
              TaskListEvent.filterByCompletion(isCompleted),
            );
      },
    );
  }

  Widget _buildDateFilter(BuildContext context, TaskListState state) {
    return PopupMenuButton<String>(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.grey[300]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.calendar_today,
              size: 16,
              color: Colors.grey[600],
            ),
            const SizedBox(width: 6),
            Text(
              _getDateFilterText('all'), // Default to 'all' filter
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: Colors.grey[600],
            ),
          ],
        ),
      ),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'all',
          child: Row(
            children: [
              Icon(Icons.clear, size: 16),
              SizedBox(width: 8),
              Text('全部日期'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'today',
          child: Row(
            children: [
              Icon(Icons.today, size: 16, color: Colors.orange),
              SizedBox(width: 8),
              Text('今天'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'tomorrow',
          child: Row(
            children: [
              Icon(Icons.schedule, size: 16, color: Colors.blue),
              SizedBox(width: 8),
              Text('明天'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'week',
          child: Row(
            children: [
              Icon(Icons.view_week, size: 16, color: Colors.purple),
              SizedBox(width: 8),
              Text('本周'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'overdue',
          child: Row(
            children: [
              Icon(Icons.warning, size: 16, color: Colors.red),
              SizedBox(width: 8),
              Text('已逾期'),
            ],
          ),
        ),
      ],
      onSelected: (filter) {
        // TODO: 实现日期过滤功能
        // context.read<TaskListBloc>().add(
        //   TaskListEvent.dateFilterChanged(filter),
        // );
      },
    );
  }

  Widget _buildSortDropdown(BuildContext context, TaskListState state) {
    return PopupMenuButton<String>(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.blue[200]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.sort,
              size: 16,
              color: Colors.blue[600],
            ),
            const SizedBox(width: 6),
            Text(
              _getSortText('priority'), // Default to 'priority' sort
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue[700],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: Colors.blue[600],
            ),
          ],
        ),
      ),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'priority',
          child: Row(
            children: [
              Icon(Icons.priority_high, size: 16),
              SizedBox(width: 8),
              Text('按优先级'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'dueDate',
          child: Row(
            children: [
              Icon(Icons.calendar_today, size: 16),
              SizedBox(width: 8),
              Text('按截止日期'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'creationDate',
          child: Row(
            children: [
              Icon(Icons.create, size: 16),
              SizedBox(width: 8),
              Text('按创建时间'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'title',
          child: Row(
            children: [
              Icon(Icons.sort_by_alpha, size: 16),
              SizedBox(width: 8),
              Text('按标题'),
            ],
          ),
        ),
      ],
      onSelected: (sortBy) {
        // TODO: 实现排序功能
        // context.read<TaskListBloc>().add(
        //   TaskListEvent.sortByChanged(sortBy),
        // );
      },
    );
  }

  String _getPriorityFilterText(Priority? priority) {
    if (priority == null) return '全部优先级';
    return _getPriorityInfo(priority).title;
  }

  String _getCompletionFilterText(bool? isCompleted) {
    if (isCompleted == null) return '全部状态';
    if (isCompleted) return '已完成';
    return '未完成';
  }

  String _getDateFilterText(String dateFilter) {
    switch (dateFilter) {
      case 'all':
        return '全部日期';
      case 'today':
        return '今天';
      case 'tomorrow':
        return '明天';
      case 'week':
        return '本周';
      case 'overdue':
        return '已逾期';
      default:
        return '全部日期';
    }
  }

  String _getSortText(String sortBy) {
    switch (sortBy) {
      case 'priority':
        return '按优先级';
      case 'dueDate':
        return '按截止日期';
      case 'creationDate':
        return '按创建时间';
      case 'title':
        return '按标题';
      default:
        return '按优先级';
    }
  }

  PriorityInfo _getPriorityInfo(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return PriorityInfo(
          title: '重要且紧急',
          color: Colors.red,
          icon: Icons.priority_high,
        );
      case Priority.importantNotUrgent:
        return PriorityInfo(
          title: '重要但不紧急',
          color: Colors.blue,
          icon: Icons.schedule,
        );
      case Priority.urgentNotImportant:
        return PriorityInfo(
          title: '紧急但不重要',
          color: Colors.orange,
          icon: Icons.warning,
        );
      case Priority.notUrgentNotImportant:
        return PriorityInfo(
          title: '不重要且不紧急',
          color: Colors.grey,
          icon: Icons.low_priority,
        );
    }
  }
}

class PriorityInfo {
  final String title;
  final Color color;
  final IconData icon;

  PriorityInfo({
    required this.title,
    required this.color,
    required this.icon,
  });
}
