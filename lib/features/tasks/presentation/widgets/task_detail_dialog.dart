import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import '../../../../app/theme.dart';
import '../../../../core/di/injection.dart';
import '../../../../domain/models/task_model.dart';
import '../../../../domain/repositories/task_repository.dart';
import '../../../../domain/services/subtask_manager.dart';
import '../../../../domain/services/task_state_manager.dart';
import '../task_editor_dialog.dart';
import '../../bloc/task_list_bloc.dart';
import '../../bloc/task_list_event.dart';
import '../../bloc/task_list_state.dart';

/// 任务详情弹窗 - 基于shadcn/ui设计风格
class TaskDetailDialog extends StatefulWidget {
  final Task task;
  final VoidCallback? onTaskUpdated;
  final VoidCallback? onTaskDeleted;

  const TaskDetailDialog({
    super.key,
    required this.task,
    this.onTaskUpdated,
    this.onTaskDeleted,
  });

  @override
  State<TaskDetailDialog> createState() => _TaskDetailDialogState();
}

class _TaskDetailDialogState extends State<TaskDetailDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TaskListBloc, TaskListState>(
      builder: (context, state) {
        // 从BLoC状态中获取最新的任务数据
        final currentTask = state.tasks.firstWhere(
          (t) => t.id == widget.task.id,
          orElse: () => widget.task, // 如果找不到，使用原始任务
        );

        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: Dialog(
                  backgroundColor: Colors.transparent,
                  insetPadding: const EdgeInsets.all(AppTheme.spacing4),
                  child: Container(
                    constraints: const BoxConstraints(
                      maxWidth: 620, // 增加最大宽度
                      maxHeight: 760, // 增加最大高度
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.cardLight,
                      borderRadius: BorderRadius.circular(AppTheme.radiusXl),
                      border: Border.all(color: AppTheme.borderLight),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 24,
                          offset: const Offset(0, 12),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildHeader(currentTask),
                        Flexible(
                          child: SingleChildScrollView(
                            padding: const EdgeInsets.fromLTRB(
                              AppTheme.spacing6, // 24px 左右内边距
                              AppTheme.spacing4, // 16px 顶部内边距
                              AppTheme.spacing6, // 24px 右内边距
                              AppTheme.spacing6, // 24px 底部内边距
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // 📋 任务信息区块
                                _buildSectionCard(
                                  child: _buildTaskInfo(currentTask),
                                ),
                                const SizedBox(height: AppTheme.spacing6), // 24px 间距

                                // ✅ 子任务区块
                                _buildSectionCard(
                                  child: _buildSubtasksSection(currentTask),
                                ),
                                const SizedBox(height: AppTheme.spacing6), // 24px 间距

                                // 📝 备注区块
                                _buildSectionCard(
                                  child: _buildNotesSection(currentTask),
                                ),
                              ],
                            ),
                          ),
                        ),
                        _buildActions(currentTask),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildHeader(Task currentTask) {
    final priorityColor = AppTheme.getPriorityColor(currentTask.priority.name);

    return Container(
      padding: const EdgeInsets.fromLTRB(
        AppTheme.spacing6, // 24px 左内边距
        AppTheme.spacing5, // 20px 顶部内边距
        AppTheme.spacing5, // 20px 右内边距
        AppTheme.spacing4, // 16px 底部内边距
      ),
      decoration: BoxDecoration(
        color: priorityColor.withOpacity(0.05),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppTheme.radiusXl),
          topRight: Radius.circular(AppTheme.radiusXl),
        ),
        border: Border(
          bottom: BorderSide(
            color: AppTheme.borderLight,
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // 顶部行：完成状态和关闭按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 完成状态切换
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Transform.scale(
                    scale: 1.2, // 增加复选框大小
                    child: Checkbox(
                      value: currentTask.isCompleted,
                      onChanged: _toggleTaskCompletion,
                      activeColor: priorityColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                      ),
                    ),
                  ),
                  const SizedBox(width: AppTheme.spacing2),
                  Text(
                    currentTask.isCompleted ? '已完成' : '未完成',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: currentTask.isCompleted ? priorityColor : AppTheme.textSecondary,
                      fontWeight: FontWeight.w600,
                      fontSize: 15, // 增加字体大小
                    ),
                  ),
                ],
              ),

              // 关闭按钮
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close_outlined, size: 22), // 增加图标大小
                style: IconButton.styleFrom(
                  backgroundColor: AppTheme.slate50,
                  foregroundColor: AppTheme.textSecondary,
                  minimumSize: const Size(40, 40), // 增加按钮大小
                  padding: const EdgeInsets.all(AppTheme.spacing2),
                ),
              ),
            ],
          ),

          const SizedBox(height: AppTheme.spacing3), // 使用主题间距

          // 任务标题行
          Row(
            children: [
              // 优先级指示器
              Container(
                width: 4, // 增加指示器宽度
                height: 40, // 增加指示器高度
                decoration: BoxDecoration(
                  color: priorityColor,
                  borderRadius: BorderRadius.circular(AppTheme.radiusXs),
                ),
              ),
              const SizedBox(width: AppTheme.spacing4), // 增加间距

              // 任务标题和优先级标签
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      currentTask.title,
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 20, // 增加标题字体大小
                        decoration: currentTask.isCompleted
                            ? TextDecoration.lineThrough
                            : null,
                        color: currentTask.isCompleted
                            ? AppTheme.textSecondary
                            : AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: AppTheme.spacing2), // 使用主题间距
                    _buildPriorityChip(currentTask),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPriorityChip(Task currentTask) {
    final priorityColor = AppTheme.getPriorityColor(currentTask.priority.name);
    final priorityText = _getPriorityText(currentTask.priority);

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing3, // 12px 水平内边距
        vertical: AppTheme.spacing1_5, // 6px 垂直内边距
      ),
      decoration: BoxDecoration(
        color: priorityColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusFull), // 完全圆角
        border: Border.all(
          color: priorityColor.withOpacity(0.3),
          width: 1, // 标准边框宽度
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8, // 增加指示器大小
            height: 8,
            decoration: BoxDecoration(
              color: priorityColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppTheme.spacing2),
          Text(
            priorityText,
            style: TextStyle(
              color: priorityColor,
              fontSize: 13, // 增加字体大小
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _getPriorityText(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return '重要且紧急';
      case Priority.importantNotUrgent:
        return '重要但不紧急';
      case Priority.urgentNotImportant:
        return '紧急但不重要';
      case Priority.notUrgentNotImportant:
        return '不重要且不紧急';
    }
  }

  void _toggleTaskCompletion(bool? value) async {
    print('🔄 TaskDetailDialog._toggleTaskCompletion() 开始执行');
    print('📝 任务ID: ${widget.task.id}');
    print('📝 新完成状态: $value');
    print('📝 Widget挂载状态: $mounted');

    if (value == null || !mounted) {
      print('❌ 参数无效或Widget未挂载，停止执行');
      return;
    }

    // 检查子任务完成状态逻辑
    if (value == true && widget.task.subtasks.isNotEmpty) {
      final incompleteSubtasks = widget.task.subtasks.where((s) => !s.isCompleted).toList();
      if (incompleteSubtasks.isNotEmpty) {
        print('❌ 存在未完成的子任务，无法完成主任务');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('请先完成所有子任务 (${incompleteSubtasks.length}个未完成)'),
              backgroundColor: Colors.orange,
              duration: const Duration(seconds: 2),
            ),
          );
        }
        return;
      }
    }

    try {
      print('📦 获取TaskRepository...');
      final taskRepository = getIt<TaskRepository>();
      print('✅ TaskRepository获取成功');

      print('🔄 创建更新后的任务对象...');
      // Use the proper domain methods for completion state changes
      final updatedTask = value
          ? widget.task.markAsCompleted()
          : widget.task.markAsIncomplete();
      print('✅ 任务对象创建成功');

      print('💾 开始更新任务到数据库...');
      await taskRepository.updateTask(updatedTask);
      print('✅ 任务数据库更新成功');

      // 再次检查Widget是否仍然挂载
      if (!mounted) {
        print('❌ Widget已卸载，停止后续操作');
        return;
      }

      print('📡 发送TaskListBloc事件...');
      final taskListBloc = getIt<TaskListBloc>();
      taskListBloc.add(
        TaskListEvent.completionToggled(
          taskId: widget.task.id,
          isCompleted: value,
        ),
      );
      print('✅ BLoC事件发送成功');

      // 显示反馈 - 在调用回调前显示，避免Widget被卸载
      if (mounted) {
        print('🎉 显示成功消息');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(value ? '任务已完成' : '任务已标记为未完成'),
            backgroundColor: value ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      print('📞 调用onTaskUpdated回调...');
      widget.onTaskUpdated?.call();
      print('✅ 回调执行完成');
      print('✅ TaskDetailDialog._toggleTaskCompletion() 执行完成');
    } catch (e) {
      print('❌ TaskDetailDialog._toggleTaskCompletion() 发生错误:');
      print('❌ 错误信息: $e');
      print('❌ 错误类型: ${e.runtimeType}');

      if (mounted) {
        print('🚨 显示错误消息给用户');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('更新失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 统一的区块卡片样式
  Widget _buildSectionCard({required Widget child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacing4),
      decoration: BoxDecoration(
        color: AppTheme.cardLight,
        borderRadius: BorderRadius.circular(AppTheme.radiusXl),
        border: Border.all(
          color: AppTheme.borderLight,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.02),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }

  Widget _buildTaskInfo(Task currentTask) {
    return Row(
        children: [
          // 创建时间
          Expanded(
            child: _buildUltraCompactTag(
              icon: Icons.schedule,
              title: '创建时间',
              content: DateFormat('yyyy-MM-dd HH:mm').format(currentTask.creationDate),
              color: AppTheme.infoColor,
            ),
          ),
          Container(width: 1, height: 20, color: Theme.of(context).dividerColor.withOpacity(0.3)),

          // 截止时间
          Expanded(
            child: _buildUltraCompactTag(
              icon: Icons.event,
              title: '截止时间',
              content: currentTask.dueDate != null
                  ? DateFormat('yyyy-MM-dd').format(currentTask.dueDate!)
                  : '未设置',
              color: currentTask.dueDate != null
                  ? (currentTask.dueDate!.isBefore(DateTime.now())
                      ? AppTheme.errorColor
                      : AppTheme.successColor)
                  : AppTheme.textMuted,
            ),
          ),
          Container(width: 1, height: 20, color: Theme.of(context).dividerColor.withOpacity(0.3)),

          // 子任务进度
          Expanded(
            child: _buildUltraCompactTag(
              icon: Icons.checklist,
              title: '子任务进度',
              content: currentTask.subtasks.isEmpty
                  ? '0/0'
                  : '${currentTask.subtasks.where((s) => s.isCompleted).length}/${currentTask.subtasks.length}',
              color: currentTask.subtasks.isEmpty
                  ? AppTheme.textMuted
                  : (currentTask.subtasks.every((s) => s.isCompleted)
                      ? AppTheme.successColor
                      : AppTheme.warningColor),
            ),
          ),
          Container(width: 1, height: 20, color: Theme.of(context).dividerColor.withOpacity(0.3)),

          // 最后更新
          Expanded(
            child: _buildUltraCompactTag(
              icon: Icons.update,
              title: '最后更新',
              content: DateFormat('yyyy-MM-dd HH:mm').format(currentTask.creationDate),
              color: AppTheme.primaryBlueDark,
            ),
          ),
        ],
      );
  }

  Widget _buildInfoCard({
    required IconData icon,
    required String title,
    required String content,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: color,
              ),
              const SizedBox(width: 6),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 紧凑的信息标签组件
  Widget _buildCompactInfoTag({
    required IconData icon,
    required String title,
    required String content,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.08),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 0.8,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 12,
                color: color,
              ),
              const SizedBox(width: 4),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          Text(
            content,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 11,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 专业的信息标签组件 - 上下结构
  Widget _buildUltraCompactTag({
    required IconData icon,
    required String title,
    required String content,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题行
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 10,
                color: color.withOpacity(0.8),
              ),
              const SizedBox(width: 3),
              Flexible(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 9,
                    fontWeight: FontWeight.w500,
                    color: color.withOpacity(0.9),
                  ),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          // 内容行
          Text(
            content,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSubtasksSection(Task currentTask) {
    if (currentTask.subtasks.isEmpty) {
      return _buildEmptySection(
        icon: Icons.checklist,
        title: '子任务',
        message: '暂无子任务',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 子任务标题 - 统一样式
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.08),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.checklist,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 6),
              Text(
                '子任务 (${currentTask.subtasks.where((s) => s.isCompleted).length}/${currentTask.subtasks.length})',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 6),
        // 紧凑的子任务列表
        ...currentTask.subtasks.map((subtask) {
          return _buildCompactSubtaskItem(subtask);
        }).toList(),
      ],
    );
  }

  Widget _buildSubtaskItem(SubTask subtask) {
    return Container(
      key: ValueKey(subtask.id), // 使用子任务ID作为唯一key
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).dividerColor.withOpacity(0.5),
        ),
      ),
      child: Row(
        children: [
          // 子任务完成状态
          Transform.scale(
            scale: 0.9,
            child: Checkbox(
              value: subtask.isCompleted,
              onChanged: (value) => _toggleSubtaskCompletion(subtask.id, value ?? false),
              activeColor: AppTheme.getPriorityColor(widget.task.priority.name),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(3),
              ),
            ),
          ),

          // 子任务标题
          Expanded(
            child: Text(
              subtask.title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                decoration: subtask.isCompleted ? TextDecoration.lineThrough : null,
                color: subtask.isCompleted
                    ? Theme.of(context).colorScheme.onSurfaceVariant
                    : null,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          // 完成状态指示器
          if (subtask.isCompleted)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.check,
                size: 12,
                color: Colors.green.shade700,
              ),
            ),
        ],
      ),
    );
  }

  /// 极度紧凑的子任务项组件
  Widget _buildCompactSubtaskItem(SubTask subtask) {
    return Container(
      key: ValueKey(subtask.id),
      margin: const EdgeInsets.only(bottom: 2),
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: subtask.isCompleted
            ? Colors.green.withOpacity(0.03)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(4),
        border: subtask.isCompleted
            ? Border.all(color: Colors.green.withOpacity(0.15), width: 0.5)
            : null,
      ),
      child: Row(
        children: [
          // 超紧凑的复选框
          SizedBox(
            width: 16,
            height: 16,
            child: Transform.scale(
              scale: 0.7,
              child: Checkbox(
                value: subtask.isCompleted,
                onChanged: (value) => _toggleSubtaskCompletion(subtask.id, value ?? false),
                activeColor: AppTheme.getPriorityColor(widget.task.priority.name),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(2),
                ),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
              ),
            ),
          ),

          const SizedBox(width: 6),

          // 子任务标题
          Expanded(
            child: Text(
              subtask.title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                decoration: subtask.isCompleted ? TextDecoration.lineThrough : null,
                color: subtask.isCompleted
                    ? Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.7)
                    : Theme.of(context).textTheme.bodySmall?.color,
                fontWeight: FontWeight.w500,
                fontSize: 12,
                height: 1.2,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 极简完成状态指示器
          if (subtask.isCompleted)
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.check,
                size: 8,
                color: Colors.green.shade600,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(Task currentTask) {
    if (currentTask.notes == null || currentTask.notes!.trim().isEmpty) {
      return _buildEmptySection(
        icon: Icons.notes,
        title: '备注',
        message: '暂无备注',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 紧凑的备注标题
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withOpacity(0.08),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.notes,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 6),
              Text(
                '备注',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12), // 减少内边距
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
            borderRadius: BorderRadius.circular(8), // 减少圆角
            border: Border.all(
              color: Theme.of(context).dividerColor.withOpacity(0.5),
              width: 0.8,
            ),
          ),
          child: Text(
            currentTask.notes!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              height: 1.4, // 减少行高
              fontSize: 13,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptySection({
    required IconData icon,
    required String title,
    required String message,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).dividerColor.withOpacity(0.3),
              style: BorderStyle.solid,
            ),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                size: 32,
                color: Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.5),
              ),
              const SizedBox(height: 8),
              Text(
                message,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActions(Task currentTask) {
    return Container(
      padding: const EdgeInsets.fromLTRB(AppTheme.spacing4, AppTheme.spacing3, AppTheme.spacing4, AppTheme.spacing4), // 减少内边距
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
        border: Border(
          top: BorderSide(
            color: Theme.of(context).dividerColor.withOpacity(0.1),
          ),
        ),
      ),
      child: Row(
        children: [
          // 编辑按钮
          Expanded(
            child: FilledButton.icon(
              onPressed: _editTask,
              icon: const Icon(Icons.edit, size: 16),
              label: const Text('编辑任务', style: TextStyle(fontSize: 14)),
              style: FilledButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing2_5), // 减少垂直内边距
                minimumSize: const Size(0, 36), // 减少最小高度
              ),
            ),
          ),

          const SizedBox(width: AppTheme.spacing2_5),

          // 删除按钮
          OutlinedButton.icon(
            onPressed: _deleteTask,
            icon: const Icon(Icons.delete, size: 16),
            label: const Text('删除', style: TextStyle(fontSize: 14)),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.errorColor,
              side: const BorderSide(color: AppTheme.errorColor, width: 1),
              padding: const EdgeInsets.symmetric(vertical: AppTheme.spacing2_5, horizontal: AppTheme.spacing3), // 减少内边距
              minimumSize: const Size(0, 36), // 减少最小高度
            ),
          ),
        ],
      ),
    );
  }

  void _toggleSubtaskCompletion(String subtaskId, bool isCompleted) async {
    if (!mounted) return;

    try {
      // 🚀 高性能无闪烁状态更新策略 - 只使用BLoC事件
      final taskListBloc = getIt<TaskListBloc>();

      // 直接发送子任务切换事件，让BLoC处理所有逻辑
      taskListBloc.add(
        TaskListEvent.subtaskToggled(
          taskId: widget.task.id,
          subtaskId: subtaskId,
          isCompleted: isCompleted,
        ),
      );

      // 调用回调通知父组件（如果需要）
      widget.onTaskUpdated?.call();

    } catch (e) {
      print('❌ 子任务状态更新失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作失败: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(milliseconds: 1500),
          ),
        );
      }
    }
  }

  void _editTask() {
    if (!mounted) return;

    // Store context reference before navigation
    final currentContext = context;
    final navigator = Navigator.of(currentContext);

    navigator.pop(); // 关闭详情弹窗

    showDialog(
      context: currentContext,
      builder: (dialogContext) => BlocProvider.value(
        value: getIt<TaskListBloc>(),
        child: TaskEditorDialog(
          existingTask: widget.task,
          onTaskSaved: (updatedTask) {
            // 更新BLoC状态
            final taskListBloc = getIt<TaskListBloc>();
            taskListBloc.add(
              TaskListEvent.taskUpdated(updatedTask),
            );

            // TaskEditorDialog会自己显示成功消息，这里不需要重复显示
            // 只调用回调通知更新
            widget.onTaskUpdated?.call();
          },
        ),
      ),
    );
  }

  void _deleteTask() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除任务「${widget.task.title}」吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          FilledButton(
            onPressed: () async {
              // Store context references before async operations
              final navigator = Navigator.of(context);
              final rootContext = context;

              navigator.pop(); // 关闭确认对话框
              navigator.pop(); // 关闭详情弹窗

              try {
                final taskRepository = getIt<TaskRepository>();
                await taskRepository.deleteTask(widget.task.id);

                if (mounted) {
                  // 更新BLoC状态
                  final taskListBloc = getIt<TaskListBloc>();
                  taskListBloc.add(
                    TaskListEvent.taskDeleted(taskId: widget.task.id),
                  );

                  widget.onTaskDeleted?.call();

                  // 使用根context显示SnackBar
                  ScaffoldMessenger.of(rootContext).showSnackBar(
                    SnackBar(
                      content: Text('任务「${widget.task.title}」已删除'),
                      backgroundColor: Colors.red,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(rootContext).showSnackBar(
                    SnackBar(
                      content: Text('删除失败: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: FilledButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
