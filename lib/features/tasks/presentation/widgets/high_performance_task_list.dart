import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../app/theme.dart';
import '../../../../domain/models/task_model.dart';
import '../../bloc/task_list_bloc.dart';
import '../../bloc/task_list_event.dart';
import '../task_editor_dialog.dart';
import 'task_detail_dialog.dart';
import '../algorithms/task_sorter.dart';
import 'performance_monitor.dart';

/// 高性能虚拟列表组件
/// 
/// 特性：
/// - 虚拟滚动：只渲染可见区域的任务
/// - 智能缓存：缓存已渲染的组件
/// - 内存优化：复用组件实例
/// - 批量更新：合并状态更新
/// - 一键置顶：底部悬浮按钮
class HighPerformanceTaskList extends StatefulWidget {
  final List<Task> tasks;
  final Priority priority;

  const HighPerformanceTaskList({
    super.key,
    required this.tasks,
    required this.priority,
  });

  @override
  State<HighPerformanceTaskList> createState() => _HighPerformanceTaskListState();
}

class _HighPerformanceTaskListState extends State<HighPerformanceTaskList>
    with AutomaticKeepAliveClientMixin {
  
  // 性能优化：保持状态，避免重建
  @override
  bool get wantKeepAlive => true;

  // 滚动控制器 - 用于一键置顶
  final ScrollController _scrollController = ScrollController();
  
  // 性能缓存
  final Map<String, Widget> _widgetCache = {};
  final Map<String, TaskStats> _statsCache = {};
  
  // 虚拟列表参数 - 优化版本
  static const double itemHeight = 80.0; // 固定高度提升性能
  static const int bufferSize = 8; // 增加缓冲区大小以提升滚动体验
  static const double scrollThreshold = 200.0; // 滚动阈值

  // 滚动状态管理
  bool _showScrollToTop = false;
  
  @override
  void initState() {
    super.initState();
    // 监听滚动事件以优化滚动到顶部按钮显示
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _widgetCache.clear();
    _statsCache.clear();
    super.dispose();
  }

  /// 滚动监听器 - 优化滚动到顶部按钮显示
  void _onScroll() {
    final shouldShow = _scrollController.hasClients &&
                      _scrollController.offset > scrollThreshold;

    if (shouldShow != _showScrollToTop) {
      setState(() {
        _showScrollToTop = shouldShow;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // AutomaticKeepAliveClientMixin 要求

    if (widget.tasks.isEmpty) {
      return _buildEmptyState();
    }

    return PerformanceMonitor(
      enabled: false, // 生产环境关闭
      child: Stack(
        children: [
          // 高性能虚拟列表
          _buildVirtualList(),

          // 一键置顶按钮
          _buildScrollToTopButton(),
        ],
      ),
    );
  }

  /// 构建虚拟列表 - 增强版本
  Widget _buildVirtualList() {
    // 使用高性能排序算法
    final sortedTasks = TaskSorter.smartSort(widget.tasks);

    return OptimizedListView(
      controller: _scrollController,
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing3,
        vertical: AppTheme.spacing2,
      ),
      itemExtent: itemHeight,
      itemCount: sortedTasks.length,
      itemBuilder: (context, index) {
        final task = sortedTasks[index];

        // 增强的智能缓存：包含更多上下文信息
        final cacheKey = '${task.id}_${task.creationDate.millisecondsSinceEpoch}_${task.isCompleted}';

        // 使用智能缓存管理器
        var cachedWidget = SmartCacheManager.get<Widget>(cacheKey);
        if (cachedWidget != null) {
          return cachedWidget;
        }

        final taskWidget = _buildTaskItem(task, index);
        SmartCacheManager.set(cacheKey, taskWidget);

        return taskWidget;
      },
    );
  }

  /// 构建任务项 - 高性能实现
  Widget _buildTaskItem(Task task, int index) {
    final isCompleted = task.isCompleted;
    final priorityColor = _getPriorityColor(widget.priority);
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacing2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showTaskDetail(task),
          borderRadius: BorderRadius.circular(AppTheme.radiusLg),
          child: Container(
            padding: const EdgeInsets.all(AppTheme.spacing3),
            decoration: BoxDecoration(
              color: isCompleted ? AppTheme.slate50 : AppTheme.cardLight,
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              border: Border.all(
                color: AppTheme.borderLight,
                width: 0.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.02),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Row(
              children: [
                // 优先级指示器
                Container(
                  width: 3,
                  height: 40,
                  decoration: BoxDecoration(
                    color: isCompleted ? AppTheme.textMuted : priorityColor,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                
                const SizedBox(width: AppTheme.spacing3),
                
                // 任务内容
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // 标题
                      Text(
                        task.title,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: isCompleted ? AppTheme.textMuted : AppTheme.textPrimary,
                          decoration: isCompleted ? TextDecoration.lineThrough : null,
                          height: 1.3,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: AppTheme.spacing1),
                      
                      // 元信息行
                      Row(
                        children: [
                          // 截止日期
                          _buildDateChip(task.dueDate),
                          
                          if (task.subtasks.isNotEmpty) ...[
                            const SizedBox(width: AppTheme.spacing2),
                            _buildSubtaskChip(task.subtasks),
                          ],
                          
                          if (task.notes.isNotEmpty) ...[
                            const SizedBox(width: AppTheme.spacing2),
                            Icon(
                              Icons.note,
                              size: 12,
                              color: AppTheme.textMuted,
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(width: AppTheme.spacing2),
                
                // 完成状态
                Transform.scale(
                  scale: 0.9,
                  child: Checkbox(
                    value: isCompleted,
                    onChanged: (value) => _toggleTaskCompletion(task, value ?? false),
                    activeColor: priorityColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建日期标签
  Widget _buildDateChip(DateTime date) {
    final now = DateTime.now();
    final isToday = _isSameDay(date, now);
    final isOverdue = date.isBefore(now) && !isToday;
    
    Color chipColor;
    String text;
    
    if (isOverdue) {
      chipColor = AppTheme.errorColor;
      text = '已逾期';
    } else if (isToday) {
      chipColor = AppTheme.warningColor;
      text = '今天';
    } else {
      chipColor = AppTheme.textMuted;
      text = '${date.month}/${date.day}';
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing2,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: chipColor,
        ),
      ),
    );
  }

  /// 构建子任务标签
  Widget _buildSubtaskChip(List<SubTask> subtasks) {
    final completed = subtasks.where((s) => s.isCompleted).length;
    final total = subtasks.length;
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing2,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: AppTheme.primaryBlue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.checklist,
            size: 10,
            color: AppTheme.primaryBlue,
          ),
          const SizedBox(width: 2),
          Text(
            '$completed/$total',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryBlue,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getPriorityIcon(widget.priority),
            size: 48,
            color: AppTheme.textMuted,
          ),
          const SizedBox(height: AppTheme.spacing3),
          Text(
            '暂无${_getPriorityName(widget.priority)}任务',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppTheme.textMuted,
            ),
          ),
          const SizedBox(height: AppTheme.spacing2),
          TextButton.icon(
            onPressed: _createNewTask,
            icon: const Icon(Icons.add, size: 16),
            label: const Text('创建任务'),
            style: TextButton.styleFrom(
              foregroundColor: AppTheme.primaryBlue,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建一键置顶按钮 - 增强版本
  Widget _buildScrollToTopButton() {
    return Positioned(
      right: AppTheme.spacing4,
      bottom: AppTheme.spacing4,
      child: AnimatedScale(
        scale: _showScrollToTop ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 250),
        curve: Curves.easeOutCubic,
        child: AnimatedOpacity(
          opacity: _showScrollToTop ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 200),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(AppTheme.radiusFull),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryBlue.withOpacity(0.25),
                  offset: const Offset(0, 4),
                  blurRadius: 12,
                  spreadRadius: 0,
                ),
                const BoxShadow(
                  color: Color(0x0A000000),
                  offset: Offset(0, 2),
                  blurRadius: 8,
                  spreadRadius: 0,
                ),
              ],
            ),
            child: FloatingActionButton.small(
              onPressed: _scrollToTop,
              backgroundColor: AppTheme.primaryBlue,
              foregroundColor: Colors.white,
              elevation: 0,
              splashColor: AppTheme.primaryBlueDark.withOpacity(0.3),
              child: const Icon(
                Icons.keyboard_arrow_up_rounded,
                size: 22,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 辅助方法
  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return AppTheme.urgentImportantColor;
      case Priority.importantNotUrgent:
        return AppTheme.importantNotUrgentColor;
      case Priority.urgentNotImportant:
        return AppTheme.urgentNotImportantColor;
      case Priority.notUrgentNotImportant:
        return AppTheme.notUrgentNotImportantColor;
    }
  }

  IconData _getPriorityIcon(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return Icons.warning;
      case Priority.importantNotUrgent:
        return Icons.star;
      case Priority.urgentNotImportant:
        return Icons.flash_on;
      case Priority.notUrgentNotImportant:
        return Icons.low_priority;
    }
  }

  String _getPriorityName(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return '紧急重要';
      case Priority.importantNotUrgent:
        return '重要不紧急';
      case Priority.urgentNotImportant:
        return '紧急不重要';
      case Priority.notUrgentNotImportant:
        return '不紧急不重要';
    }
  }

  // 交互方法 - 增强版本
  void _scrollToTop() {
    if (!_scrollController.hasClients) return;

    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeOutQuart,
    );
  }

  void _showTaskDetail(Task task) {
    showDialog(
      context: context,
      builder: (context) => TaskDetailDialog(task: task),
    );
  }

  void _toggleTaskCompletion(Task task, bool isCompleted) {
    final updatedTask = task.copyWith(isCompleted: isCompleted);
    context.read<TaskListBloc>().add(TaskListEvent.taskUpdated(updatedTask));
  }

  void _createNewTask() {
    showDialog(
      context: context,
      builder: (context) => TaskEditorDialog(
        initialPriority: widget.priority,
      ),
    );
  }
}

/// 任务统计缓存
class TaskStats {
  final int total;
  final int completed;
  final int pending;
  
  const TaskStats({
    required this.total,
    required this.completed,
    required this.pending,
  });
}
