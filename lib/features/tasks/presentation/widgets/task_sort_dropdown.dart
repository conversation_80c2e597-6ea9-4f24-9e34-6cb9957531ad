import 'package:flutter/material.dart';

class TaskSortDropdown extends StatelessWidget {
  final String currentSortBy;
  final ValueChanged<String> onSortChanged;

  const TaskSortDropdown({
    super.key,
    required this.currentSortBy,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'priority',
          child: Row(
            children: [
              Icon(Icons.priority_high, size: 16),
              SizedBox(width: 8),
              Text('按优先级'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'dueDate',
          child: Row(
            children: [
              Icon(Icons.calendar_today, size: 16),
              SizedBox(width: 8),
              Text('按截止日期'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'creationDate',
          child: Row(
            children: [
              Icon(Icons.create, size: 16),
              SizedBox(width: 8),
              Text('按创建时间'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'title',
          child: Row(
            children: [
              Icon(Icons.sort_by_alpha, size: 16),
              SizedBox(width: 8),
              Text('按标题'),
            ],
          ),
        ),
      ],
      onSelected: onSortChanged,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.blue[50],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.blue[200]!),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.sort,
              size: 16,
              color: Colors.blue[600],
            ),
            const SizedBox(width: 6),
            Text(
              _getSortText(currentSortBy),
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue[700],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              size: 16,
              color: Colors.blue[600],
            ),
          ],
        ),
      ),
    );
  }

  String _getSortText(String sortBy) {
    switch (sortBy) {
      case 'priority':
        return '按优先级';
      case 'dueDate':
        return '按截止日期';
      case 'creationDate':
        return '按创建时间';
      case 'title':
        return '按标题';
      default:
        return '按优先级';
    }
  }
}
