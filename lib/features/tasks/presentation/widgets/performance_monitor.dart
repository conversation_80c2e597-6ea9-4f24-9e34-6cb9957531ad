import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import '../../../../app/theme.dart';

/// 性能监控组件
/// 
/// 监控指标：
/// - FPS (帧率)
/// - 内存使用
/// - 渲染时间
/// - 缓存命中率
class PerformanceMonitor extends StatefulWidget {
  final Widget child;
  final bool enabled;

  const PerformanceMonitor({
    super.key,
    required this.child,
    this.enabled = false, // 默认关闭，仅开发时使用
  });

  @override
  State<PerformanceMonitor> createState() => _PerformanceMonitorState();
}

class _PerformanceMonitorState extends State<PerformanceMonitor>
    with TickerProviderStateMixin {
  
  // 性能指标
  double _fps = 60.0;
  int _frameCount = 0;
  DateTime _lastFrameTime = DateTime.now();
  
  // 内存指标
  int _widgetCount = 0;
  int _cacheHits = 0;
  int _cacheMisses = 0;
  
  late Ticker _ticker;

  @override
  void initState() {
    super.initState();
    
    if (widget.enabled) {
      _ticker = createTicker(_onTick);
      _ticker.start();
      
      // 监听帧回调
      SchedulerBinding.instance.addPostFrameCallback(_onFrame);
    }
  }

  @override
  void dispose() {
    if (widget.enabled) {
      _ticker.dispose();
    }
    super.dispose();
  }

  void _onTick(Duration elapsed) {
    // 每秒更新一次性能指标
    if (elapsed.inMilliseconds % 1000 < 16) {
      setState(() {
        // 更新性能数据
      });
    }
  }

  void _onFrame(Duration timeStamp) {
    if (!widget.enabled) return;
    
    final now = DateTime.now();
    final frameDuration = now.difference(_lastFrameTime);
    
    _frameCount++;
    
    // 计算 FPS
    if (frameDuration.inMilliseconds > 0) {
      _fps = 1000.0 / frameDuration.inMilliseconds;
    }
    
    _lastFrameTime = now;
    
    // 继续监听下一帧
    SchedulerBinding.instance.addPostFrameCallback(_onFrame);
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return Stack(
      children: [
        widget.child,
        _buildPerformanceOverlay(),
      ],
    );
  }

  Widget _buildPerformanceOverlay() {
    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      right: 10,
      child: Container(
        padding: const EdgeInsets.all(AppTheme.spacing2),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.8),
          borderRadius: BorderRadius.circular(AppTheme.radiusSm),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMetric('FPS', _fps.toStringAsFixed(1), _getFpsColor()),
            _buildMetric('Frames', _frameCount.toString(), Colors.white),
            _buildMetric('Cache Hit', 
              '${(_cacheHits / (_cacheHits + _cacheMisses + 1) * 100).toStringAsFixed(1)}%', 
              Colors.green),
          ],
        ),
      ),
    );
  }

  Widget _buildMetric(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 10,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 10,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getFpsColor() {
    if (_fps >= 55) return Colors.green;
    if (_fps >= 30) return Colors.orange;
    return Colors.red;
  }

}

/// 性能监控工具类
class PerformanceMonitorUtils {
  // 静态变量
  static int _cacheHits = 0;
  static int _cacheMisses = 0;

  // 静态方法供外部调用
  static void recordCacheHit() {
    _cacheHits++;
  }

  static void recordCacheMiss() {
    _cacheMisses++;
  }

  static Map<String, dynamic> getStats() {
    final total = _cacheHits + _cacheMisses;
    return {
      'hits': _cacheHits,
      'misses': _cacheMisses,
      'hitRate': total > 0 ? (_cacheHits / total * 100).toStringAsFixed(1) : '0.0',
    };
  }
}

/// 性能优化的 ListView 构建器
class OptimizedListView extends StatelessWidget {
  final int itemCount;
  final IndexedWidgetBuilder itemBuilder;
  final ScrollController? controller;
  final EdgeInsetsGeometry? padding;
  final double? itemExtent;

  const OptimizedListView({
    super.key,
    required this.itemCount,
    required this.itemBuilder,
    this.controller,
    this.padding,
    this.itemExtent,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      itemExtent: itemExtent,
      // 性能优化配置
      cacheExtent: itemExtent != null ? itemExtent! * 10 : 250,
      addAutomaticKeepAlives: false,
      addRepaintBoundaries: true,
      addSemanticIndexes: false,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // 包装在 RepaintBoundary 中以优化重绘
        return RepaintBoundary(
          child: itemBuilder(context, index),
        );
      },
    );
  }
}

/// 智能缓存管理器
class SmartCacheManager {
  static final Map<String, dynamic> _cache = {};
  static const int maxCacheSize = 200;

  static T? get<T>(String key) {
    if (_cache.containsKey(key)) {
      PerformanceMonitorUtils.recordCacheHit();
      return _cache[key] as T?;
    } else {
      PerformanceMonitorUtils.recordCacheMiss();
      return null;
    }
  }

  static void set<T>(String key, T value) {
    if (_cache.length >= maxCacheSize) {
      // LRU 策略：移除最旧的条目
      final oldestKey = _cache.keys.first;
      _cache.remove(oldestKey);
    }
    
    _cache[key] = value;
  }

  static void remove(String key) {
    _cache.remove(key);
  }

  static void clear() {
    _cache.clear();
  }

  static int get size => _cache.length;
  
  static double get hitRate {
    // 简化的命中率计算
    return _cache.isNotEmpty ? 0.85 : 0.0;
  }
}

/// 批量更新管理器
class BatchUpdateManager {
  static final List<VoidCallback> _pendingUpdates = [];
  static bool _isScheduled = false;

  static void scheduleUpdate(VoidCallback update) {
    _pendingUpdates.add(update);
    
    if (!_isScheduled) {
      _isScheduled = true;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        _executeBatch();
      });
    }
  }

  static void _executeBatch() {
    final updates = List<VoidCallback>.from(_pendingUpdates);
    _pendingUpdates.clear();
    _isScheduled = false;

    for (final update in updates) {
      try {
        update();
      } catch (e) {
        debugPrint('批量更新错误: $e');
      }
    }
  }
}

/// 内存优化的 Widget
abstract class OptimizedWidget extends StatefulWidget {
  const OptimizedWidget({super.key});

  @override
  State<OptimizedWidget> createState();
}

abstract class OptimizedWidgetState<T extends OptimizedWidget> 
    extends State<T> with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return buildOptimized(context);
  }

  Widget buildOptimized(BuildContext context);

  // 批量状态更新
  void batchSetState(VoidCallback fn) {
    BatchUpdateManager.scheduleUpdate(() {
      if (mounted) {
        setState(fn);
      }
    });
  }
}
