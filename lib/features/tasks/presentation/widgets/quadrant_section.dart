import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../app/theme.dart';
import '../../../../domain/models/task_model.dart';
import '../../bloc/task_list_bloc.dart';
import '../../bloc/task_list_event.dart';

import '../task_editor_dialog.dart';
import 'task_item_widget.dart';
import 'task_detail_dialog.dart';
import 'modern_task_card.dart';

class QuadrantSection extends StatefulWidget {
  final String title;
  final Color color;
  final List<Task> tasks;
  final Priority priority;

  const QuadrantSection({
    super.key,
    required this.title,
    required this.color,
    required this.tasks,
    required this.priority,
  });

  @override
  State<QuadrantSection> createState() => _QuadrantSectionState();
}

class _QuadrantSectionState extends State<QuadrantSection>
    with SingleTickerProviderStateMixin {
  bool _isExpanded = false; // 默认收起
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  late Animation<double> _iconRotationAnimation;

  // 性能优化：缓存动画值以避免重复计算
  double _cachedExpandValue = 1.0;
  double _cachedIconRotation = 0.0;

  @override
  void initState() {
    super.initState();

    // 高性能动画控制器：使用优化的持续时间和曲线
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 250), // 减少动画时间提升性能
      vsync: this,
    );

    // 使用高性能的FastOutSlowIn曲线，减少计算开销
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.fastOutSlowIn,
    );

    // 优化图标旋转动画，使用线性插值减少计算
    _iconRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.5,
    ).animate(_animationController);

    // 性能优化：添加动画监听器缓存值
    _animationController.addListener(_updateCachedValues);

    if (_isExpanded) {
      _animationController.value = 1.0;
      _updateCachedValues();
    }
  }

  void _updateCachedValues() {
    _cachedExpandValue = _expandAnimation.value;
    _cachedIconRotation = _iconRotationAnimation.value;
  }

  @override
  void dispose() {
    _animationController.removeListener(_updateCachedValues);
    _animationController.dispose();
    super.dispose();
  }

  // 高性能切换方法：使用异步操作避免阻塞UI
  void _toggleExpanded() {
    if (_animationController.isAnimating) return; // 防止重复动画

    setState(() {
      _isExpanded = !_isExpanded;
    });

    // 使用高效的动画控制
    if (_isExpanded) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return DragTarget<Task>(
      onWillAcceptWithDetails: (details) => details.data.priority != widget.priority,
      onAcceptWithDetails: (details) => _handleTaskDrop(context, details.data),
      builder: (context, candidateData, rejectedData) {
        final isHighlighted = candidateData.isNotEmpty;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          margin: const EdgeInsets.only(bottom: AppTheme.spacing2),
          decoration: BoxDecoration(
            color: isHighlighted
                ? widget.color.withOpacity(0.08)
                : AppTheme.cardLight,
            borderRadius: BorderRadius.circular(AppTheme.radiusXl),
            border: Border.all(
              color: isHighlighted ? widget.color : AppTheme.borderLight,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.03),
                blurRadius: 4,
                offset: const Offset(0, 1),
              ),
              if (isHighlighted)
                BoxShadow(
                  color: widget.color.withOpacity(0.1),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 象限标题 - 紧凑设计
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing4,
                  vertical: AppTheme.spacing3,
                ),
                decoration: BoxDecoration(
                  color: widget.color.withOpacity(0.05),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(AppTheme.radiusXl),
                    topRight: Radius.circular(AppTheme.radiusXl),
                  ),
                  border: Border(
                    bottom: BorderSide(
                      color: AppTheme.borderLight,
                      width: 0.5,
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    // 优先级图标
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: widget.color,
                        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                      ),
                      child: Icon(
                        _getPriorityIcon(widget.priority),
                        color: Colors.white,
                        size: 18,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacing3),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.title,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppTheme.textPrimary,
                            ),
                          ),
                          Text(
                            _getPriorityDescription(widget.priority),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.textSecondary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // 任务数量徽章
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppTheme.spacing2,
                        vertical: AppTheme.spacing1,
                      ),
                      decoration: BoxDecoration(
                        color: widget.color.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                      ),
                      child: Text(
                        '${widget.tasks.length}',
                        style: TextStyle(
                          color: widget.color,
                          fontSize: 12,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    // 添加任务按钮
                    InkWell(
                      onTap: () => _showAddTaskDialog(context),
                      borderRadius: BorderRadius.circular(20),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        child: Icon(
                          Icons.add,
                          size: 20,
                          color: widget.color,
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    // 展开收起按钮 - 性能优化版本
                    InkWell(
                      onTap: _toggleExpanded,
                      borderRadius: BorderRadius.circular(20),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        child: AnimatedBuilder(
                          animation: _animationController,
                          builder: (context, child) {
                            // 使用缓存值避免重复计算
                            return Transform.rotate(
                              angle: _cachedIconRotation * 3.14159,
                              child: child,
                            );
                          },
                          child: Icon(
                            Icons.expand_more,
                            size: 20,
                            color: widget.color,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // 拖拽提示
              if (isHighlighted)
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: widget.color.withAlpha(26),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: widget.color.withAlpha(77)),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.move_down, color: widget.color, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        '释放以移动到此象限',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: widget.color,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ],
                  ),
                ),

              // 任务列表 - 高性能展开收起动画
              AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  // 性能优化：使用缓存值和条件渲染
                  if (_cachedExpandValue == 0.0 && !_isExpanded) {
                    return const SizedBox.shrink();
                  }

                  return SizeTransition(
                    sizeFactor: _expandAnimation,
                    child: _buildTaskList(context, isHighlighted),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // 高性能任务列表构建方法
  Widget _buildTaskList(BuildContext context, bool isHighlighted) {
    if (widget.tasks.isEmpty && !isHighlighted) {
      return Container(
        padding: const EdgeInsets.all(AppTheme.spacing6),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.task_alt,
                size: 32,
                color: AppTheme.textMuted,
              ),
              const SizedBox(height: AppTheme.spacing2),
              Text(
                '暂无任务',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.textMuted,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 性能优化：使用ListView.builder代替Column以支持大量任务
    if (widget.tasks.length > 10) {
      return Container(
        height: 300, // 限制高度避免无限扩展
        padding: const EdgeInsets.all(AppTheme.spacing3),
        child: ListView.builder(
          itemCount: widget.tasks.length,
          itemBuilder: (context, index) {
            final task = widget.tasks[index];
            return ModernTaskCard(
              task: task,
              onTap: () => _showTaskDetailDialog(context, task),
              onToggleComplete: (isCompleted) =>
                  _toggleTaskCompletion(context, task, isCompleted),
            );
          },
        ),
      );
    }

    // 对于少量任务，使用Column保持原有布局
    return Padding(
      padding: const EdgeInsets.all(AppTheme.spacing3),
      child: Column(
        children: widget.tasks.map((task) => ModernTaskCard(
          task: task,
          onTap: () => _showTaskDetailDialog(context, task),
          onToggleComplete: (isCompleted) =>
              _toggleTaskCompletion(context, task, isCompleted),
        )).toList(),
      ),
    );
  }

  void _handleTaskDrop(BuildContext context, Task task) {
    if (task.priority == widget.priority) return;

    // 更新任务优先级
    final updatedTask = task.copyWith(priority: widget.priority);
    context.read<TaskListBloc>().add(TaskListEvent.taskUpdated(updatedTask));

    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('任务「${task.title}」已移动到「${widget.title}」'),
        backgroundColor: widget.color,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showAddTaskDialog(BuildContext context) {
    final state = context.read<TaskListBloc>().state;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<TaskListBloc>(),
        child: TaskEditorDialog(
          selectedDate: state.date,
          initialPriority: widget.priority,
          onTaskSaved: (task) {
            // 回调会在TaskEditorDialog内部处理
          },
        ),
      ),
    );
  }

  void _showTaskDetailDialog(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<TaskListBloc>(),
        child: TaskDetailDialog(
          task: task,
          onTaskUpdated: () {
            // 不需要手动刷新，BLoC会自动处理
            // 避免使用context，因为Widget可能已经被卸载
          },
          onTaskDeleted: () {
            // 任务已删除，无需额外操作
          },
        ),
      ),
    );
  }

  void _toggleTaskCompletion(BuildContext context, Task task, bool isCompleted) {
    context.read<TaskListBloc>().add(
      TaskListEvent.completionToggled(
        taskId: task.id,
        isCompleted: isCompleted,
      ),
    );
  }

  IconData _getPriorityIcon(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return Icons.warning;
      case Priority.importantNotUrgent:
        return Icons.star;
      case Priority.urgentNotImportant:
        return Icons.flash_on;
      case Priority.notUrgentNotImportant:
        return Icons.low_priority;
    }
  }

  String _getPriorityDescription(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return '立即处理';
      case Priority.importantNotUrgent:
        return '计划安排';
      case Priority.urgentNotImportant:
        return '委托他人';
      case Priority.notUrgentNotImportant:
        return '有空再做';
    }
  }
}
