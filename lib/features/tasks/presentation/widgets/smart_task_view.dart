import 'package:flutter/material.dart';
import '../../../../app/theme.dart';
import '../../../../domain/models/task_model.dart';

/// 智能任务视图 - 解决大数据量时的交互问题
class SmartTaskView extends StatefulWidget {
  final List<Task> tasks;
  
  const SmartTaskView({
    super.key,
    required this.tasks,
  });

  @override
  State<SmartTaskView> createState() => _SmartTaskViewState();
}

class _SmartTaskViewState extends State<SmartTaskView> {
  TaskViewMode _viewMode = TaskViewMode.smart;
  String _searchQuery = '';
  Priority? _filterPriority;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildToolbar(),
        Expanded(
          child: _buildContent(),
        ),
      ],
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacing3),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: AppTheme.borderLight),
        ),
      ),
      child: Column(
        children: [
          // 搜索框
          TextField(
            decoration: InputDecoration(
              hintText: '搜索任务...',
              prefixIcon: const Icon(Icons.search, size: 20),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing3,
                vertical: AppTheme.spacing2,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                borderSide: const BorderSide(color: AppTheme.borderLight),
              ),
            ),
            onChanged: (value) => setState(() => _searchQuery = value),
          ),
          
          const SizedBox(height: AppTheme.spacing2),
          
          // 视图模式切换
          Row(
            children: [
              _buildViewModeChip(TaskViewMode.smart, '智能视图', Icons.auto_awesome),
              const SizedBox(width: AppTheme.spacing2),
              _buildViewModeChip(TaskViewMode.priority, '优先级', Icons.priority_high),
              const SizedBox(width: AppTheme.spacing2),
              _buildViewModeChip(TaskViewMode.timeline, '时间线', Icons.schedule),
              const Spacer(),
              _buildQuickFilter(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildViewModeChip(TaskViewMode mode, String label, IconData icon) {
    final isSelected = _viewMode == mode;
    return InkWell(
      onTap: () => setState(() => _viewMode = mode),
      borderRadius: BorderRadius.circular(AppTheme.radiusSm),
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: AppTheme.spacing3,
          vertical: AppTheme.spacing2,
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryBlue.withOpacity(0.1) : null,
          borderRadius: BorderRadius.circular(AppTheme.radiusSm),
          border: Border.all(
            color: isSelected ? AppTheme.primaryBlue : AppTheme.borderLight,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? AppTheme.primaryBlue : AppTheme.textSecondary,
            ),
            const SizedBox(width: AppTheme.spacing1),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isSelected ? AppTheme.primaryBlue : AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickFilter() {
    return PopupMenuButton<Priority?>(
      icon: Icon(
        Icons.filter_list,
        color: _filterPriority != null ? AppTheme.primaryBlue : AppTheme.textSecondary,
      ),
      onSelected: (priority) => setState(() => _filterPriority = priority),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: null,
          child: Text('全部'),
        ),
        ...Priority.values.map((priority) => PopupMenuItem(
          value: priority,
          child: Text(priority.displayName),
        )),
      ],
    );
  }

  Widget _buildContent() {
    final filteredTasks = _getFilteredTasks();
    
    switch (_viewMode) {
      case TaskViewMode.smart:
        return _buildSmartView(filteredTasks);
      case TaskViewMode.priority:
        return _buildPriorityView(filteredTasks);
      case TaskViewMode.timeline:
        return _buildTimelineView(filteredTasks);
    }
  }

  List<Task> _getFilteredTasks() {
    var tasks = widget.tasks;
    
    // 搜索过滤
    if (_searchQuery.isNotEmpty) {
      tasks = tasks.where((task) => 
        task.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        task.notes.toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();
    }
    
    // 优先级过滤
    if (_filterPriority != null) {
      tasks = tasks.where((task) => task.priority == _filterPriority).toList();
    }
    
    return tasks;
  }

  Widget _buildSmartView(List<Task> tasks) {
    // 智能排序：紧急 + 截止日期 + 优先级
    final sortedTasks = List<Task>.from(tasks);
    sortedTasks.sort((a, b) {
      // 首先按是否今日截止
      final aIsToday = _isToday(a.dueDate);
      final bIsToday = _isToday(b.dueDate);
      if (aIsToday != bIsToday) return aIsToday ? -1 : 1;
      
      // 然后按优先级
      final priorityCompare = a.priority.quadrant.compareTo(b.priority.quadrant);
      if (priorityCompare != 0) return priorityCompare;
      
      // 最后按截止日期
      return a.dueDate.compareTo(b.dueDate);
    });

    return ListView.builder(
      padding: const EdgeInsets.all(AppTheme.spacing3),
      itemCount: sortedTasks.length,
      itemBuilder: (context, index) {
        final task = sortedTasks[index];
        return _buildSmartTaskCard(task);
      },
    );
  }

  Widget _buildPriorityView(List<Task> tasks) {
    // 按优先级分组，但只显示有任务的组
    final groups = <Priority, List<Task>>{};
    for (final task in tasks) {
      groups.putIfAbsent(task.priority, () => []).add(task);
    }

    return ListView(
      padding: const EdgeInsets.all(AppTheme.spacing3),
      children: groups.entries.map((entry) {
        return _buildCompactPriorityGroup(entry.key, entry.value);
      }).toList(),
    );
  }

  Widget _buildTimelineView(List<Task> tasks) {
    // 按日期分组
    final groups = <String, List<Task>>{};
    for (final task in tasks) {
      final dateKey = _formatDate(task.dueDate);
      groups.putIfAbsent(dateKey, () => []).add(task);
    }

    return ListView(
      padding: const EdgeInsets.all(AppTheme.spacing3),
      children: groups.entries.map((entry) {
        return _buildTimelineGroup(entry.key, entry.value);
      }).toList(),
    );
  }

  Widget _buildSmartTaskCard(Task task) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppTheme.spacing2),
      padding: const EdgeInsets.all(AppTheme.spacing3),
      decoration: BoxDecoration(
        color: AppTheme.cardLight,
        borderRadius: BorderRadius.circular(AppTheme.radiusLg),
        border: Border.all(color: AppTheme.borderLight),
      ),
      child: Row(
        children: [
          // 优先级指示器
          Container(
            width: 4,
            height: 40,
            decoration: BoxDecoration(
              color: _getPriorityColor(task.priority),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: AppTheme.spacing3),
          
          // 任务内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  task.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                if (task.notes.isNotEmpty) ...[
                  const SizedBox(height: AppTheme.spacing1),
                  Text(
                    task.notes,
                    style: TextStyle(
                      color: AppTheme.textSecondary,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const SizedBox(height: AppTheme.spacing1),
                Row(
                  children: [
                    _buildPriorityChip(task.priority),
                    const SizedBox(width: AppTheme.spacing2),
                    _buildDateChip(task.dueDate),
                  ],
                ),
              ],
            ),
          ),
          
          // 快速操作
          Checkbox(
            value: task.isCompleted,
            onChanged: (value) {
              // TODO: 实现完成状态切换
            },
          ),
        ],
      ),
    );
  }

  // 辅助方法
  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    if (_isToday(date)) return '今天';
    if (date.difference(now).inDays == 1) return '明天';
    if (date.difference(now).inDays == -1) return '昨天';
    return '${date.month}月${date.day}日';
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return AppTheme.urgentImportantColor;
      case Priority.importantNotUrgent:
        return AppTheme.importantNotUrgentColor;
      case Priority.urgentNotImportant:
        return AppTheme.urgentNotImportantColor;
      case Priority.notUrgentNotImportant:
        return AppTheme.notUrgentNotImportantColor;
    }
  }

  Widget _buildPriorityChip(Priority priority) {
    final color = _getPriorityColor(priority);
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing2,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
      ),
      child: Text(
        priority.displayName,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildDateChip(DateTime date) {
    final isToday = _isToday(date);
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing2,
        vertical: 2,
      ),
      decoration: BoxDecoration(
        color: isToday ? AppTheme.warningColor.withOpacity(0.1) : AppTheme.slate100,
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
      ),
      child: Text(
        _formatDate(date),
        style: TextStyle(
          color: isToday ? AppTheme.warningColor : AppTheme.textSecondary,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildCompactPriorityGroup(Priority priority, List<Task> tasks) {
    // TODO: 实现紧凑的优先级分组
    return const SizedBox.shrink();
  }

  Widget _buildTimelineGroup(String date, List<Task> tasks) {
    // TODO: 实现时间线分组
    return const SizedBox.shrink();
  }
}

enum TaskViewMode {
  smart,    // 智能视图：按紧急程度和截止日期排序
  priority, // 优先级视图：按四象限分组
  timeline, // 时间线视图：按日期分组
}
