import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../domain/models/task_model.dart';

class TaskItemWidget extends StatelessWidget {
  final Task task;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onToggleComplete;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const TaskItemWidget({
    super.key,
    required this.task,
    this.onTap,
    this.onToggleComplete,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10.0), // 减小圆角：12->10
          border: Border.all(
            color: _getPriorityColor(task.priority).withAlpha(77),
            width: 1.2, // 减小边框：1.5->1.2
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(20), // 减少阴影：26->20
              spreadRadius: 0.5, // 减少扩散：1->0.5
              blurRadius: 3, // 减少模糊：4->3
              offset: const Offset(0, 1), // 减少偏移：2->1
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0), // 减少内边距：16->12
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 任务标题和操作按钮
              Row(
                children: [
                  // 完成状态复选框 - 紧凑化
                  if (onToggleComplete != null)
                    Transform.scale(
                      scale: 0.9, // 缩小复选框
                      child: Checkbox(
                        value: task.isCompleted,
                        onChanged: (value) {
                          if (value != null) {
                            onToggleComplete!(value);
                          }
                        },
                        activeColor: _getPriorityColor(task.priority),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    ),

                  // 任务标题 - 紧凑字体
                  Expanded(
                    child: Tooltip(
                      message: task.title,
                      child: Text(
                        task.title,
                        style: TextStyle(
                          fontSize: 14, // 减小字号：16->14
                          fontWeight: FontWeight.w600,
                          decoration: task.isCompleted
                              ? TextDecoration.lineThrough
                              : null,
                          color: task.isCompleted
                              ? Colors.grey[600]
                              : Colors.grey[800],
                          height: 1.3, // 紧凑行高
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),

                  // 操作按钮 - 紧凑化
                  if (onEdit != null || onDelete != null)
                    PopupMenuButton<String>(
                      icon: Icon(
                        Icons.more_vert,
                        color: Colors.grey[600],
                        size: 18, // 减小图标：20->18
                      ),
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit?.call();
                            break;
                          case 'delete':
                            onDelete?.call();
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        if (onEdit != null)
                          const PopupMenuItem(
                            value: 'edit',
                            child: Row(
                              children: [
                                Icon(Icons.edit, size: 16), // 减小图标
                                SizedBox(width: 6), // 减小间距
                                Text('编辑', style: TextStyle(fontSize: 13)), // 减小字号
                              ],
                            ),
                          ),
                        if (onDelete != null)
                          const PopupMenuItem(
                            value: 'delete',
                            child: Row(
                              children: [
                                Icon(Icons.delete, size: 16, color: Colors.red), // 减小图标
                                SizedBox(width: 6), // 减小间距
                                Text('删除', style: TextStyle(color: Colors.red, fontSize: 13)), // 减小字号
                              ],
                            ),
                          ),
                      ],
                    ),
                ],
              ),

              // 任务备注 - 紧凑化
              if (task.notes.isNotEmpty) ...[
                const SizedBox(height: 6), // 减小间距：8->6
                Text(
                  task.notes,
                  style: TextStyle(
                    fontSize: 12, // 减小字号：14->12
                    color: Colors.grey[600],
                    fontStyle: FontStyle.italic,
                    height: 1.3, // 紧凑行高
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],

              const SizedBox(height: 8), // 减小间距：12->8

              // 任务元信息 - 紧凑化
              Row(
                children: [
                  // 优先级标签
                  Flexible(
                    flex: 0,
                    child: _buildPriorityTag(task.priority),
                  ),

                  const SizedBox(width: 6), // 减小间距：8->6

                  // 截止日期
                  Flexible(
                    flex: 1,
                    child: _buildDueDateInfo(task.dueDate),
                  ),

                  const SizedBox(width: 6), // 减小间距：8->6

                  // 子任务数量
                  if (task.subtasks.isNotEmpty)
                    Flexible(
                      flex: 0,
                      child: _buildSubtasksInfo(task.subtasks),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityTag(Priority priority) {
    final priorityInfo = _getPriorityInfo(priority);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // 减小内边距
      decoration: BoxDecoration(
        color: priorityInfo.color.withAlpha(26),
        borderRadius: BorderRadius.circular(8), // 减小圆角：12->8
        border: Border.all(color: priorityInfo.color.withAlpha(77)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            priorityInfo.icon,
            size: 10, // 减小图标：12->10
            color: priorityInfo.color,
          ),
          const SizedBox(width: 3), // 减小间距：4->3
          Text(
            priorityInfo.shortTitle,
            style: TextStyle(
              fontSize: 10, // 减小字号：11->10
              fontWeight: FontWeight.w600,
              color: priorityInfo.color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDueDateInfo(DateTime dueDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dueDateOnly = DateTime(dueDate.year, dueDate.month, dueDate.day);
    final daysUntilDue = dueDateOnly.difference(today).inDays;

    Color dateColor;
    IconData dateIcon;
    String dateText;

    if (task.isCompleted) {
      dateColor = Colors.green;
      dateIcon = Icons.check_circle;
      dateText = '已完成';
    } else if (daysUntilDue < 0) {
      dateColor = Colors.red;
      dateIcon = Icons.schedule;
      dateText = '已逾期 ${daysUntilDue.abs()} 天';
    } else if (daysUntilDue == 0) {
      dateColor = Colors.orange;
      dateIcon = Icons.today;
      dateText = '今天到期';
    } else if (daysUntilDue == 1) {
      dateColor = Colors.orange;
      dateIcon = Icons.schedule;
      dateText = '明天到期';
    } else if (daysUntilDue <= 3) {
      dateColor = Colors.amber;
      dateIcon = Icons.schedule;
      dateText = '$daysUntilDue 天后到期';
    } else {
      dateColor = Colors.grey;
      dateIcon = Icons.calendar_today;
      dateText = DateFormat('M月d日').format(dueDate);
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // 减小内边距
      decoration: BoxDecoration(
        color: dateColor.withAlpha(26),
        borderRadius: BorderRadius.circular(8), // 减小圆角：12->8
        border: Border.all(color: dateColor.withAlpha(77)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            dateIcon,
            size: 10, // 减小图标：12->10
            color: dateColor,
          ),
          const SizedBox(width: 3), // 减小间距：4->3
          Flexible(
            child: Text(
              dateText,
              style: TextStyle(
                fontSize: 10, // 减小字号：11->10
                fontWeight: FontWeight.w600,
                color: dateColor,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubtasksInfo(List<SubTask> subtasks) {
    final completedSubtasks =
        subtasks.where((subtask) => subtask.isCompleted).length;
    final totalSubtasks = subtasks.length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // 减小内边距
      decoration: BoxDecoration(
        color: Colors.blue.withAlpha(26),
        borderRadius: BorderRadius.circular(8), // 减小圆角：12->8
        border: Border.all(color: Colors.blue.withAlpha(77)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.checklist,
            size: 10, // 减小图标：12->10
            color: Colors.blue,
          ),
          const SizedBox(width: 3), // 减小间距：4->3
          Text(
            '$completedSubtasks/$totalSubtasks',
            style: const TextStyle(
              fontSize: 10, // 减小字号：11->10
              fontWeight: FontWeight.w600,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return Colors.red;
      case Priority.importantNotUrgent:
        return Colors.blue;
      case Priority.urgentNotImportant:
        return Colors.orange;
      case Priority.notUrgentNotImportant:
        return Colors.grey;
    }
  }

  PriorityInfo _getPriorityInfo(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return PriorityInfo(
          title: '重要且紧急',
          shortTitle: '紧急',
          color: Colors.red,
          icon: Icons.priority_high,
        );
      case Priority.importantNotUrgent:
        return PriorityInfo(
          title: '重要但不紧急',
          shortTitle: '重要',
          color: Colors.blue,
          icon: Icons.schedule,
        );
      case Priority.urgentNotImportant:
        return PriorityInfo(
          title: '紧急但不重要',
          shortTitle: '紧急',
          color: Colors.orange,
          icon: Icons.warning,
        );
      case Priority.notUrgentNotImportant:
        return PriorityInfo(
          title: '不重要且不紧急',
          shortTitle: '普通',
          color: Colors.grey,
          icon: Icons.low_priority,
        );
    }
  }
}

class PriorityInfo {
  final String title;
  final String shortTitle;
  final Color color;
  final IconData icon;

  PriorityInfo({
    required this.title,
    required this.shortTitle,
    required this.color,
    required this.icon,
  });
}
