import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../app/theme.dart';
import '../../../../domain/models/task_model.dart';

/// 现代化任务卡片组件 - 基于 shadcn/ui 设计风格
class ModernTaskCard extends StatefulWidget {
  final Task task;
  final VoidCallback? onTap;
  final ValueChanged<bool>? onToggleComplete;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const ModernTaskCard({
    super.key,
    required this.task,
    this.onTap,
    this.onToggleComplete,
    this.onEdit,
    this.onDelete,
  });

  @override
  State<ModernTaskCard> createState() => _ModernTaskCardState();
}

class _ModernTaskCardState extends State<ModernTaskCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final priorityColor = AppTheme.getPriorityColor(widget.task.priority.name);
    final isCompleted = widget.task.isCompleted;
    final hasSubtasks = widget.task.subtasks.isNotEmpty;
    final completedSubtasks = widget.task.subtasks.where((s) => s.isCompleted).length;

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: MouseRegion(
            onEnter: (_) {
              if (mounted) {
                setState(() => _isHovered = true);
                _animationController.forward();
              }
            },
            onExit: (_) {
              if (mounted) {
                setState(() => _isHovered = false);
                _animationController.reverse();
              }
            },
            child: GestureDetector(
              onTap: widget.onTap,
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 150),
                margin: const EdgeInsets.only(bottom: AppTheme.spacing3),
                decoration: BoxDecoration(
                  color: isCompleted
                      ? AppTheme.slate50
                      : AppTheme.cardLight,
                  borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                  border: Border.all(
                    color: _isHovered
                        ? priorityColor.withOpacity(0.4)
                        : AppTheme.borderLight,
                    width: _isHovered ? 1.5 : 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.02),
                      blurRadius: 2,
                      offset: const Offset(0, 1),
                    ),
                    if (_isHovered)
                      BoxShadow(
                        color: priorityColor.withOpacity(0.1),
                        blurRadius: 12,
                        offset: const Offset(0, 6),
                      ),
                  ],
                ),
                child: Column(
                  children: [
                    // 主要内容区域
                    Padding(
                      padding: const EdgeInsets.all(AppTheme.spacing3),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 头部：优先级指示器 + 标题 + 完成状态
                          Row(
                            children: [
                              // 优先级指示器
                              Container(
                                width: 3,
                                height: 20,
                                decoration: BoxDecoration(
                                  color: priorityColor,
                                  borderRadius: BorderRadius.circular(AppTheme.radiusXs),
                                ),
                              ),
                              const SizedBox(width: AppTheme.spacing2),

                              // 任务标题
                              Expanded(
                                child: Tooltip(
                                  message: widget.task.title,
                                  child: Text(
                                    widget.task.title,
                                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 14,
                                      decoration: isCompleted ? TextDecoration.lineThrough : null,
                                      color: isCompleted
                                          ? AppTheme.textMuted
                                          : AppTheme.textPrimary,
                                      height: 1.3,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ),
                              
                              const SizedBox(width: AppTheme.spacing2),
                              
                              // 完成状态复选框
                              Transform.scale(
                                scale: 0.9,
                                child: Checkbox(
                                  value: isCompleted,
                                  onChanged: (value) => widget.onToggleComplete?.call(value ?? false),
                                  activeColor: priorityColor,
                                  checkColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                                  ),
                                  side: BorderSide(
                                    color: isCompleted ? priorityColor : AppTheme.borderLight,
                                    width: 1.5,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          
                          // 描述（如果有）
                          if (widget.task.notes.isNotEmpty) ...[
                            const SizedBox(height: AppTheme.spacing2),
                            Text(
                              widget.task.notes,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppTheme.textSecondary,
                                fontSize: 12,
                                height: 1.4,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                          
                          const SizedBox(height: AppTheme.spacing3),
                          
                          // 底部信息栏
                          Wrap(
                            spacing: AppTheme.spacing2,
                            runSpacing: AppTheme.spacing1,
                            children: [
                              // 优先级标签
                              _buildPriorityChip(priorityColor),

                              // 子任务进度（如果有）
                              if (hasSubtasks)
                                _buildSubtaskProgress(completedSubtasks, widget.task.subtasks.length),

                              // 截止时间
                              _buildDueDateChip(widget.task.dueDate),
                            ],
                          ),
                        ],
                      ),
                    ),
                    
                    // 子任务预览（如果有且未完成）
                    if (hasSubtasks && !isCompleted)
                      _buildSubtaskPreview(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPriorityChip(Color priorityColor) {
    final priorityText = _getPriorityText(widget.task.priority);
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing2,
        vertical: AppTheme.spacing1,
      ),
      decoration: BoxDecoration(
        color: priorityColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
        border: Border.all(
          color: priorityColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: priorityColor,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: AppTheme.spacing1),
          Text(
            priorityText,
            style: TextStyle(
              color: priorityColor,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubtaskProgress(int completed, int total) {
    final progress = completed / total;
    final progressColor = progress == 1.0 ? AppTheme.successColor : AppTheme.infoColor;
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing2,
        vertical: AppTheme.spacing1,
      ),
      decoration: BoxDecoration(
        color: progressColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.checklist,
            size: 12,
            color: progressColor,
          ),
          const SizedBox(width: AppTheme.spacing1),
          Text(
            '$completed/$total',
            style: TextStyle(
              color: progressColor,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDueDateChip(DateTime dueDate) {
    final now = DateTime.now();
    final isOverdue = dueDate.isBefore(now);
    final isToday = DateUtils.isSameDay(dueDate, now);
    final isTomorrow = DateUtils.isSameDay(dueDate, now.add(const Duration(days: 1)));
    
    Color chipColor;
    String dateText;
    
    if (isOverdue) {
      chipColor = AppTheme.errorColor;
      dateText = '已逾期';
    } else if (isToday) {
      chipColor = AppTheme.warningColor;
      dateText = '今天';
    } else if (isTomorrow) {
      chipColor = AppTheme.infoColor;
      dateText = '明天';
    } else {
      chipColor = AppTheme.textMuted;
      dateText = DateFormat('MM/dd').format(dueDate);
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing2,
        vertical: AppTheme.spacing1,
      ),
      decoration: BoxDecoration(
        color: chipColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppTheme.radiusSm),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.schedule,
            size: 12,
            color: chipColor,
          ),
          const SizedBox(width: AppTheme.spacing1),
          Text(
            dateText,
            style: TextStyle(
              color: chipColor,
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSubtaskPreview() {
    final uncompletedSubtasks = widget.task.subtasks
        .where((s) => !s.isCompleted)
        .take(2)
        .toList();

    if (uncompletedSubtasks.isEmpty) return const SizedBox.shrink();

    return Container(
      decoration: BoxDecoration(
        color: AppTheme.slate50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppTheme.radiusLg),
          bottomRight: Radius.circular(AppTheme.radiusLg),
        ),
        border: const Border(
          top: BorderSide(color: AppTheme.borderLight, width: 0.5),
        ),
      ),
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing3,
        vertical: AppTheme.spacing2,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '待完成子任务',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: AppTheme.textMuted,
              fontWeight: FontWeight.w600,
              fontSize: 11,
            ),
          ),
          const SizedBox(height: AppTheme.spacing2),
          ...uncompletedSubtasks.map((subtask) => Container(
            margin: const EdgeInsets.only(bottom: AppTheme.spacing1),
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacing2,
              vertical: AppTheme.spacing1,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(AppTheme.radiusSm),
              border: Border.all(
                color: AppTheme.borderLight,
                width: 0.5,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 3,
                  height: 3,
                  decoration: const BoxDecoration(
                    color: AppTheme.textMuted,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: AppTheme.spacing2),
                Expanded(
                  child: Text(
                    subtask.title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondary,
                      fontSize: 11,
                      height: 1.3,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          )),
          if (widget.task.subtasks.length > 2)
            Padding(
              padding: const EdgeInsets.only(top: AppTheme.spacing1),
              child: Text(
                '还有 ${widget.task.subtasks.length - 2} 个子任务...',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: AppTheme.textMuted,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _getPriorityText(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return '紧急重要';
      case Priority.importantNotUrgent:
        return '重要不紧急';
      case Priority.urgentNotImportant:
        return '紧急不重要';
      case Priority.notUrgentNotImportant:
        return '不紧急不重要';
    }
  }
}