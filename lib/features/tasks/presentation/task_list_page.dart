import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/di/injection.dart';
import '../bloc/task_list_bloc.dart';
import '../bloc/task_list_event.dart';
import '../bloc/task_list_state.dart';
import '../../../../domain/models/task_model.dart';
import 'widgets/task_item_widget.dart';
import 'widgets/task_filter_bar.dart';

import 'task_editor_dialog.dart';
import 'widgets/task_detail_dialog.dart';
import '../../../core/utils/keyboard_shortcuts.dart';

class TaskListPage extends StatelessWidget {
  const TaskListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<TaskListBloc>()
        ..add(TaskListEvent.subscriptionRequested(DateTime.now())),
      child: const TaskListView(),
    );
  }
}

class TaskListView extends StatelessWidget {
  const TaskListView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(context),
      body: Column(
        children: [
          // 过滤和排序栏
          const TaskFilterBar(),

          // 任务列表
          Expanded(
            child: BlocBuilder<TaskListBloc, TaskListState>(
              builder: (context, state) {
                return _buildTaskList(context, state);
              },
            ),
          ),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      title: Row(
        children: [
          Icon(Icons.task_alt, color: Colors.blue[700]),
          const SizedBox(width: 12),
          const Text(
            '任务列表',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      backgroundColor: Colors.white,
      elevation: 0,
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => _showSearchDialog(context),
        ),
        IconButton(
          icon: const Icon(Icons.more_vert),
          onPressed: () => _showMoreOptions(context),
        ),
      ],
    );
  }

  Widget _buildTaskList(BuildContext context, TaskListState state) {
    if (state.status == TaskListStatus.loading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在加载任务...'),
          ],
        ),
      );
    }

    if (state.status == TaskListStatus.failure) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              state.errorMessage ?? '未知错误',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Colors.grey[600],
                  ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context
                    .read<TaskListBloc>()
                    .add(TaskListEvent.subscriptionRequested(DateTime.now()));
              },
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (state.tasks.isEmpty) {
      return _buildEmptyState(context);
    }

    // 按优先级和完成状态分组
    final groupedTasksMap = _groupTasksByPriority(state.tasks);
    final groupedTasks = groupedTasksMap.entries
        .where((entry) => entry.value.isNotEmpty)
        .toList();

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: groupedTasks.length,
      itemBuilder: (context, index) {
        final group = groupedTasks[index];
        return _buildTaskGroup(context, group);
      },
    );
  }

  Widget _buildTaskGroup(
      BuildContext context, MapEntry<Priority, List<Task>> group) {
    final priority = group.key;
    final tasks = group.value;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 优先级标题
        _buildPriorityHeader(context, priority, tasks.length),

        const SizedBox(height: 12),

        // 该优先级的任务列表
        ...tasks.map((task) => Padding(
              padding: const EdgeInsets.only(bottom: 8.0),
              child: TaskItemWidget(
                task: task,
                onTap: () => _onTaskTap(context, task),
                onToggleComplete: (isCompleted) =>
                    _onTaskToggleComplete(context, task, isCompleted),
                onEdit: () => _onTaskEdit(context, task),
                onDelete: () => _onTaskDelete(context, task),
              ),
            )),

        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildPriorityHeader(
      BuildContext context, Priority priority, int taskCount) {
    final priorityInfo = _getPriorityInfo(priority);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: priorityInfo.color.withAlpha(26),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: priorityInfo.color.withAlpha(77)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            priorityInfo.icon,
            size: 16,
            color: priorityInfo.color,
          ),
          const SizedBox(width: 8),
          Text(
            priorityInfo.title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: priorityInfo.color,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: priorityInfo.color,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              '$taskCount',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.task_alt,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            '暂无任务',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            '点击右下角按钮创建新任务',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showCreateTaskDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('创建任务'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () => _showCreateTaskDialog(context),
      icon: const Icon(Icons.add),
      label: const Text('新建任务'),
      backgroundColor: Colors.blue[600],
      foregroundColor: Colors.white,
    );
  }

  Map<Priority, List<Task>> _groupTasksByPriority(List<Task> tasks) {
    final grouped = <Priority, List<Task>>{};

    for (final priority in Priority.values) {
      grouped[priority] =
          tasks.where((task) => task.priority == priority).toList()
            ..sort((a, b) {
              // 先按完成状态排序，再按截止日期排序
              if (a.isCompleted != b.isCompleted) {
                return a.isCompleted ? 1 : -1;
              }
              return a.dueDate.compareTo(b.dueDate);
            });
    }

    // 按优先级顺序返回
    return {
      Priority.urgentImportant: grouped[Priority.urgentImportant] ?? [],
      Priority.importantNotUrgent: grouped[Priority.importantNotUrgent] ?? [],
      Priority.urgentNotImportant: grouped[Priority.urgentNotImportant] ?? [],
      Priority.notUrgentNotImportant:
          grouped[Priority.notUrgentNotImportant] ?? [],
    };
  }

  PriorityInfo _getPriorityInfo(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return PriorityInfo(
          title: '重要且紧急',
          color: Colors.red,
          icon: Icons.priority_high,
        );
      case Priority.importantNotUrgent:
        return PriorityInfo(
          title: '重要但不紧急',
          color: Colors.blue,
          icon: Icons.schedule,
        );
      case Priority.urgentNotImportant:
        return PriorityInfo(
          title: '紧急但不重要',
          color: Colors.orange,
          icon: Icons.warning,
        );
      case Priority.notUrgentNotImportant:
        return PriorityInfo(
          title: '不重要且不紧急',
          color: Colors.grey,
          icon: Icons.low_priority,
        );
    }
  }

  void _onTaskTap(BuildContext context, Task task) {
    // 显示任务详情弹窗
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<TaskListBloc>(),
        child: TaskDetailDialog(
          task: task,
          onTaskUpdated: () {
            // 不需要重新加载，状态管理器会自动更新
            // 移除loadRequested调用以避免闪烁和滚动重置
          },
          onTaskDeleted: () {
            // 任务已删除，无需额外操作
          },
        ),
      ),
    );
  }

  void _onTaskToggleComplete(
      BuildContext context, Task task, bool isCompleted) {
    final taskListBloc = getIt<TaskListBloc>();
    taskListBloc.add(TaskListEvent.completionToggled(
      taskId: task.id,
      isCompleted: isCompleted,
    ));

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${isCompleted ? '已完成' : '已取消完成'}任务: ${task.title}'),
        backgroundColor: isCompleted ? Colors.green : Colors.orange,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _onTaskEdit(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (context) => TaskEditorDialog(
        existingTask: task,
        onTaskSaved: (updatedTask) {
          final taskListBloc = getIt<TaskListBloc>();
          taskListBloc.add(TaskListEvent.taskUpdated(updatedTask));
        },
      ),
    );
  }

  void _onTaskDelete(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除任务「${task.title}」吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              final taskListBloc = getIt<TaskListBloc>();
              taskListBloc.add(TaskListEvent.taskDeleted(taskId: task.id));

              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('已删除任务: ${task.title}'),
                  backgroundColor: Colors.red,
                  duration: const Duration(seconds: 2),
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showCreateTaskDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => TaskEditorDialog(
        onTaskSaved: (newTask) {
          final taskListBloc = getIt<TaskListBloc>();
          taskListBloc.add(TaskListEvent.taskCreated(newTask));
        },
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => SearchDialog(
        onSearch: (query) {
          final taskListBloc = getIt<TaskListBloc>();
          taskListBloc.add(TaskListEvent.searchRequested(query));
        },
        onClear: () {
          final taskListBloc = getIt<TaskListBloc>();
          taskListBloc.add(const TaskListEvent.searchCleared());
        },
      ),
    );
  }

  void _showMoreOptions(BuildContext context) {
    // For now, this is a placeholder for future functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('更多选项功能即将实现')),
    );
  }
}

class PriorityInfo {
  final String title;
  final Color color;
  final IconData icon;

  PriorityInfo({
    required this.title,
    required this.color,
    required this.icon,
  });
}
