import 'package:flutter/material.dart';
import '../../../app/theme.dart';
import '../../../core/di/injection.dart';
import '../../../domain/models/task_model.dart';
import '../../../domain/repositories/task_repository.dart';
import '../../../domain/services/task_state_manager.dart';
import '../bloc/task_list_bloc.dart';
import '../bloc/task_list_event.dart';

class TaskEditorDialog extends StatefulWidget {
  final Task? existingTask;
  final DateTime? selectedDate;
  final Priority? initialPriority;
  final Function(Task)? onTaskSaved;

  const TaskEditorDialog({
    super.key,
    this.existingTask,
    this.selectedDate,
    this.initialPriority,
    this.onTaskSaved,
  });

  @override
  State<TaskEditorDialog> createState() => _TaskEditorDialogState();
}

class _TaskEditorDialogState extends State<TaskEditorDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _notesController = TextEditingController();
  final _titleFocusNode = FocusNode();
  final _notesFocusNode = FocusNode();

  late DateTime _selectedDate;
  late Priority _selectedPriority;
  final List<SubTask> _subtasks = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate ?? DateTime.now();
    _selectedPriority = widget.initialPriority ?? Priority.urgentImportant;

    if (widget.existingTask != null) {
      // 从TaskStateManager获取最新的任务数据
      final stateManager = TaskStateManager();
      final latestTask = stateManager.getTask(widget.existingTask!.id) ?? widget.existingTask!;

      print('📥 编辑页面：从TaskStateManager获取最新任务数据');
      print('📝 任务ID: ${latestTask.id}, 子任务数量: ${latestTask.subtasks.length}');

      _titleController.text = latestTask.title;
      _notesController.text = latestTask.notes;
      _selectedDate = latestTask.dueDate;
      _selectedPriority = latestTask.priority;
      _subtasks.addAll(latestTask.subtasks);

      print('✅ 编辑页面：任务数据初始化完成');
    }

    // 自动聚焦到标题输入框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _titleFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _notesController.dispose();
    _titleFocusNode.dispose();
    _notesFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 580, // 增加宽度以提供更好的阅读体验
        constraints: const BoxConstraints(maxHeight: 720), // 增加高度限制
        decoration: BoxDecoration(
          color: AppTheme.cardLight,
          borderRadius: BorderRadius.circular(AppTheme.radiusXl),
          border: Border.all(color: AppTheme.borderLight),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 24,
              offset: const Offset(0, 12),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Flexible(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.fromLTRB(
                    AppTheme.spacing6, // 24px 左右内边距
                    AppTheme.spacing4, // 16px 顶部内边距
                    AppTheme.spacing6, // 24px 右内边距
                    AppTheme.spacing6, // 24px 底部内边距
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildTitleInput(),
                      const SizedBox(height: AppTheme.spacing5), // 20px 间距
                      _buildCompactInfoRow(),
                      const SizedBox(height: AppTheme.spacing5), // 20px 间距
                      _buildNotesInput(),
                      const SizedBox(height: AppTheme.spacing5), // 20px 间距
                      _buildSubTasksSection(),
                    ],
                  ),
                ),
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.spacing6, // 增加水平内边距
        vertical: AppTheme.spacing5, // 增加垂直内边距
      ),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppTheme.borderLight,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(AppTheme.spacing2_5), // 稍微增加图标容器内边距
            decoration: BoxDecoration(
              color: AppTheme.primaryBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
            ),
            child: Icon(
              widget.existingTask != null ? Icons.edit_outlined : Icons.add_task_outlined,
              color: AppTheme.primaryBlue,
              size: 20, // 增加图标大小
            ),
          ),
          const SizedBox(width: AppTheme.spacing4), // 增加间距
          Expanded(
            child: Text(
              widget.existingTask != null ? '编辑任务' : '创建新任务',
              style: Theme.of(context).textTheme.titleLarge?.copyWith( // 使用更大的标题样式
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimary,
                fontSize: 20, // 明确设置字体大小
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close_outlined, size: 20),
            tooltip: '关闭 (Esc)',
            style: IconButton.styleFrom(
              backgroundColor: AppTheme.slate50,
              foregroundColor: AppTheme.textSecondary,
              minimumSize: const Size(40, 40), // 增加按钮最小尺寸
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '任务标题',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
            fontSize: 16, // 明确设置字体大小
          ),
        ),
        const SizedBox(height: AppTheme.spacing3), // 使用主题间距
        TextFormField(
          controller: _titleController,
          focusNode: _titleFocusNode,
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontSize: 16, // 增加输入文本大小
            fontWeight: FontWeight.w500,
          ),
          decoration: InputDecoration(
            hintText: '输入任务标题...',
            hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.textMuted,
              fontSize: 16,
            ),
            prefixIcon: const Icon(
              Icons.task_alt_outlined,
              size: 20,
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacing4,
              vertical: AppTheme.spacing4, // 增加垂直内边距
            ),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入任务标题';
            }
            return null;
          },
          onFieldSubmitted: (_) {
            if (_formKey.currentState!.validate()) {
              _saveTask();
            }
          },
        ),
      ],
    );
  }

  Widget _buildPrioritySelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '优先级',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: AppTheme.spacing2),
        Wrap(
          spacing: AppTheme.spacing2,
          runSpacing: AppTheme.spacing2,
          children: Priority.values.map((priority) {
            final isSelected = _selectedPriority == priority;
            final color = _getPriorityColor(priority);
            final label = _getPriorityLabel(priority);

            return InkWell(
              onTap: () => setState(() => _selectedPriority = priority),
              borderRadius: BorderRadius.circular(AppTheme.radiusSm),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 150),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing3,
                  vertical: AppTheme.spacing2,
                ),
                decoration: BoxDecoration(
                  color: isSelected
                      ? color.withOpacity(0.1)
                      : AppTheme.slate50,
                  border: Border.all(
                    color: isSelected ? color : AppTheme.borderLight,
                    width: isSelected ? 1.5 : 1,
                  ),
                  borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacing2),
                    Text(
                      label,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isSelected ? color : AppTheme.textSecondary,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '截止日期',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: AppTheme.spacing2),
        InkWell(
          onTap: _selectDate,
          borderRadius: BorderRadius.circular(AppTheme.radiusLg),
          child: Container(
            padding: const EdgeInsets.symmetric(
              vertical: AppTheme.spacing3,
              horizontal: AppTheme.spacing3,
            ),
            decoration: BoxDecoration(
              border: Border.all(color: AppTheme.borderLight),
              borderRadius: BorderRadius.circular(AppTheme.radiusLg),
              color: AppTheme.cardLight,
            ),
            child: Row(
              children: [
                const Icon(Icons.calendar_today),
                const SizedBox(width: 12),
                Text(
                  _formatDate(_selectedDate),
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
                const Spacer(),
                if (_isToday(_selectedDate))
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: AppTheme.primaryBlue.withAlpha(26),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '今天',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.primaryBlue,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 紧凑的信息行：优先级和日期选择器在同一行
  Widget _buildCompactInfoRow() {
    return Row(
      children: [
        // 优先级选择器（紧凑版）
        Expanded(
          flex: 3,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '优先级',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                  fontSize: 16, // 增加标签字体大小
                ),
              ),
              const SizedBox(height: AppTheme.spacing3), // 使用主题间距
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing3,
                  vertical: AppTheme.spacing3, // 增加垂直内边距
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: AppTheme.borderLight, width: 1),
                  borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                  color: AppTheme.cardLight,
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<Priority>(
                    value: _selectedPriority,
                    isExpanded: true,
                    isDense: false, // 取消紧凑模式以获得更好的视觉效果
                    onChanged: (Priority? newValue) {
                      if (newValue != null) {
                        setState(() => _selectedPriority = newValue);
                      }
                    },
                    items: Priority.values.map((priority) {
                      final color = _getPriorityColor(priority);
                      final label = _getPriorityLabel(priority);
                      return DropdownMenuItem<Priority>(
                        value: priority,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 10, // 增加指示器大小
                              height: 10,
                              decoration: BoxDecoration(
                                color: color,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: AppTheme.spacing2),
                            Flexible(
                              child: Text(
                                label,
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontSize: 14, // 增加字体大小
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(width: AppTheme.spacing4), // 增加间距

        // 日期选择器（紧凑版）
        Expanded(
          flex: 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '截止日期',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                  fontSize: 16, // 增加标签字体大小
                ),
              ),
              const SizedBox(height: AppTheme.spacing3), // 使用主题间距
              InkWell(
                onTap: _selectDate,
                borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    vertical: AppTheme.spacing3,
                    horizontal: AppTheme.spacing3,
                  ),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppTheme.borderLight, width: 1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusLg),
                    color: AppTheme.cardLight,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.calendar_today_outlined,
                        size: 16, // 增加图标大小
                        color: AppTheme.textSecondary,
                      ),
                      const SizedBox(width: AppTheme.spacing2),
                      Flexible(
                        child: Text(
                          _formatCompactDate(_selectedDate),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontSize: 14, // 增加字体大小
                            fontWeight: FontWeight.w500,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildNotesInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '备注 (可选)',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
            fontSize: 16, // 明确设置字体大小
          ),
        ),
        const SizedBox(height: AppTheme.spacing3), // 使用主题间距
        TextFormField(
          controller: _notesController,
          focusNode: _notesFocusNode,
          maxLines: 4, // 增加行数以提供更好的输入体验
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            fontSize: 15, // 增加输入文本大小
            height: 1.5, // 增加行高
          ),
          decoration: InputDecoration(
            hintText: '添加任务描述或备注...',
            hintStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.textMuted,
              fontSize: 15,
            ),
            prefixIcon: const Padding(
              padding: EdgeInsets.only(bottom: 60), // 调整图标位置
              child: Icon(
                Icons.notes_outlined,
                size: 20,
              ),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacing4,
              vertical: AppTheme.spacing4, // 增加垂直内边距
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubTasksSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 子任务标题行
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: AppTheme.spacing2,
            vertical: AppTheme.spacing2,
          ),
          child: Row(
            children: [
              Icon(
                Icons.checklist_outlined,
                size: 18, // 增加图标大小
                color: Theme.of(context).colorScheme.primary.withOpacity(0.8),
              ),
              const SizedBox(width: AppTheme.spacing2),
              Text(
                '子任务 (${_subtasks.length})',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 16, // 增加字体大小
                ),
              ),
              const Spacer(),
              InkWell(
                onTap: _addSubTask,
                borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppTheme.spacing2,
                    vertical: AppTheme.spacing1_5,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(AppTheme.radiusSm),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.add,
                        size: 14, // 增加图标大小
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: AppTheme.spacing1),
                      Text(
                        '添加',
                        style: TextStyle(
                          fontSize: 13, // 增加字体大小
                          fontWeight: FontWeight.w500,
                          color: Theme.of(context).colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: AppTheme.spacing3), // 使用主题间距
        if (_subtasks.isEmpty)
          Container(
            padding: const EdgeInsets.all(12), // 减少内边距
            decoration: BoxDecoration(
              color: Theme.of(context)
                  .colorScheme
                  .surfaceContainerHighest
                  .withAlpha(77),
              borderRadius: BorderRadius.circular(6), // 减少圆角
              border: Border.all(
                color: Theme.of(context).dividerColor,
                width: 0.8,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.checklist,
                  size: 16,
                  color: Theme.of(context)
                      .textTheme
                      .bodyMedium
                      ?.color
                      ?.withAlpha(153),
                ),
                const SizedBox(width: 6),
                Text(
                  '暂无子任务',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.color
                            ?.withAlpha(153),
                      ),
                ),
              ],
            ),
          )
        else
          ..._subtasks.asMap().entries.map((entry) {
            final index = entry.key;
            final subtask = entry.value;
            return _buildCompactSubTaskItem(subtask, index);
          }),
      ],
    );
  }

  Widget _buildSubTaskItem(SubTask subtask, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Checkbox(
            value: subtask.isCompleted,
            onChanged: (value) {
              _toggleSubtaskCompletion(subtask.id, value ?? false);
            },
          ),
          Expanded(
            child: Text(
              subtask.title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    decoration:
                        subtask.isCompleted ? TextDecoration.lineThrough : null,
                    color: subtask.isCompleted
                        ? Theme.of(context)
                            .textTheme
                            .bodyMedium
                            ?.color
                            ?.withAlpha(153)
                        : null,
                  ),
            ),
          ),
          IconButton(
            onPressed: () => _removeSubTask(index),
            icon: const Icon(Icons.remove_circle_outline),
            tooltip: '删除子任务',
            constraints: const BoxConstraints(),
            padding: const EdgeInsets.all(4),
          ),
        ],
      ),
    );
  }

  /// 极度紧凑的子任务项组件
  Widget _buildCompactSubTaskItem(SubTask subtask, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 2),
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: subtask.isCompleted
            ? Colors.green.withOpacity(0.03)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(4),
        border: subtask.isCompleted
            ? Border.all(color: Colors.green.withOpacity(0.15), width: 0.5)
            : null,
      ),
      child: Row(
        children: [
          // 超紧凑的复选框
          SizedBox(
            width: 16,
            height: 16,
            child: Transform.scale(
              scale: 0.7,
              child: Checkbox(
                value: subtask.isCompleted,
                onChanged: (value) {
                  _toggleSubtaskCompletion(subtask.id, value ?? false);
                },
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                visualDensity: VisualDensity.compact,
              ),
            ),
          ),

          const SizedBox(width: 6),

          // 子任务标题
          Expanded(
            child: Text(
              subtask.title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                decoration: subtask.isCompleted ? TextDecoration.lineThrough : null,
                color: subtask.isCompleted
                    ? Theme.of(context)
                        .textTheme
                        .bodyMedium
                        ?.color
                        ?.withAlpha(153)
                    : Theme.of(context).textTheme.bodySmall?.color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
                height: 1.2,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // 极简删除按钮
          InkWell(
            onTap: () => _removeSubTask(index),
            borderRadius: BorderRadius.circular(3),
            child: Container(
              padding: const EdgeInsets.all(3),
              child: Icon(
                Icons.remove_circle_outline,
                size: 14,
                color: Colors.red.withOpacity(0.6),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.fromLTRB(
        AppTheme.spacing6, // 24px 左右内边距
        AppTheme.spacing4, // 16px 顶部内边距
        AppTheme.spacing6, // 24px 右内边距
        AppTheme.spacing6, // 24px 底部内边距
      ),
      decoration: const BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppTheme.borderLight,
            width: 1, // 标准边框宽度
          ),
        ),
      ),
      child: Row(
        children: [
          OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing5, // 20px 水平内边距
                vertical: AppTheme.spacing3, // 12px 垂直内边距
              ),
              minimumSize: const Size(100, 44), // 增加最小尺寸
              textStyle: const TextStyle(
                fontSize: 15, // 增加字体大小
                fontWeight: FontWeight.w500,
              ),
            ),
            child: const Text('取消'),
          ),
          const SizedBox(width: AppTheme.spacing4), // 增加间距
          Expanded(
            child: FilledButton(
              onPressed: _isLoading ? null : _saveTask,
              style: FilledButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.spacing5, // 20px 水平内边距
                  vertical: AppTheme.spacing3, // 12px 垂直内边距
                ),
                minimumSize: const Size(0, 44), // 增加最小高度
                textStyle: const TextStyle(
                  fontSize: 15, // 增加字体大小
                  fontWeight: FontWeight.w600,
                ),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 16, // 增加加载指示器大小
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                  : Text(
                      widget.existingTask != null ? '保存更改' : '创建任务',
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getPriorityColor(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return AppTheme.urgentImportantColor;
      case Priority.importantNotUrgent:
        return AppTheme.importantNotUrgentColor;
      case Priority.urgentNotImportant:
        return AppTheme.urgentNotImportantColor;
      case Priority.notUrgentNotImportant:
        return AppTheme.notUrgentNotImportantColor;
    }
  }

  String _getPriorityLabel(Priority priority) {
    switch (priority) {
      case Priority.urgentImportant:
        return '重要且紧急';
      case Priority.importantNotUrgent:
        return '重要但不紧急';
      case Priority.urgentNotImportant:
        return '不重要但紧急';
      case Priority.notUrgentNotImportant:
        return '不重要不紧急';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference == 0) {
      return '今天 (${date.month}月${date.day}日)';
    } else if (difference == 1) {
      return '明天 (${date.month}月${date.day}日)';
    } else if (difference == -1) {
      return '昨天 (${date.month}月${date.day}日)';
    } else {
      return '${date.year}年${date.month}月${date.day}日';
    }
  }

  /// 紧凑的日期格式化
  String _formatCompactDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    if (difference == 0) {
      return '今天';
    } else if (difference == 1) {
      return '明天';
    } else if (difference == -1) {
      return '昨天';
    } else {
      return '${date.month}/${date.day}';
    }
  }

  bool _isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year &&
        date.month == now.month &&
        date.day == now.day;
  }

  Future<void> _selectDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365 * 2)),
    );

    if (picked != null) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  void _addSubTask() {
    showDialog(
      context: context,
      builder: (context) => _SubTaskDialog(
        onSave: (title) {
          setState(() {
            _subtasks.add(SubTask(
              id: DateTime.now().millisecondsSinceEpoch.toString(),
              parentTaskId: widget.existingTask?.id ?? 'temp_parent',
              title: title,
              isCompleted: false,
            ));
          });
        },
      ),
    );
  }

  void _removeSubTask(int index) {
    setState(() {
      _subtasks.removeAt(index);
    });
  }

  /// 高性能子任务切换 - 与详情页面保持一致
  void _toggleSubtaskCompletion(String subtaskId, bool isCompleted) {
    // 如果是现有任务，使用TaskStateManager进行同步
    if (widget.existingTask != null) {
      print('🔄 编辑页面：使用TaskStateManager切换子任务');
      print('📝 任务ID: ${widget.existingTask!.id}, 子任务ID: $subtaskId, 状态: $isCompleted');

      try {
        // 通过BLoC事件同步到TaskStateManager
        final taskListBloc = getIt<TaskListBloc>();
        taskListBloc.add(
          TaskListEvent.subtaskToggled(
            taskId: widget.existingTask!.id,
            subtaskId: subtaskId,
            isCompleted: isCompleted,
          ),
        );

        // 同时更新本地状态以保持UI响应
        setState(() {
          final index = _subtasks.indexWhere((s) => s.id == subtaskId);
          if (index != -1) {
            _subtasks[index] = _subtasks[index].copyWith(isCompleted: isCompleted);
          }
        });

        print('✅ 编辑页面：子任务状态同步完成');
      } catch (e) {
        print('❌ 编辑页面：子任务切换失败: $e');
      }
    } else {
      // 新任务，只更新本地状态
      setState(() {
        final index = _subtasks.indexWhere((s) => s.id == subtaskId);
        if (index != -1) {
          _subtasks[index] = _subtasks[index].copyWith(isCompleted: isCompleted);
        }
      });
    }
  }

  Future<void> _saveTask() async {
    print('🔄 TaskEditorDialog._saveTask() 开始执行');
    print('📝 表单验证状态: ${_formKey.currentState?.validate()}');

    if (!_formKey.currentState!.validate()) {
      print('❌ 表单验证失败，停止保存');
      return;
    }

    print('✅ 表单验证通过，开始保存任务');

    if (!mounted) {
      print('❌ Widget未挂载，停止保存');
      return;
    }



    setState(() {
      _isLoading = true;
    });

    try {
      print('📋 任务数据准备:');
      print('  - 标题: ${_titleController.text.trim()}');
      print('  - 备注: ${_notesController.text.trim()}');
      print('  - 截止日期: $_selectedDate');
      print('  - 优先级: $_selectedPriority');
      print('  - 子任务数量: ${_subtasks.length}');
      print('  - 是否更新现有任务: ${widget.existingTask != null}');

      final task = widget.existingTask?.updateWith(
        title: _titleController.text.trim(),
        notes: _notesController.text.trim(),
        dueDate: _selectedDate,
        priority: _selectedPriority,
        subtasks: _subtasks,
      ) ?? Task.create(
        title: _titleController.text.trim(),
        notes: _notesController.text.trim(),
        dueDate: _selectedDate,
        priority: _selectedPriority,
        subtasks: _subtasks,
      );

      print('✅ 任务对象创建成功: ${task.id}');

      // 使用Repository和TaskStateManager进行数据操作
      final taskRepository = getIt<TaskRepository>();
      print('📦 获取TaskRepository成功');

      if (widget.existingTask != null) {
        print('🔄 开始更新现有任务...');
        await taskRepository.updateTask(task);
        print('✅ 任务更新成功');

        // 同步到TaskStateManager
        print('🔄 同步任务到TaskStateManager...');
        final stateManager = TaskStateManager();
        stateManager.upsertTask(task);
        print('✅ TaskStateManager同步完成');
      } else {
        print('🆕 开始创建新任务...');
        await taskRepository.createTask(task);
        print('✅ 任务创建成功');

        // 同步到TaskStateManager
        print('🔄 同步新任务到TaskStateManager...');
        final stateManager = TaskStateManager();
        stateManager.upsertTask(task);
        print('✅ TaskStateManager同步完成');
      }

      // 调用回调函数
      if (widget.onTaskSaved != null) {
        print('📞 调用onTaskSaved回调函数');
        widget.onTaskSaved!(task);
        print('✅ 回调函数执行完成');
      }

      if (mounted) {
        print('🔙 关闭对话框并显示成功消息');
        Navigator.of(context).pop(task);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('任务已保存'),
            backgroundColor: AppTheme.successColor,
          ),
        );
        print('✅ TaskEditorDialog._saveTask() 执行完成');
      }
    } catch (error) {
      // 打印详细错误信息到控制台
      print('❌ TaskEditorDialog._saveTask() 发生错误:');
      print('❌ 错误信息: $error');
      print('❌ 错误类型: ${error.runtimeType}');
      if (error is Error) {
        print('❌ 堆栈跟踪: ${error.stackTrace}');
      }

      if (mounted) {
        print('🚨 显示错误消息给用户');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('保存失败: $error'),
            backgroundColor: AppTheme.errorColor,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      if (mounted) {
        print('🔄 重置加载状态');
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}

class _SubTaskDialog extends StatefulWidget {
  final Function(String) onSave;

  const _SubTaskDialog({required this.onSave});

  @override
  State<_SubTaskDialog> createState() => __SubTaskDialogState();
}

class __SubTaskDialogState extends State<_SubTaskDialog> {
  final _controller = TextEditingController();
  final _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('添加子任务'),
      content: TextField(
        controller: _controller,
        focusNode: _focusNode,
        decoration: const InputDecoration(
          hintText: '输入子任务标题...',
        ),
        onSubmitted: (value) {
          if (value.trim().isNotEmpty) {
            widget.onSave(value.trim());
            Navigator.of(context).pop();
          }
        },
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        FilledButton(
          onPressed: () {
            final title = _controller.text.trim();
            if (title.isNotEmpty) {
              widget.onSave(title);
              Navigator.of(context).pop();
            }
          },
          child: const Text('添加'),
        ),
      ],
    );
  }
}
