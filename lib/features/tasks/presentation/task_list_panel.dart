import 'dart:async';
import 'package:flutter/material.dart';
import '../../../app/theme.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../domain/models/task_model.dart';
import '../bloc/task_list_bloc.dart';
import '../bloc/task_list_state.dart';
import '../bloc/task_list_event.dart';
import '../../calendar/bloc/calendar_bloc.dart';
import '../../calendar/bloc/calendar_state.dart';
import 'widgets/task_item_widget.dart';
import 'task_editor_dialog.dart';
import 'widgets/task_detail_dialog.dart';
import '../../../core/di/injection.dart';

/// 高性能任务列表面板 - 在现有TaskListView基础上添加优先级tabs导航
///
/// 设计原则：
/// 1. 零冗余：复用现有TaskListView的所有逻辑
/// 2. 高性能：使用高效的数据分组算法
/// 3. 可扩展：支持未来的优先级扩展
class TaskListPanel extends StatefulWidget {
  const TaskListPanel({super.key});

  @override
  State<TaskListPanel> createState() => _TaskListPanelState();
}

class _TaskListPanelState extends State<TaskListPanel>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true; // 保持状态，避免重建

  late TabController _tabController;

  // 高性能滚动控制器 - 每个Tab独立控制
  final Map<Priority, ScrollController> _scrollControllers = {};
  bool _showScrollToTop = false;

  // 防闪烁机制 - 缓存上一次的任务列表
  List<Task> _previousTasks = [];

  // 滚动位置保存定时器和缓存
  Timer? _scrollSaveTimer;
  final Map<Priority, double> _scrollPositions = {};

  // 高性能优先级配置 - 使用常量避免重复计算
  static const _priorityConfigs = <Priority, _PriorityConfig>{
    Priority.urgentImportant: _PriorityConfig(
      label: '紧急重要',
      icon: Icons.warning,
      color: AppTheme.urgentImportantColor,
    ),
    Priority.importantNotUrgent: _PriorityConfig(
      label: '重要不紧急',
      icon: Icons.star,
      color: AppTheme.importantNotUrgentColor,
    ),
    Priority.urgentNotImportant: _PriorityConfig(
      label: '紧急不重要',
      icon: Icons.flash_on,
      color: AppTheme.urgentNotImportantColor,
    ),
    Priority.notUrgentNotImportant: _PriorityConfig(
      label: '不紧急不重要',
      icon: Icons.low_priority,
      color: AppTheme.notUrgentNotImportantColor,
    ),
  };

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: Priority.values.length, vsync: this);

    // 初始化滚动控制器 - 高性能预分配
    for (final priority in Priority.values) {
      final controller = ScrollController();
      controller.addListener(() => _onScrollChanged(controller, priority));
      _scrollControllers[priority] = controller;
      _scrollPositions[priority] = 0.0; // 初始化滚动位置
    }

    // 监听Tab切换以触发高亮更新 - 高性能监听器
    _tabController.addListener(() {
      if (mounted) setState(() {});
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollSaveTimer?.cancel();
    // 释放所有滚动控制器 - 防止内存泄漏
    for (final controller in _scrollControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以启用AutomaticKeepAliveClientMixin

    return Scaffold(
      backgroundColor: Colors.white,
      // 移除AppBar，让内容直接填充整个空间
      body: BlocBuilder<CalendarBloc, CalendarState>(
        builder: (context, calendarState) {
          return BlocConsumer<TaskListBloc, TaskListState>(
            // 高性能重建条件 - 最小化不必要的重建
            buildWhen: (previous, current) {
              // 只在关键状态变化时重建，避免闪烁
              return previous.status != current.status ||
                     _hasSignificantTaskChanges(previous.tasks, current.tasks) ||
                     previous.date != current.date;
            },
            // 监听状态变化进行滚动位置恢复
            listener: (context, state) {
              // 恢复滚动位置（异步执行，避免阻塞UI）
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _restoreScrollPositions();
              });
            },
            builder: (context, taskState) {
              if (taskState.status == TaskListStatus.loading) {
                return const _LoadingView();
              }

              if (taskState.status == TaskListStatus.failure) {
                return _ErrorView(
                  message: taskState.errorMessage ?? '加载失败',
                  onRetry: () => _retryLoad(context),
                );
              }

              if (taskState.tasks.isEmpty) {
                return const _EmptyView();
              }

              // 🎯 按选中日期过滤任务 - 高性能实现
              final selectedDate = calendarState.selectedDate;
              final filteredTasks = _filterTasksByDate(taskState.tasks, selectedDate);

              // 高性能任务分组 - O(n)时间复杂度
              final groupedTasks = _groupTasksByPriority(filteredTasks);

              return Column(
                children: [
                  _buildDateHeader(selectedDate),
                  _buildTabBar(groupedTasks),
                  Expanded(
                    child: Stack(
                      children: [
                        TabBarView(
                          controller: _tabController,
                          children: Priority.values.map((priority) {
                            final tasks = groupedTasks[priority] ?? [];
                            return _TaskListTab(
                              tasks: tasks,
                              scrollController: _scrollControllers[priority]!,
                            );
                          }).toList(),
                        ),
                        // 置顶按钮 - 悬浮在右下角
                        _buildScrollToTopButton(),
                      ],
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }



  Widget _buildTabBar(Map<Priority, List<Task>> groupedTasks) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.slate50,
        border: const Border(bottom: BorderSide(color: AppTheme.borderLight)),
      ),
      padding: const EdgeInsets.all(16),
      child: _buildGridTabs(groupedTasks),
    );
  }

  /// 构建2x2网格Tab布局 - 高性能网格算法
  Widget _buildGridTabs(Map<Priority, List<Task>> groupedTasks) {
    final priorities = Priority.values;

    return Column(
      children: [
        // 第一行：紧急重要 + 重要不紧急
        Row(
          children: [
            Expanded(child: _buildGridTab(priorities[0], groupedTasks, 0)),
            const SizedBox(width: 12),
            Expanded(child: _buildGridTab(priorities[1], groupedTasks, 1)),
          ],
        ),
        const SizedBox(height: 12),
        // 第二行：紧急不重要 + 不紧急不重要
        Row(
          children: [
            Expanded(child: _buildGridTab(priorities[2], groupedTasks, 2)),
            const SizedBox(width: 12),
            Expanded(child: _buildGridTab(priorities[3], groupedTasks, 3)),
          ],
        ),
      ],
    );
  }

  /// 构建单个网格Tab - 优化的点击性能
  Widget _buildGridTab(Priority priority, Map<Priority, List<Task>> groupedTasks, int index) {
    final config = _priorityConfigs[priority]!;
    final tasks = groupedTasks[priority] ?? [];
    final incompleteCount = tasks.where((t) => !t.isCompleted).length;
    final isSelected = _tabController.index == index;

    return GestureDetector(
      onTap: () => _tabController.animateTo(index),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        padding: const EdgeInsets.all(14), // 稍微减少内边距
        decoration: BoxDecoration(
          color: isSelected ? config.color.withOpacity(0.15) : Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: isSelected ? config.color : Colors.grey[300]!,
            width: isSelected ? 2.5 : 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: config.color.withOpacity(0.25),
              blurRadius: 12,
              offset: const Offset(0, 3),
              spreadRadius: 1,
            ),
          ] : [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: _GridTabContent(
          config: config,
          incompleteCount: incompleteCount,
          totalCount: tasks.length,
          isSelected: isSelected,
        ),
      ),
    );
  }

  /// 🎯 按日期过滤任务 - 高性能实现
  ///
  /// 时间复杂度: O(n) - 单次遍历
  List<Task> _filterTasksByDate(List<Task> tasks, DateTime selectedDate) {
    final selectedYear = selectedDate.year;
    final selectedMonth = selectedDate.month;
    final selectedDay = selectedDate.day;

    return tasks.where((task) {
      final taskDate = task.dueDate ?? task.creationDate;
      return taskDate.year == selectedYear &&
             taskDate.month == selectedMonth &&
             taskDate.day == selectedDay;
    }).toList();
  }

  /// 高性能任务分组算法 - O(n)时间复杂度，单次遍历
  Map<Priority, List<Task>> _groupTasksByPriority(List<Task> tasks) {
    final groups = <Priority, List<Task>>{
      for (final priority in Priority.values) priority: <Task>[],
    };

    for (final task in tasks) {
      groups[task.priority]!.add(task);
    }

    // 对每个组内的任务进行排序：未完成在前，按截止日期排序
    for (final group in groups.values) {
      group.sort((a, b) {
        if (a.isCompleted != b.isCompleted) {
          return a.isCompleted ? 1 : -1;
        }
        return a.dueDate.compareTo(b.dueDate);
      });
    }

    return groups;
  }

  /// 构建日期头部组件
  Widget _buildDateHeader(DateTime selectedDate) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(selectedDate.year, selectedDate.month, selectedDate.day);

    String dateText;
    if (selectedDay == today) {
      dateText = '今日任务';
    } else if (selectedDay == today.add(const Duration(days: 1))) {
      dateText = '明日任务';
    } else if (selectedDay == today.subtract(const Duration(days: 1))) {
      dateText = '昨日任务';
    } else {
      dateText = '${selectedDate.month}月${selectedDate.day}日任务';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppTheme.primaryBlue.withOpacity(0.1),
        border: const Border(bottom: BorderSide(color: AppTheme.borderLight)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.calendar_today_outlined,
            size: 20,
            color: AppTheme.primaryBlue,
          ),
          const SizedBox(width: 8),
          Text(
            dateText,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryBlue,
            ),
          ),
        ],
      ),
    );
  }

  void _retryLoad(BuildContext context) {
    context.read<TaskListBloc>().add(
      TaskListEvent.subscriptionRequested(DateTime.now()),
    );
  }





  /// 滚动监听 - 高性能滚动检测和位置保存
  void _onScrollChanged(ScrollController controller, Priority priority) {
    final shouldShow = controller.hasClients && controller.offset > 100;
    if (_showScrollToTop != shouldShow) {
      setState(() {
        _showScrollToTop = shouldShow;
      });
    }

    // 保存滚动位置（防抖处理）
    if (controller.hasClients) {
      _scrollPositions[priority] = controller.offset;
      _scrollSaveTimer?.cancel();
      _scrollSaveTimer = Timer(const Duration(milliseconds: 100), () {
        // 延迟保存，避免频繁更新
      });
    }
  }

  /// 检测是否有显著的任务变化需要重建UI
  bool _hasSignificantTaskChanges(List<Task> previous, List<Task> current) {
    // 如果数量变化超过阈值，需要重建
    if ((current.length - previous.length).abs() > 5) {
      return true;
    }

    // 如果任务ID集合发生变化，需要重建
    final previousIds = previous.map((t) => t.id).toSet();
    final currentIds = current.map((t) => t.id).toSet();

    return previousIds.difference(currentIds).isNotEmpty ||
           currentIds.difference(previousIds).isNotEmpty;
  }

  /// 恢复滚动位置
  void _restoreScrollPositions() {
    for (final entry in _scrollControllers.entries) {
      final priority = entry.key;
      final controller = entry.value;
      final savedPosition = _scrollPositions[priority] ?? 0.0;

      if (controller.hasClients && savedPosition > 0) {
        try {
          controller.jumpTo(savedPosition.clamp(0.0, controller.position.maxScrollExtent));
        } catch (e) {
          // 忽略滚动恢复错误，避免崩溃
        }
      }
    }
  }

  /// 置顶功能 - 高性能动画滚动
  void _scrollToTop() {
    final currentPriority = Priority.values[_tabController.index];
    final controller = _scrollControllers[currentPriority];
    if (controller?.hasClients == true) {
      controller!.animateTo(
        0,
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeOutCubic,
      );
    }
  }

  /// 构建置顶按钮 - 高性能悬浮按钮
  Widget _buildScrollToTopButton() {
    return AnimatedPositioned(
      duration: const Duration(milliseconds: 300),
      bottom: _showScrollToTop ? 16 : -60,
      right: 16,
      child: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(28),
        child: Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: AppTheme.primaryBlue,
            borderRadius: BorderRadius.circular(28),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryBlue.withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: IconButton(
            onPressed: _scrollToTop,
            icon: const Icon(
              Icons.keyboard_arrow_up,
              color: Colors.white,
              size: 28,
            ),
          ),
        ),
      ),
    );
  }
}

/// 优先级配置数据类 - 不可变，高性能
class _PriorityConfig {
  const _PriorityConfig({
    required this.label,
    required this.icon,
    required this.color,
  });

  final String label;
  final IconData icon;
  final Color color;
}

/// 网格Tab内容组件 - 左icon右内容，文字上下排列
class _GridTabContent extends StatelessWidget {
  const _GridTabContent({
    required this.config,
    required this.incompleteCount,
    required this.totalCount,
    required this.isSelected,
  });

  final _PriorityConfig config;
  final int incompleteCount;
  final int totalCount;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final w = constraints.maxWidth;
        final bool compact = w < 40; // 极窄场景下自适应收缩，避免Row横向溢出
        final double iconSize = compact ? 20 : 24;
        final double gap = compact ? 6 : 12;

        return Row(
          children: [
            // 左侧图标（可在极窄时缩小）
            SizedBox(
              width: iconSize,
              height: iconSize,
              child: Icon(
                config.icon,
                size: iconSize,
                color: isSelected ? config.color : config.color.withOpacity(0.7),
              ),
            ),
            SizedBox(width: gap),
            // 右侧内容：文字上下排列（溢出省略）
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    config.label,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? config.color : Colors.grey[700],
                      height: 1.2,
                    ),
                    maxLines: 1,
                    softWrap: false,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 3),
                  Text(
                    '$incompleteCount/$totalCount',
                    style: TextStyle(
                      fontSize: 11,
                      color: isSelected ? config.color.withOpacity(0.8) : Colors.grey[500],
                      fontWeight: FontWeight.w500,
                    ),
                    maxLines: 1,
                    softWrap: false,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}

/// 任务列表Tab - 高性能滚动保持，支持无感更新
class _TaskListTab extends StatefulWidget {
  const _TaskListTab({
    required this.tasks,
    required this.scrollController,
  });

  final List<Task> tasks;
  final ScrollController scrollController;

  @override
  State<_TaskListTab> createState() => _TaskListTabState();
}

class _TaskListTabState extends State<_TaskListTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true; // 保持状态，避免重建时滚动重置

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用以启用AutomaticKeepAliveClientMixin

    if (widget.tasks.isEmpty) {
      return const Center(
        child: Text('该分类暂无任务', style: TextStyle(color: Colors.grey)),
      );
    }

    return Container(
      constraints: const BoxConstraints(maxWidth: 600), // 限制最大宽度（UI优化报告）
      child: ListView.builder(
        controller: widget.scrollController, // 使用传入的滚动控制器
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8), // 减少内边距
        itemCount: widget.tasks.length,
        // 高性能优化：使用key保持item状态
        itemBuilder: (context, index) {
          final task = widget.tasks[index];
          return Container(
            key: ValueKey(task.id), // 使用稳定的任务ID作为key
            margin: const EdgeInsets.only(bottom: 8), // 减少间距
            constraints: const BoxConstraints(maxWidth: 580), // 任务项最大宽度（UI优化报告）
            child: TaskItemWidget(
              task: task,
              onTap: () => _onTaskTap(context, task),
              onToggleComplete: (isCompleted) => _onTaskToggleComplete(context, task, isCompleted),
              onEdit: () => _onTaskEdit(context, task),
              onDelete: () => _onTaskDelete(context, task),
            ),
          );
        },
      ),
    );
  }

  void _onTaskTap(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (dialogContext) => BlocProvider.value(
        value: context.read<TaskListBloc>(),
        child: TaskDetailDialog(
          task: task,
          onTaskUpdated: () {
            // 不需要重新加载，状态管理器会自动更新
            // 移除loadRequested调用以避免闪烁和滚动重置
          },
          onTaskDeleted: () {},
        ),
      ),
    );
  }

  void _onTaskToggleComplete(BuildContext context, Task task, bool isCompleted) {
    final taskListBloc = getIt<TaskListBloc>();
    taskListBloc.add(TaskListEvent.completionToggled(
      taskId: task.id,
      isCompleted: isCompleted,
    ));
  }

  void _onTaskEdit(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (context) => TaskEditorDialog(
        existingTask: task,
        onTaskSaved: (updatedTask) {
          final taskListBloc = getIt<TaskListBloc>();
          taskListBloc.add(TaskListEvent.taskUpdated(updatedTask));
        },
      ),
    );
  }

  void _onTaskDelete(BuildContext context, Task task) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除任务「${task.title}」吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              final taskListBloc = getIt<TaskListBloc>();
              taskListBloc.add(TaskListEvent.taskDeleted(taskId: task.id));
            },
            style: ElevatedButton.styleFrom(backgroundColor: AppTheme.errorColor),
            child: const Text('删除', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}

/// 加载状态视图
class _LoadingView extends StatelessWidget {
  const _LoadingView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('正在加载任务...'),
        ],
      ),
    );
  }
}

/// 错误状态视图
class _ErrorView extends StatelessWidget {
  const _ErrorView({required this.message, required this.onRetry});

  final String message;
  final VoidCallback onRetry;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(message, style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 16),
          ElevatedButton(onPressed: onRetry, child: const Text('重试')),
        ],
      ),
    );
  }
}

/// 空状态视图
class _EmptyView extends StatelessWidget {
  const _EmptyView();

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.task_alt, size: 80, color: Colors.grey),
          SizedBox(height: 16),
          Text('暂无任务', style: TextStyle(fontSize: 18, color: Colors.grey)),
          SizedBox(height: 8),
          Text('点击右上角按钮创建新任务', style: TextStyle(color: Colors.grey)),
        ],
      ),
    );
  }
}