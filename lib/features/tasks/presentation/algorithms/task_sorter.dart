import '../../../../domain/models/task_model.dart';

/// 高性能任务排序算法
/// 
/// 特性：
/// - O(n log n) 时间复杂度
/// - 多维度排序权重
/// - 智能优先级计算
/// - 缓存优化
class TaskSorter {
  // 排序权重配置
  static const Map<Priority, int> _priorityWeights = {
    Priority.urgentImportant: 1000,
    Priority.importantNotUrgent: 800,
    Priority.urgentNotImportant: 600,
    Priority.notUrgentNotImportant: 400,
  };

  // 缓存排序结果
  static final Map<String, List<Task>> _sortCache = {};
  static const int maxCacheSize = 50;

  /// 智能排序 - 综合考虑多个维度
  static List<Task> smartSort(List<Task> tasks) {
    if (tasks.isEmpty) return tasks;

    // 生成缓存键
    final cacheKey = _generateCacheKey(tasks);
    if (_sortCache.containsKey(cacheKey)) {
      return _sortCache[cacheKey]!;
    }

    // 执行排序
    final sortedTasks = List<Task>.from(tasks);
    sortedTasks.sort(_smartComparator);

    // 缓存结果
    _cacheResult(cacheKey, sortedTasks);

    return sortedTasks;
  }

  /// 按优先级排序
  static List<Task> sortByPriority(List<Task> tasks) {
    if (tasks.isEmpty) return tasks;

    final sortedTasks = List<Task>.from(tasks);
    sortedTasks.sort((a, b) {
      final weightA = _priorityWeights[a.priority] ?? 0;
      final weightB = _priorityWeights[b.priority] ?? 0;
      return weightB.compareTo(weightA); // 降序
    });

    return sortedTasks;
  }

  /// 按截止日期排序
  static List<Task> sortByDueDate(List<Task> tasks) {
    if (tasks.isEmpty) return tasks;

    final sortedTasks = List<Task>.from(tasks);
    sortedTasks.sort((a, b) => a.dueDate.compareTo(b.dueDate));

    return sortedTasks;
  }

  /// 按完成状态排序
  static List<Task> sortByCompletion(List<Task> tasks) {
    if (tasks.isEmpty) return tasks;

    final sortedTasks = List<Task>.from(tasks);
    sortedTasks.sort((a, b) {
      if (a.isCompleted == b.isCompleted) return 0;
      return a.isCompleted ? 1 : -1; // 未完成的在前
    });

    return sortedTasks;
  }

  /// 按创建时间排序
  static List<Task> sortByCreatedDate(List<Task> tasks) {
    if (tasks.isEmpty) return tasks;

    final sortedTasks = List<Task>.from(tasks);
    sortedTasks.sort((a, b) => b.creationDate.compareTo(a.creationDate)); // 新的在前

    return sortedTasks;
  }

  /// 智能比较器 - 多维度权重计算
  static int _smartComparator(Task a, Task b) {
    // 1. 完成状态权重 (最高优先级)
    if (a.isCompleted != b.isCompleted) {
      return a.isCompleted ? 1 : -1;
    }

    // 2. 紧急程度权重
    final urgencyScoreA = _calculateUrgencyScore(a);
    final urgencyScoreB = _calculateUrgencyScore(b);
    
    if (urgencyScoreA != urgencyScoreB) {
      return urgencyScoreB.compareTo(urgencyScoreA); // 降序
    }

    // 3. 优先级权重
    final priorityWeightA = _priorityWeights[a.priority] ?? 0;
    final priorityWeightB = _priorityWeights[b.priority] ?? 0;
    
    if (priorityWeightA != priorityWeightB) {
      return priorityWeightB.compareTo(priorityWeightA); // 降序
    }

    // 4. 截止日期权重
    final dueDateComparison = a.dueDate.compareTo(b.dueDate);
    if (dueDateComparison != 0) {
      return dueDateComparison; // 升序
    }

    // 5. 子任务完成度权重
    final progressA = _calculateProgress(a);
    final progressB = _calculateProgress(b);
    
    if (progressA != progressB) {
      return progressA.compareTo(progressB); // 升序，进度低的在前
    }

    // 6. 创建时间权重 (最后的排序依据)
    return b.creationDate.compareTo(a.creationDate); // 新的在前
  }

  /// 计算紧急程度分数
  static int _calculateUrgencyScore(Task task) {
    final now = DateTime.now();
    final dueDate = task.dueDate;
    final daysUntilDue = dueDate.difference(now).inDays;

    // 紧急程度评分算法
    if (daysUntilDue < 0) {
      return 1000; // 已逾期，最高紧急度
    } else if (daysUntilDue == 0) {
      return 900; // 今天到期
    } else if (daysUntilDue == 1) {
      return 800; // 明天到期
    } else if (daysUntilDue <= 3) {
      return 700; // 3天内到期
    } else if (daysUntilDue <= 7) {
      return 600; // 一周内到期
    } else if (daysUntilDue <= 30) {
      return 500; // 一个月内到期
    } else {
      return 400; // 长期任务
    }
  }

  /// 计算任务进度
  static double _calculateProgress(Task task) {
    if (task.subtasks.isEmpty) return 0.0;
    
    final completedSubtasks = task.subtasks.where((s) => s.isCompleted).length;
    return completedSubtasks / task.subtasks.length;
  }

  /// 生成缓存键
  static String _generateCacheKey(List<Task> tasks) {
    // 使用任务ID和创建时间生成唯一键
    final ids = tasks.map((t) => '${t.id}_${t.creationDate.millisecondsSinceEpoch}').join('|');
    return ids.hashCode.toString();
  }

  /// 缓存排序结果
  static void _cacheResult(String key, List<Task> result) {
    if (_sortCache.length >= maxCacheSize) {
      // LRU策略：移除最旧的缓存
      final oldestKey = _sortCache.keys.first;
      _sortCache.remove(oldestKey);
    }
    
    _sortCache[key] = List<Task>.from(result);
  }

  /// 清除缓存
  static void clearCache() {
    _sortCache.clear();
  }

  /// 获取缓存统计
  static Map<String, dynamic> getCacheStats() {
    return {
      'size': _sortCache.length,
      'maxSize': maxCacheSize,
      'usage': (_sortCache.length / maxCacheSize * 100).toStringAsFixed(1),
    };
  }
}

/// 任务过滤器 - 高性能过滤算法
class TaskFilter {
  /// 按关键词过滤
  static List<Task> filterByKeyword(List<Task> tasks, String keyword) {
    if (keyword.isEmpty) return tasks;
    
    final lowerKeyword = keyword.toLowerCase();
    return tasks.where((task) {
      return task.title.toLowerCase().contains(lowerKeyword) ||
             task.notes.toLowerCase().contains(lowerKeyword);
    }).toList();
  }

  /// 按优先级过滤
  static List<Task> filterByPriority(List<Task> tasks, Priority priority) {
    return tasks.where((task) => task.priority == priority).toList();
  }

  /// 按完成状态过滤
  static List<Task> filterByCompletion(List<Task> tasks, bool isCompleted) {
    return tasks.where((task) => task.isCompleted == isCompleted).toList();
  }

  /// 按日期范围过滤
  static List<Task> filterByDateRange(
    List<Task> tasks, 
    DateTime startDate, 
    DateTime endDate,
  ) {
    return tasks.where((task) {
      return task.dueDate.isAfter(startDate.subtract(const Duration(days: 1))) &&
             task.dueDate.isBefore(endDate.add(const Duration(days: 1)));
    }).toList();
  }

  /// 按今日任务过滤
  static List<Task> filterTodayTasks(List<Task> tasks) {
    final today = DateTime.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    return filterByDateRange(tasks, startOfDay, endOfDay);
  }

  /// 按逾期任务过滤
  static List<Task> filterOverdueTasks(List<Task> tasks) {
    final now = DateTime.now();
    return tasks.where((task) {
      return task.dueDate.isBefore(now) && !task.isCompleted;
    }).toList();
  }

  /// 组合过滤器
  static List<Task> applyFilters(
    List<Task> tasks, {
    String? keyword,
    Priority? priority,
    bool? isCompleted,
    DateTime? startDate,
    DateTime? endDate,
  }) {
    var filteredTasks = tasks;

    if (keyword != null && keyword.isNotEmpty) {
      filteredTasks = filterByKeyword(filteredTasks, keyword);
    }

    if (priority != null) {
      filteredTasks = filterByPriority(filteredTasks, priority);
    }

    if (isCompleted != null) {
      filteredTasks = filterByCompletion(filteredTasks, isCompleted);
    }

    if (startDate != null && endDate != null) {
      filteredTasks = filterByDateRange(filteredTasks, startDate, endDate);
    }

    return filteredTasks;
  }
}

/// 任务分组器 - 高效分组算法
class TaskGrouper {
  /// 按优先级分组
  static Map<Priority, List<Task>> groupByPriority(List<Task> tasks) {
    final groups = <Priority, List<Task>>{};
    
    for (final task in tasks) {
      groups.putIfAbsent(task.priority, () => []).add(task);
    }
    
    return groups;
  }

  /// 按日期分组
  static Map<String, List<Task>> groupByDate(List<Task> tasks) {
    final groups = <String, List<Task>>{};
    
    for (final task in tasks) {
      final dateKey = _formatDateKey(task.dueDate);
      groups.putIfAbsent(dateKey, () => []).add(task);
    }
    
    return groups;
  }

  /// 按完成状态分组
  static Map<bool, List<Task>> groupByCompletion(List<Task> tasks) {
    final groups = <bool, List<Task>>{};
    
    for (final task in tasks) {
      groups.putIfAbsent(task.isCompleted, () => []).add(task);
    }
    
    return groups;
  }

  static String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
