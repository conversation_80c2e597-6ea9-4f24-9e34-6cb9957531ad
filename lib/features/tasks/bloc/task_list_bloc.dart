import 'dart:async';

import 'package:clock/clock.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../../../domain/domain.dart';
import '../../../domain/services/subtask_manager.dart';
import '../../../domain/services/task_state_manager.dart';

import 'task_list_event.dart';
import 'task_list_state.dart';

/// TaskListBloc manages the state of task list operations
///
/// Features:
/// - Task CRUD operations with optimistic updates
/// - Real-time task updates using streams
/// - Comprehensive error handling and logging
/// - Performance monitoring and caching
/// - Search and filtering capabilities
@injectable
class TaskListBloc extends Bloc<TaskListEvent, TaskListState> {
  final TaskRepository _taskRepository;
  final Clock _clock;
  final TaskStateManager _stateManager = TaskStateManager();
  StreamSubscription? _tasksSubscription;
  StreamSubscription? _stateSubscription;
  Timer? _debounceTimer;

  // Cache for frequently accessed tasks
  final Map<String, List<Task>> _taskCache = {};
  static const Duration _cacheExpiry = Duration(minutes: 5);
  final Map<String, DateTime> _cacheTimestamps = {};

  TaskListBloc(this._taskRepository, this._clock)
      : super(_createInitialState(_clock)) {
    // 监听状态管理器事件
    _stateSubscription = _stateManager.eventStream.listen(_onStateManagerEvent);

    // Register all event handlers
    on<TaskListEvent>((event, emit) async {
      await event.map(
        loadRequested: (e) => _onLoadRequested(emit),
        subscriptionRequested: (e) => _onSubscriptionRequested(e.date, emit),
        monthSubscriptionRequested: (e) => _onMonthSubscriptionRequested(e.year, e.month, emit),
        completionToggled: (e) =>
            _onCompletionToggled(e.taskId, e.isCompleted, emit),
        taskDeleted: (e) => _onTaskDeleted(e.taskId, e.skipConfirmation, emit),
        taskUpdated: (e) => _onTaskUpdated(e.task, emit),
        taskCreated: (e) => _onTaskCreated(e.task, emit),
        batchTasksUpdated: (e) => _onBatchTasksUpdated(e.tasks, emit),
        batchTasksDeleted: (e) =>
            _onBatchTasksDeleted(e.taskIds, e.skipConfirmation, emit),
        taskPriorityChanged: (e) =>
            _onTaskPriorityChanged(e.taskId, e.newPriority, emit),
        taskDateChanged: (e) => _onTaskDateChanged(e.taskId, e.newDate, emit),
        subtaskToggled: (e) =>
            _onSubtaskToggled(e.taskId, e.subtaskId, e.isCompleted, emit),
        refreshRequested: (e) => _onRefreshRequested(emit),
        searchRequested: (e) => _onSearchRequested(e.query, emit),
        searchCleared: (e) => _onSearchCleared(emit),
        filterByPriority: (e) => _onFilterByPriority(e.priority, emit),
        filterByCompletion: (e) => _onFilterByCompletion(e.isCompleted, emit),
        filtersCleared: (e) => _onFiltersCleared(emit),
        retryRequested: (e) => _onRetryRequested(emit),
        errorCleared: (e) => _onErrorCleared(emit),
        tasksUpdated: (e) => _onTasksUpdated(e.tasks, emit),
        errorOccurred: (e) => _onErrorOccurred(e.message, e.exception, emit),
      );
    });
  }

  // ==================== EVENT HANDLERS ====================

  /// 处理加载请求事件
  Future<void> _onLoadRequested(Emitter<TaskListState> emit) async {
    emit(state.copyWith(status: TaskListStatus.loading));

    try {
      final tasks = await _taskRepository.getAllTasks();
      emit(state.copyWith(
        status: TaskListStatus.success,
        tasks: tasks,
      ));
    } catch (error) {
      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: error.toString(),
      ));
    }
  }

  Future<void> _onSubscriptionRequested(
    DateTime date,
    Emitter<TaskListState> emit,
  ) async {
    emit(state.copyWith(
      status: TaskListStatus.loading,
      currentOperation: TaskListOperation.loadingTasks,
      date: date,
      errorMessage: null,
      lastException: null,
    ));

    // Check cache first
    final cacheKey = date.toIso8601String();
    if (_isCacheValid(cacheKey)) {
      final cachedTasks = _taskCache[cacheKey]!;

      // 直接在这里更新状态，而不是调用_onTasksUpdated
      final filteredTasks = _applyFilters(cachedTasks);
      final statistics = _calculateStatistics(cachedTasks);

      emit(state.copyWith(
        status: TaskListStatus.success,
        currentOperation: TaskListOperation.none,
        tasks: cachedTasks,
        filteredTasks: filteredTasks,
        totalTasks: statistics['total'] ?? 0,
        completedTasks: statistics['completed'] ?? 0,
        pendingTasks: statistics['pending'] ?? 0,
        overdueTasksCount: statistics['overdue'] ?? 0,
        lastUpdated: _clock.now(),
        errorMessage: null,
        lastException: null,
        canRetry: false,
      ));

      return;
    }

    // Cancel existing subscription
    await _tasksSubscription?.cancel();

    // 订阅数据流以进行实时更新
    try {
      _tasksSubscription = _taskRepository.watchTasksByDate(date).listen(
        (tasks) {
          _updateCache(date, tasks);
          add(TaskListEvent.tasksUpdated(tasks));
        },
        onError: (error) {
          add(TaskListEvent.errorOccurred(
            message: 'Failed to watch tasks: ${error.toString()}',
            exception: error is Exception ? error : Exception(error.toString()),
          ));
        },
      );
    } catch (error) {
      emit(state.copyWith(
        status: TaskListStatus.failure,
        currentOperation: TaskListOperation.none,
        errorMessage: 'Failed to subscribe to tasks: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        canRetry: true,
      ));
    }
  }

  Future<void> _onMonthSubscriptionRequested(
    int year,
    int month,
    Emitter<TaskListState> emit,
  ) async {
    emit(state.copyWith(
      status: TaskListStatus.loading,
      currentOperation: TaskListOperation.loadingTasks,
    ));

    // Cancel existing subscription
    await _tasksSubscription?.cancel();

    // 订阅月份数据流以进行实时更新
    try {
      _tasksSubscription = _taskRepository.watchTasksForMonth(year: year, month: month).listen(
        (tasks) {
          _updateCache(DateTime(year, month, 1), tasks);
          add(TaskListEvent.tasksUpdated(tasks));
        },
        onError: (error) {
          add(TaskListEvent.errorOccurred(
            message: 'Failed to watch month tasks: ${error.toString()}',
            exception: error is Exception ? error : Exception(error.toString()),
          ));
        },
      );
    } catch (error) {
      emit(state.copyWith(
        status: TaskListStatus.failure,
        currentOperation: TaskListOperation.none,
        errorMessage: 'Failed to subscribe to month tasks: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        canRetry: true,
      ));
    }
  }

  Future<void> _onCompletionToggled(
    String taskId,
    bool isCompleted,
    Emitter<TaskListState> emit,
  ) async {
    print('🔄 TaskListBloc._onCompletionToggled() 开始执行');
    print('📝 任务ID: $taskId');
    print('📝 新完成状态: $isCompleted');

    // Optimistic update
    final updatedTaskLoadingStates =
        Map<String, bool>.from(state.taskLoadingStates);
    updatedTaskLoadingStates[taskId] = true;
    print('📝 设置任务加载状态为true');

    emit(state.copyWith(
      currentOperation: TaskListOperation.togglingCompletion,
      taskLoadingStates: updatedTaskLoadingStates,
    ));
    print('✅ 状态更新为切换完成状态中');

    try {
      print('🔍 查找任务...');
      final task = state.tasks.firstWhere(
        (t) => t.id == taskId,
        orElse: () => throw TaskNotFoundException(taskId),
      );
      print('✅ 找到任务: ${task.title}');

      print('🔄 创建更新后的任务对象...');
      final updatedTask = isCompleted
          ? task.markAsCompleted()
          : task.markAsIncomplete();
      print('✅ 任务对象创建成功，完成时间: ${updatedTask.completionDate}');

      print('💾 开始调用Repository更新任务...');
      await _taskRepository.updateTask(updatedTask);
      print('✅ Repository更新任务成功');

      // 立即更新UI中的任务列表
      final updatedTasks = state.tasks.map((t) {
        if (t.id == taskId) {
          return updatedTask;
        }
        return t;
      }).toList();

      final filteredTasks = _applyFilters(updatedTasks);
      final statistics = _calculateStatistics(updatedTasks);

      final clearedLoadingStates =
          Map<String, bool>.from(state.taskLoadingStates);
      clearedLoadingStates.remove(taskId);
      print('📝 清除任务加载状态');

      emit(state.copyWith(
        status: TaskListStatus.success,
        currentOperation: TaskListOperation.none,
        tasks: updatedTasks,
        filteredTasks: filteredTasks,
        totalTasks: statistics['total'] ?? 0,
        completedTasks: statistics['completed'] ?? 0,
        pendingTasks: statistics['pending'] ?? 0,
        overdueTasksCount: statistics['overdue'] ?? 0,
        taskLoadingStates: clearedLoadingStates,
        lastUpdated: _clock.now(),
      ));
      print('✅ TaskListBloc._onCompletionToggled() 执行完成');
    } catch (error) {
      print('❌ TaskListBloc._onCompletionToggled() 发生错误:');
      print('❌ 错误信息: $error');
      print('❌ 错误类型: ${error.runtimeType}');

      final clearedLoadingStates =
          Map<String, bool>.from(state.taskLoadingStates);
      clearedLoadingStates.remove(taskId);
      print('📝 错误时清除任务加载状态');

      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Failed to toggle task completion: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        currentOperation: TaskListOperation.none,
        taskLoadingStates: clearedLoadingStates,
      ));
      print('🚨 错误状态已发送');
    }
  }

  Future<void> _onTaskDeleted(
    String taskId,
    bool skipConfirmation,
    Emitter<TaskListState> emit,
  ) async {
    // Add to deleting list
    final updatedDeletingIds = List<String>.from(state.deletingTaskIds);
    updatedDeletingIds.add(taskId);

    emit(state.copyWith(
      status: TaskListStatus.deleting,
      currentOperation: TaskListOperation.deletingTask,
      deletingTaskIds: updatedDeletingIds,
    ));

    try {
      await _taskRepository.deleteTask(taskId);

      // 立即从UI中移除已删除的任务
      final updatedTasks = state.tasks.where((t) => t.id != taskId).toList();
      final filteredTasks = _applyFilters(updatedTasks);
      final statistics = _calculateStatistics(updatedTasks);

      final clearedDeletingIds = List<String>.from(state.deletingTaskIds);
      clearedDeletingIds.remove(taskId);

      emit(state.copyWith(
        status: TaskListStatus.success,
        currentOperation: TaskListOperation.none,
        tasks: updatedTasks,
        filteredTasks: filteredTasks,
        totalTasks: statistics['total'] ?? 0,
        completedTasks: statistics['completed'] ?? 0,
        pendingTasks: statistics['pending'] ?? 0,
        overdueTasksCount: statistics['overdue'] ?? 0,
        deletingTaskIds: clearedDeletingIds,
        lastUpdated: _clock.now(),
      ));
    } catch (error) {
      final clearedDeletingIds = List<String>.from(state.deletingTaskIds);
      clearedDeletingIds.remove(taskId);
      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Failed to delete task: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        currentOperation: TaskListOperation.none,
        deletingTaskIds: clearedDeletingIds,
      ));
    }
  }

  Future<void> _onTaskUpdated(
    Task task,
    Emitter<TaskListState> emit,
  ) async {
    print('🔄 TaskListBloc._onTaskUpdated() 开始执行');
    print('📝 任务ID: ${task.id}');
    print('📝 任务标题: ${task.title}');
    print('📝 任务完成状态: ${task.isCompleted}');

    // Add to updating list
    final updatedUpdatingIds = List<String>.from(state.updatingTaskIds);
    updatedUpdatingIds.add(task.id);
    print('📝 添加到更新列表: ${updatedUpdatingIds.length} 个任务正在更新');

    emit(state.copyWith(
      status: TaskListStatus.updating,
      currentOperation: TaskListOperation.updatingTask,
      updatingTaskIds: updatedUpdatingIds,
    ));
    print('✅ 状态更新为更新中');

    try {
      print('💾 开始调用Repository更新任务...');
      await _taskRepository.updateTask(task);
      print('✅ Repository更新任务成功');

      // 立即更新UI中的任务列表
      final updatedTasks = state.tasks.map((t) {
        if (t.id == task.id) {
          return task;
        }
        return t;
      }).toList();

      final filteredTasks = _applyFilters(updatedTasks);
      final statistics = _calculateStatistics(updatedTasks);

      final clearedUpdatingIds = List<String>.from(state.updatingTaskIds);
      clearedUpdatingIds.remove(task.id);
      print('📝 从更新列表移除: ${clearedUpdatingIds.length} 个任务仍在更新');

      emit(state.copyWith(
        status: TaskListStatus.success,
        currentOperation: TaskListOperation.none,
        tasks: updatedTasks,
        filteredTasks: filteredTasks,
        totalTasks: statistics['total'] ?? 0,
        completedTasks: statistics['completed'] ?? 0,
        pendingTasks: statistics['pending'] ?? 0,
        overdueTasksCount: statistics['overdue'] ?? 0,
        updatingTaskIds: clearedUpdatingIds,
        lastUpdated: _clock.now(),
      ));

      print('✅ TaskListBloc._onTaskUpdated() 执行完成');
    } catch (error) {
      print('❌ TaskListBloc._onTaskUpdated() 发生错误:');
      print('❌ 错误信息: $error');
      print('❌ 错误类型: ${error.runtimeType}');

      final clearedUpdatingIds = List<String>.from(state.updatingTaskIds);
      clearedUpdatingIds.remove(task.id);
      print('📝 错误时从更新列表移除: ${clearedUpdatingIds.length} 个任务仍在更新');

      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Failed to update task: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        currentOperation: TaskListOperation.none,
        updatingTaskIds: clearedUpdatingIds,
      ));
      print('🚨 错误状态已发送');
    }
  }

  Future<void> _onTaskCreated(
    Task task,
    Emitter<TaskListState> emit,
  ) async {
    print('🔄 TaskListBloc._onTaskCreated() 开始执行');
    print('📝 任务ID: ${task.id}');
    print('📝 任务标题: ${task.title}');

    emit(state.copyWith(
      currentOperation: TaskListOperation.creatingTask,
    ));
    print('✅ 状态更新为创建中');

    try {
      print('💾 开始调用Repository创建任务...');
      await _taskRepository.createTask(task);
      print('✅ Repository创建任务成功');

      // 立即将新任务添加到UI中的任务列表
      final updatedTasks = [...state.tasks, task];
      final filteredTasks = _applyFilters(updatedTasks);
      final statistics = _calculateStatistics(updatedTasks);

      emit(state.copyWith(
        status: TaskListStatus.success,
        currentOperation: TaskListOperation.none,
        tasks: updatedTasks,
        filteredTasks: filteredTasks,
        totalTasks: statistics['total'] ?? 0,
        completedTasks: statistics['completed'] ?? 0,
        pendingTasks: statistics['pending'] ?? 0,
        overdueTasksCount: statistics['overdue'] ?? 0,
        lastUpdated: _clock.now(),
      ));

      print('✅ TaskListBloc._onTaskCreated() 执行完成');
    } catch (error) {
      print('❌ TaskListBloc._onTaskCreated() 发生错误:');
      print('❌ 错误信息: $error');
      print('❌ 错误类型: ${error.runtimeType}');

      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Failed to create task: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        currentOperation: TaskListOperation.none,
      ));
      print('🚨 错误状态已发送');
    }
  }

  Future<void> _onBatchTasksUpdated(
    List<Task> tasks,
    Emitter<TaskListState> emit,
  ) async {
    emit(state.copyWith(
      status: TaskListStatus.updating,
      currentOperation: TaskListOperation.batchUpdating,
    ));

    try {
      // Fallback to individual updates for now
      // TODO: Add batch update method to TaskRepository interface
      for (final task in tasks) {
        await _taskRepository.updateTask(task);
      }
      emit(state.copyWith(
        status: TaskListStatus.success,
        currentOperation: TaskListOperation.none,
      ));
    } catch (error) {
      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Failed to batch update tasks: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        currentOperation: TaskListOperation.none,
      ));
    }
  }

  Future<void> _onBatchTasksDeleted(
    List<String> taskIds,
    bool skipConfirmation,
    Emitter<TaskListState> emit,
  ) async {
    emit(state.copyWith(
      status: TaskListStatus.deleting,
      currentOperation: TaskListOperation.batchDeleting,
      deletingTaskIds: taskIds,
    ));

    try {
      await _taskRepository.deleteTasks(taskIds);
      emit(state.copyWith(
        status: TaskListStatus.success,
        currentOperation: TaskListOperation.none,
        deletingTaskIds: [],
      ));
    } catch (error) {
      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Failed to batch delete tasks: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        currentOperation: TaskListOperation.none,
        deletingTaskIds: [],
      ));
    }
  }

  Future<void> _onTaskPriorityChanged(
    String taskId,
    Priority newPriority,
    Emitter<TaskListState> emit,
  ) async {
    try {
      final task = state.tasks.firstWhere(
        (t) => t.id == taskId,
        orElse: () => throw TaskNotFoundException(taskId),
      );

      final updatedTask = task.copyWith(priority: newPriority);
      await _taskRepository.updateTask(updatedTask);

      // 立即更新UI中的任务列表
      final updatedTasks = state.tasks.map((t) {
        if (t.id == taskId) {
          return updatedTask;
        }
        return t;
      }).toList();

      final filteredTasks = _applyFilters(updatedTasks);
      final statistics = _calculateStatistics(updatedTasks);

      emit(state.copyWith(
        status: TaskListStatus.success,
        currentOperation: TaskListOperation.none,
        tasks: updatedTasks,
        filteredTasks: filteredTasks,
        totalTasks: statistics['total'] ?? 0,
        completedTasks: statistics['completed'] ?? 0,
        pendingTasks: statistics['pending'] ?? 0,
        overdueTasksCount: statistics['overdue'] ?? 0,
        lastUpdated: _clock.now(),
      ));
    } catch (error) {
      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Failed to change task priority: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        canRetry: true,
      ));
    }
  }

  Future<void> _onTaskDateChanged(
    String taskId,
    DateTime newDate,
    Emitter<TaskListState> emit,
  ) async {
    try {
      final task = state.tasks.firstWhere(
        (t) => t.id == taskId,
        orElse: () => throw TaskNotFoundException(taskId),
      );

      final updatedTask = task.updateWith(dueDate: newDate);
      await _taskRepository.updateTask(updatedTask);

      // 立即更新UI中的任务列表
      final updatedTasks = state.tasks.map((t) {
        if (t.id == taskId) {
          return updatedTask;
        }
        return t;
      }).toList();

      final filteredTasks = _applyFilters(updatedTasks);
      final statistics = _calculateStatistics(updatedTasks);

      emit(state.copyWith(
        status: TaskListStatus.success,
        currentOperation: TaskListOperation.none,
        tasks: updatedTasks,
        filteredTasks: filteredTasks,
        totalTasks: statistics['total'] ?? 0,
        completedTasks: statistics['completed'] ?? 0,
        pendingTasks: statistics['pending'] ?? 0,
        overdueTasksCount: statistics['overdue'] ?? 0,
        lastUpdated: _clock.now(),
      ));
    } catch (error) {
      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Failed to change task date: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        canRetry: true,
      ));
    }
  }

  Future<void> _onSubtaskToggled(
    String taskId,
    String subtaskId,
    bool isCompleted,
    Emitter<TaskListState> emit,
  ) async {
    print('🚀 高性能子任务切换开始');
    print('📝 任务ID: $taskId, 子任务ID: $subtaskId, 状态: $isCompleted');

    try {
      // 使用O(1)状态管理器切换子任务
      final updatedTask = _stateManager.toggleSubtask(taskId, subtaskId, isCompleted);

      if (updatedTask == null) {
        throw TaskNotFoundException(taskId);
      }

      print('✅ 状态管理器更新成功');

      // 异步更新数据库（不阻塞UI）
      _taskRepository.updateTask(updatedTask).catchError((error) {
        print('❌ 数据库更新失败: $error');
        // 可以在这里实现重试逻辑
      });

      // 立即更新UI状态（从状态管理器获取最新数据）
      final allTasks = _stateManager.allTasks;
      final filteredTasks = _applyFilters(allTasks);
      final statistics = _calculateStatistics(allTasks);

      emit(state.copyWith(
        status: TaskListStatus.success,
        currentOperation: TaskListOperation.none,
        tasks: allTasks,
        filteredTasks: filteredTasks,
        totalTasks: statistics['total'] ?? 0,
        completedTasks: statistics['completed'] ?? 0,
        pendingTasks: statistics['pending'] ?? 0,
        overdueTasksCount: statistics['overdue'] ?? 0,
        lastUpdated: _clock.now(),
      ));

      print('🎉 高性能子任务切换完成');
    } catch (error) {
      print('❌ 子任务切换失败: $error');
      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Failed to toggle subtask: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        canRetry: true,
      ));
    }
  }

  Future<void> _onRefreshRequested(Emitter<TaskListState> emit) async {
    emit(state.copyWith(
      status: TaskListStatus.refreshing,
      currentOperation: TaskListOperation.refreshing,
    ));

    try {
      // Clear cache and reload data
      _clearCache();

      // Reload tasks for current date
      add(TaskListEvent.subscriptionRequested(state.date));
    } catch (error) {
      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Failed to refresh tasks: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        canRetry: true,
      ));
    }
  }

  Future<void> _onSearchRequested(
    String query,
    Emitter<TaskListState> emit,
  ) async {
    await _performSearch(query, emit);
  }

  Future<void> _performSearch(
    String query,
    Emitter<TaskListState> emit,
  ) async {
    try {
      List<Task> searchResults;

      if (query.trim().isNotEmpty) {
        searchResults = await _taskRepository.searchTasks(query);
      } else {
        searchResults = state.tasks;
      }

      final filteredResults = _applyFilters(searchResults);

      emit(state.copyWith(
        searchQuery: query,
        filteredTasks: filteredResults,
        hasActiveFilters: query.isNotEmpty ||
            state.priorityFilter != null ||
            state.completionFilter != null,
      ));
    } catch (error) {
      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Search failed: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
      ));
    }
  }

  Future<void> _onSearchCleared(Emitter<TaskListState> emit) async {
    _debounceTimer?.cancel();

    final filteredTasks = _applyFilters(state.tasks);

    emit(state.copyWith(
      searchQuery: '',
      filteredTasks: filteredTasks,
      hasActiveFilters:
          state.priorityFilter != null || state.completionFilter != null,
    ));
  }

  Future<void> _onFilterByPriority(
      Priority? priority, Emitter<TaskListState> emit) async {
    final filteredTasks = _applyFilters(state.tasks, priorityFilter: priority);

    emit(state.copyWith(
      priorityFilter: priority,
      filteredTasks: filteredTasks,
      hasActiveFilters: state.searchQuery.isNotEmpty ||
          priority != null ||
          state.completionFilter != null,
    ));
  }

  Future<void> _onFilterByCompletion(
      bool? isCompleted, Emitter<TaskListState> emit) async {
    final filteredTasks =
        _applyFilters(state.tasks, completionFilter: isCompleted);

    emit(state.copyWith(
      completionFilter: isCompleted,
      filteredTasks: filteredTasks,
      hasActiveFilters: state.searchQuery.isNotEmpty ||
          state.priorityFilter != null ||
          isCompleted != null,
    ));
  }

  Future<void> _onFiltersCleared(Emitter<TaskListState> emit) async {
    _debounceTimer?.cancel();
    emit(state.copyWith(
      searchQuery: '',
      priorityFilter: null,
      completionFilter: null,
      filteredTasks: state.tasks,
      hasActiveFilters: false,
    ));
  }

  Future<void> _onRetryRequested(Emitter<TaskListState> emit) async {
    if (!state.canRetry) return;

    emit(state.copyWith(
      status: TaskListStatus.loading,
      errorMessage: null,
      lastException: null,
      canRetry: false,
    ));

    try {
      // Retry the last operation based on current state
      add(TaskListEvent.subscriptionRequested(state.date));
    } catch (error) {
      emit(state.copyWith(
        status: TaskListStatus.failure,
        errorMessage: 'Retry failed: ${error.toString()}',
        lastException: error is Exception ? error : Exception(error.toString()),
        canRetry: true,
      ));
    }
  }

  Future<void> _onErrorCleared(Emitter<TaskListState> emit) async {
    emit(state.copyWith(
      errorMessage: null,
      lastException: null,
      canRetry: false,
    ));
  }

  Future<void> _onTasksUpdated(
    List<Task> tasks,
    Emitter<TaskListState> emit,
  ) async {
    print('📥 同步${tasks.length}个任务到状态管理器');

    // 将任务同步到高性能状态管理器
    _stateManager.upsertTasks(tasks);

    // 从状态管理器获取最新数据
    final allTasks = _stateManager.allTasks;
    final filteredTasks = _applyFilters(allTasks);
    final statistics = _calculateStatistics(allTasks);

    emit(state.copyWith(
      status: TaskListStatus.success,
      currentOperation: TaskListOperation.none,
      tasks: allTasks,
      filteredTasks: filteredTasks,
      totalTasks: statistics['total'] ?? 0,
      completedTasks: statistics['completed'] ?? 0,
      pendingTasks: statistics['pending'] ?? 0,
      overdueTasksCount: statistics['overdue'] ?? 0,
      lastUpdated: _clock.now(),
      errorMessage: null,
      lastException: null,
      canRetry: false,
    ));

    print('✅ 状态管理器同步完成，当前任务数: ${_stateManager.taskCount}');
  }

  Future<void> _onErrorOccurred(
    String message,
    Exception? exception,
    Emitter<TaskListState> emit,
  ) async {
    emit(state.copyWith(
      status: TaskListStatus.failure,
      currentOperation: TaskListOperation.none,
      errorMessage: message,
      lastException: exception,
      canRetry: true,
    ));
  }

  // ==================== HELPER METHODS ====================

  List<Task> _applyFilters(
    List<Task> tasks, {
    Priority? priorityFilter,
    bool? completionFilter,
  }) {
    var filtered = tasks;

    // Apply search filter
    if (state.searchQuery.isNotEmpty) {
      final query = state.searchQuery.toLowerCase();
      filtered = filtered
          .where((task) =>
              task.title.toLowerCase().contains(query) ||
              task.notes.toLowerCase().contains(query))
          .toList();
    }

    // Apply priority filter
    final priority = priorityFilter ?? state.priorityFilter;
    if (priority != null) {
      filtered = filtered.where((task) => task.priority == priority).toList();
    }

    // Apply completion filter
    final completion = completionFilter ?? state.completionFilter;
    if (completion != null) {
      filtered =
          filtered.where((task) => task.isCompleted == completion).toList();
    }

    return filtered;
  }

  Map<String, int> _calculateStatistics(List<Task> tasks) {
    final now = _clock.now();
    int completed = 0;
    int overdue = 0;

    for (final task in tasks) {
      if (task.isCompleted) {
        completed++;
      } else if (task.dueDate.isBefore(now)) {
        overdue++;
      }
    }

    return {
      'total': tasks.length,
      'completed': completed,
      'pending': tasks.length - completed,
      'overdue': overdue,
    };
  }

  void _updateCache(DateTime date, List<Task> tasks) {
    final key = date.toIso8601String();
    _taskCache[key] = tasks;
    _cacheTimestamps[key] = _clock.now();

    // Clean up old cache entries
    _cleanupCache();
  }

  bool _isCacheValid(String key) {
    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return false;

    return _clock.now().difference(timestamp) < _cacheExpiry;
  }

  void _cleanupCache() {
    final now = _clock.now();
    final keysToRemove = <String>[];

    for (final entry in _cacheTimestamps.entries) {
      if (now.difference(entry.value) > _cacheExpiry) {
        keysToRemove.add(entry.key);
      }
    }

    for (final key in keysToRemove) {
      _taskCache.remove(key);
      _cacheTimestamps.remove(key);
    }
  }

  void _clearCache() {
    _taskCache.clear();
    _cacheTimestamps.clear();
  }

  /// 处理状态管理器事件
  void _onStateManagerEvent(TaskStateEvent event) {
    // 这里可以添加额外的业务逻辑
    print('📡 状态管理器事件: ${event.runtimeType}');
  }

  @override
  Future<void> close() {
    _tasksSubscription?.cancel();
    _stateSubscription?.cancel();
    _debounceTimer?.cancel();
    _stateManager.dispose();
    return super.close();
  }

  static TaskListState _createInitialState(Clock clock) => TaskListState(
        status: TaskListStatus.initial,
        currentOperation: TaskListOperation.none,
        tasks: [],
        filteredTasks: [],
        date: clock.now(),
      );
}
