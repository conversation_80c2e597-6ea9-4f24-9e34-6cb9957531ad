// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'task_list_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TaskListState {
  TaskListStatus get status => throw _privateConstructorUsedError;
  TaskListOperation get currentOperation => throw _privateConstructorUsedError;
  List<Task> get tasks => throw _privateConstructorUsedError;
  List<Task> get filteredTasks => throw _privateConstructorUsedError;
  DateTime get date =>
      throw _privateConstructorUsedError; // Search and filtering
  String get searchQuery => throw _privateConstructorUsedError;
  Priority? get priorityFilter => throw _privateConstructorUsedError;
  bool? get completionFilter => throw _privateConstructorUsedError;
  bool get hasActiveFilters =>
      throw _privateConstructorUsedError; // Error handling
  String? get errorMessage => throw _privateConstructorUsedError;
  Exception? get lastException => throw _privateConstructorUsedError;
  bool get canRetry =>
      throw _privateConstructorUsedError; // Loading states for specific operations
  Map<String, bool> get taskLoadingStates => throw _privateConstructorUsedError;
  List<String> get deletingTaskIds => throw _privateConstructorUsedError;
  List<String> get updatingTaskIds =>
      throw _privateConstructorUsedError; // Statistics
  int get totalTasks => throw _privateConstructorUsedError;
  int get completedTasks => throw _privateConstructorUsedError;
  int get pendingTasks => throw _privateConstructorUsedError;
  int get overdueTasksCount =>
      throw _privateConstructorUsedError; // Performance metrics
  DateTime? get lastUpdated => throw _privateConstructorUsedError;
  int get loadDurationMs => throw _privateConstructorUsedError;

  /// Create a copy of TaskListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $TaskListStateCopyWith<TaskListState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskListStateCopyWith<$Res> {
  factory $TaskListStateCopyWith(
          TaskListState value, $Res Function(TaskListState) then) =
      _$TaskListStateCopyWithImpl<$Res, TaskListState>;
  @useResult
  $Res call(
      {TaskListStatus status,
      TaskListOperation currentOperation,
      List<Task> tasks,
      List<Task> filteredTasks,
      DateTime date,
      String searchQuery,
      Priority? priorityFilter,
      bool? completionFilter,
      bool hasActiveFilters,
      String? errorMessage,
      Exception? lastException,
      bool canRetry,
      Map<String, bool> taskLoadingStates,
      List<String> deletingTaskIds,
      List<String> updatingTaskIds,
      int totalTasks,
      int completedTasks,
      int pendingTasks,
      int overdueTasksCount,
      DateTime? lastUpdated,
      int loadDurationMs});
}

/// @nodoc
class _$TaskListStateCopyWithImpl<$Res, $Val extends TaskListState>
    implements $TaskListStateCopyWith<$Res> {
  _$TaskListStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TaskListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? currentOperation = null,
    Object? tasks = null,
    Object? filteredTasks = null,
    Object? date = null,
    Object? searchQuery = null,
    Object? priorityFilter = freezed,
    Object? completionFilter = freezed,
    Object? hasActiveFilters = null,
    Object? errorMessage = freezed,
    Object? lastException = freezed,
    Object? canRetry = null,
    Object? taskLoadingStates = null,
    Object? deletingTaskIds = null,
    Object? updatingTaskIds = null,
    Object? totalTasks = null,
    Object? completedTasks = null,
    Object? pendingTasks = null,
    Object? overdueTasksCount = null,
    Object? lastUpdated = freezed,
    Object? loadDurationMs = null,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as TaskListStatus,
      currentOperation: null == currentOperation
          ? _value.currentOperation
          : currentOperation // ignore: cast_nullable_to_non_nullable
              as TaskListOperation,
      tasks: null == tasks
          ? _value.tasks
          : tasks // ignore: cast_nullable_to_non_nullable
              as List<Task>,
      filteredTasks: null == filteredTasks
          ? _value.filteredTasks
          : filteredTasks // ignore: cast_nullable_to_non_nullable
              as List<Task>,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      priorityFilter: freezed == priorityFilter
          ? _value.priorityFilter
          : priorityFilter // ignore: cast_nullable_to_non_nullable
              as Priority?,
      completionFilter: freezed == completionFilter
          ? _value.completionFilter
          : completionFilter // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasActiveFilters: null == hasActiveFilters
          ? _value.hasActiveFilters
          : hasActiveFilters // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      lastException: freezed == lastException
          ? _value.lastException
          : lastException // ignore: cast_nullable_to_non_nullable
              as Exception?,
      canRetry: null == canRetry
          ? _value.canRetry
          : canRetry // ignore: cast_nullable_to_non_nullable
              as bool,
      taskLoadingStates: null == taskLoadingStates
          ? _value.taskLoadingStates
          : taskLoadingStates // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      deletingTaskIds: null == deletingTaskIds
          ? _value.deletingTaskIds
          : deletingTaskIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      updatingTaskIds: null == updatingTaskIds
          ? _value.updatingTaskIds
          : updatingTaskIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      totalTasks: null == totalTasks
          ? _value.totalTasks
          : totalTasks // ignore: cast_nullable_to_non_nullable
              as int,
      completedTasks: null == completedTasks
          ? _value.completedTasks
          : completedTasks // ignore: cast_nullable_to_non_nullable
              as int,
      pendingTasks: null == pendingTasks
          ? _value.pendingTasks
          : pendingTasks // ignore: cast_nullable_to_non_nullable
              as int,
      overdueTasksCount: null == overdueTasksCount
          ? _value.overdueTasksCount
          : overdueTasksCount // ignore: cast_nullable_to_non_nullable
              as int,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      loadDurationMs: null == loadDurationMs
          ? _value.loadDurationMs
          : loadDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TaskListStateImplCopyWith<$Res>
    implements $TaskListStateCopyWith<$Res> {
  factory _$$TaskListStateImplCopyWith(
          _$TaskListStateImpl value, $Res Function(_$TaskListStateImpl) then) =
      __$$TaskListStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {TaskListStatus status,
      TaskListOperation currentOperation,
      List<Task> tasks,
      List<Task> filteredTasks,
      DateTime date,
      String searchQuery,
      Priority? priorityFilter,
      bool? completionFilter,
      bool hasActiveFilters,
      String? errorMessage,
      Exception? lastException,
      bool canRetry,
      Map<String, bool> taskLoadingStates,
      List<String> deletingTaskIds,
      List<String> updatingTaskIds,
      int totalTasks,
      int completedTasks,
      int pendingTasks,
      int overdueTasksCount,
      DateTime? lastUpdated,
      int loadDurationMs});
}

/// @nodoc
class __$$TaskListStateImplCopyWithImpl<$Res>
    extends _$TaskListStateCopyWithImpl<$Res, _$TaskListStateImpl>
    implements _$$TaskListStateImplCopyWith<$Res> {
  __$$TaskListStateImplCopyWithImpl(
      _$TaskListStateImpl _value, $Res Function(_$TaskListStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? currentOperation = null,
    Object? tasks = null,
    Object? filteredTasks = null,
    Object? date = null,
    Object? searchQuery = null,
    Object? priorityFilter = freezed,
    Object? completionFilter = freezed,
    Object? hasActiveFilters = null,
    Object? errorMessage = freezed,
    Object? lastException = freezed,
    Object? canRetry = null,
    Object? taskLoadingStates = null,
    Object? deletingTaskIds = null,
    Object? updatingTaskIds = null,
    Object? totalTasks = null,
    Object? completedTasks = null,
    Object? pendingTasks = null,
    Object? overdueTasksCount = null,
    Object? lastUpdated = freezed,
    Object? loadDurationMs = null,
  }) {
    return _then(_$TaskListStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as TaskListStatus,
      currentOperation: null == currentOperation
          ? _value.currentOperation
          : currentOperation // ignore: cast_nullable_to_non_nullable
              as TaskListOperation,
      tasks: null == tasks
          ? _value._tasks
          : tasks // ignore: cast_nullable_to_non_nullable
              as List<Task>,
      filteredTasks: null == filteredTasks
          ? _value._filteredTasks
          : filteredTasks // ignore: cast_nullable_to_non_nullable
              as List<Task>,
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      searchQuery: null == searchQuery
          ? _value.searchQuery
          : searchQuery // ignore: cast_nullable_to_non_nullable
              as String,
      priorityFilter: freezed == priorityFilter
          ? _value.priorityFilter
          : priorityFilter // ignore: cast_nullable_to_non_nullable
              as Priority?,
      completionFilter: freezed == completionFilter
          ? _value.completionFilter
          : completionFilter // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasActiveFilters: null == hasActiveFilters
          ? _value.hasActiveFilters
          : hasActiveFilters // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      lastException: freezed == lastException
          ? _value.lastException
          : lastException // ignore: cast_nullable_to_non_nullable
              as Exception?,
      canRetry: null == canRetry
          ? _value.canRetry
          : canRetry // ignore: cast_nullable_to_non_nullable
              as bool,
      taskLoadingStates: null == taskLoadingStates
          ? _value._taskLoadingStates
          : taskLoadingStates // ignore: cast_nullable_to_non_nullable
              as Map<String, bool>,
      deletingTaskIds: null == deletingTaskIds
          ? _value._deletingTaskIds
          : deletingTaskIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      updatingTaskIds: null == updatingTaskIds
          ? _value._updatingTaskIds
          : updatingTaskIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      totalTasks: null == totalTasks
          ? _value.totalTasks
          : totalTasks // ignore: cast_nullable_to_non_nullable
              as int,
      completedTasks: null == completedTasks
          ? _value.completedTasks
          : completedTasks // ignore: cast_nullable_to_non_nullable
              as int,
      pendingTasks: null == pendingTasks
          ? _value.pendingTasks
          : pendingTasks // ignore: cast_nullable_to_non_nullable
              as int,
      overdueTasksCount: null == overdueTasksCount
          ? _value.overdueTasksCount
          : overdueTasksCount // ignore: cast_nullable_to_non_nullable
              as int,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      loadDurationMs: null == loadDurationMs
          ? _value.loadDurationMs
          : loadDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$TaskListStateImpl implements _TaskListState {
  const _$TaskListStateImpl(
      {required this.status,
      required this.currentOperation,
      required final List<Task> tasks,
      required final List<Task> filteredTasks,
      required this.date,
      this.searchQuery = '',
      this.priorityFilter,
      this.completionFilter,
      this.hasActiveFilters = false,
      this.errorMessage,
      this.lastException,
      this.canRetry = false,
      final Map<String, bool> taskLoadingStates = const {},
      final List<String> deletingTaskIds = const [],
      final List<String> updatingTaskIds = const [],
      this.totalTasks = 0,
      this.completedTasks = 0,
      this.pendingTasks = 0,
      this.overdueTasksCount = 0,
      this.lastUpdated,
      this.loadDurationMs = 0})
      : _tasks = tasks,
        _filteredTasks = filteredTasks,
        _taskLoadingStates = taskLoadingStates,
        _deletingTaskIds = deletingTaskIds,
        _updatingTaskIds = updatingTaskIds;

  @override
  final TaskListStatus status;
  @override
  final TaskListOperation currentOperation;
  final List<Task> _tasks;
  @override
  List<Task> get tasks {
    if (_tasks is EqualUnmodifiableListView) return _tasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tasks);
  }

  final List<Task> _filteredTasks;
  @override
  List<Task> get filteredTasks {
    if (_filteredTasks is EqualUnmodifiableListView) return _filteredTasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_filteredTasks);
  }

  @override
  final DateTime date;
// Search and filtering
  @override
  @JsonKey()
  final String searchQuery;
  @override
  final Priority? priorityFilter;
  @override
  final bool? completionFilter;
  @override
  @JsonKey()
  final bool hasActiveFilters;
// Error handling
  @override
  final String? errorMessage;
  @override
  final Exception? lastException;
  @override
  @JsonKey()
  final bool canRetry;
// Loading states for specific operations
  final Map<String, bool> _taskLoadingStates;
// Loading states for specific operations
  @override
  @JsonKey()
  Map<String, bool> get taskLoadingStates {
    if (_taskLoadingStates is EqualUnmodifiableMapView)
      return _taskLoadingStates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_taskLoadingStates);
  }

  final List<String> _deletingTaskIds;
  @override
  @JsonKey()
  List<String> get deletingTaskIds {
    if (_deletingTaskIds is EqualUnmodifiableListView) return _deletingTaskIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_deletingTaskIds);
  }

  final List<String> _updatingTaskIds;
  @override
  @JsonKey()
  List<String> get updatingTaskIds {
    if (_updatingTaskIds is EqualUnmodifiableListView) return _updatingTaskIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_updatingTaskIds);
  }

// Statistics
  @override
  @JsonKey()
  final int totalTasks;
  @override
  @JsonKey()
  final int completedTasks;
  @override
  @JsonKey()
  final int pendingTasks;
  @override
  @JsonKey()
  final int overdueTasksCount;
// Performance metrics
  @override
  final DateTime? lastUpdated;
  @override
  @JsonKey()
  final int loadDurationMs;

  @override
  String toString() {
    return 'TaskListState(status: $status, currentOperation: $currentOperation, tasks: $tasks, filteredTasks: $filteredTasks, date: $date, searchQuery: $searchQuery, priorityFilter: $priorityFilter, completionFilter: $completionFilter, hasActiveFilters: $hasActiveFilters, errorMessage: $errorMessage, lastException: $lastException, canRetry: $canRetry, taskLoadingStates: $taskLoadingStates, deletingTaskIds: $deletingTaskIds, updatingTaskIds: $updatingTaskIds, totalTasks: $totalTasks, completedTasks: $completedTasks, pendingTasks: $pendingTasks, overdueTasksCount: $overdueTasksCount, lastUpdated: $lastUpdated, loadDurationMs: $loadDurationMs)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskListStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.currentOperation, currentOperation) ||
                other.currentOperation == currentOperation) &&
            const DeepCollectionEquality().equals(other._tasks, _tasks) &&
            const DeepCollectionEquality()
                .equals(other._filteredTasks, _filteredTasks) &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.searchQuery, searchQuery) ||
                other.searchQuery == searchQuery) &&
            (identical(other.priorityFilter, priorityFilter) ||
                other.priorityFilter == priorityFilter) &&
            (identical(other.completionFilter, completionFilter) ||
                other.completionFilter == completionFilter) &&
            (identical(other.hasActiveFilters, hasActiveFilters) ||
                other.hasActiveFilters == hasActiveFilters) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.lastException, lastException) ||
                other.lastException == lastException) &&
            (identical(other.canRetry, canRetry) ||
                other.canRetry == canRetry) &&
            const DeepCollectionEquality()
                .equals(other._taskLoadingStates, _taskLoadingStates) &&
            const DeepCollectionEquality()
                .equals(other._deletingTaskIds, _deletingTaskIds) &&
            const DeepCollectionEquality()
                .equals(other._updatingTaskIds, _updatingTaskIds) &&
            (identical(other.totalTasks, totalTasks) ||
                other.totalTasks == totalTasks) &&
            (identical(other.completedTasks, completedTasks) ||
                other.completedTasks == completedTasks) &&
            (identical(other.pendingTasks, pendingTasks) ||
                other.pendingTasks == pendingTasks) &&
            (identical(other.overdueTasksCount, overdueTasksCount) ||
                other.overdueTasksCount == overdueTasksCount) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.loadDurationMs, loadDurationMs) ||
                other.loadDurationMs == loadDurationMs));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        status,
        currentOperation,
        const DeepCollectionEquality().hash(_tasks),
        const DeepCollectionEquality().hash(_filteredTasks),
        date,
        searchQuery,
        priorityFilter,
        completionFilter,
        hasActiveFilters,
        errorMessage,
        lastException,
        canRetry,
        const DeepCollectionEquality().hash(_taskLoadingStates),
        const DeepCollectionEquality().hash(_deletingTaskIds),
        const DeepCollectionEquality().hash(_updatingTaskIds),
        totalTasks,
        completedTasks,
        pendingTasks,
        overdueTasksCount,
        lastUpdated,
        loadDurationMs
      ]);

  /// Create a copy of TaskListState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskListStateImplCopyWith<_$TaskListStateImpl> get copyWith =>
      __$$TaskListStateImplCopyWithImpl<_$TaskListStateImpl>(this, _$identity);
}

abstract class _TaskListState implements TaskListState {
  const factory _TaskListState(
      {required final TaskListStatus status,
      required final TaskListOperation currentOperation,
      required final List<Task> tasks,
      required final List<Task> filteredTasks,
      required final DateTime date,
      final String searchQuery,
      final Priority? priorityFilter,
      final bool? completionFilter,
      final bool hasActiveFilters,
      final String? errorMessage,
      final Exception? lastException,
      final bool canRetry,
      final Map<String, bool> taskLoadingStates,
      final List<String> deletingTaskIds,
      final List<String> updatingTaskIds,
      final int totalTasks,
      final int completedTasks,
      final int pendingTasks,
      final int overdueTasksCount,
      final DateTime? lastUpdated,
      final int loadDurationMs}) = _$TaskListStateImpl;

  @override
  TaskListStatus get status;
  @override
  TaskListOperation get currentOperation;
  @override
  List<Task> get tasks;
  @override
  List<Task> get filteredTasks;
  @override
  DateTime get date; // Search and filtering
  @override
  String get searchQuery;
  @override
  Priority? get priorityFilter;
  @override
  bool? get completionFilter;
  @override
  bool get hasActiveFilters; // Error handling
  @override
  String? get errorMessage;
  @override
  Exception? get lastException;
  @override
  bool get canRetry; // Loading states for specific operations
  @override
  Map<String, bool> get taskLoadingStates;
  @override
  List<String> get deletingTaskIds;
  @override
  List<String> get updatingTaskIds; // Statistics
  @override
  int get totalTasks;
  @override
  int get completedTasks;
  @override
  int get pendingTasks;
  @override
  int get overdueTasksCount; // Performance metrics
  @override
  DateTime? get lastUpdated;
  @override
  int get loadDurationMs;

  /// Create a copy of TaskListState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskListStateImplCopyWith<_$TaskListStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
