import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../domain/models/task_model.dart';

part 'task_list_event.freezed.dart';

@freezed
abstract class TaskListEvent with _$TaskListEvent {
  /// Load tasks request
  const factory TaskListEvent.loadRequested() = _LoadRequested;

  /// Request subscription to tasks for a specific date
  const factory TaskListEvent.subscriptionRequested(DateTime date) = _SubscriptionRequested;

  /// Request subscription to tasks for a specific month
  const factory TaskListEvent.monthSubscriptionRequested({
    required int year,
    required int month,
  }) = _MonthSubscriptionRequested;
  
  /// Toggle task completion status
  const factory TaskListEvent.completionToggled({
    required String taskId,
    required bool isCompleted,
  }) = _CompletionToggled;

  /// Delete a task with confirmation
  const factory TaskListEvent.taskDeleted({
    required String taskId,
    @Default(false) bool skipConfirmation,
  }) = _TaskDeleted;

  /// Update an existing task
  const factory TaskListEvent.taskUpdated(Task task) = _TaskUpdated;

  /// Create a new task
  const factory TaskListEvent.taskCreated(Task task) = _TaskCreated;

  /// Batch update multiple tasks
  const factory TaskListEvent.batchTasksUpdated(List<Task> tasks) = _BatchTasksUpdated;

  /// Batch delete multiple tasks
  const factory TaskListEvent.batchTasksDeleted({
    required List<String> taskIds,
    @Default(false) bool skipConfirmation,
  }) = _BatchTasksDeleted;

  /// Move task to different priority (quadrant)
  const factory TaskListEvent.taskPriorityChanged({
    required String taskId,
    required Priority newPriority,
  }) = _TaskPriorityChanged;

  /// Move task to different date
  const factory TaskListEvent.taskDateChanged({
    required String taskId,
    required DateTime newDate,
  }) = _TaskDateChanged;

  /// Toggle subtask completion
  const factory TaskListEvent.subtaskToggled({
    required String taskId,
    required String subtaskId,
    required bool isCompleted,
  }) = _SubtaskToggled;

  /// Refresh tasks (force reload)
  const factory TaskListEvent.refreshRequested() = _RefreshRequested;

  /// Search tasks
  const factory TaskListEvent.searchRequested(String query) = _SearchRequested;

  /// Clear search
  const factory TaskListEvent.searchCleared() = _SearchCleared;

  /// Filter tasks by priority
  const factory TaskListEvent.filterByPriority(Priority? priority) = _FilterByPriority;

  /// Filter tasks by completion status
  const factory TaskListEvent.filterByCompletion(bool? isCompleted) = _FilterByCompletion;

  /// Clear all filters
  const factory TaskListEvent.filtersCleared() = _FiltersCleared;

  /// Retry failed operation
  const factory TaskListEvent.retryRequested() = _RetryRequested;

  /// Clear error state
  const factory TaskListEvent.errorCleared() = _ErrorCleared;

  /// (Internal) Tasks stream updated
  const factory TaskListEvent.tasksUpdated(List<Task> tasks) = _TasksUpdated;

  /// (Internal) Error occurred
  const factory TaskListEvent.errorOccurred({
    required String message,
    Exception? exception,
  }) = _ErrorOccurred;
}