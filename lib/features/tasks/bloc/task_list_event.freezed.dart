// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'task_list_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TaskListEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TaskListEventCopyWith<$Res> {
  factory $TaskListEventCopyWith(
          TaskListEvent value, $Res Function(TaskListEvent) then) =
      _$TaskListEventCopyWithImpl<$Res, TaskListEvent>;
}

/// @nodoc
class _$TaskListEventCopyWithImpl<$Res, $Val extends TaskListEvent>
    implements $TaskListEventCopyWith<$Res> {
  _$TaskListEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$LoadRequestedImplCopyWith<$Res> {
  factory _$$LoadRequestedImplCopyWith(
          _$LoadRequestedImpl value, $Res Function(_$LoadRequestedImpl) then) =
      __$$LoadRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$LoadRequestedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$LoadRequestedImpl>
    implements _$$LoadRequestedImplCopyWith<$Res> {
  __$$LoadRequestedImplCopyWithImpl(
      _$LoadRequestedImpl _value, $Res Function(_$LoadRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$LoadRequestedImpl implements _LoadRequested {
  const _$LoadRequestedImpl();

  @override
  String toString() {
    return 'TaskListEvent.loadRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$LoadRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return loadRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return loadRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (loadRequested != null) {
      return loadRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return loadRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return loadRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (loadRequested != null) {
      return loadRequested(this);
    }
    return orElse();
  }
}

abstract class _LoadRequested implements TaskListEvent {
  const factory _LoadRequested() = _$LoadRequestedImpl;
}

/// @nodoc
abstract class _$$SubscriptionRequestedImplCopyWith<$Res> {
  factory _$$SubscriptionRequestedImplCopyWith(
          _$SubscriptionRequestedImpl value,
          $Res Function(_$SubscriptionRequestedImpl) then) =
      __$$SubscriptionRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime date});
}

/// @nodoc
class __$$SubscriptionRequestedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$SubscriptionRequestedImpl>
    implements _$$SubscriptionRequestedImplCopyWith<$Res> {
  __$$SubscriptionRequestedImplCopyWithImpl(_$SubscriptionRequestedImpl _value,
      $Res Function(_$SubscriptionRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
  }) {
    return _then(_$SubscriptionRequestedImpl(
      null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$SubscriptionRequestedImpl implements _SubscriptionRequested {
  const _$SubscriptionRequestedImpl(this.date);

  @override
  final DateTime date;

  @override
  String toString() {
    return 'TaskListEvent.subscriptionRequested(date: $date)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubscriptionRequestedImpl &&
            (identical(other.date, date) || other.date == date));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubscriptionRequestedImplCopyWith<_$SubscriptionRequestedImpl>
      get copyWith => __$$SubscriptionRequestedImplCopyWithImpl<
          _$SubscriptionRequestedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return subscriptionRequested(date);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return subscriptionRequested?.call(date);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (subscriptionRequested != null) {
      return subscriptionRequested(date);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return subscriptionRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return subscriptionRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (subscriptionRequested != null) {
      return subscriptionRequested(this);
    }
    return orElse();
  }
}

abstract class _SubscriptionRequested implements TaskListEvent {
  const factory _SubscriptionRequested(final DateTime date) =
      _$SubscriptionRequestedImpl;

  DateTime get date;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubscriptionRequestedImplCopyWith<_$SubscriptionRequestedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MonthSubscriptionRequestedImplCopyWith<$Res> {
  factory _$$MonthSubscriptionRequestedImplCopyWith(
          _$MonthSubscriptionRequestedImpl value,
          $Res Function(_$MonthSubscriptionRequestedImpl) then) =
      __$$MonthSubscriptionRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int year, int month});
}

/// @nodoc
class __$$MonthSubscriptionRequestedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$MonthSubscriptionRequestedImpl>
    implements _$$MonthSubscriptionRequestedImplCopyWith<$Res> {
  __$$MonthSubscriptionRequestedImplCopyWithImpl(
      _$MonthSubscriptionRequestedImpl _value,
      $Res Function(_$MonthSubscriptionRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = null,
    Object? month = null,
  }) {
    return _then(_$MonthSubscriptionRequestedImpl(
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      month: null == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$MonthSubscriptionRequestedImpl implements _MonthSubscriptionRequested {
  const _$MonthSubscriptionRequestedImpl(
      {required this.year, required this.month});

  @override
  final int year;
  @override
  final int month;

  @override
  String toString() {
    return 'TaskListEvent.monthSubscriptionRequested(year: $year, month: $month)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonthSubscriptionRequestedImpl &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.month, month) || other.month == month));
  }

  @override
  int get hashCode => Object.hash(runtimeType, year, month);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MonthSubscriptionRequestedImplCopyWith<_$MonthSubscriptionRequestedImpl>
      get copyWith => __$$MonthSubscriptionRequestedImplCopyWithImpl<
          _$MonthSubscriptionRequestedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return monthSubscriptionRequested(year, month);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return monthSubscriptionRequested?.call(year, month);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (monthSubscriptionRequested != null) {
      return monthSubscriptionRequested(year, month);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return monthSubscriptionRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return monthSubscriptionRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (monthSubscriptionRequested != null) {
      return monthSubscriptionRequested(this);
    }
    return orElse();
  }
}

abstract class _MonthSubscriptionRequested implements TaskListEvent {
  const factory _MonthSubscriptionRequested(
      {required final int year,
      required final int month}) = _$MonthSubscriptionRequestedImpl;

  int get year;
  int get month;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MonthSubscriptionRequestedImplCopyWith<_$MonthSubscriptionRequestedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CompletionToggledImplCopyWith<$Res> {
  factory _$$CompletionToggledImplCopyWith(_$CompletionToggledImpl value,
          $Res Function(_$CompletionToggledImpl) then) =
      __$$CompletionToggledImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String taskId, bool isCompleted});
}

/// @nodoc
class __$$CompletionToggledImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$CompletionToggledImpl>
    implements _$$CompletionToggledImplCopyWith<$Res> {
  __$$CompletionToggledImplCopyWithImpl(_$CompletionToggledImpl _value,
      $Res Function(_$CompletionToggledImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = null,
    Object? isCompleted = null,
  }) {
    return _then(_$CompletionToggledImpl(
      taskId: null == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$CompletionToggledImpl implements _CompletionToggled {
  const _$CompletionToggledImpl(
      {required this.taskId, required this.isCompleted});

  @override
  final String taskId;
  @override
  final bool isCompleted;

  @override
  String toString() {
    return 'TaskListEvent.completionToggled(taskId: $taskId, isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompletionToggledImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted));
  }

  @override
  int get hashCode => Object.hash(runtimeType, taskId, isCompleted);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompletionToggledImplCopyWith<_$CompletionToggledImpl> get copyWith =>
      __$$CompletionToggledImplCopyWithImpl<_$CompletionToggledImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return completionToggled(taskId, isCompleted);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return completionToggled?.call(taskId, isCompleted);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (completionToggled != null) {
      return completionToggled(taskId, isCompleted);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return completionToggled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return completionToggled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (completionToggled != null) {
      return completionToggled(this);
    }
    return orElse();
  }
}

abstract class _CompletionToggled implements TaskListEvent {
  const factory _CompletionToggled(
      {required final String taskId,
      required final bool isCompleted}) = _$CompletionToggledImpl;

  String get taskId;
  bool get isCompleted;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompletionToggledImplCopyWith<_$CompletionToggledImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TaskDeletedImplCopyWith<$Res> {
  factory _$$TaskDeletedImplCopyWith(
          _$TaskDeletedImpl value, $Res Function(_$TaskDeletedImpl) then) =
      __$$TaskDeletedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String taskId, bool skipConfirmation});
}

/// @nodoc
class __$$TaskDeletedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$TaskDeletedImpl>
    implements _$$TaskDeletedImplCopyWith<$Res> {
  __$$TaskDeletedImplCopyWithImpl(
      _$TaskDeletedImpl _value, $Res Function(_$TaskDeletedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = null,
    Object? skipConfirmation = null,
  }) {
    return _then(_$TaskDeletedImpl(
      taskId: null == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      skipConfirmation: null == skipConfirmation
          ? _value.skipConfirmation
          : skipConfirmation // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$TaskDeletedImpl implements _TaskDeleted {
  const _$TaskDeletedImpl(
      {required this.taskId, this.skipConfirmation = false});

  @override
  final String taskId;
  @override
  @JsonKey()
  final bool skipConfirmation;

  @override
  String toString() {
    return 'TaskListEvent.taskDeleted(taskId: $taskId, skipConfirmation: $skipConfirmation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskDeletedImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.skipConfirmation, skipConfirmation) ||
                other.skipConfirmation == skipConfirmation));
  }

  @override
  int get hashCode => Object.hash(runtimeType, taskId, skipConfirmation);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskDeletedImplCopyWith<_$TaskDeletedImpl> get copyWith =>
      __$$TaskDeletedImplCopyWithImpl<_$TaskDeletedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return taskDeleted(taskId, skipConfirmation);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return taskDeleted?.call(taskId, skipConfirmation);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskDeleted != null) {
      return taskDeleted(taskId, skipConfirmation);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return taskDeleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return taskDeleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskDeleted != null) {
      return taskDeleted(this);
    }
    return orElse();
  }
}

abstract class _TaskDeleted implements TaskListEvent {
  const factory _TaskDeleted(
      {required final String taskId,
      final bool skipConfirmation}) = _$TaskDeletedImpl;

  String get taskId;
  bool get skipConfirmation;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskDeletedImplCopyWith<_$TaskDeletedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TaskUpdatedImplCopyWith<$Res> {
  factory _$$TaskUpdatedImplCopyWith(
          _$TaskUpdatedImpl value, $Res Function(_$TaskUpdatedImpl) then) =
      __$$TaskUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Task task});

  $TaskCopyWith<$Res> get task;
}

/// @nodoc
class __$$TaskUpdatedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$TaskUpdatedImpl>
    implements _$$TaskUpdatedImplCopyWith<$Res> {
  __$$TaskUpdatedImplCopyWithImpl(
      _$TaskUpdatedImpl _value, $Res Function(_$TaskUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? task = null,
  }) {
    return _then(_$TaskUpdatedImpl(
      null == task
          ? _value.task
          : task // ignore: cast_nullable_to_non_nullable
              as Task,
    ));
  }

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TaskCopyWith<$Res> get task {
    return $TaskCopyWith<$Res>(_value.task, (value) {
      return _then(_value.copyWith(task: value));
    });
  }
}

/// @nodoc

class _$TaskUpdatedImpl implements _TaskUpdated {
  const _$TaskUpdatedImpl(this.task);

  @override
  final Task task;

  @override
  String toString() {
    return 'TaskListEvent.taskUpdated(task: $task)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskUpdatedImpl &&
            (identical(other.task, task) || other.task == task));
  }

  @override
  int get hashCode => Object.hash(runtimeType, task);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskUpdatedImplCopyWith<_$TaskUpdatedImpl> get copyWith =>
      __$$TaskUpdatedImplCopyWithImpl<_$TaskUpdatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return taskUpdated(task);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return taskUpdated?.call(task);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskUpdated != null) {
      return taskUpdated(task);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return taskUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return taskUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskUpdated != null) {
      return taskUpdated(this);
    }
    return orElse();
  }
}

abstract class _TaskUpdated implements TaskListEvent {
  const factory _TaskUpdated(final Task task) = _$TaskUpdatedImpl;

  Task get task;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskUpdatedImplCopyWith<_$TaskUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TaskCreatedImplCopyWith<$Res> {
  factory _$$TaskCreatedImplCopyWith(
          _$TaskCreatedImpl value, $Res Function(_$TaskCreatedImpl) then) =
      __$$TaskCreatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Task task});

  $TaskCopyWith<$Res> get task;
}

/// @nodoc
class __$$TaskCreatedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$TaskCreatedImpl>
    implements _$$TaskCreatedImplCopyWith<$Res> {
  __$$TaskCreatedImplCopyWithImpl(
      _$TaskCreatedImpl _value, $Res Function(_$TaskCreatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? task = null,
  }) {
    return _then(_$TaskCreatedImpl(
      null == task
          ? _value.task
          : task // ignore: cast_nullable_to_non_nullable
              as Task,
    ));
  }

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $TaskCopyWith<$Res> get task {
    return $TaskCopyWith<$Res>(_value.task, (value) {
      return _then(_value.copyWith(task: value));
    });
  }
}

/// @nodoc

class _$TaskCreatedImpl implements _TaskCreated {
  const _$TaskCreatedImpl(this.task);

  @override
  final Task task;

  @override
  String toString() {
    return 'TaskListEvent.taskCreated(task: $task)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskCreatedImpl &&
            (identical(other.task, task) || other.task == task));
  }

  @override
  int get hashCode => Object.hash(runtimeType, task);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskCreatedImplCopyWith<_$TaskCreatedImpl> get copyWith =>
      __$$TaskCreatedImplCopyWithImpl<_$TaskCreatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return taskCreated(task);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return taskCreated?.call(task);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskCreated != null) {
      return taskCreated(task);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return taskCreated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return taskCreated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskCreated != null) {
      return taskCreated(this);
    }
    return orElse();
  }
}

abstract class _TaskCreated implements TaskListEvent {
  const factory _TaskCreated(final Task task) = _$TaskCreatedImpl;

  Task get task;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskCreatedImplCopyWith<_$TaskCreatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BatchTasksUpdatedImplCopyWith<$Res> {
  factory _$$BatchTasksUpdatedImplCopyWith(_$BatchTasksUpdatedImpl value,
          $Res Function(_$BatchTasksUpdatedImpl) then) =
      __$$BatchTasksUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Task> tasks});
}

/// @nodoc
class __$$BatchTasksUpdatedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$BatchTasksUpdatedImpl>
    implements _$$BatchTasksUpdatedImplCopyWith<$Res> {
  __$$BatchTasksUpdatedImplCopyWithImpl(_$BatchTasksUpdatedImpl _value,
      $Res Function(_$BatchTasksUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tasks = null,
  }) {
    return _then(_$BatchTasksUpdatedImpl(
      null == tasks
          ? _value._tasks
          : tasks // ignore: cast_nullable_to_non_nullable
              as List<Task>,
    ));
  }
}

/// @nodoc

class _$BatchTasksUpdatedImpl implements _BatchTasksUpdated {
  const _$BatchTasksUpdatedImpl(final List<Task> tasks) : _tasks = tasks;

  final List<Task> _tasks;
  @override
  List<Task> get tasks {
    if (_tasks is EqualUnmodifiableListView) return _tasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tasks);
  }

  @override
  String toString() {
    return 'TaskListEvent.batchTasksUpdated(tasks: $tasks)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BatchTasksUpdatedImpl &&
            const DeepCollectionEquality().equals(other._tasks, _tasks));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_tasks));

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BatchTasksUpdatedImplCopyWith<_$BatchTasksUpdatedImpl> get copyWith =>
      __$$BatchTasksUpdatedImplCopyWithImpl<_$BatchTasksUpdatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return batchTasksUpdated(tasks);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return batchTasksUpdated?.call(tasks);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (batchTasksUpdated != null) {
      return batchTasksUpdated(tasks);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return batchTasksUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return batchTasksUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (batchTasksUpdated != null) {
      return batchTasksUpdated(this);
    }
    return orElse();
  }
}

abstract class _BatchTasksUpdated implements TaskListEvent {
  const factory _BatchTasksUpdated(final List<Task> tasks) =
      _$BatchTasksUpdatedImpl;

  List<Task> get tasks;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BatchTasksUpdatedImplCopyWith<_$BatchTasksUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$BatchTasksDeletedImplCopyWith<$Res> {
  factory _$$BatchTasksDeletedImplCopyWith(_$BatchTasksDeletedImpl value,
          $Res Function(_$BatchTasksDeletedImpl) then) =
      __$$BatchTasksDeletedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<String> taskIds, bool skipConfirmation});
}

/// @nodoc
class __$$BatchTasksDeletedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$BatchTasksDeletedImpl>
    implements _$$BatchTasksDeletedImplCopyWith<$Res> {
  __$$BatchTasksDeletedImplCopyWithImpl(_$BatchTasksDeletedImpl _value,
      $Res Function(_$BatchTasksDeletedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskIds = null,
    Object? skipConfirmation = null,
  }) {
    return _then(_$BatchTasksDeletedImpl(
      taskIds: null == taskIds
          ? _value._taskIds
          : taskIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      skipConfirmation: null == skipConfirmation
          ? _value.skipConfirmation
          : skipConfirmation // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$BatchTasksDeletedImpl implements _BatchTasksDeleted {
  const _$BatchTasksDeletedImpl(
      {required final List<String> taskIds, this.skipConfirmation = false})
      : _taskIds = taskIds;

  final List<String> _taskIds;
  @override
  List<String> get taskIds {
    if (_taskIds is EqualUnmodifiableListView) return _taskIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_taskIds);
  }

  @override
  @JsonKey()
  final bool skipConfirmation;

  @override
  String toString() {
    return 'TaskListEvent.batchTasksDeleted(taskIds: $taskIds, skipConfirmation: $skipConfirmation)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BatchTasksDeletedImpl &&
            const DeepCollectionEquality().equals(other._taskIds, _taskIds) &&
            (identical(other.skipConfirmation, skipConfirmation) ||
                other.skipConfirmation == skipConfirmation));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_taskIds), skipConfirmation);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BatchTasksDeletedImplCopyWith<_$BatchTasksDeletedImpl> get copyWith =>
      __$$BatchTasksDeletedImplCopyWithImpl<_$BatchTasksDeletedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return batchTasksDeleted(taskIds, skipConfirmation);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return batchTasksDeleted?.call(taskIds, skipConfirmation);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (batchTasksDeleted != null) {
      return batchTasksDeleted(taskIds, skipConfirmation);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return batchTasksDeleted(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return batchTasksDeleted?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (batchTasksDeleted != null) {
      return batchTasksDeleted(this);
    }
    return orElse();
  }
}

abstract class _BatchTasksDeleted implements TaskListEvent {
  const factory _BatchTasksDeleted(
      {required final List<String> taskIds,
      final bool skipConfirmation}) = _$BatchTasksDeletedImpl;

  List<String> get taskIds;
  bool get skipConfirmation;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BatchTasksDeletedImplCopyWith<_$BatchTasksDeletedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TaskPriorityChangedImplCopyWith<$Res> {
  factory _$$TaskPriorityChangedImplCopyWith(_$TaskPriorityChangedImpl value,
          $Res Function(_$TaskPriorityChangedImpl) then) =
      __$$TaskPriorityChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String taskId, Priority newPriority});
}

/// @nodoc
class __$$TaskPriorityChangedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$TaskPriorityChangedImpl>
    implements _$$TaskPriorityChangedImplCopyWith<$Res> {
  __$$TaskPriorityChangedImplCopyWithImpl(_$TaskPriorityChangedImpl _value,
      $Res Function(_$TaskPriorityChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = null,
    Object? newPriority = null,
  }) {
    return _then(_$TaskPriorityChangedImpl(
      taskId: null == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      newPriority: null == newPriority
          ? _value.newPriority
          : newPriority // ignore: cast_nullable_to_non_nullable
              as Priority,
    ));
  }
}

/// @nodoc

class _$TaskPriorityChangedImpl implements _TaskPriorityChanged {
  const _$TaskPriorityChangedImpl(
      {required this.taskId, required this.newPriority});

  @override
  final String taskId;
  @override
  final Priority newPriority;

  @override
  String toString() {
    return 'TaskListEvent.taskPriorityChanged(taskId: $taskId, newPriority: $newPriority)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskPriorityChangedImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.newPriority, newPriority) ||
                other.newPriority == newPriority));
  }

  @override
  int get hashCode => Object.hash(runtimeType, taskId, newPriority);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskPriorityChangedImplCopyWith<_$TaskPriorityChangedImpl> get copyWith =>
      __$$TaskPriorityChangedImplCopyWithImpl<_$TaskPriorityChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return taskPriorityChanged(taskId, newPriority);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return taskPriorityChanged?.call(taskId, newPriority);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskPriorityChanged != null) {
      return taskPriorityChanged(taskId, newPriority);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return taskPriorityChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return taskPriorityChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskPriorityChanged != null) {
      return taskPriorityChanged(this);
    }
    return orElse();
  }
}

abstract class _TaskPriorityChanged implements TaskListEvent {
  const factory _TaskPriorityChanged(
      {required final String taskId,
      required final Priority newPriority}) = _$TaskPriorityChangedImpl;

  String get taskId;
  Priority get newPriority;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskPriorityChangedImplCopyWith<_$TaskPriorityChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$TaskDateChangedImplCopyWith<$Res> {
  factory _$$TaskDateChangedImplCopyWith(_$TaskDateChangedImpl value,
          $Res Function(_$TaskDateChangedImpl) then) =
      __$$TaskDateChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String taskId, DateTime newDate});
}

/// @nodoc
class __$$TaskDateChangedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$TaskDateChangedImpl>
    implements _$$TaskDateChangedImplCopyWith<$Res> {
  __$$TaskDateChangedImplCopyWithImpl(
      _$TaskDateChangedImpl _value, $Res Function(_$TaskDateChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = null,
    Object? newDate = null,
  }) {
    return _then(_$TaskDateChangedImpl(
      taskId: null == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      newDate: null == newDate
          ? _value.newDate
          : newDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$TaskDateChangedImpl implements _TaskDateChanged {
  const _$TaskDateChangedImpl({required this.taskId, required this.newDate});

  @override
  final String taskId;
  @override
  final DateTime newDate;

  @override
  String toString() {
    return 'TaskListEvent.taskDateChanged(taskId: $taskId, newDate: $newDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TaskDateChangedImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.newDate, newDate) || other.newDate == newDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, taskId, newDate);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TaskDateChangedImplCopyWith<_$TaskDateChangedImpl> get copyWith =>
      __$$TaskDateChangedImplCopyWithImpl<_$TaskDateChangedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return taskDateChanged(taskId, newDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return taskDateChanged?.call(taskId, newDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskDateChanged != null) {
      return taskDateChanged(taskId, newDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return taskDateChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return taskDateChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (taskDateChanged != null) {
      return taskDateChanged(this);
    }
    return orElse();
  }
}

abstract class _TaskDateChanged implements TaskListEvent {
  const factory _TaskDateChanged(
      {required final String taskId,
      required final DateTime newDate}) = _$TaskDateChangedImpl;

  String get taskId;
  DateTime get newDate;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TaskDateChangedImplCopyWith<_$TaskDateChangedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SubtaskToggledImplCopyWith<$Res> {
  factory _$$SubtaskToggledImplCopyWith(_$SubtaskToggledImpl value,
          $Res Function(_$SubtaskToggledImpl) then) =
      __$$SubtaskToggledImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String taskId, String subtaskId, bool isCompleted});
}

/// @nodoc
class __$$SubtaskToggledImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$SubtaskToggledImpl>
    implements _$$SubtaskToggledImplCopyWith<$Res> {
  __$$SubtaskToggledImplCopyWithImpl(
      _$SubtaskToggledImpl _value, $Res Function(_$SubtaskToggledImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? taskId = null,
    Object? subtaskId = null,
    Object? isCompleted = null,
  }) {
    return _then(_$SubtaskToggledImpl(
      taskId: null == taskId
          ? _value.taskId
          : taskId // ignore: cast_nullable_to_non_nullable
              as String,
      subtaskId: null == subtaskId
          ? _value.subtaskId
          : subtaskId // ignore: cast_nullable_to_non_nullable
              as String,
      isCompleted: null == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc

class _$SubtaskToggledImpl implements _SubtaskToggled {
  const _$SubtaskToggledImpl(
      {required this.taskId,
      required this.subtaskId,
      required this.isCompleted});

  @override
  final String taskId;
  @override
  final String subtaskId;
  @override
  final bool isCompleted;

  @override
  String toString() {
    return 'TaskListEvent.subtaskToggled(taskId: $taskId, subtaskId: $subtaskId, isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubtaskToggledImpl &&
            (identical(other.taskId, taskId) || other.taskId == taskId) &&
            (identical(other.subtaskId, subtaskId) ||
                other.subtaskId == subtaskId) &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted));
  }

  @override
  int get hashCode => Object.hash(runtimeType, taskId, subtaskId, isCompleted);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubtaskToggledImplCopyWith<_$SubtaskToggledImpl> get copyWith =>
      __$$SubtaskToggledImplCopyWithImpl<_$SubtaskToggledImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return subtaskToggled(taskId, subtaskId, isCompleted);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return subtaskToggled?.call(taskId, subtaskId, isCompleted);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (subtaskToggled != null) {
      return subtaskToggled(taskId, subtaskId, isCompleted);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return subtaskToggled(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return subtaskToggled?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (subtaskToggled != null) {
      return subtaskToggled(this);
    }
    return orElse();
  }
}

abstract class _SubtaskToggled implements TaskListEvent {
  const factory _SubtaskToggled(
      {required final String taskId,
      required final String subtaskId,
      required final bool isCompleted}) = _$SubtaskToggledImpl;

  String get taskId;
  String get subtaskId;
  bool get isCompleted;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubtaskToggledImplCopyWith<_$SubtaskToggledImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$RefreshRequestedImplCopyWith<$Res> {
  factory _$$RefreshRequestedImplCopyWith(_$RefreshRequestedImpl value,
          $Res Function(_$RefreshRequestedImpl) then) =
      __$$RefreshRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RefreshRequestedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$RefreshRequestedImpl>
    implements _$$RefreshRequestedImplCopyWith<$Res> {
  __$$RefreshRequestedImplCopyWithImpl(_$RefreshRequestedImpl _value,
      $Res Function(_$RefreshRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RefreshRequestedImpl implements _RefreshRequested {
  const _$RefreshRequestedImpl();

  @override
  String toString() {
    return 'TaskListEvent.refreshRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RefreshRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return refreshRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return refreshRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (refreshRequested != null) {
      return refreshRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return refreshRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return refreshRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (refreshRequested != null) {
      return refreshRequested(this);
    }
    return orElse();
  }
}

abstract class _RefreshRequested implements TaskListEvent {
  const factory _RefreshRequested() = _$RefreshRequestedImpl;
}

/// @nodoc
abstract class _$$SearchRequestedImplCopyWith<$Res> {
  factory _$$SearchRequestedImplCopyWith(_$SearchRequestedImpl value,
          $Res Function(_$SearchRequestedImpl) then) =
      __$$SearchRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String query});
}

/// @nodoc
class __$$SearchRequestedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$SearchRequestedImpl>
    implements _$$SearchRequestedImplCopyWith<$Res> {
  __$$SearchRequestedImplCopyWithImpl(
      _$SearchRequestedImpl _value, $Res Function(_$SearchRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? query = null,
  }) {
    return _then(_$SearchRequestedImpl(
      null == query
          ? _value.query
          : query // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$SearchRequestedImpl implements _SearchRequested {
  const _$SearchRequestedImpl(this.query);

  @override
  final String query;

  @override
  String toString() {
    return 'TaskListEvent.searchRequested(query: $query)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SearchRequestedImpl &&
            (identical(other.query, query) || other.query == query));
  }

  @override
  int get hashCode => Object.hash(runtimeType, query);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SearchRequestedImplCopyWith<_$SearchRequestedImpl> get copyWith =>
      __$$SearchRequestedImplCopyWithImpl<_$SearchRequestedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return searchRequested(query);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return searchRequested?.call(query);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (searchRequested != null) {
      return searchRequested(query);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return searchRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return searchRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (searchRequested != null) {
      return searchRequested(this);
    }
    return orElse();
  }
}

abstract class _SearchRequested implements TaskListEvent {
  const factory _SearchRequested(final String query) = _$SearchRequestedImpl;

  String get query;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SearchRequestedImplCopyWith<_$SearchRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$SearchClearedImplCopyWith<$Res> {
  factory _$$SearchClearedImplCopyWith(
          _$SearchClearedImpl value, $Res Function(_$SearchClearedImpl) then) =
      __$$SearchClearedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$SearchClearedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$SearchClearedImpl>
    implements _$$SearchClearedImplCopyWith<$Res> {
  __$$SearchClearedImplCopyWithImpl(
      _$SearchClearedImpl _value, $Res Function(_$SearchClearedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$SearchClearedImpl implements _SearchCleared {
  const _$SearchClearedImpl();

  @override
  String toString() {
    return 'TaskListEvent.searchCleared()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$SearchClearedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return searchCleared();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return searchCleared?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (searchCleared != null) {
      return searchCleared();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return searchCleared(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return searchCleared?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (searchCleared != null) {
      return searchCleared(this);
    }
    return orElse();
  }
}

abstract class _SearchCleared implements TaskListEvent {
  const factory _SearchCleared() = _$SearchClearedImpl;
}

/// @nodoc
abstract class _$$FilterByPriorityImplCopyWith<$Res> {
  factory _$$FilterByPriorityImplCopyWith(_$FilterByPriorityImpl value,
          $Res Function(_$FilterByPriorityImpl) then) =
      __$$FilterByPriorityImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Priority? priority});
}

/// @nodoc
class __$$FilterByPriorityImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$FilterByPriorityImpl>
    implements _$$FilterByPriorityImplCopyWith<$Res> {
  __$$FilterByPriorityImplCopyWithImpl(_$FilterByPriorityImpl _value,
      $Res Function(_$FilterByPriorityImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? priority = freezed,
  }) {
    return _then(_$FilterByPriorityImpl(
      freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as Priority?,
    ));
  }
}

/// @nodoc

class _$FilterByPriorityImpl implements _FilterByPriority {
  const _$FilterByPriorityImpl(this.priority);

  @override
  final Priority? priority;

  @override
  String toString() {
    return 'TaskListEvent.filterByPriority(priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterByPriorityImpl &&
            (identical(other.priority, priority) ||
                other.priority == priority));
  }

  @override
  int get hashCode => Object.hash(runtimeType, priority);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterByPriorityImplCopyWith<_$FilterByPriorityImpl> get copyWith =>
      __$$FilterByPriorityImplCopyWithImpl<_$FilterByPriorityImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return filterByPriority(priority);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return filterByPriority?.call(priority);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (filterByPriority != null) {
      return filterByPriority(priority);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return filterByPriority(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return filterByPriority?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (filterByPriority != null) {
      return filterByPriority(this);
    }
    return orElse();
  }
}

abstract class _FilterByPriority implements TaskListEvent {
  const factory _FilterByPriority(final Priority? priority) =
      _$FilterByPriorityImpl;

  Priority? get priority;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FilterByPriorityImplCopyWith<_$FilterByPriorityImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FilterByCompletionImplCopyWith<$Res> {
  factory _$$FilterByCompletionImplCopyWith(_$FilterByCompletionImpl value,
          $Res Function(_$FilterByCompletionImpl) then) =
      __$$FilterByCompletionImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool? isCompleted});
}

/// @nodoc
class __$$FilterByCompletionImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$FilterByCompletionImpl>
    implements _$$FilterByCompletionImplCopyWith<$Res> {
  __$$FilterByCompletionImplCopyWithImpl(_$FilterByCompletionImpl _value,
      $Res Function(_$FilterByCompletionImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCompleted = freezed,
  }) {
    return _then(_$FilterByCompletionImpl(
      freezed == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$FilterByCompletionImpl implements _FilterByCompletion {
  const _$FilterByCompletionImpl(this.isCompleted);

  @override
  final bool? isCompleted;

  @override
  String toString() {
    return 'TaskListEvent.filterByCompletion(isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FilterByCompletionImpl &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isCompleted);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FilterByCompletionImplCopyWith<_$FilterByCompletionImpl> get copyWith =>
      __$$FilterByCompletionImplCopyWithImpl<_$FilterByCompletionImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return filterByCompletion(isCompleted);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return filterByCompletion?.call(isCompleted);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (filterByCompletion != null) {
      return filterByCompletion(isCompleted);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return filterByCompletion(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return filterByCompletion?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (filterByCompletion != null) {
      return filterByCompletion(this);
    }
    return orElse();
  }
}

abstract class _FilterByCompletion implements TaskListEvent {
  const factory _FilterByCompletion(final bool? isCompleted) =
      _$FilterByCompletionImpl;

  bool? get isCompleted;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FilterByCompletionImplCopyWith<_$FilterByCompletionImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FiltersClearedImplCopyWith<$Res> {
  factory _$$FiltersClearedImplCopyWith(_$FiltersClearedImpl value,
          $Res Function(_$FiltersClearedImpl) then) =
      __$$FiltersClearedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FiltersClearedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$FiltersClearedImpl>
    implements _$$FiltersClearedImplCopyWith<$Res> {
  __$$FiltersClearedImplCopyWithImpl(
      _$FiltersClearedImpl _value, $Res Function(_$FiltersClearedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FiltersClearedImpl implements _FiltersCleared {
  const _$FiltersClearedImpl();

  @override
  String toString() {
    return 'TaskListEvent.filtersCleared()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FiltersClearedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return filtersCleared();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return filtersCleared?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (filtersCleared != null) {
      return filtersCleared();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return filtersCleared(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return filtersCleared?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (filtersCleared != null) {
      return filtersCleared(this);
    }
    return orElse();
  }
}

abstract class _FiltersCleared implements TaskListEvent {
  const factory _FiltersCleared() = _$FiltersClearedImpl;
}

/// @nodoc
abstract class _$$RetryRequestedImplCopyWith<$Res> {
  factory _$$RetryRequestedImplCopyWith(_$RetryRequestedImpl value,
          $Res Function(_$RetryRequestedImpl) then) =
      __$$RetryRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RetryRequestedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$RetryRequestedImpl>
    implements _$$RetryRequestedImplCopyWith<$Res> {
  __$$RetryRequestedImplCopyWithImpl(
      _$RetryRequestedImpl _value, $Res Function(_$RetryRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RetryRequestedImpl implements _RetryRequested {
  const _$RetryRequestedImpl();

  @override
  String toString() {
    return 'TaskListEvent.retryRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RetryRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return retryRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return retryRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (retryRequested != null) {
      return retryRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return retryRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return retryRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (retryRequested != null) {
      return retryRequested(this);
    }
    return orElse();
  }
}

abstract class _RetryRequested implements TaskListEvent {
  const factory _RetryRequested() = _$RetryRequestedImpl;
}

/// @nodoc
abstract class _$$ErrorClearedImplCopyWith<$Res> {
  factory _$$ErrorClearedImplCopyWith(
          _$ErrorClearedImpl value, $Res Function(_$ErrorClearedImpl) then) =
      __$$ErrorClearedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ErrorClearedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$ErrorClearedImpl>
    implements _$$ErrorClearedImplCopyWith<$Res> {
  __$$ErrorClearedImplCopyWithImpl(
      _$ErrorClearedImpl _value, $Res Function(_$ErrorClearedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ErrorClearedImpl implements _ErrorCleared {
  const _$ErrorClearedImpl();

  @override
  String toString() {
    return 'TaskListEvent.errorCleared()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ErrorClearedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return errorCleared();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return errorCleared?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorCleared != null) {
      return errorCleared();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return errorCleared(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return errorCleared?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorCleared != null) {
      return errorCleared(this);
    }
    return orElse();
  }
}

abstract class _ErrorCleared implements TaskListEvent {
  const factory _ErrorCleared() = _$ErrorClearedImpl;
}

/// @nodoc
abstract class _$$TasksUpdatedImplCopyWith<$Res> {
  factory _$$TasksUpdatedImplCopyWith(
          _$TasksUpdatedImpl value, $Res Function(_$TasksUpdatedImpl) then) =
      __$$TasksUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({List<Task> tasks});
}

/// @nodoc
class __$$TasksUpdatedImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$TasksUpdatedImpl>
    implements _$$TasksUpdatedImplCopyWith<$Res> {
  __$$TasksUpdatedImplCopyWithImpl(
      _$TasksUpdatedImpl _value, $Res Function(_$TasksUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? tasks = null,
  }) {
    return _then(_$TasksUpdatedImpl(
      null == tasks
          ? _value._tasks
          : tasks // ignore: cast_nullable_to_non_nullable
              as List<Task>,
    ));
  }
}

/// @nodoc

class _$TasksUpdatedImpl implements _TasksUpdated {
  const _$TasksUpdatedImpl(final List<Task> tasks) : _tasks = tasks;

  final List<Task> _tasks;
  @override
  List<Task> get tasks {
    if (_tasks is EqualUnmodifiableListView) return _tasks;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tasks);
  }

  @override
  String toString() {
    return 'TaskListEvent.tasksUpdated(tasks: $tasks)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TasksUpdatedImpl &&
            const DeepCollectionEquality().equals(other._tasks, _tasks));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, const DeepCollectionEquality().hash(_tasks));

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$TasksUpdatedImplCopyWith<_$TasksUpdatedImpl> get copyWith =>
      __$$TasksUpdatedImplCopyWithImpl<_$TasksUpdatedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return tasksUpdated(tasks);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return tasksUpdated?.call(tasks);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (tasksUpdated != null) {
      return tasksUpdated(tasks);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return tasksUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return tasksUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (tasksUpdated != null) {
      return tasksUpdated(this);
    }
    return orElse();
  }
}

abstract class _TasksUpdated implements TaskListEvent {
  const factory _TasksUpdated(final List<Task> tasks) = _$TasksUpdatedImpl;

  List<Task> get tasks;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$TasksUpdatedImplCopyWith<_$TasksUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorOccurredImplCopyWith<$Res> {
  factory _$$ErrorOccurredImplCopyWith(
          _$ErrorOccurredImpl value, $Res Function(_$ErrorOccurredImpl) then) =
      __$$ErrorOccurredImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, Exception? exception});
}

/// @nodoc
class __$$ErrorOccurredImplCopyWithImpl<$Res>
    extends _$TaskListEventCopyWithImpl<$Res, _$ErrorOccurredImpl>
    implements _$$ErrorOccurredImplCopyWith<$Res> {
  __$$ErrorOccurredImplCopyWithImpl(
      _$ErrorOccurredImpl _value, $Res Function(_$ErrorOccurredImpl) _then)
      : super(_value, _then);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? exception = freezed,
  }) {
    return _then(_$ErrorOccurredImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      exception: freezed == exception
          ? _value.exception
          : exception // ignore: cast_nullable_to_non_nullable
              as Exception?,
    ));
  }
}

/// @nodoc

class _$ErrorOccurredImpl implements _ErrorOccurred {
  const _$ErrorOccurredImpl({required this.message, this.exception});

  @override
  final String message;
  @override
  final Exception? exception;

  @override
  String toString() {
    return 'TaskListEvent.errorOccurred(message: $message, exception: $exception)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorOccurredImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.exception, exception) ||
                other.exception == exception));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, exception);

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorOccurredImplCopyWith<_$ErrorOccurredImpl> get copyWith =>
      __$$ErrorOccurredImplCopyWithImpl<_$ErrorOccurredImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function() loadRequested,
    required TResult Function(DateTime date) subscriptionRequested,
    required TResult Function(int year, int month) monthSubscriptionRequested,
    required TResult Function(String taskId, bool isCompleted)
        completionToggled,
    required TResult Function(String taskId, bool skipConfirmation) taskDeleted,
    required TResult Function(Task task) taskUpdated,
    required TResult Function(Task task) taskCreated,
    required TResult Function(List<Task> tasks) batchTasksUpdated,
    required TResult Function(List<String> taskIds, bool skipConfirmation)
        batchTasksDeleted,
    required TResult Function(String taskId, Priority newPriority)
        taskPriorityChanged,
    required TResult Function(String taskId, DateTime newDate) taskDateChanged,
    required TResult Function(String taskId, String subtaskId, bool isCompleted)
        subtaskToggled,
    required TResult Function() refreshRequested,
    required TResult Function(String query) searchRequested,
    required TResult Function() searchCleared,
    required TResult Function(Priority? priority) filterByPriority,
    required TResult Function(bool? isCompleted) filterByCompletion,
    required TResult Function() filtersCleared,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(List<Task> tasks) tasksUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return errorOccurred(message, exception);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function()? loadRequested,
    TResult? Function(DateTime date)? subscriptionRequested,
    TResult? Function(int year, int month)? monthSubscriptionRequested,
    TResult? Function(String taskId, bool isCompleted)? completionToggled,
    TResult? Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult? Function(Task task)? taskUpdated,
    TResult? Function(Task task)? taskCreated,
    TResult? Function(List<Task> tasks)? batchTasksUpdated,
    TResult? Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult? Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult? Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult? Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult? Function()? refreshRequested,
    TResult? Function(String query)? searchRequested,
    TResult? Function()? searchCleared,
    TResult? Function(Priority? priority)? filterByPriority,
    TResult? Function(bool? isCompleted)? filterByCompletion,
    TResult? Function()? filtersCleared,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(List<Task> tasks)? tasksUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return errorOccurred?.call(message, exception);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function()? loadRequested,
    TResult Function(DateTime date)? subscriptionRequested,
    TResult Function(int year, int month)? monthSubscriptionRequested,
    TResult Function(String taskId, bool isCompleted)? completionToggled,
    TResult Function(String taskId, bool skipConfirmation)? taskDeleted,
    TResult Function(Task task)? taskUpdated,
    TResult Function(Task task)? taskCreated,
    TResult Function(List<Task> tasks)? batchTasksUpdated,
    TResult Function(List<String> taskIds, bool skipConfirmation)?
        batchTasksDeleted,
    TResult Function(String taskId, Priority newPriority)? taskPriorityChanged,
    TResult Function(String taskId, DateTime newDate)? taskDateChanged,
    TResult Function(String taskId, String subtaskId, bool isCompleted)?
        subtaskToggled,
    TResult Function()? refreshRequested,
    TResult Function(String query)? searchRequested,
    TResult Function()? searchCleared,
    TResult Function(Priority? priority)? filterByPriority,
    TResult Function(bool? isCompleted)? filterByCompletion,
    TResult Function()? filtersCleared,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(List<Task> tasks)? tasksUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorOccurred != null) {
      return errorOccurred(message, exception);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_LoadRequested value) loadRequested,
    required TResult Function(_SubscriptionRequested value)
        subscriptionRequested,
    required TResult Function(_MonthSubscriptionRequested value)
        monthSubscriptionRequested,
    required TResult Function(_CompletionToggled value) completionToggled,
    required TResult Function(_TaskDeleted value) taskDeleted,
    required TResult Function(_TaskUpdated value) taskUpdated,
    required TResult Function(_TaskCreated value) taskCreated,
    required TResult Function(_BatchTasksUpdated value) batchTasksUpdated,
    required TResult Function(_BatchTasksDeleted value) batchTasksDeleted,
    required TResult Function(_TaskPriorityChanged value) taskPriorityChanged,
    required TResult Function(_TaskDateChanged value) taskDateChanged,
    required TResult Function(_SubtaskToggled value) subtaskToggled,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_SearchRequested value) searchRequested,
    required TResult Function(_SearchCleared value) searchCleared,
    required TResult Function(_FilterByPriority value) filterByPriority,
    required TResult Function(_FilterByCompletion value) filterByCompletion,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_TasksUpdated value) tasksUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return errorOccurred(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_LoadRequested value)? loadRequested,
    TResult? Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult? Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult? Function(_CompletionToggled value)? completionToggled,
    TResult? Function(_TaskDeleted value)? taskDeleted,
    TResult? Function(_TaskUpdated value)? taskUpdated,
    TResult? Function(_TaskCreated value)? taskCreated,
    TResult? Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult? Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult? Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult? Function(_TaskDateChanged value)? taskDateChanged,
    TResult? Function(_SubtaskToggled value)? subtaskToggled,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_SearchRequested value)? searchRequested,
    TResult? Function(_SearchCleared value)? searchCleared,
    TResult? Function(_FilterByPriority value)? filterByPriority,
    TResult? Function(_FilterByCompletion value)? filterByCompletion,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_TasksUpdated value)? tasksUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return errorOccurred?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_LoadRequested value)? loadRequested,
    TResult Function(_SubscriptionRequested value)? subscriptionRequested,
    TResult Function(_MonthSubscriptionRequested value)?
        monthSubscriptionRequested,
    TResult Function(_CompletionToggled value)? completionToggled,
    TResult Function(_TaskDeleted value)? taskDeleted,
    TResult Function(_TaskUpdated value)? taskUpdated,
    TResult Function(_TaskCreated value)? taskCreated,
    TResult Function(_BatchTasksUpdated value)? batchTasksUpdated,
    TResult Function(_BatchTasksDeleted value)? batchTasksDeleted,
    TResult Function(_TaskPriorityChanged value)? taskPriorityChanged,
    TResult Function(_TaskDateChanged value)? taskDateChanged,
    TResult Function(_SubtaskToggled value)? subtaskToggled,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_SearchRequested value)? searchRequested,
    TResult Function(_SearchCleared value)? searchCleared,
    TResult Function(_FilterByPriority value)? filterByPriority,
    TResult Function(_FilterByCompletion value)? filterByCompletion,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_TasksUpdated value)? tasksUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorOccurred != null) {
      return errorOccurred(this);
    }
    return orElse();
  }
}

abstract class _ErrorOccurred implements TaskListEvent {
  const factory _ErrorOccurred(
      {required final String message,
      final Exception? exception}) = _$ErrorOccurredImpl;

  String get message;
  Exception? get exception;

  /// Create a copy of TaskListEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorOccurredImplCopyWith<_$ErrorOccurredImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
