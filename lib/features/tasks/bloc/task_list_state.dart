import 'package:clock/clock.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../domain/models/task_model.dart';

part 'task_list_state.freezed.dart';

enum TaskListStatus {
  initial,
  loading,
  success,
  failure,
  refreshing,
  updating,
  deleting,
  searching,
}

enum TaskListOperation {
  none,
  loadingTasks,
  creatingTask,
  updatingTask,
  deletingTask,
  togglingCompletion,
  changingPriority,
  changingDate,
  batchUpdating,
  batchDeleting,
  searching,
  refreshing,
}

@freezed
abstract class TaskListState with _$TaskListState {
  const factory TaskListState({
    required TaskListStatus status,
    required TaskListOperation currentOperation,
    required List<Task> tasks,
    required List<Task> filteredTasks,
    required DateTime date,

    // Search and filtering
    @Default('') String searchQuery,
    Priority? priorityFilter,
    bool? completionFilter,
    @Default(false) bool hasActiveFilters,

    // Error handling
    String? errorMessage,
    Exception? lastException,
    @Default(false) bool canRetry,

    // Loading states for specific operations
    @Default({}) Map<String, bool> taskLoadingStates,
    @Default([]) List<String> deletingTaskIds,
    @Default([]) List<String> updatingTaskIds,

    // Statistics
    @Default(0) int totalTasks,
    @Default(0) int completedTasks,
    @Default(0) int pendingTasks,
    @Default(0) int overdueTasksCount,

    // Performance metrics
    DateTime? lastUpdated,
    @Default(0) int loadDurationMs,
  }) = _TaskListState;

  factory TaskListState.initial() => TaskListState(
        status: TaskListStatus.initial,
        currentOperation: TaskListOperation.none,
        tasks: [],
        filteredTasks: [],
        date: clock.now(),
      );
}

extension TaskListStateX on TaskListState {
  /// Check if currently loading
  bool get isLoading =>
      status == TaskListStatus.loading ||
      status == TaskListStatus.refreshing ||
      status == TaskListStatus.updating ||
      status == TaskListStatus.deleting ||
      status == TaskListStatus.searching;

  /// Check if has error
  bool get hasError => status == TaskListStatus.failure;

  /// Check if has tasks
  bool get hasTasks => filteredTasks.isNotEmpty;

  /// Get completion rate as percentage
  double get completionRate =>
      totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0.0;

  /// Get tasks grouped by priority
  Map<Priority, List<Task>> get tasksByPriority {
    final grouped = <Priority, List<Task>>{};
    for (final priority in Priority.values) {
      grouped[priority] =
          filteredTasks.where((task) => task.priority == priority).toList();
    }
    return grouped;
  }

  /// Get overdue tasks
  List<Task> get overdueTasks {
    final now = clock.now();
    return filteredTasks
        .where((task) => !task.isCompleted && task.dueDate.isBefore(now))
        .toList();
  }

  /// Get today's tasks
  List<Task> get todaysTasks {
    final today = clock.now();
    final startOfDay = DateTime(today.year, today.month, today.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return filteredTasks
        .where((task) =>
            task.dueDate.isAfter(startOfDay) && task.dueDate.isBefore(endOfDay))
        .toList();
  }

  /// Check if specific task is loading
  bool isTaskLoading(String taskId) => taskLoadingStates[taskId] ?? false;

  /// Check if specific task is being deleted
  bool isTaskDeleting(String taskId) => deletingTaskIds.contains(taskId);

  /// Check if specific task is being updated
  bool isTaskUpdating(String taskId) => updatingTaskIds.contains(taskId);
}
