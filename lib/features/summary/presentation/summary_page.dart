import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/di/injection.dart';

import '../bloc/summary_bloc.dart';
import '../bloc/summary_event.dart';
import '../bloc/summary_state.dart';
import 'widgets/summary_statistics_card.dart';
import 'widgets/quadrant_distribution_chart.dart';
import 'widgets/highlights_section.dart';

/// 总结报告页面
///
/// 显示月度或年度的任务统计数据和分析报告
class SummaryPage extends StatelessWidget {
  final int year;
  final int? month; // null表示年度总结

  const SummaryPage({
    super.key,
    required this.year,
    this.month,
  });

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<SummaryBloc>()
        ..add(
          month != null
              ? SummaryEvent.monthlyRequested(year: year, month: month!)
              : SummaryEvent.yearlyRequested(year: year),
        ),
      child: Scaffold(
        appBar: AppBar(
          title: Text(month != null ? '月度总结' : '年度总结'),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => context.go('/'),
          ),
        ),
        body: BlocBuilder<SummaryBloc, SummaryState>(
          builder: (context, state) {
            return switch (state.status) {
              SummaryStatus.initial ||
              SummaryStatus.loading ||
              SummaryStatus.refreshing ||
              SummaryStatus.exporting ||
              SummaryStatus.calculating =>
                const Center(child: CircularProgressIndicator()),
              SummaryStatus.failure => Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 64,
                        color: Colors.red,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        '加载失败',
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        state.errorMessage ?? '未知错误',
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          context.read<SummaryBloc>().add(
                                month != null
                                    ? SummaryEvent.monthlyRequested(
                                        year: year, month: month!)
                                    : SummaryEvent.yearlyRequested(year: year),
                              );
                        },
                        child: const Text('重试'),
                      ),
                    ],
                  ),
                ),
              SummaryStatus.success => _buildSuccessContent(context, state),
            };
          },
        ),
      ),
    );
  }

  Widget _buildSuccessContent(BuildContext context, SummaryState state) {
    final report = state.report!;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Text(
            report.period,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 24),

          // 统计卡片
          SummaryStatisticsCard(report: report),
          const SizedBox(height: 24),

          // 象限分布图表
          QuadrantDistributionChart(
            distribution: report.quadrantDistribution,
          ),
          const SizedBox(height: 24),

          // 高光时刻
          if (report.highlights.isNotEmpty) ...[
            HighlightsSection(highlights: report.highlights),
            const SizedBox(height: 24),
          ],
        ],
      ),
    );
  }
}
