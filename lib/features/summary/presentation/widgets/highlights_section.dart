import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../domain/models/task_model.dart';

/// 高光时刻区域组件
///
/// 显示本周期内完成的重要任务列表
class HighlightsSection extends StatelessWidget {
  final List<Task> highlights;

  const HighlightsSection({
    super.key,
    required this.highlights,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  '高光时刻',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...highlights.asMap().entries.map((entry) {
              final index = entry.key;
              final task = entry.value;
              final isLast = index == highlights.length - 1;

              return Column(
                children: [
                  _buildHighlightItem(context, task, index + 1),
                  if (!isLast) const SizedBox(height: 12),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildHighlightItem(BuildContext context, Task task, int rank) {
    final theme = Theme.of(context);
    final dateFormat = DateFormat('MM月dd日');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withAlpha(77),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getPriorityColor(task.priority).withAlpha(77),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // 排名徽章
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: _getRankColor(rank),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                '$rank',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),

          // 任务信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  task.title,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: _getPriorityColor(task.priority).withAlpha(51),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        _getPriorityLabel(task.priority),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: _getPriorityColor(task.priority),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    if (task.completionDate != null) ...[
                      const Icon(
                        Icons.check_circle,
                        size: 14,
                        color: Colors.green,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '完成于 ${dateFormat.format(task.completionDate!)}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(179),
                        ),
                      ),
                    ],
                  ],
                ),
                if (task.subtasks.isNotEmpty) ...[
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.list,
                        size: 14,
                        color: theme.colorScheme.onSurface.withAlpha(179),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '包含 ${task.subtasks.length} 个子任务',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(179),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getRankColor(int rank) {
    return switch (rank) {
      1 => Colors.amber, // 金色
      2 => Colors.grey[400]!, // 银色
      3 => Colors.brown, // 铜色
      _ => Colors.blue, // 其他
    };
  }

  Color _getPriorityColor(Priority priority) {
    return switch (priority) {
      Priority.urgentImportant => const Color(0xFFFF3B30), // 红色
      Priority.importantNotUrgent => const Color(0xFFFF9500), // 橙色
      Priority.urgentNotImportant => const Color(0xFF007AFF), // 蓝色
      Priority.notUrgentNotImportant => const Color(0xFF8E8E93), // 灰色
    };
  }

  String _getPriorityLabel(Priority priority) {
    return switch (priority) {
      Priority.urgentImportant => '重要且紧急',
      Priority.importantNotUrgent => '重要但不紧急',
      Priority.urgentNotImportant => '不重要但紧急',
      Priority.notUrgentNotImportant => '不重要不紧急',
    };
  }
}
