import 'package:flutter/material.dart';
import '../../../../domain/models/task_model.dart';

/// 象限分布图表组件
///
/// 以条形图形式显示四个象限的任务数量分布
class QuadrantDistributionChart extends StatelessWidget {
  final Map<Priority, int> distribution;

  const QuadrantDistributionChart({
    super.key,
    required this.distribution,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final totalTasks = distribution.values.fold(0, (sum, count) => sum + count);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '象限分布',
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (totalTasks == 0)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(32),
                  child: Column(
                    children: [
                      Icon(
                        Icons.inbox_outlined,
                        size: 48,
                        color: theme.colorScheme.onSurface.withAlpha(128),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '暂无任务数据',
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: theme.colorScheme.onSurface.withAlpha(179),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              Column(
                children: Priority.values.map((priority) {
                  final count = distribution[priority] ?? 0;
                  final percentage =
                      totalTasks > 0 ? (count / totalTasks) * 100 : 0.0;

                  return Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: _buildQuadrantBar(
                      context,
                      priority,
                      count,
                      percentage,
                    ),
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuadrantBar(
    BuildContext context,
    Priority priority,
    int count,
    double percentage,
  ) {
    final theme = Theme.of(context);
    final color = _getPriorityColor(priority);
    final label = _getPriorityLabel(priority);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              '$count个 (${percentage.toStringAsFixed(1)}%)',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withAlpha(179),
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: theme.colorScheme.surfaceContainerHighest,
            borderRadius: BorderRadius.circular(4),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: percentage / 100,
            child: Container(
              decoration: BoxDecoration(
                color: color,
                borderRadius: BorderRadius.circular(4),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getPriorityColor(Priority priority) {
    return switch (priority) {
      Priority.urgentImportant => const Color(0xFFFF3B30), // 红色
      Priority.importantNotUrgent => const Color(0xFFFF9500), // 橙色
      Priority.urgentNotImportant => const Color(0xFF007AFF), // 蓝色
      Priority.notUrgentNotImportant => const Color(0xFF8E8E93), // 灰色
    };
  }

  String _getPriorityLabel(Priority priority) {
    return switch (priority) {
      Priority.urgentImportant => '重要且紧急',
      Priority.importantNotUrgent => '重要但不紧急',
      Priority.urgentNotImportant => '不重要但紧急',
      Priority.notUrgentNotImportant => '不重要不紧急',
    };
  }
}
