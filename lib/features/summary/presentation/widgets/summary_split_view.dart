import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/di/injection.dart';
import '../../bloc/summary_bloc.dart';
import '../../bloc/summary_event.dart';
import '../../bloc/summary_state.dart';
import '../widgets/summary_statistics_card.dart';
import '../widgets/quadrant_distribution_chart.dart';
import '../widgets/highlights_section.dart';

/// 本月/年总结并排视图（显示在主内容区，不开新页面）
class SummarySplitView extends StatelessWidget {
  const SummarySplitView({super.key});

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    return Row(
      children: [
        // 左：本月总结
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: BlocProvider(
              create: (_) => getIt<SummaryBloc>()
                ..add(SummaryEvent.monthlyRequested(year: now.year, month: now.month)),
              child: _SummaryPane(title: '本月总结'),
            ),
          ),
        ),
        // 右：本年总结
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: BlocProvider(
              create: (_) => getIt<SummaryBloc>()
                ..add(SummaryEvent.yearlyRequested(year: now.year)),
              child: _SummaryPane(title: '本年总结'),
            ),
          ),
        ),
      ],
    );
  }
}

class _SummaryPane extends StatelessWidget {
  final String title;
  const _SummaryPane({required this.title});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w700,
                  ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: BlocBuilder<SummaryBloc, SummaryState>(
                builder: (context, state) {
                  switch (state.status) {
                    case SummaryStatus.loading:
                    case SummaryStatus.initial:
                    case SummaryStatus.refreshing:
                    case SummaryStatus.exporting:
                    case SummaryStatus.calculating:
                      return const Center(child: CircularProgressIndicator());
                    case SummaryStatus.failure:
                      return Center(
                        child: Text(
                          state.errorMessage ?? '加载失败',
                          style: Theme.of(context).textTheme.bodyMedium,
                        ),
                      );
                    case SummaryStatus.success:
                      final report = state.report!;
                      return SingleChildScrollView(
                        padding: const EdgeInsets.only(right: 8),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SummaryStatisticsCard(report: report),
                            const SizedBox(height: 16),
                            QuadrantDistributionChart(
                              distribution: report.quadrantDistribution,
                            ),
                            if (report.highlights.isNotEmpty) ...[
                              const SizedBox(height: 16),
                              HighlightsSection(highlights: report.highlights),
                            ],
                          ],
                        ),
                      );
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

