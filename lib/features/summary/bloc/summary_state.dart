import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../domain/models/summary_report_model.dart';
import '../../../domain/models/summary_data.dart';
import '../../../domain/models/task_model.dart';

part 'summary_state.freezed.dart';

enum SummaryStatus {
  initial,
  loading,
  success,
  failure,
  refreshing,
  exporting,
  calculating,
}

enum SummaryOperation {
  none,
  loadingReport,
  calculatingTrends,
  analyzingQuadrants,
  calculatingPatterns,
  preparingCharts,
  exporting,
  comparing,
  refreshing,
}

@freezed
abstract class SummaryState with _$SummaryState {
  const factory SummaryState({
    required SummaryStatus status,
    required SummaryOperation currentOperation,

    // Core summary data
    SummaryReport? report,
    SummaryData? summaryData,

    // Analysis data
    ProductivityTrends? productivityTrends,
    QuadrantAnalysis? quadrantAnalysis,
    CompletionPatterns? completionPatterns,
    PerformanceMetrics? performanceMetrics,
    List<TaskHighlight>? highlights,

    // Chart data
    @Default({}) Map<SummaryChartType, ChartData> chartData,

    // Comparison data
    PeriodComparison? periodComparison,

    // Filtering
    Priority? priorityFilter,
    bool? completionFilter,
    @Default(false) bool hasActiveFilters,

    // Date range
    DateTime? startDate,
    DateTime? endDate,
    @Default(SummaryPeriod.monthly) SummaryPeriod currentPeriod,

    // Export data
    String? exportData,
    SummaryExportFormat? lastExportFormat,

    // Loading states
    @Default({}) Map<SummaryOperation, bool> operationStates,

    // Error handling
    String? errorMessage,
    Exception? lastException,
    @Default(false) bool canRetry,

    // Performance metrics
    DateTime? lastUpdated,
    @Default(0) int calculationDurationMs,
    @Default(0) int dataLoadDurationMs,
  }) = _SummaryState;

  factory SummaryState.initial() => SummaryState(
        status: SummaryStatus.initial,
        currentOperation: SummaryOperation.none,
        startDate: DateTime.now().subtract(const Duration(days: 30)),
        endDate: DateTime.now(),
      );
}
