// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'summary_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SummaryEvent {
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SummaryEventCopyWith<$Res> {
  factory $SummaryEventCopyWith(
          SummaryEvent value, $Res Function(SummaryEvent) then) =
      _$SummaryEventCopyWithImpl<$Res, SummaryEvent>;
}

/// @nodoc
class _$SummaryEventCopyWithImpl<$Res, $Val extends SummaryEvent>
    implements $SummaryEventCopyWith<$Res> {
  _$SummaryEventCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc
abstract class _$$MonthlyRequestedImplCopyWith<$Res> {
  factory _$$MonthlyRequestedImplCopyWith(_$MonthlyRequestedImpl value,
          $Res Function(_$MonthlyRequestedImpl) then) =
      __$$MonthlyRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int year, int month});
}

/// @nodoc
class __$$MonthlyRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$MonthlyRequestedImpl>
    implements _$$MonthlyRequestedImplCopyWith<$Res> {
  __$$MonthlyRequestedImplCopyWithImpl(_$MonthlyRequestedImpl _value,
      $Res Function(_$MonthlyRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = null,
    Object? month = null,
  }) {
    return _then(_$MonthlyRequestedImpl(
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
      month: null == month
          ? _value.month
          : month // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$MonthlyRequestedImpl implements _MonthlyRequested {
  const _$MonthlyRequestedImpl({required this.year, required this.month});

  @override
  final int year;
  @override
  final int month;

  @override
  String toString() {
    return 'SummaryEvent.monthlyRequested(year: $year, month: $month)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MonthlyRequestedImpl &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.month, month) || other.month == month));
  }

  @override
  int get hashCode => Object.hash(runtimeType, year, month);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MonthlyRequestedImplCopyWith<_$MonthlyRequestedImpl> get copyWith =>
      __$$MonthlyRequestedImplCopyWithImpl<_$MonthlyRequestedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return monthlyRequested(year, month);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return monthlyRequested?.call(year, month);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (monthlyRequested != null) {
      return monthlyRequested(year, month);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return monthlyRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return monthlyRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (monthlyRequested != null) {
      return monthlyRequested(this);
    }
    return orElse();
  }
}

abstract class _MonthlyRequested implements SummaryEvent {
  const factory _MonthlyRequested(
      {required final int year,
      required final int month}) = _$MonthlyRequestedImpl;

  int get year;
  int get month;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MonthlyRequestedImplCopyWith<_$MonthlyRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$YearlyRequestedImplCopyWith<$Res> {
  factory _$$YearlyRequestedImplCopyWith(_$YearlyRequestedImpl value,
          $Res Function(_$YearlyRequestedImpl) then) =
      __$$YearlyRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int year});
}

/// @nodoc
class __$$YearlyRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$YearlyRequestedImpl>
    implements _$$YearlyRequestedImplCopyWith<$Res> {
  __$$YearlyRequestedImplCopyWithImpl(
      _$YearlyRequestedImpl _value, $Res Function(_$YearlyRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? year = null,
  }) {
    return _then(_$YearlyRequestedImpl(
      year: null == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$YearlyRequestedImpl implements _YearlyRequested {
  const _$YearlyRequestedImpl({required this.year});

  @override
  final int year;

  @override
  String toString() {
    return 'SummaryEvent.yearlyRequested(year: $year)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$YearlyRequestedImpl &&
            (identical(other.year, year) || other.year == year));
  }

  @override
  int get hashCode => Object.hash(runtimeType, year);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$YearlyRequestedImplCopyWith<_$YearlyRequestedImpl> get copyWith =>
      __$$YearlyRequestedImplCopyWithImpl<_$YearlyRequestedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return yearlyRequested(year);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return yearlyRequested?.call(year);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (yearlyRequested != null) {
      return yearlyRequested(year);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return yearlyRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return yearlyRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (yearlyRequested != null) {
      return yearlyRequested(this);
    }
    return orElse();
  }
}

abstract class _YearlyRequested implements SummaryEvent {
  const factory _YearlyRequested({required final int year}) =
      _$YearlyRequestedImpl;

  int get year;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$YearlyRequestedImplCopyWith<_$YearlyRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DateRangeRequestedImplCopyWith<$Res> {
  factory _$$DateRangeRequestedImplCopyWith(_$DateRangeRequestedImpl value,
          $Res Function(_$DateRangeRequestedImpl) then) =
      __$$DateRangeRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime startDate, DateTime endDate});
}

/// @nodoc
class __$$DateRangeRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$DateRangeRequestedImpl>
    implements _$$DateRangeRequestedImplCopyWith<$Res> {
  __$$DateRangeRequestedImplCopyWithImpl(_$DateRangeRequestedImpl _value,
      $Res Function(_$DateRangeRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
  }) {
    return _then(_$DateRangeRequestedImpl(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$DateRangeRequestedImpl implements _DateRangeRequested {
  const _$DateRangeRequestedImpl(
      {required this.startDate, required this.endDate});

  @override
  final DateTime startDate;
  @override
  final DateTime endDate;

  @override
  String toString() {
    return 'SummaryEvent.dateRangeRequested(startDate: $startDate, endDate: $endDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DateRangeRequestedImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, startDate, endDate);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DateRangeRequestedImplCopyWith<_$DateRangeRequestedImpl> get copyWith =>
      __$$DateRangeRequestedImplCopyWithImpl<_$DateRangeRequestedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return dateRangeRequested(startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return dateRangeRequested?.call(startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (dateRangeRequested != null) {
      return dateRangeRequested(startDate, endDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return dateRangeRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return dateRangeRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (dateRangeRequested != null) {
      return dateRangeRequested(this);
    }
    return orElse();
  }
}

abstract class _DateRangeRequested implements SummaryEvent {
  const factory _DateRangeRequested(
      {required final DateTime startDate,
      required final DateTime endDate}) = _$DateRangeRequestedImpl;

  DateTime get startDate;
  DateTime get endDate;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DateRangeRequestedImplCopyWith<_$DateRangeRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ProductivityTrendsRequestedImplCopyWith<$Res> {
  factory _$$ProductivityTrendsRequestedImplCopyWith(
          _$ProductivityTrendsRequestedImpl value,
          $Res Function(_$ProductivityTrendsRequestedImpl) then) =
      __$$ProductivityTrendsRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime startDate, DateTime endDate, SummaryPeriod period});
}

/// @nodoc
class __$$ProductivityTrendsRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$ProductivityTrendsRequestedImpl>
    implements _$$ProductivityTrendsRequestedImplCopyWith<$Res> {
  __$$ProductivityTrendsRequestedImplCopyWithImpl(
      _$ProductivityTrendsRequestedImpl _value,
      $Res Function(_$ProductivityTrendsRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? period = null,
  }) {
    return _then(_$ProductivityTrendsRequestedImpl(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period: null == period
          ? _value.period
          : period // ignore: cast_nullable_to_non_nullable
              as SummaryPeriod,
    ));
  }
}

/// @nodoc

class _$ProductivityTrendsRequestedImpl
    implements _ProductivityTrendsRequested {
  const _$ProductivityTrendsRequestedImpl(
      {required this.startDate,
      required this.endDate,
      this.period = SummaryPeriod.monthly});

  @override
  final DateTime startDate;
  @override
  final DateTime endDate;
  @override
  @JsonKey()
  final SummaryPeriod period;

  @override
  String toString() {
    return 'SummaryEvent.productivityTrendsRequested(startDate: $startDate, endDate: $endDate, period: $period)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductivityTrendsRequestedImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.period, period) || other.period == period));
  }

  @override
  int get hashCode => Object.hash(runtimeType, startDate, endDate, period);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductivityTrendsRequestedImplCopyWith<_$ProductivityTrendsRequestedImpl>
      get copyWith => __$$ProductivityTrendsRequestedImplCopyWithImpl<
          _$ProductivityTrendsRequestedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return productivityTrendsRequested(startDate, endDate, period);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return productivityTrendsRequested?.call(startDate, endDate, period);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (productivityTrendsRequested != null) {
      return productivityTrendsRequested(startDate, endDate, period);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return productivityTrendsRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return productivityTrendsRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (productivityTrendsRequested != null) {
      return productivityTrendsRequested(this);
    }
    return orElse();
  }
}

abstract class _ProductivityTrendsRequested implements SummaryEvent {
  const factory _ProductivityTrendsRequested(
      {required final DateTime startDate,
      required final DateTime endDate,
      final SummaryPeriod period}) = _$ProductivityTrendsRequestedImpl;

  DateTime get startDate;
  DateTime get endDate;
  SummaryPeriod get period;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ProductivityTrendsRequestedImplCopyWith<_$ProductivityTrendsRequestedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$QuadrantAnalysisRequestedImplCopyWith<$Res> {
  factory _$$QuadrantAnalysisRequestedImplCopyWith(
          _$QuadrantAnalysisRequestedImpl value,
          $Res Function(_$QuadrantAnalysisRequestedImpl) then) =
      __$$QuadrantAnalysisRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime startDate, DateTime endDate});
}

/// @nodoc
class __$$QuadrantAnalysisRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$QuadrantAnalysisRequestedImpl>
    implements _$$QuadrantAnalysisRequestedImplCopyWith<$Res> {
  __$$QuadrantAnalysisRequestedImplCopyWithImpl(
      _$QuadrantAnalysisRequestedImpl _value,
      $Res Function(_$QuadrantAnalysisRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
  }) {
    return _then(_$QuadrantAnalysisRequestedImpl(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$QuadrantAnalysisRequestedImpl implements _QuadrantAnalysisRequested {
  const _$QuadrantAnalysisRequestedImpl(
      {required this.startDate, required this.endDate});

  @override
  final DateTime startDate;
  @override
  final DateTime endDate;

  @override
  String toString() {
    return 'SummaryEvent.quadrantAnalysisRequested(startDate: $startDate, endDate: $endDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QuadrantAnalysisRequestedImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, startDate, endDate);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QuadrantAnalysisRequestedImplCopyWith<_$QuadrantAnalysisRequestedImpl>
      get copyWith => __$$QuadrantAnalysisRequestedImplCopyWithImpl<
          _$QuadrantAnalysisRequestedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return quadrantAnalysisRequested(startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return quadrantAnalysisRequested?.call(startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (quadrantAnalysisRequested != null) {
      return quadrantAnalysisRequested(startDate, endDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return quadrantAnalysisRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return quadrantAnalysisRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (quadrantAnalysisRequested != null) {
      return quadrantAnalysisRequested(this);
    }
    return orElse();
  }
}

abstract class _QuadrantAnalysisRequested implements SummaryEvent {
  const factory _QuadrantAnalysisRequested(
      {required final DateTime startDate,
      required final DateTime endDate}) = _$QuadrantAnalysisRequestedImpl;

  DateTime get startDate;
  DateTime get endDate;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QuadrantAnalysisRequestedImplCopyWith<_$QuadrantAnalysisRequestedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CompletionPatternsRequestedImplCopyWith<$Res> {
  factory _$$CompletionPatternsRequestedImplCopyWith(
          _$CompletionPatternsRequestedImpl value,
          $Res Function(_$CompletionPatternsRequestedImpl) then) =
      __$$CompletionPatternsRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({int daysPeriod});
}

/// @nodoc
class __$$CompletionPatternsRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$CompletionPatternsRequestedImpl>
    implements _$$CompletionPatternsRequestedImplCopyWith<$Res> {
  __$$CompletionPatternsRequestedImplCopyWithImpl(
      _$CompletionPatternsRequestedImpl _value,
      $Res Function(_$CompletionPatternsRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? daysPeriod = null,
  }) {
    return _then(_$CompletionPatternsRequestedImpl(
      daysPeriod: null == daysPeriod
          ? _value.daysPeriod
          : daysPeriod // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$CompletionPatternsRequestedImpl
    implements _CompletionPatternsRequested {
  const _$CompletionPatternsRequestedImpl({this.daysPeriod = 90});

  @override
  @JsonKey()
  final int daysPeriod;

  @override
  String toString() {
    return 'SummaryEvent.completionPatternsRequested(daysPeriod: $daysPeriod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompletionPatternsRequestedImpl &&
            (identical(other.daysPeriod, daysPeriod) ||
                other.daysPeriod == daysPeriod));
  }

  @override
  int get hashCode => Object.hash(runtimeType, daysPeriod);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompletionPatternsRequestedImplCopyWith<_$CompletionPatternsRequestedImpl>
      get copyWith => __$$CompletionPatternsRequestedImplCopyWithImpl<
          _$CompletionPatternsRequestedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return completionPatternsRequested(daysPeriod);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return completionPatternsRequested?.call(daysPeriod);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (completionPatternsRequested != null) {
      return completionPatternsRequested(daysPeriod);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return completionPatternsRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return completionPatternsRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (completionPatternsRequested != null) {
      return completionPatternsRequested(this);
    }
    return orElse();
  }
}

abstract class _CompletionPatternsRequested implements SummaryEvent {
  const factory _CompletionPatternsRequested({final int daysPeriod}) =
      _$CompletionPatternsRequestedImpl;

  int get daysPeriod;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompletionPatternsRequestedImplCopyWith<_$CompletionPatternsRequestedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PerformanceMetricsRequestedImplCopyWith<$Res> {
  factory _$$PerformanceMetricsRequestedImplCopyWith(
          _$PerformanceMetricsRequestedImpl value,
          $Res Function(_$PerformanceMetricsRequestedImpl) then) =
      __$$PerformanceMetricsRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$PerformanceMetricsRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$PerformanceMetricsRequestedImpl>
    implements _$$PerformanceMetricsRequestedImplCopyWith<$Res> {
  __$$PerformanceMetricsRequestedImplCopyWithImpl(
      _$PerformanceMetricsRequestedImpl _value,
      $Res Function(_$PerformanceMetricsRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$PerformanceMetricsRequestedImpl
    implements _PerformanceMetricsRequested {
  const _$PerformanceMetricsRequestedImpl();

  @override
  String toString() {
    return 'SummaryEvent.performanceMetricsRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PerformanceMetricsRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return performanceMetricsRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return performanceMetricsRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (performanceMetricsRequested != null) {
      return performanceMetricsRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return performanceMetricsRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return performanceMetricsRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (performanceMetricsRequested != null) {
      return performanceMetricsRequested(this);
    }
    return orElse();
  }
}

abstract class _PerformanceMetricsRequested implements SummaryEvent {
  const factory _PerformanceMetricsRequested() =
      _$PerformanceMetricsRequestedImpl;
}

/// @nodoc
abstract class _$$HighlightsRequestedImplCopyWith<$Res> {
  factory _$$HighlightsRequestedImplCopyWith(_$HighlightsRequestedImpl value,
          $Res Function(_$HighlightsRequestedImpl) then) =
      __$$HighlightsRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({DateTime startDate, DateTime endDate, int maxHighlights});
}

/// @nodoc
class __$$HighlightsRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$HighlightsRequestedImpl>
    implements _$$HighlightsRequestedImplCopyWith<$Res> {
  __$$HighlightsRequestedImplCopyWithImpl(_$HighlightsRequestedImpl _value,
      $Res Function(_$HighlightsRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? startDate = null,
    Object? endDate = null,
    Object? maxHighlights = null,
  }) {
    return _then(_$HighlightsRequestedImpl(
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      maxHighlights: null == maxHighlights
          ? _value.maxHighlights
          : maxHighlights // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$HighlightsRequestedImpl implements _HighlightsRequested {
  const _$HighlightsRequestedImpl(
      {required this.startDate, required this.endDate, this.maxHighlights = 5});

  @override
  final DateTime startDate;
  @override
  final DateTime endDate;
  @override
  @JsonKey()
  final int maxHighlights;

  @override
  String toString() {
    return 'SummaryEvent.highlightsRequested(startDate: $startDate, endDate: $endDate, maxHighlights: $maxHighlights)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HighlightsRequestedImpl &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.maxHighlights, maxHighlights) ||
                other.maxHighlights == maxHighlights));
  }

  @override
  int get hashCode =>
      Object.hash(runtimeType, startDate, endDate, maxHighlights);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$HighlightsRequestedImplCopyWith<_$HighlightsRequestedImpl> get copyWith =>
      __$$HighlightsRequestedImplCopyWithImpl<_$HighlightsRequestedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return highlightsRequested(startDate, endDate, maxHighlights);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return highlightsRequested?.call(startDate, endDate, maxHighlights);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (highlightsRequested != null) {
      return highlightsRequested(startDate, endDate, maxHighlights);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return highlightsRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return highlightsRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (highlightsRequested != null) {
      return highlightsRequested(this);
    }
    return orElse();
  }
}

abstract class _HighlightsRequested implements SummaryEvent {
  const factory _HighlightsRequested(
      {required final DateTime startDate,
      required final DateTime endDate,
      final int maxHighlights}) = _$HighlightsRequestedImpl;

  DateTime get startDate;
  DateTime get endDate;
  int get maxHighlights;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$HighlightsRequestedImplCopyWith<_$HighlightsRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ChartDataRequestedImplCopyWith<$Res> {
  factory _$$ChartDataRequestedImplCopyWith(_$ChartDataRequestedImpl value,
          $Res Function(_$ChartDataRequestedImpl) then) =
      __$$ChartDataRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SummaryChartType chartType, DateTime startDate, DateTime endDate});
}

/// @nodoc
class __$$ChartDataRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$ChartDataRequestedImpl>
    implements _$$ChartDataRequestedImplCopyWith<$Res> {
  __$$ChartDataRequestedImplCopyWithImpl(_$ChartDataRequestedImpl _value,
      $Res Function(_$ChartDataRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chartType = null,
    Object? startDate = null,
    Object? endDate = null,
  }) {
    return _then(_$ChartDataRequestedImpl(
      chartType: null == chartType
          ? _value.chartType
          : chartType // ignore: cast_nullable_to_non_nullable
              as SummaryChartType,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$ChartDataRequestedImpl implements _ChartDataRequested {
  const _$ChartDataRequestedImpl(
      {required this.chartType,
      required this.startDate,
      required this.endDate});

  @override
  final SummaryChartType chartType;
  @override
  final DateTime startDate;
  @override
  final DateTime endDate;

  @override
  String toString() {
    return 'SummaryEvent.chartDataRequested(chartType: $chartType, startDate: $startDate, endDate: $endDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ChartDataRequestedImpl &&
            (identical(other.chartType, chartType) ||
                other.chartType == chartType) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, chartType, startDate, endDate);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ChartDataRequestedImplCopyWith<_$ChartDataRequestedImpl> get copyWith =>
      __$$ChartDataRequestedImplCopyWithImpl<_$ChartDataRequestedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return chartDataRequested(chartType, startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return chartDataRequested?.call(chartType, startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (chartDataRequested != null) {
      return chartDataRequested(chartType, startDate, endDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return chartDataRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return chartDataRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (chartDataRequested != null) {
      return chartDataRequested(this);
    }
    return orElse();
  }
}

abstract class _ChartDataRequested implements SummaryEvent {
  const factory _ChartDataRequested(
      {required final SummaryChartType chartType,
      required final DateTime startDate,
      required final DateTime endDate}) = _$ChartDataRequestedImpl;

  SummaryChartType get chartType;
  DateTime get startDate;
  DateTime get endDate;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ChartDataRequestedImplCopyWith<_$ChartDataRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ExportRequestedImplCopyWith<$Res> {
  factory _$$ExportRequestedImplCopyWith(_$ExportRequestedImpl value,
          $Res Function(_$ExportRequestedImpl) then) =
      __$$ExportRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SummaryExportFormat format, DateTime startDate, DateTime endDate});
}

/// @nodoc
class __$$ExportRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$ExportRequestedImpl>
    implements _$$ExportRequestedImplCopyWith<$Res> {
  __$$ExportRequestedImplCopyWithImpl(
      _$ExportRequestedImpl _value, $Res Function(_$ExportRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? format = null,
    Object? startDate = null,
    Object? endDate = null,
  }) {
    return _then(_$ExportRequestedImpl(
      format: null == format
          ? _value.format
          : format // ignore: cast_nullable_to_non_nullable
              as SummaryExportFormat,
      startDate: null == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
      endDate: null == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$ExportRequestedImpl implements _ExportRequested {
  const _$ExportRequestedImpl(
      {required this.format, required this.startDate, required this.endDate});

  @override
  final SummaryExportFormat format;
  @override
  final DateTime startDate;
  @override
  final DateTime endDate;

  @override
  String toString() {
    return 'SummaryEvent.exportRequested(format: $format, startDate: $startDate, endDate: $endDate)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExportRequestedImpl &&
            (identical(other.format, format) || other.format == format) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate));
  }

  @override
  int get hashCode => Object.hash(runtimeType, format, startDate, endDate);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ExportRequestedImplCopyWith<_$ExportRequestedImpl> get copyWith =>
      __$$ExportRequestedImplCopyWithImpl<_$ExportRequestedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return exportRequested(format, startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return exportRequested?.call(format, startDate, endDate);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (exportRequested != null) {
      return exportRequested(format, startDate, endDate);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return exportRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return exportRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (exportRequested != null) {
      return exportRequested(this);
    }
    return orElse();
  }
}

abstract class _ExportRequested implements SummaryEvent {
  const factory _ExportRequested(
      {required final SummaryExportFormat format,
      required final DateTime startDate,
      required final DateTime endDate}) = _$ExportRequestedImpl;

  SummaryExportFormat get format;
  DateTime get startDate;
  DateTime get endDate;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ExportRequestedImplCopyWith<_$ExportRequestedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PeriodComparisonRequestedImplCopyWith<$Res> {
  factory _$$PeriodComparisonRequestedImplCopyWith(
          _$PeriodComparisonRequestedImpl value,
          $Res Function(_$PeriodComparisonRequestedImpl) then) =
      __$$PeriodComparisonRequestedImplCopyWithImpl<$Res>;
  @useResult
  $Res call(
      {DateTime period1Start,
      DateTime period1End,
      DateTime period2Start,
      DateTime period2End});
}

/// @nodoc
class __$$PeriodComparisonRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$PeriodComparisonRequestedImpl>
    implements _$$PeriodComparisonRequestedImplCopyWith<$Res> {
  __$$PeriodComparisonRequestedImplCopyWithImpl(
      _$PeriodComparisonRequestedImpl _value,
      $Res Function(_$PeriodComparisonRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? period1Start = null,
    Object? period1End = null,
    Object? period2Start = null,
    Object? period2End = null,
  }) {
    return _then(_$PeriodComparisonRequestedImpl(
      period1Start: null == period1Start
          ? _value.period1Start
          : period1Start // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period1End: null == period1End
          ? _value.period1End
          : period1End // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period2Start: null == period2Start
          ? _value.period2Start
          : period2Start // ignore: cast_nullable_to_non_nullable
              as DateTime,
      period2End: null == period2End
          ? _value.period2End
          : period2End // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$PeriodComparisonRequestedImpl implements _PeriodComparisonRequested {
  const _$PeriodComparisonRequestedImpl(
      {required this.period1Start,
      required this.period1End,
      required this.period2Start,
      required this.period2End});

  @override
  final DateTime period1Start;
  @override
  final DateTime period1End;
  @override
  final DateTime period2Start;
  @override
  final DateTime period2End;

  @override
  String toString() {
    return 'SummaryEvent.periodComparisonRequested(period1Start: $period1Start, period1End: $period1End, period2Start: $period2Start, period2End: $period2End)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PeriodComparisonRequestedImpl &&
            (identical(other.period1Start, period1Start) ||
                other.period1Start == period1Start) &&
            (identical(other.period1End, period1End) ||
                other.period1End == period1End) &&
            (identical(other.period2Start, period2Start) ||
                other.period2Start == period2Start) &&
            (identical(other.period2End, period2End) ||
                other.period2End == period2End));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType, period1Start, period1End, period2Start, period2End);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PeriodComparisonRequestedImplCopyWith<_$PeriodComparisonRequestedImpl>
      get copyWith => __$$PeriodComparisonRequestedImplCopyWithImpl<
          _$PeriodComparisonRequestedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return periodComparisonRequested(
        period1Start, period1End, period2Start, period2End);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return periodComparisonRequested?.call(
        period1Start, period1End, period2Start, period2End);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (periodComparisonRequested != null) {
      return periodComparisonRequested(
          period1Start, period1End, period2Start, period2End);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return periodComparisonRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return periodComparisonRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (periodComparisonRequested != null) {
      return periodComparisonRequested(this);
    }
    return orElse();
  }
}

abstract class _PeriodComparisonRequested implements SummaryEvent {
  const factory _PeriodComparisonRequested(
      {required final DateTime period1Start,
      required final DateTime period1End,
      required final DateTime period2Start,
      required final DateTime period2End}) = _$PeriodComparisonRequestedImpl;

  DateTime get period1Start;
  DateTime get period1End;
  DateTime get period2Start;
  DateTime get period2End;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PeriodComparisonRequestedImplCopyWith<_$PeriodComparisonRequestedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$PriorityFilterChangedImplCopyWith<$Res> {
  factory _$$PriorityFilterChangedImplCopyWith(
          _$PriorityFilterChangedImpl value,
          $Res Function(_$PriorityFilterChangedImpl) then) =
      __$$PriorityFilterChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({Priority? priority});
}

/// @nodoc
class __$$PriorityFilterChangedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$PriorityFilterChangedImpl>
    implements _$$PriorityFilterChangedImplCopyWith<$Res> {
  __$$PriorityFilterChangedImplCopyWithImpl(_$PriorityFilterChangedImpl _value,
      $Res Function(_$PriorityFilterChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? priority = freezed,
  }) {
    return _then(_$PriorityFilterChangedImpl(
      freezed == priority
          ? _value.priority
          : priority // ignore: cast_nullable_to_non_nullable
              as Priority?,
    ));
  }
}

/// @nodoc

class _$PriorityFilterChangedImpl implements _PriorityFilterChanged {
  const _$PriorityFilterChangedImpl(this.priority);

  @override
  final Priority? priority;

  @override
  String toString() {
    return 'SummaryEvent.priorityFilterChanged(priority: $priority)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PriorityFilterChangedImpl &&
            (identical(other.priority, priority) ||
                other.priority == priority));
  }

  @override
  int get hashCode => Object.hash(runtimeType, priority);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$PriorityFilterChangedImplCopyWith<_$PriorityFilterChangedImpl>
      get copyWith => __$$PriorityFilterChangedImplCopyWithImpl<
          _$PriorityFilterChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return priorityFilterChanged(priority);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return priorityFilterChanged?.call(priority);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (priorityFilterChanged != null) {
      return priorityFilterChanged(priority);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return priorityFilterChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return priorityFilterChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (priorityFilterChanged != null) {
      return priorityFilterChanged(this);
    }
    return orElse();
  }
}

abstract class _PriorityFilterChanged implements SummaryEvent {
  const factory _PriorityFilterChanged(final Priority? priority) =
      _$PriorityFilterChangedImpl;

  Priority? get priority;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$PriorityFilterChangedImplCopyWith<_$PriorityFilterChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$CompletionFilterChangedImplCopyWith<$Res> {
  factory _$$CompletionFilterChangedImplCopyWith(
          _$CompletionFilterChangedImpl value,
          $Res Function(_$CompletionFilterChangedImpl) then) =
      __$$CompletionFilterChangedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({bool? isCompleted});
}

/// @nodoc
class __$$CompletionFilterChangedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$CompletionFilterChangedImpl>
    implements _$$CompletionFilterChangedImplCopyWith<$Res> {
  __$$CompletionFilterChangedImplCopyWithImpl(
      _$CompletionFilterChangedImpl _value,
      $Res Function(_$CompletionFilterChangedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isCompleted = freezed,
  }) {
    return _then(_$CompletionFilterChangedImpl(
      freezed == isCompleted
          ? _value.isCompleted
          : isCompleted // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc

class _$CompletionFilterChangedImpl implements _CompletionFilterChanged {
  const _$CompletionFilterChangedImpl(this.isCompleted);

  @override
  final bool? isCompleted;

  @override
  String toString() {
    return 'SummaryEvent.completionFilterChanged(isCompleted: $isCompleted)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CompletionFilterChangedImpl &&
            (identical(other.isCompleted, isCompleted) ||
                other.isCompleted == isCompleted));
  }

  @override
  int get hashCode => Object.hash(runtimeType, isCompleted);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CompletionFilterChangedImplCopyWith<_$CompletionFilterChangedImpl>
      get copyWith => __$$CompletionFilterChangedImplCopyWithImpl<
          _$CompletionFilterChangedImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return completionFilterChanged(isCompleted);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return completionFilterChanged?.call(isCompleted);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (completionFilterChanged != null) {
      return completionFilterChanged(isCompleted);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return completionFilterChanged(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return completionFilterChanged?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (completionFilterChanged != null) {
      return completionFilterChanged(this);
    }
    return orElse();
  }
}

abstract class _CompletionFilterChanged implements SummaryEvent {
  const factory _CompletionFilterChanged(final bool? isCompleted) =
      _$CompletionFilterChangedImpl;

  bool? get isCompleted;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CompletionFilterChangedImplCopyWith<_$CompletionFilterChangedImpl>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$FiltersClearedImplCopyWith<$Res> {
  factory _$$FiltersClearedImplCopyWith(_$FiltersClearedImpl value,
          $Res Function(_$FiltersClearedImpl) then) =
      __$$FiltersClearedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$FiltersClearedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$FiltersClearedImpl>
    implements _$$FiltersClearedImplCopyWith<$Res> {
  __$$FiltersClearedImplCopyWithImpl(
      _$FiltersClearedImpl _value, $Res Function(_$FiltersClearedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$FiltersClearedImpl implements _FiltersCleared {
  const _$FiltersClearedImpl();

  @override
  String toString() {
    return 'SummaryEvent.filtersCleared()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$FiltersClearedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return filtersCleared();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return filtersCleared?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (filtersCleared != null) {
      return filtersCleared();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return filtersCleared(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return filtersCleared?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (filtersCleared != null) {
      return filtersCleared(this);
    }
    return orElse();
  }
}

abstract class _FiltersCleared implements SummaryEvent {
  const factory _FiltersCleared() = _$FiltersClearedImpl;
}

/// @nodoc
abstract class _$$RefreshRequestedImplCopyWith<$Res> {
  factory _$$RefreshRequestedImplCopyWith(_$RefreshRequestedImpl value,
          $Res Function(_$RefreshRequestedImpl) then) =
      __$$RefreshRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RefreshRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$RefreshRequestedImpl>
    implements _$$RefreshRequestedImplCopyWith<$Res> {
  __$$RefreshRequestedImplCopyWithImpl(_$RefreshRequestedImpl _value,
      $Res Function(_$RefreshRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RefreshRequestedImpl implements _RefreshRequested {
  const _$RefreshRequestedImpl();

  @override
  String toString() {
    return 'SummaryEvent.refreshRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RefreshRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return refreshRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return refreshRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (refreshRequested != null) {
      return refreshRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return refreshRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return refreshRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (refreshRequested != null) {
      return refreshRequested(this);
    }
    return orElse();
  }
}

abstract class _RefreshRequested implements SummaryEvent {
  const factory _RefreshRequested() = _$RefreshRequestedImpl;
}

/// @nodoc
abstract class _$$RetryRequestedImplCopyWith<$Res> {
  factory _$$RetryRequestedImplCopyWith(_$RetryRequestedImpl value,
          $Res Function(_$RetryRequestedImpl) then) =
      __$$RetryRequestedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$RetryRequestedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$RetryRequestedImpl>
    implements _$$RetryRequestedImplCopyWith<$Res> {
  __$$RetryRequestedImplCopyWithImpl(
      _$RetryRequestedImpl _value, $Res Function(_$RetryRequestedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$RetryRequestedImpl implements _RetryRequested {
  const _$RetryRequestedImpl();

  @override
  String toString() {
    return 'SummaryEvent.retryRequested()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$RetryRequestedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return retryRequested();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return retryRequested?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (retryRequested != null) {
      return retryRequested();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return retryRequested(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return retryRequested?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (retryRequested != null) {
      return retryRequested(this);
    }
    return orElse();
  }
}

abstract class _RetryRequested implements SummaryEvent {
  const factory _RetryRequested() = _$RetryRequestedImpl;
}

/// @nodoc
abstract class _$$ErrorClearedImplCopyWith<$Res> {
  factory _$$ErrorClearedImplCopyWith(
          _$ErrorClearedImpl value, $Res Function(_$ErrorClearedImpl) then) =
      __$$ErrorClearedImplCopyWithImpl<$Res>;
}

/// @nodoc
class __$$ErrorClearedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$ErrorClearedImpl>
    implements _$$ErrorClearedImplCopyWith<$Res> {
  __$$ErrorClearedImplCopyWithImpl(
      _$ErrorClearedImpl _value, $Res Function(_$ErrorClearedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
}

/// @nodoc

class _$ErrorClearedImpl implements _ErrorCleared {
  const _$ErrorClearedImpl();

  @override
  String toString() {
    return 'SummaryEvent.errorCleared()';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType && other is _$ErrorClearedImpl);
  }

  @override
  int get hashCode => runtimeType.hashCode;

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return errorCleared();
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return errorCleared?.call();
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorCleared != null) {
      return errorCleared();
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return errorCleared(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return errorCleared?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorCleared != null) {
      return errorCleared(this);
    }
    return orElse();
  }
}

abstract class _ErrorCleared implements SummaryEvent {
  const factory _ErrorCleared() = _$ErrorClearedImpl;
}

/// @nodoc
abstract class _$$SummaryDataUpdatedImplCopyWith<$Res> {
  factory _$$SummaryDataUpdatedImplCopyWith(_$SummaryDataUpdatedImpl value,
          $Res Function(_$SummaryDataUpdatedImpl) then) =
      __$$SummaryDataUpdatedImplCopyWithImpl<$Res>;
  @useResult
  $Res call({SummaryData data});

  $SummaryDataCopyWith<$Res> get data;
}

/// @nodoc
class __$$SummaryDataUpdatedImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$SummaryDataUpdatedImpl>
    implements _$$SummaryDataUpdatedImplCopyWith<$Res> {
  __$$SummaryDataUpdatedImplCopyWithImpl(_$SummaryDataUpdatedImpl _value,
      $Res Function(_$SummaryDataUpdatedImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? data = null,
  }) {
    return _then(_$SummaryDataUpdatedImpl(
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as SummaryData,
    ));
  }

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SummaryDataCopyWith<$Res> get data {
    return $SummaryDataCopyWith<$Res>(_value.data, (value) {
      return _then(_value.copyWith(data: value));
    });
  }
}

/// @nodoc

class _$SummaryDataUpdatedImpl implements _SummaryDataUpdated {
  const _$SummaryDataUpdatedImpl({required this.data});

  @override
  final SummaryData data;

  @override
  String toString() {
    return 'SummaryEvent.summaryDataUpdated(data: $data)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SummaryDataUpdatedImpl &&
            (identical(other.data, data) || other.data == data));
  }

  @override
  int get hashCode => Object.hash(runtimeType, data);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SummaryDataUpdatedImplCopyWith<_$SummaryDataUpdatedImpl> get copyWith =>
      __$$SummaryDataUpdatedImplCopyWithImpl<_$SummaryDataUpdatedImpl>(
          this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return summaryDataUpdated(data);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return summaryDataUpdated?.call(data);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (summaryDataUpdated != null) {
      return summaryDataUpdated(data);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return summaryDataUpdated(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return summaryDataUpdated?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (summaryDataUpdated != null) {
      return summaryDataUpdated(this);
    }
    return orElse();
  }
}

abstract class _SummaryDataUpdated implements SummaryEvent {
  const factory _SummaryDataUpdated({required final SummaryData data}) =
      _$SummaryDataUpdatedImpl;

  SummaryData get data;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SummaryDataUpdatedImplCopyWith<_$SummaryDataUpdatedImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$ErrorOccurredImplCopyWith<$Res> {
  factory _$$ErrorOccurredImplCopyWith(
          _$ErrorOccurredImpl value, $Res Function(_$ErrorOccurredImpl) then) =
      __$$ErrorOccurredImplCopyWithImpl<$Res>;
  @useResult
  $Res call({String message, Exception? exception});
}

/// @nodoc
class __$$ErrorOccurredImplCopyWithImpl<$Res>
    extends _$SummaryEventCopyWithImpl<$Res, _$ErrorOccurredImpl>
    implements _$$ErrorOccurredImplCopyWith<$Res> {
  __$$ErrorOccurredImplCopyWithImpl(
      _$ErrorOccurredImpl _value, $Res Function(_$ErrorOccurredImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? message = null,
    Object? exception = freezed,
  }) {
    return _then(_$ErrorOccurredImpl(
      message: null == message
          ? _value.message
          : message // ignore: cast_nullable_to_non_nullable
              as String,
      exception: freezed == exception
          ? _value.exception
          : exception // ignore: cast_nullable_to_non_nullable
              as Exception?,
    ));
  }
}

/// @nodoc

class _$ErrorOccurredImpl implements _ErrorOccurred {
  const _$ErrorOccurredImpl({required this.message, this.exception});

  @override
  final String message;
  @override
  final Exception? exception;

  @override
  String toString() {
    return 'SummaryEvent.errorOccurred(message: $message, exception: $exception)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ErrorOccurredImpl &&
            (identical(other.message, message) || other.message == message) &&
            (identical(other.exception, exception) ||
                other.exception == exception));
  }

  @override
  int get hashCode => Object.hash(runtimeType, message, exception);

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$ErrorOccurredImplCopyWith<_$ErrorOccurredImpl> get copyWith =>
      __$$ErrorOccurredImplCopyWithImpl<_$ErrorOccurredImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(int year, int month) monthlyRequested,
    required TResult Function(int year) yearlyRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        dateRangeRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)
        productivityTrendsRequested,
    required TResult Function(DateTime startDate, DateTime endDate)
        quadrantAnalysisRequested,
    required TResult Function(int daysPeriod) completionPatternsRequested,
    required TResult Function() performanceMetricsRequested,
    required TResult Function(
            DateTime startDate, DateTime endDate, int maxHighlights)
        highlightsRequested,
    required TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)
        chartDataRequested,
    required TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)
        exportRequested,
    required TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)
        periodComparisonRequested,
    required TResult Function(Priority? priority) priorityFilterChanged,
    required TResult Function(bool? isCompleted) completionFilterChanged,
    required TResult Function() filtersCleared,
    required TResult Function() refreshRequested,
    required TResult Function() retryRequested,
    required TResult Function() errorCleared,
    required TResult Function(SummaryData data) summaryDataUpdated,
    required TResult Function(String message, Exception? exception)
        errorOccurred,
  }) {
    return errorOccurred(message, exception);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(int year, int month)? monthlyRequested,
    TResult? Function(int year)? yearlyRequested,
    TResult? Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult? Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult? Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult? Function(int daysPeriod)? completionPatternsRequested,
    TResult? Function()? performanceMetricsRequested,
    TResult? Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult? Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult? Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult? Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult? Function(Priority? priority)? priorityFilterChanged,
    TResult? Function(bool? isCompleted)? completionFilterChanged,
    TResult? Function()? filtersCleared,
    TResult? Function()? refreshRequested,
    TResult? Function()? retryRequested,
    TResult? Function()? errorCleared,
    TResult? Function(SummaryData data)? summaryDataUpdated,
    TResult? Function(String message, Exception? exception)? errorOccurred,
  }) {
    return errorOccurred?.call(message, exception);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(int year, int month)? monthlyRequested,
    TResult Function(int year)? yearlyRequested,
    TResult Function(DateTime startDate, DateTime endDate)? dateRangeRequested,
    TResult Function(
            DateTime startDate, DateTime endDate, SummaryPeriod period)?
        productivityTrendsRequested,
    TResult Function(DateTime startDate, DateTime endDate)?
        quadrantAnalysisRequested,
    TResult Function(int daysPeriod)? completionPatternsRequested,
    TResult Function()? performanceMetricsRequested,
    TResult Function(DateTime startDate, DateTime endDate, int maxHighlights)?
        highlightsRequested,
    TResult Function(
            SummaryChartType chartType, DateTime startDate, DateTime endDate)?
        chartDataRequested,
    TResult Function(
            SummaryExportFormat format, DateTime startDate, DateTime endDate)?
        exportRequested,
    TResult Function(DateTime period1Start, DateTime period1End,
            DateTime period2Start, DateTime period2End)?
        periodComparisonRequested,
    TResult Function(Priority? priority)? priorityFilterChanged,
    TResult Function(bool? isCompleted)? completionFilterChanged,
    TResult Function()? filtersCleared,
    TResult Function()? refreshRequested,
    TResult Function()? retryRequested,
    TResult Function()? errorCleared,
    TResult Function(SummaryData data)? summaryDataUpdated,
    TResult Function(String message, Exception? exception)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorOccurred != null) {
      return errorOccurred(message, exception);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_MonthlyRequested value) monthlyRequested,
    required TResult Function(_YearlyRequested value) yearlyRequested,
    required TResult Function(_DateRangeRequested value) dateRangeRequested,
    required TResult Function(_ProductivityTrendsRequested value)
        productivityTrendsRequested,
    required TResult Function(_QuadrantAnalysisRequested value)
        quadrantAnalysisRequested,
    required TResult Function(_CompletionPatternsRequested value)
        completionPatternsRequested,
    required TResult Function(_PerformanceMetricsRequested value)
        performanceMetricsRequested,
    required TResult Function(_HighlightsRequested value) highlightsRequested,
    required TResult Function(_ChartDataRequested value) chartDataRequested,
    required TResult Function(_ExportRequested value) exportRequested,
    required TResult Function(_PeriodComparisonRequested value)
        periodComparisonRequested,
    required TResult Function(_PriorityFilterChanged value)
        priorityFilterChanged,
    required TResult Function(_CompletionFilterChanged value)
        completionFilterChanged,
    required TResult Function(_FiltersCleared value) filtersCleared,
    required TResult Function(_RefreshRequested value) refreshRequested,
    required TResult Function(_RetryRequested value) retryRequested,
    required TResult Function(_ErrorCleared value) errorCleared,
    required TResult Function(_SummaryDataUpdated value) summaryDataUpdated,
    required TResult Function(_ErrorOccurred value) errorOccurred,
  }) {
    return errorOccurred(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_MonthlyRequested value)? monthlyRequested,
    TResult? Function(_YearlyRequested value)? yearlyRequested,
    TResult? Function(_DateRangeRequested value)? dateRangeRequested,
    TResult? Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult? Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult? Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult? Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult? Function(_HighlightsRequested value)? highlightsRequested,
    TResult? Function(_ChartDataRequested value)? chartDataRequested,
    TResult? Function(_ExportRequested value)? exportRequested,
    TResult? Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult? Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult? Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult? Function(_FiltersCleared value)? filtersCleared,
    TResult? Function(_RefreshRequested value)? refreshRequested,
    TResult? Function(_RetryRequested value)? retryRequested,
    TResult? Function(_ErrorCleared value)? errorCleared,
    TResult? Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult? Function(_ErrorOccurred value)? errorOccurred,
  }) {
    return errorOccurred?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_MonthlyRequested value)? monthlyRequested,
    TResult Function(_YearlyRequested value)? yearlyRequested,
    TResult Function(_DateRangeRequested value)? dateRangeRequested,
    TResult Function(_ProductivityTrendsRequested value)?
        productivityTrendsRequested,
    TResult Function(_QuadrantAnalysisRequested value)?
        quadrantAnalysisRequested,
    TResult Function(_CompletionPatternsRequested value)?
        completionPatternsRequested,
    TResult Function(_PerformanceMetricsRequested value)?
        performanceMetricsRequested,
    TResult Function(_HighlightsRequested value)? highlightsRequested,
    TResult Function(_ChartDataRequested value)? chartDataRequested,
    TResult Function(_ExportRequested value)? exportRequested,
    TResult Function(_PeriodComparisonRequested value)?
        periodComparisonRequested,
    TResult Function(_PriorityFilterChanged value)? priorityFilterChanged,
    TResult Function(_CompletionFilterChanged value)? completionFilterChanged,
    TResult Function(_FiltersCleared value)? filtersCleared,
    TResult Function(_RefreshRequested value)? refreshRequested,
    TResult Function(_RetryRequested value)? retryRequested,
    TResult Function(_ErrorCleared value)? errorCleared,
    TResult Function(_SummaryDataUpdated value)? summaryDataUpdated,
    TResult Function(_ErrorOccurred value)? errorOccurred,
    required TResult orElse(),
  }) {
    if (errorOccurred != null) {
      return errorOccurred(this);
    }
    return orElse();
  }
}

abstract class _ErrorOccurred implements SummaryEvent {
  const factory _ErrorOccurred(
      {required final String message,
      final Exception? exception}) = _$ErrorOccurredImpl;

  String get message;
  Exception? get exception;

  /// Create a copy of SummaryEvent
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$ErrorOccurredImplCopyWith<_$ErrorOccurredImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
