import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../domain/models/task_model.dart';
import '../../../domain/models/summary_data.dart';

part 'summary_event.freezed.dart';

/// Events for Summary feature
@freezed
abstract class SummaryEvent with _$SummaryEvent {
  /// Request monthly summary report
  const factory SummaryEvent.monthlyRequested({
    required int year,
    required int month,
  }) = _MonthlyRequested;

  /// Request yearly summary report
  const factory SummaryEvent.yearlyRequested({
    required int year,
  }) = _YearlyRequested;

  /// Request custom date range summary
  const factory SummaryEvent.dateRangeRequested({
    required DateTime startDate,
    required DateTime endDate,
  }) = _DateRangeRequested;

  /// Request productivity trends analysis
  const factory SummaryEvent.productivityTrendsRequested({
    required DateTime startDate,
    required DateTime endDate,
    @Default(SummaryPeriod.monthly) SummaryPeriod period,
  }) = _ProductivityTrendsRequested;

  /// Request quadrant distribution analysis
  const factory SummaryEvent.quadrantAnalysisRequested({
    required DateTime startDate,
    required DateTime endDate,
  }) = _QuadrantAnalysisRequested;

  /// Request completion patterns analysis
  const factory SummaryEvent.completionPatternsRequested({
    @Default(90) int daysPeriod,
  }) = _CompletionPatternsRequested;

  /// Request performance metrics
  const factory SummaryEvent.performanceMetricsRequested() = _PerformanceMetricsRequested;

  /// Request highlights calculation
  const factory SummaryEvent.highlightsRequested({
    required DateTime startDate,
    required DateTime endDate,
    @Default(5) int maxHighlights,
  }) = _HighlightsRequested;

  /// Request chart data preparation
  const factory SummaryEvent.chartDataRequested({
    required SummaryChartType chartType,
    required DateTime startDate,
    required DateTime endDate,
  }) = _ChartDataRequested;

  /// Export summary data
  const factory SummaryEvent.exportRequested({
    required SummaryExportFormat format,
    required DateTime startDate,
    required DateTime endDate,
  }) = _ExportRequested;

  /// Compare periods
  const factory SummaryEvent.periodComparisonRequested({
    required DateTime period1Start,
    required DateTime period1End,
    required DateTime period2Start,
    required DateTime period2End,
  }) = _PeriodComparisonRequested;

  /// Filter summary by priority
  const factory SummaryEvent.priorityFilterChanged(Priority? priority) = _PriorityFilterChanged;

  /// Filter summary by completion status
  const factory SummaryEvent.completionFilterChanged(bool? isCompleted) = _CompletionFilterChanged;

  /// Clear all filters
  const factory SummaryEvent.filtersCleared() = _FiltersCleared;

  /// Refresh current summary
  const factory SummaryEvent.refreshRequested() = _RefreshRequested;

  /// Retry failed operation
  const factory SummaryEvent.retryRequested() = _RetryRequested;

  /// Clear error state
  const factory SummaryEvent.errorCleared() = _ErrorCleared;

  /// (Internal) Summary data updated
  const factory SummaryEvent.summaryDataUpdated({
    required SummaryData data,
  }) = _SummaryDataUpdated;

  /// (Internal) Error occurred
  const factory SummaryEvent.errorOccurred({
    required String message,
    Exception? exception,
  }) = _ErrorOccurred;
}
