// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'summary_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$SummaryState {
  SummaryStatus get status => throw _privateConstructorUsedError;
  SummaryOperation get currentOperation =>
      throw _privateConstructorUsedError; // Core summary data
  SummaryReport? get report => throw _privateConstructorUsedError;
  SummaryData? get summaryData =>
      throw _privateConstructorUsedError; // Analysis data
  ProductivityTrends? get productivityTrends =>
      throw _privateConstructorUsedError;
  QuadrantAnalysis? get quadrantAnalysis => throw _privateConstructorUsedError;
  CompletionPatterns? get completionPatterns =>
      throw _privateConstructorUsedError;
  PerformanceMetrics? get performanceMetrics =>
      throw _privateConstructorUsedError;
  List<TaskHighlight>? get highlights =>
      throw _privateConstructorUsedError; // Chart data
  Map<SummaryChartType, ChartData> get chartData =>
      throw _privateConstructorUsedError; // Comparison data
  PeriodComparison? get periodComparison =>
      throw _privateConstructorUsedError; // Filtering
  Priority? get priorityFilter => throw _privateConstructorUsedError;
  bool? get completionFilter => throw _privateConstructorUsedError;
  bool get hasActiveFilters => throw _privateConstructorUsedError; // Date range
  DateTime? get startDate => throw _privateConstructorUsedError;
  DateTime? get endDate => throw _privateConstructorUsedError;
  SummaryPeriod get currentPeriod =>
      throw _privateConstructorUsedError; // Export data
  String? get exportData => throw _privateConstructorUsedError;
  SummaryExportFormat? get lastExportFormat =>
      throw _privateConstructorUsedError; // Loading states
  Map<SummaryOperation, bool> get operationStates =>
      throw _privateConstructorUsedError; // Error handling
  String? get errorMessage => throw _privateConstructorUsedError;
  Exception? get lastException => throw _privateConstructorUsedError;
  bool get canRetry =>
      throw _privateConstructorUsedError; // Performance metrics
  DateTime? get lastUpdated => throw _privateConstructorUsedError;
  int get calculationDurationMs => throw _privateConstructorUsedError;
  int get dataLoadDurationMs => throw _privateConstructorUsedError;

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SummaryStateCopyWith<SummaryState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SummaryStateCopyWith<$Res> {
  factory $SummaryStateCopyWith(
          SummaryState value, $Res Function(SummaryState) then) =
      _$SummaryStateCopyWithImpl<$Res, SummaryState>;
  @useResult
  $Res call(
      {SummaryStatus status,
      SummaryOperation currentOperation,
      SummaryReport? report,
      SummaryData? summaryData,
      ProductivityTrends? productivityTrends,
      QuadrantAnalysis? quadrantAnalysis,
      CompletionPatterns? completionPatterns,
      PerformanceMetrics? performanceMetrics,
      List<TaskHighlight>? highlights,
      Map<SummaryChartType, ChartData> chartData,
      PeriodComparison? periodComparison,
      Priority? priorityFilter,
      bool? completionFilter,
      bool hasActiveFilters,
      DateTime? startDate,
      DateTime? endDate,
      SummaryPeriod currentPeriod,
      String? exportData,
      SummaryExportFormat? lastExportFormat,
      Map<SummaryOperation, bool> operationStates,
      String? errorMessage,
      Exception? lastException,
      bool canRetry,
      DateTime? lastUpdated,
      int calculationDurationMs,
      int dataLoadDurationMs});

  $SummaryReportCopyWith<$Res>? get report;
  $SummaryDataCopyWith<$Res>? get summaryData;
  $ProductivityTrendsCopyWith<$Res>? get productivityTrends;
  $QuadrantAnalysisCopyWith<$Res>? get quadrantAnalysis;
  $CompletionPatternsCopyWith<$Res>? get completionPatterns;
  $PerformanceMetricsCopyWith<$Res>? get performanceMetrics;
  $PeriodComparisonCopyWith<$Res>? get periodComparison;
}

/// @nodoc
class _$SummaryStateCopyWithImpl<$Res, $Val extends SummaryState>
    implements $SummaryStateCopyWith<$Res> {
  _$SummaryStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? currentOperation = null,
    Object? report = freezed,
    Object? summaryData = freezed,
    Object? productivityTrends = freezed,
    Object? quadrantAnalysis = freezed,
    Object? completionPatterns = freezed,
    Object? performanceMetrics = freezed,
    Object? highlights = freezed,
    Object? chartData = null,
    Object? periodComparison = freezed,
    Object? priorityFilter = freezed,
    Object? completionFilter = freezed,
    Object? hasActiveFilters = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? currentPeriod = null,
    Object? exportData = freezed,
    Object? lastExportFormat = freezed,
    Object? operationStates = null,
    Object? errorMessage = freezed,
    Object? lastException = freezed,
    Object? canRetry = null,
    Object? lastUpdated = freezed,
    Object? calculationDurationMs = null,
    Object? dataLoadDurationMs = null,
  }) {
    return _then(_value.copyWith(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as SummaryStatus,
      currentOperation: null == currentOperation
          ? _value.currentOperation
          : currentOperation // ignore: cast_nullable_to_non_nullable
              as SummaryOperation,
      report: freezed == report
          ? _value.report
          : report // ignore: cast_nullable_to_non_nullable
              as SummaryReport?,
      summaryData: freezed == summaryData
          ? _value.summaryData
          : summaryData // ignore: cast_nullable_to_non_nullable
              as SummaryData?,
      productivityTrends: freezed == productivityTrends
          ? _value.productivityTrends
          : productivityTrends // ignore: cast_nullable_to_non_nullable
              as ProductivityTrends?,
      quadrantAnalysis: freezed == quadrantAnalysis
          ? _value.quadrantAnalysis
          : quadrantAnalysis // ignore: cast_nullable_to_non_nullable
              as QuadrantAnalysis?,
      completionPatterns: freezed == completionPatterns
          ? _value.completionPatterns
          : completionPatterns // ignore: cast_nullable_to_non_nullable
              as CompletionPatterns?,
      performanceMetrics: freezed == performanceMetrics
          ? _value.performanceMetrics
          : performanceMetrics // ignore: cast_nullable_to_non_nullable
              as PerformanceMetrics?,
      highlights: freezed == highlights
          ? _value.highlights
          : highlights // ignore: cast_nullable_to_non_nullable
              as List<TaskHighlight>?,
      chartData: null == chartData
          ? _value.chartData
          : chartData // ignore: cast_nullable_to_non_nullable
              as Map<SummaryChartType, ChartData>,
      periodComparison: freezed == periodComparison
          ? _value.periodComparison
          : periodComparison // ignore: cast_nullable_to_non_nullable
              as PeriodComparison?,
      priorityFilter: freezed == priorityFilter
          ? _value.priorityFilter
          : priorityFilter // ignore: cast_nullable_to_non_nullable
              as Priority?,
      completionFilter: freezed == completionFilter
          ? _value.completionFilter
          : completionFilter // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasActiveFilters: null == hasActiveFilters
          ? _value.hasActiveFilters
          : hasActiveFilters // ignore: cast_nullable_to_non_nullable
              as bool,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentPeriod: null == currentPeriod
          ? _value.currentPeriod
          : currentPeriod // ignore: cast_nullable_to_non_nullable
              as SummaryPeriod,
      exportData: freezed == exportData
          ? _value.exportData
          : exportData // ignore: cast_nullable_to_non_nullable
              as String?,
      lastExportFormat: freezed == lastExportFormat
          ? _value.lastExportFormat
          : lastExportFormat // ignore: cast_nullable_to_non_nullable
              as SummaryExportFormat?,
      operationStates: null == operationStates
          ? _value.operationStates
          : operationStates // ignore: cast_nullable_to_non_nullable
              as Map<SummaryOperation, bool>,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      lastException: freezed == lastException
          ? _value.lastException
          : lastException // ignore: cast_nullable_to_non_nullable
              as Exception?,
      canRetry: null == canRetry
          ? _value.canRetry
          : canRetry // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      calculationDurationMs: null == calculationDurationMs
          ? _value.calculationDurationMs
          : calculationDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
      dataLoadDurationMs: null == dataLoadDurationMs
          ? _value.dataLoadDurationMs
          : dataLoadDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SummaryReportCopyWith<$Res>? get report {
    if (_value.report == null) {
      return null;
    }

    return $SummaryReportCopyWith<$Res>(_value.report!, (value) {
      return _then(_value.copyWith(report: value) as $Val);
    });
  }

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $SummaryDataCopyWith<$Res>? get summaryData {
    if (_value.summaryData == null) {
      return null;
    }

    return $SummaryDataCopyWith<$Res>(_value.summaryData!, (value) {
      return _then(_value.copyWith(summaryData: value) as $Val);
    });
  }

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $ProductivityTrendsCopyWith<$Res>? get productivityTrends {
    if (_value.productivityTrends == null) {
      return null;
    }

    return $ProductivityTrendsCopyWith<$Res>(_value.productivityTrends!,
        (value) {
      return _then(_value.copyWith(productivityTrends: value) as $Val);
    });
  }

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $QuadrantAnalysisCopyWith<$Res>? get quadrantAnalysis {
    if (_value.quadrantAnalysis == null) {
      return null;
    }

    return $QuadrantAnalysisCopyWith<$Res>(_value.quadrantAnalysis!, (value) {
      return _then(_value.copyWith(quadrantAnalysis: value) as $Val);
    });
  }

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CompletionPatternsCopyWith<$Res>? get completionPatterns {
    if (_value.completionPatterns == null) {
      return null;
    }

    return $CompletionPatternsCopyWith<$Res>(_value.completionPatterns!,
        (value) {
      return _then(_value.copyWith(completionPatterns: value) as $Val);
    });
  }

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PerformanceMetricsCopyWith<$Res>? get performanceMetrics {
    if (_value.performanceMetrics == null) {
      return null;
    }

    return $PerformanceMetricsCopyWith<$Res>(_value.performanceMetrics!,
        (value) {
      return _then(_value.copyWith(performanceMetrics: value) as $Val);
    });
  }

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $PeriodComparisonCopyWith<$Res>? get periodComparison {
    if (_value.periodComparison == null) {
      return null;
    }

    return $PeriodComparisonCopyWith<$Res>(_value.periodComparison!, (value) {
      return _then(_value.copyWith(periodComparison: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$SummaryStateImplCopyWith<$Res>
    implements $SummaryStateCopyWith<$Res> {
  factory _$$SummaryStateImplCopyWith(
          _$SummaryStateImpl value, $Res Function(_$SummaryStateImpl) then) =
      __$$SummaryStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {SummaryStatus status,
      SummaryOperation currentOperation,
      SummaryReport? report,
      SummaryData? summaryData,
      ProductivityTrends? productivityTrends,
      QuadrantAnalysis? quadrantAnalysis,
      CompletionPatterns? completionPatterns,
      PerformanceMetrics? performanceMetrics,
      List<TaskHighlight>? highlights,
      Map<SummaryChartType, ChartData> chartData,
      PeriodComparison? periodComparison,
      Priority? priorityFilter,
      bool? completionFilter,
      bool hasActiveFilters,
      DateTime? startDate,
      DateTime? endDate,
      SummaryPeriod currentPeriod,
      String? exportData,
      SummaryExportFormat? lastExportFormat,
      Map<SummaryOperation, bool> operationStates,
      String? errorMessage,
      Exception? lastException,
      bool canRetry,
      DateTime? lastUpdated,
      int calculationDurationMs,
      int dataLoadDurationMs});

  @override
  $SummaryReportCopyWith<$Res>? get report;
  @override
  $SummaryDataCopyWith<$Res>? get summaryData;
  @override
  $ProductivityTrendsCopyWith<$Res>? get productivityTrends;
  @override
  $QuadrantAnalysisCopyWith<$Res>? get quadrantAnalysis;
  @override
  $CompletionPatternsCopyWith<$Res>? get completionPatterns;
  @override
  $PerformanceMetricsCopyWith<$Res>? get performanceMetrics;
  @override
  $PeriodComparisonCopyWith<$Res>? get periodComparison;
}

/// @nodoc
class __$$SummaryStateImplCopyWithImpl<$Res>
    extends _$SummaryStateCopyWithImpl<$Res, _$SummaryStateImpl>
    implements _$$SummaryStateImplCopyWith<$Res> {
  __$$SummaryStateImplCopyWithImpl(
      _$SummaryStateImpl _value, $Res Function(_$SummaryStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? status = null,
    Object? currentOperation = null,
    Object? report = freezed,
    Object? summaryData = freezed,
    Object? productivityTrends = freezed,
    Object? quadrantAnalysis = freezed,
    Object? completionPatterns = freezed,
    Object? performanceMetrics = freezed,
    Object? highlights = freezed,
    Object? chartData = null,
    Object? periodComparison = freezed,
    Object? priorityFilter = freezed,
    Object? completionFilter = freezed,
    Object? hasActiveFilters = null,
    Object? startDate = freezed,
    Object? endDate = freezed,
    Object? currentPeriod = null,
    Object? exportData = freezed,
    Object? lastExportFormat = freezed,
    Object? operationStates = null,
    Object? errorMessage = freezed,
    Object? lastException = freezed,
    Object? canRetry = null,
    Object? lastUpdated = freezed,
    Object? calculationDurationMs = null,
    Object? dataLoadDurationMs = null,
  }) {
    return _then(_$SummaryStateImpl(
      status: null == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as SummaryStatus,
      currentOperation: null == currentOperation
          ? _value.currentOperation
          : currentOperation // ignore: cast_nullable_to_non_nullable
              as SummaryOperation,
      report: freezed == report
          ? _value.report
          : report // ignore: cast_nullable_to_non_nullable
              as SummaryReport?,
      summaryData: freezed == summaryData
          ? _value.summaryData
          : summaryData // ignore: cast_nullable_to_non_nullable
              as SummaryData?,
      productivityTrends: freezed == productivityTrends
          ? _value.productivityTrends
          : productivityTrends // ignore: cast_nullable_to_non_nullable
              as ProductivityTrends?,
      quadrantAnalysis: freezed == quadrantAnalysis
          ? _value.quadrantAnalysis
          : quadrantAnalysis // ignore: cast_nullable_to_non_nullable
              as QuadrantAnalysis?,
      completionPatterns: freezed == completionPatterns
          ? _value.completionPatterns
          : completionPatterns // ignore: cast_nullable_to_non_nullable
              as CompletionPatterns?,
      performanceMetrics: freezed == performanceMetrics
          ? _value.performanceMetrics
          : performanceMetrics // ignore: cast_nullable_to_non_nullable
              as PerformanceMetrics?,
      highlights: freezed == highlights
          ? _value._highlights
          : highlights // ignore: cast_nullable_to_non_nullable
              as List<TaskHighlight>?,
      chartData: null == chartData
          ? _value._chartData
          : chartData // ignore: cast_nullable_to_non_nullable
              as Map<SummaryChartType, ChartData>,
      periodComparison: freezed == periodComparison
          ? _value.periodComparison
          : periodComparison // ignore: cast_nullable_to_non_nullable
              as PeriodComparison?,
      priorityFilter: freezed == priorityFilter
          ? _value.priorityFilter
          : priorityFilter // ignore: cast_nullable_to_non_nullable
              as Priority?,
      completionFilter: freezed == completionFilter
          ? _value.completionFilter
          : completionFilter // ignore: cast_nullable_to_non_nullable
              as bool?,
      hasActiveFilters: null == hasActiveFilters
          ? _value.hasActiveFilters
          : hasActiveFilters // ignore: cast_nullable_to_non_nullable
              as bool,
      startDate: freezed == startDate
          ? _value.startDate
          : startDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      endDate: freezed == endDate
          ? _value.endDate
          : endDate // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      currentPeriod: null == currentPeriod
          ? _value.currentPeriod
          : currentPeriod // ignore: cast_nullable_to_non_nullable
              as SummaryPeriod,
      exportData: freezed == exportData
          ? _value.exportData
          : exportData // ignore: cast_nullable_to_non_nullable
              as String?,
      lastExportFormat: freezed == lastExportFormat
          ? _value.lastExportFormat
          : lastExportFormat // ignore: cast_nullable_to_non_nullable
              as SummaryExportFormat?,
      operationStates: null == operationStates
          ? _value._operationStates
          : operationStates // ignore: cast_nullable_to_non_nullable
              as Map<SummaryOperation, bool>,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
      lastException: freezed == lastException
          ? _value.lastException
          : lastException // ignore: cast_nullable_to_non_nullable
              as Exception?,
      canRetry: null == canRetry
          ? _value.canRetry
          : canRetry // ignore: cast_nullable_to_non_nullable
              as bool,
      lastUpdated: freezed == lastUpdated
          ? _value.lastUpdated
          : lastUpdated // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      calculationDurationMs: null == calculationDurationMs
          ? _value.calculationDurationMs
          : calculationDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
      dataLoadDurationMs: null == dataLoadDurationMs
          ? _value.dataLoadDurationMs
          : dataLoadDurationMs // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc

class _$SummaryStateImpl implements _SummaryState {
  const _$SummaryStateImpl(
      {required this.status,
      required this.currentOperation,
      this.report,
      this.summaryData,
      this.productivityTrends,
      this.quadrantAnalysis,
      this.completionPatterns,
      this.performanceMetrics,
      final List<TaskHighlight>? highlights,
      final Map<SummaryChartType, ChartData> chartData = const {},
      this.periodComparison,
      this.priorityFilter,
      this.completionFilter,
      this.hasActiveFilters = false,
      this.startDate,
      this.endDate,
      this.currentPeriod = SummaryPeriod.monthly,
      this.exportData,
      this.lastExportFormat,
      final Map<SummaryOperation, bool> operationStates = const {},
      this.errorMessage,
      this.lastException,
      this.canRetry = false,
      this.lastUpdated,
      this.calculationDurationMs = 0,
      this.dataLoadDurationMs = 0})
      : _highlights = highlights,
        _chartData = chartData,
        _operationStates = operationStates;

  @override
  final SummaryStatus status;
  @override
  final SummaryOperation currentOperation;
// Core summary data
  @override
  final SummaryReport? report;
  @override
  final SummaryData? summaryData;
// Analysis data
  @override
  final ProductivityTrends? productivityTrends;
  @override
  final QuadrantAnalysis? quadrantAnalysis;
  @override
  final CompletionPatterns? completionPatterns;
  @override
  final PerformanceMetrics? performanceMetrics;
  final List<TaskHighlight>? _highlights;
  @override
  List<TaskHighlight>? get highlights {
    final value = _highlights;
    if (value == null) return null;
    if (_highlights is EqualUnmodifiableListView) return _highlights;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

// Chart data
  final Map<SummaryChartType, ChartData> _chartData;
// Chart data
  @override
  @JsonKey()
  Map<SummaryChartType, ChartData> get chartData {
    if (_chartData is EqualUnmodifiableMapView) return _chartData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_chartData);
  }

// Comparison data
  @override
  final PeriodComparison? periodComparison;
// Filtering
  @override
  final Priority? priorityFilter;
  @override
  final bool? completionFilter;
  @override
  @JsonKey()
  final bool hasActiveFilters;
// Date range
  @override
  final DateTime? startDate;
  @override
  final DateTime? endDate;
  @override
  @JsonKey()
  final SummaryPeriod currentPeriod;
// Export data
  @override
  final String? exportData;
  @override
  final SummaryExportFormat? lastExportFormat;
// Loading states
  final Map<SummaryOperation, bool> _operationStates;
// Loading states
  @override
  @JsonKey()
  Map<SummaryOperation, bool> get operationStates {
    if (_operationStates is EqualUnmodifiableMapView) return _operationStates;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_operationStates);
  }

// Error handling
  @override
  final String? errorMessage;
  @override
  final Exception? lastException;
  @override
  @JsonKey()
  final bool canRetry;
// Performance metrics
  @override
  final DateTime? lastUpdated;
  @override
  @JsonKey()
  final int calculationDurationMs;
  @override
  @JsonKey()
  final int dataLoadDurationMs;

  @override
  String toString() {
    return 'SummaryState(status: $status, currentOperation: $currentOperation, report: $report, summaryData: $summaryData, productivityTrends: $productivityTrends, quadrantAnalysis: $quadrantAnalysis, completionPatterns: $completionPatterns, performanceMetrics: $performanceMetrics, highlights: $highlights, chartData: $chartData, periodComparison: $periodComparison, priorityFilter: $priorityFilter, completionFilter: $completionFilter, hasActiveFilters: $hasActiveFilters, startDate: $startDate, endDate: $endDate, currentPeriod: $currentPeriod, exportData: $exportData, lastExportFormat: $lastExportFormat, operationStates: $operationStates, errorMessage: $errorMessage, lastException: $lastException, canRetry: $canRetry, lastUpdated: $lastUpdated, calculationDurationMs: $calculationDurationMs, dataLoadDurationMs: $dataLoadDurationMs)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SummaryStateImpl &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.currentOperation, currentOperation) ||
                other.currentOperation == currentOperation) &&
            (identical(other.report, report) || other.report == report) &&
            (identical(other.summaryData, summaryData) ||
                other.summaryData == summaryData) &&
            (identical(other.productivityTrends, productivityTrends) ||
                other.productivityTrends == productivityTrends) &&
            (identical(other.quadrantAnalysis, quadrantAnalysis) ||
                other.quadrantAnalysis == quadrantAnalysis) &&
            (identical(other.completionPatterns, completionPatterns) ||
                other.completionPatterns == completionPatterns) &&
            (identical(other.performanceMetrics, performanceMetrics) ||
                other.performanceMetrics == performanceMetrics) &&
            const DeepCollectionEquality()
                .equals(other._highlights, _highlights) &&
            const DeepCollectionEquality()
                .equals(other._chartData, _chartData) &&
            (identical(other.periodComparison, periodComparison) ||
                other.periodComparison == periodComparison) &&
            (identical(other.priorityFilter, priorityFilter) ||
                other.priorityFilter == priorityFilter) &&
            (identical(other.completionFilter, completionFilter) ||
                other.completionFilter == completionFilter) &&
            (identical(other.hasActiveFilters, hasActiveFilters) ||
                other.hasActiveFilters == hasActiveFilters) &&
            (identical(other.startDate, startDate) ||
                other.startDate == startDate) &&
            (identical(other.endDate, endDate) || other.endDate == endDate) &&
            (identical(other.currentPeriod, currentPeriod) ||
                other.currentPeriod == currentPeriod) &&
            (identical(other.exportData, exportData) ||
                other.exportData == exportData) &&
            (identical(other.lastExportFormat, lastExportFormat) ||
                other.lastExportFormat == lastExportFormat) &&
            const DeepCollectionEquality()
                .equals(other._operationStates, _operationStates) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage) &&
            (identical(other.lastException, lastException) ||
                other.lastException == lastException) &&
            (identical(other.canRetry, canRetry) ||
                other.canRetry == canRetry) &&
            (identical(other.lastUpdated, lastUpdated) ||
                other.lastUpdated == lastUpdated) &&
            (identical(other.calculationDurationMs, calculationDurationMs) ||
                other.calculationDurationMs == calculationDurationMs) &&
            (identical(other.dataLoadDurationMs, dataLoadDurationMs) ||
                other.dataLoadDurationMs == dataLoadDurationMs));
  }

  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        status,
        currentOperation,
        report,
        summaryData,
        productivityTrends,
        quadrantAnalysis,
        completionPatterns,
        performanceMetrics,
        const DeepCollectionEquality().hash(_highlights),
        const DeepCollectionEquality().hash(_chartData),
        periodComparison,
        priorityFilter,
        completionFilter,
        hasActiveFilters,
        startDate,
        endDate,
        currentPeriod,
        exportData,
        lastExportFormat,
        const DeepCollectionEquality().hash(_operationStates),
        errorMessage,
        lastException,
        canRetry,
        lastUpdated,
        calculationDurationMs,
        dataLoadDurationMs
      ]);

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SummaryStateImplCopyWith<_$SummaryStateImpl> get copyWith =>
      __$$SummaryStateImplCopyWithImpl<_$SummaryStateImpl>(this, _$identity);
}

abstract class _SummaryState implements SummaryState {
  const factory _SummaryState(
      {required final SummaryStatus status,
      required final SummaryOperation currentOperation,
      final SummaryReport? report,
      final SummaryData? summaryData,
      final ProductivityTrends? productivityTrends,
      final QuadrantAnalysis? quadrantAnalysis,
      final CompletionPatterns? completionPatterns,
      final PerformanceMetrics? performanceMetrics,
      final List<TaskHighlight>? highlights,
      final Map<SummaryChartType, ChartData> chartData,
      final PeriodComparison? periodComparison,
      final Priority? priorityFilter,
      final bool? completionFilter,
      final bool hasActiveFilters,
      final DateTime? startDate,
      final DateTime? endDate,
      final SummaryPeriod currentPeriod,
      final String? exportData,
      final SummaryExportFormat? lastExportFormat,
      final Map<SummaryOperation, bool> operationStates,
      final String? errorMessage,
      final Exception? lastException,
      final bool canRetry,
      final DateTime? lastUpdated,
      final int calculationDurationMs,
      final int dataLoadDurationMs}) = _$SummaryStateImpl;

  @override
  SummaryStatus get status;
  @override
  SummaryOperation get currentOperation; // Core summary data
  @override
  SummaryReport? get report;
  @override
  SummaryData? get summaryData; // Analysis data
  @override
  ProductivityTrends? get productivityTrends;
  @override
  QuadrantAnalysis? get quadrantAnalysis;
  @override
  CompletionPatterns? get completionPatterns;
  @override
  PerformanceMetrics? get performanceMetrics;
  @override
  List<TaskHighlight>? get highlights; // Chart data
  @override
  Map<SummaryChartType, ChartData> get chartData; // Comparison data
  @override
  PeriodComparison? get periodComparison; // Filtering
  @override
  Priority? get priorityFilter;
  @override
  bool? get completionFilter;
  @override
  bool get hasActiveFilters; // Date range
  @override
  DateTime? get startDate;
  @override
  DateTime? get endDate;
  @override
  SummaryPeriod get currentPeriod; // Export data
  @override
  String? get exportData;
  @override
  SummaryExportFormat? get lastExportFormat; // Loading states
  @override
  Map<SummaryOperation, bool> get operationStates; // Error handling
  @override
  String? get errorMessage;
  @override
  Exception? get lastException;
  @override
  bool get canRetry; // Performance metrics
  @override
  DateTime? get lastUpdated;
  @override
  int get calculationDurationMs;
  @override
  int get dataLoadDurationMs;

  /// Create a copy of SummaryState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SummaryStateImplCopyWith<_$SummaryStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
