import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../core/di/injection.dart';
import '../../features/calendar/bloc/calendar_bloc.dart';
import '../../features/calendar/bloc/calendar_event.dart';
import '../../features/calendar/bloc/calendar_state.dart';
import '../../features/calendar/presentation/widgets/calendar_month_view.dart';
import '../../features/calendar/presentation/widgets/calendar_year_view.dart';
import '../../features/calendar/presentation/widgets/calendar_quadrant_view.dart';
import '../../features/calendar/presentation/widgets/calendar_toolbar.dart';
import '../../features/tasks/presentation/task_list_panel.dart';
import '../../features/tasks/bloc/task_list_bloc.dart';
import '../../features/tasks/bloc/task_list_event.dart';
import '../../features/summary/presentation/summary_page.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  int _currentIndex = 0;

  final List<Widget> _pages = [
    const CalendarView(),
    MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => getIt<TaskListBloc>()
            ..add(TaskListEvent.subscriptionRequested(DateTime.now())),
        ),
        BlocProvider(
          create: (context) => getIt<CalendarBloc>(),
        ),
      ],
      child: const TaskListPanel(),
    ),
    const SummaryView(),
  ];

  final List<BottomNavigationBarItem> _bottomNavItems = [
    const BottomNavigationBarItem(
      icon: Icon(Icons.calendar_month),
      label: '日历',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.task_alt),
      label: '任务',
    ),
    const BottomNavigationBarItem(
      icon: Icon(Icons.analytics),
      label: '总结',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withAlpha(26),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
            });
          },
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.white,
          selectedItemColor: Colors.blue[600],
          unselectedItemColor: Colors.grey[600],
          selectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
          ),
          items: _bottomNavItems,
        ),
      ),
    );
  }
}

class CalendarView extends StatelessWidget {
  const CalendarView({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) =>
          getIt<CalendarBloc>()..add(const CalendarEvent.started()),
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        body: Column(
          children: [
            // 工具栏
            const CalendarToolbar(),

            // 日历内容
            Expanded(
              child: BlocBuilder<CalendarBloc, CalendarState>(
                builder: (context, state) {
                  return _buildCalendarContent(context, state);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalendarContent(BuildContext context, CalendarState state) {
    switch (state.viewType) {
      case CalendarViewType.month:
        return const CalendarMonthView();
      case CalendarViewType.year:
        return const CalendarYearView();
      case CalendarViewType.quadrant:
        return const CalendarQuadrantView();
      default:
        return const CalendarMonthView();
    }
  }
}

class TaskListView extends StatelessWidget {
  const TaskListView({super.key});

  @override
  Widget build(BuildContext context) {
    return const TaskListPanel();
  }
}

class SummaryView extends StatelessWidget {
  const SummaryView({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: const Text(
            '总结报告',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: Colors.white,
          elevation: 0,
          bottom: TabBar(
            labelColor: Colors.blue[600],
            unselectedLabelColor: Colors.grey[600],
            indicatorColor: Colors.blue[600],
            labelStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
            tabs: const [
              Tab(text: '本月总结'),
              Tab(text: '本年总结'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            SummaryPage(year: DateTime.now().year, month: DateTime.now().month),
            SummaryPage(year: DateTime.now().year),
          ],
        ),
      ),
    );
  }
}
