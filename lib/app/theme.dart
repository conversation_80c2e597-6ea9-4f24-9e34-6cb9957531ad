import 'package:flutter/material.dart';

class AppTheme {
  // shadcn/ui 设计系统颜色调色板
  // 基于 HSL 的科学配色系统

  // 主色调 - 基于 shadcn/ui 的蓝色系统
  static const Color primaryBlue = Color(0xFF007AFF); // macOS System Blue
  static const Color primaryBlueLight = Color(0xFF3B82F6); // Blue 500
  static const Color primaryBlueDark = Color(0xFF1D4ED8); // Blue 700

  // 中性色系 - 基于 shadcn/ui 的 Slate 色系
  static const Color slate50 = Color(0xFFF8FAFC);
  static const Color slate100 = Color(0xFFF1F5F9);
  static const Color slate200 = Color(0xFFE2E8F0);
  static const Color slate300 = Color(0xFFCBD5E1);
  static const Color slate400 = Color(0xFF94A3B8);
  static const Color slate500 = Color(0xFF64748B);
  static const Color slate600 = Color(0xFF475569);
  static const Color slate700 = Color(0xFF334155);
  static const Color slate800 = Color(0xFF1E293B);
  static const Color slate900 = Color(0xFF0F172A);
  static const Color slate950 = Color(0xFF020617);

  // 表面颜色
  static const Color surfaceLight = slate50;
  static const Color surfaceDark = slate950;
  static const Color cardLight = Colors.white;
  static const Color cardDark = slate900;

  // 边框颜色
  static const Color borderLight = slate200;
  static const Color borderDark = slate800;

  // 优先级颜色 - 基于 shadcn/ui 语义化颜色
  static const Color urgentImportantColor = Color(0xFFFF3B30); // Red (macOS) - 危险/紧急
  static const Color importantNotUrgentColor = Color(0xFFFF9500); // Orange (macOS) - 警告/重要
  static const Color urgentNotImportantColor = Color(0xFF007AFF); // Blue (macOS) - 信息/一般
  static const Color notUrgentNotImportantColor = Color(0xFF8E8E93); // Gray (macOS) - 次要

  // 文本颜色 - 基于 shadcn/ui 的语义化文本颜色
  static const Color textPrimary = slate900; // 主要文本
  static const Color textSecondary = slate600; // 次要文本
  static const Color textTertiary = slate400; // 三级文本
  static const Color textMuted = slate500; // 静音文本

  // 状态颜色 - 基于 shadcn/ui 的语义化状态颜色
  static const Color successColor = Color(0xFF16A34A); // Green 600
  static const Color warningColor = Color(0xFFEA580C); // Orange 600
  static const Color errorColor = Color(0xFFDC2626); // Red 600
  static const Color infoColor = Color(0xFF007AFF); // macOS System Blue

  // 设计系统 tokens
  // 圆角系统
  static const double radiusXs = 2.0;
  static const double radiusSm = 4.0;
  static const double radiusMd = 6.0;
  static const double radiusLg = 8.0;
  static const double radiusXl = 12.0;
  static const double radius2xl = 16.0;
  static const double radius3xl = 24.0;

  // 间距系统 - 基于 shadcn/ui 的 4px 基准，更精确的控制
  static const double spacing0_5 = 2.0;  // 0.125rem
  static const double spacing1 = 4.0;    // 0.25rem
  static const double spacing1_5 = 6.0;  // 0.375rem
  static const double spacing2 = 8.0;    // 0.5rem
  static const double spacing2_5 = 10.0; // 0.625rem
  static const double spacing3 = 12.0;   // 0.75rem
  static const double spacing3_5 = 14.0; // 0.875rem
  static const double spacing4 = 16.0;   // 1rem
  static const double spacing5 = 20.0;   // 1.25rem
  static const double spacing6 = 24.0;   // 1.5rem
  static const double spacing7 = 28.0;   // 1.75rem
  static const double spacing8 = 32.0;   // 2rem
  static const double spacing9 = 36.0;   // 2.25rem
  static const double spacing10 = 40.0;  // 2.5rem
  static const double spacing11 = 44.0;  // 2.75rem
  static const double spacing12 = 48.0;  // 3rem
  static const double spacing14 = 56.0;  // 3.5rem
  static const double spacing16 = 64.0;  // 4rem
  static const double spacing20 = 80.0;  // 5rem
  static const double spacing24 = 96.0;  // 6rem
  static const double spacing28 = 112.0; // 7rem
  static const double spacing32 = 128.0; // 8rem

  // 圆角系统增强 - 添加 full 圆角
  static const double radiusFull = 9999.0; // 完全圆角

  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: primaryBlue,
      brightness: Brightness.light,
      surface: surfaceLight,
      onSurface: textPrimary,
      surfaceVariant: slate100,
      onSurfaceVariant: textSecondary,
      outline: borderLight,
      outlineVariant: slate100,
    ),
    scaffoldBackgroundColor: surfaceLight,

    // 卡片主题 - 基于 shadcn/ui 的卡片设计
    cardTheme: CardThemeData(
      color: cardLight,
      elevation: 0,
      shadowColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(radiusXl),
        side: const BorderSide(color: borderLight, width: 1),
      ),
      margin: EdgeInsets.zero,
    ),

    // AppBar主题 - 基于 shadcn/ui 的导航栏设计
    appBarTheme: const AppBarTheme(
      backgroundColor: Colors.white,
      foregroundColor: textPrimary,
      elevation: 0,
      scrolledUnderElevation: 0,
      centerTitle: false,
      surfaceTintColor: Colors.transparent,
      titleTextStyle: TextStyle(
        color: textPrimary,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        letterSpacing: -0.5,
      ),
    ),

    // 输入框主题 - 基于 shadcn/ui 的表单设计
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Colors.white,
      contentPadding: const EdgeInsets.symmetric(horizontal: spacing3, vertical: spacing2),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusLg),
        borderSide: const BorderSide(color: borderLight),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusLg),
        borderSide: const BorderSide(color: borderLight),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(radiusLg),
        borderSide: const BorderSide(color: primaryBlue, width: 2),
      ),
      hintStyle: TextStyle(color: textMuted),
      labelStyle: TextStyle(color: textSecondary),
    ),

    // 按钮主题 - 基于 shadcn/ui 的按钮设计
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        shadowColor: Colors.transparent,
        padding: const EdgeInsets.symmetric(horizontal: spacing4, vertical: spacing3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusLg),
        ),
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
    ),

    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: primaryBlue,
        foregroundColor: Colors.white,
        elevation: 0,
        shadowColor: Colors.transparent,
        padding: const EdgeInsets.symmetric(horizontal: spacing4, vertical: spacing3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusLg),
        ),
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
    ),

    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: textPrimary,
        backgroundColor: Colors.transparent,
        elevation: 0,
        shadowColor: Colors.transparent,
        padding: const EdgeInsets.symmetric(horizontal: spacing4, vertical: spacing3),
        side: const BorderSide(color: borderLight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusLg),
        ),
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
    ),

    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryBlue,
        backgroundColor: Colors.transparent,
        elevation: 0,
        shadowColor: Colors.transparent,
        padding: const EdgeInsets.symmetric(horizontal: spacing4, vertical: spacing2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusLg),
        ),
        textStyle: const TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
    ),

    // 分割线主题
    dividerTheme: const DividerThemeData(
      color: borderLight,
      thickness: 1,
      space: 1,
    ),

    // 文本主题 - 基于 shadcn/ui 的字体系统
    textTheme: const TextTheme(
      // 标题 - 使用更紧凑的字间距
      headlineLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w700,
        color: textPrimary,
        letterSpacing: -1.0,
        height: 1.2,
      ),
      headlineMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.w600,
        color: textPrimary,
        letterSpacing: -0.8,
        height: 1.2,
      ),
      headlineSmall: TextStyle(
        fontSize: 24,
        fontWeight: FontWeight.w600,
        color: textPrimary,
        letterSpacing: -0.6,
        height: 1.3,
      ),

      // 标题
      titleLarge: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        color: textPrimary,
        letterSpacing: -0.4,
        height: 1.3,
      ),
      titleMedium: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w500,
        color: textPrimary,
      ),
      titleSmall: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: textSecondary,
      ),

      // 正文
      bodyLarge: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: textPrimary,
      ),
      bodyMedium: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: textPrimary,
      ),
      bodySmall: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: textSecondary,
      ),

      // 标签
      labelLarge: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: textPrimary,
      ),
      labelMedium: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: textSecondary,
      ),
      labelSmall: TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.w500,
        color: textTertiary,
      ),
    ),
  );

  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: ColorScheme.fromSeed(
      seedColor: primaryBlue,
      brightness: Brightness.dark,
      surface: surfaceDark,
    ),
    scaffoldBackgroundColor: surfaceDark,
    cardTheme: CardThemeData(
      color: cardDark,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade800, width: 1),
      ),
      margin: EdgeInsets.zero,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: cardDark,
      foregroundColor: Colors.white,
      elevation: 0,
      scrolledUnderElevation: 1,
      centerTitle: false,
      titleTextStyle: TextStyle(
        color: Colors.white,
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: cardDark,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey.shade700),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: Colors.grey.shade700),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: primaryBlueLight, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: primaryBlueLight,
        foregroundColor: Colors.white,
        elevation: 0,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    ),
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: primaryBlueLight,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    ),
    dividerTheme: DividerThemeData(
      color: Colors.grey.shade800,
      thickness: 1,
      space: 1,
    ),
  );

  // 获取优先级颜色的工具方法
  static Color getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'urgent_important':
        return urgentImportantColor;
      case 'important_not_urgent':
        return importantNotUrgentColor;
      case 'urgent_not_important':
        return urgentNotImportantColor;
      case 'not_urgent_not_important':
        return notUrgentNotImportantColor;
      default:
        return notUrgentNotImportantColor;
    }
  }

  // 获取优先级颜色的浅色版本
  static Color getPriorityColorLight(String priority) {
    final color = getPriorityColor(priority);
    return color.withAlpha(26);
  }
}
