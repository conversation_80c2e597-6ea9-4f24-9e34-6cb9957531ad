import 'package:go_router/go_router.dart';
import '../features/calendar/presentation/calendar_page.dart';
import '../features/summary/presentation/summary_page.dart';

final GoRouter appRouter = GoRouter(
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      name: 'home',
      builder: (context, state) => const CalendarPage(),
    ),
    GoRoute(
      path: '/summary/monthly/:year/:month',
      name: 'monthly-summary',
      builder: (context, state) {
        final year = int.parse(state.pathParameters['year']!);
        final month = int.parse(state.pathParameters['month']!);
        return SummaryPage(year: year, month: month);
      },
    ),
    GoRoute(
      path: '/summary/yearly/:year',
      name: 'yearly-summary',
      builder: (context, state) {
        final year = int.parse(state.pathParameters['year']!);
        return SummaryPage(year: year);
      },
    ),
  ],
);
