import 'dart:developer' as developer;

/// Handles database and repository-level exceptions with proper error mapping
class RepositoryExceptionHandler {
  /// Handles database operations with proper exception handling and logging
  static Future<T> handleDatabaseOperation<T>(
    String operation,
    Future<T> Function() databaseCall, {
    String? context,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      developer.log(
        'Starting database operation: $operation',
        name: 'RepositoryExceptionHandler',
        level: 500, // Debug level
      );

      final result = await databaseCall();

      developer.log(
        'Database operation completed successfully: $operation',
        name: 'RepositoryExceptionHandler',
        level: 500,
      );

      return result;
    } catch (error, stackTrace) {
      // Log the error with context
      developer.log(
        'Database operation failed: $operation - ${error.toString()}',
        name: 'RepositoryExceptionHandler_Error',
        level: 1000, // Error level
        error: error,
        stackTrace: stackTrace,
      );

      if (context != null) {
        developer.log(
          'Operation context: $context',
          name: 'RepositoryExceptionHandler_Context',
          level: 1000,
        );
      }

      if (metadata != null && metadata.isNotEmpty) {
        developer.log(
          'Operation metadata: ${metadata.toString()}',
          name: 'RepositoryExceptionHandler_Metadata',
          level: 1000,
        );
      }

      // Re-throw the error to maintain the original exception flow
      rethrow;
    }
  }

  /// Handles repository operations with retry logic for transient failures
  static Future<T> handleRepositoryOperationWithRetry<T>(
    String operation,
    Future<T> Function() repositoryCall, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(milliseconds: 500),
    String? context,
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await handleDatabaseOperation(
          '$operation (attempt $attempt)',
          repositoryCall,
          context: context,
          metadata: {'attempt': attempt, 'maxRetries': maxRetries},
        );
      } catch (error) {
        if (attempt == maxRetries) {
          developer.log(
            'Repository operation failed after $maxRetries attempts: $operation',
            name: 'RepositoryExceptionHandler_FinalFailure',
            level: 1000,
            error: error,
          );
          rethrow;
        }

        developer.log(
          'Repository operation failed, retrying: $operation (attempt $attempt/$maxRetries)',
          name: 'RepositoryExceptionHandler_Retry',
          level: 900, // Warning level
        );

        await Future.delayed(retryDelay);
      }
    }

    throw Exception('Repository operation failed after all retry attempts: $operation');
  }

  /// Maps database exceptions to domain-specific exceptions
  static Exception mapDatabaseException(Object error, String operation) {
    final errorMessage = error.toString().toLowerCase();

    if (errorMessage.contains('constraint') || errorMessage.contains('unique')) {
      return Exception('Data constraint violation in $operation: ${error.toString()}');
    }

    if (errorMessage.contains('not found') || errorMessage.contains('no such')) {
      return Exception('Resource not found in $operation: ${error.toString()}');
    }

    if (errorMessage.contains('timeout') || errorMessage.contains('connection')) {
      return Exception('Database connection issue in $operation: ${error.toString()}');
    }

    if (errorMessage.contains('permission') || errorMessage.contains('access')) {
      return Exception('Database access denied in $operation: ${error.toString()}');
    }

    // Default case - return generic repository exception
    return Exception('Database operation failed in $operation: ${error.toString()}');
  }

  /// Logs performance metrics for database operations
  static void logPerformance(String operation, Duration duration) {
    final durationMs = duration.inMilliseconds;
    
    developer.log(
      'Database operation performance: $operation took ${durationMs}ms',
      name: 'RepositoryExceptionHandler_Performance',
      level: 500,
    );

    // Log warning for slow operations
    if (durationMs > 1000) {
      developer.log(
        'Slow database operation detected: $operation took ${durationMs}ms',
        name: 'RepositoryExceptionHandler_SlowOp',
        level: 900,
      );
    }
  }
}