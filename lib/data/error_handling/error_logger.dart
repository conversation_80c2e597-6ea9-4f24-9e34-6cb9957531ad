import 'dart:developer' as developer;

/// Defines the severity level of an error.
enum ErrorSeverity {
  info,
  warning,
  error,
  critical,
}

/// Centralized error logging utility
class ErrorLogger {
  static final ErrorLogger _instance = ErrorLogger._internal();
  static ErrorLogger get instance => _instance;

  final Map<String, int> _errorCounts = {};
  final List<Map<String, dynamic>> _recentErrors = [];

  ErrorLogger._internal();

  /// Logs informational messages
  Future<void> logInfo({
    required String operation,
    required String message,
    Map<String, dynamic>? context,
  }) async {
    _log(
      severity: ErrorSeverity.info,
      operation: operation,
      message: message,
      context: context,
    );
  }

  /// Logs error messages
  Future<void> logError({
    required String operation,
    required Object error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
    ErrorSeverity severity = ErrorSeverity.error,
  }) async {
    _log(
      severity: severity,
      operation: operation,
      message: 'Error: ${error.toString()}',
      error: error,
      stackTrace: stackTrace,
      context: context,
    );
  }

  /// Logs warning messages
  Future<void> logWarning({
    required String operation,
    required String message,
    Map<String, dynamic>? context,
  }) async {
    _log(
      severity: ErrorSeverity.warning,
      operation: operation,
      message: 'Warning: $message',
      context: context,
    );
  }

  void _log({
    required ErrorSeverity severity,
    required String operation,
    required String message,
    Object? error,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) {
    final logName = 'AppLogger.${severity.name}.$operation';
    int level = _severityToLevel(severity);

    developer.log(
      message,
      name: logName,
      level: level,
      error: error,
      stackTrace: stackTrace,
    );

    if (context != null && context.isNotEmpty) {
      developer.log(
        'Context: ${context.toString()}',
        name: '$logName.Context',
        level: level,
      );
    }

    // Update statistics for warnings and errors
    if (severity == ErrorSeverity.error ||
        severity == ErrorSeverity.warning ||
        severity == ErrorSeverity.critical) {
      final key = severity.name;
      _errorCounts[key] = (_errorCounts[key] ?? 0) + 1;
      _errorCounts['total_errors_last_hour'] =
          (_errorCounts['total_errors_last_hour'] ?? 0) +
              1; // Simplified for now

      // Keep a small buffer of recent errors
      if (_recentErrors.length > 50) {
        _recentErrors.removeAt(0);
      }
      _recentErrors.add({
        'timestamp': DateTime.now().toIso8601String(),
        'severity': severity.name,
        'operation': operation,
        'message': message,
        'error': error.toString(),
      });
    }
  }

  int _severityToLevel(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.info:
        return 800;
      case ErrorSeverity.warning:
        return 900;
      case ErrorSeverity.error:
        return 1000;
      case ErrorSeverity.critical:
        return 1200;
    }
  }

  /// Records performance metrics
  void recordPerformance(String operation, Duration duration) {
    final durationMs = duration.inMilliseconds;

    developer.log(
      'Performance: $operation took ${durationMs}ms',
      name: 'PerformanceLogger',
      level: 500, // Debug level
    );

    // Log warning for slow operations
    if (durationMs > 1000) {
      logWarning(
        operation: 'SlowOperation',
        message: '$operation took ${durationMs}ms',
        context: {'duration_ms': durationMs},
      );
    }
  }

  /// Get statistics about logged errors.
  Map<String, dynamic> getErrorStatistics() {
    return {
      'error_counts': Map.unmodifiable(_errorCounts),
      'recent_errors': List.unmodifiable(_recentErrors),
    };
  }
}
