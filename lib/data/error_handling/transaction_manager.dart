import 'dart:async';
import '../datasources/local_database.dart';
import 'error_logger.dart';

import '../repositories/exceptions/repository_exceptions.dart';

/// Manages database transactions with proper error handling, logging, and retry logic.
class TransactionManager {
  final LocalDatabase _database;
  final ErrorLogger _logger = ErrorLogger.instance;
  final Map<String, int> _transactionStats = {
    'total_started': 0,
    'total_completed': 0,
    'total_failed': 0,
  };

  TransactionManager(this._database);

  /// Executes a database operation within a transaction.
  Future<T> executeTransaction<T>(
    String operationName,
    Future<T> Function(dynamic txn) operation, {
    bool isLongRunning = false,
  }) async {
    _transactionStats['total_started'] =
        (_transactionStats['total_started'] ?? 0) + 1;
    final startTime = DateTime.now();
    await _logger.logInfo(
      operation: 'TransactionManager.start',
      message: 'Starting transaction: $operationName',
      context: {'is_long_running': isLongRunning},
    );

    try {
      final result = await _database.transaction(() async {
        // Pass the database instance as the transaction object, as Drift transactions
        // are managed on the database object itself.
        return await operation(_database);
      });

      _transactionStats['total_completed'] =
          (_transactionStats['total_completed'] ?? 0) + 1;
      final duration = DateTime.now().difference(startTime);
      _logger.recordPerformance('Transaction.$operationName', duration);

      await _logger.logInfo(
        operation: 'TransactionManager.success',
        message: 'Transaction completed successfully: $operationName',
        context: {'duration_ms': duration.inMilliseconds},
      );
      return result;
    } catch (e, s) {
      _transactionStats['total_failed'] =
          (_transactionStats['total_failed'] ?? 0) + 1;
      final duration = DateTime.now().difference(startTime);
      await _logger.logError(
        operation: 'TransactionManager.failure',
        error: e,
        stackTrace: s,
        context: {
          'operation_name': operationName,
          'duration_ms': duration.inMilliseconds
        },
      );
      // Re-throw as a domain-specific exception
      throw DatabaseOperationException(
          'Transaction failed for $operationName: $e');
    }
  }

  /// Executes a transaction with an explicit savepoint.
  Future<T> executeWithSavepoint<T>(
    String operationName,
    Future<T> Function(dynamic txn, String savepoint) operation,
  ) async {
    // This is a simplified version. Drift's transaction block doesn't directly
    // expose savepoints in a simple way. This would require a more complex setup
    // using custom statements inside the transaction. For now, we'll just run
    // it as a regular transaction.
    return executeTransaction(operationName, (txn) async {
      // A real implementation would be:
      // await txn.customStatement('SAVEPOINT my_savepoint');
      // try {
      //   final result = await operation(txn, 'my_savepoint');
      //   await txn.customStatement('RELEASE SAVEPOINT my_savepoint');
      //   return result;
      // } catch (e) {
      //   await txn.customStatement('ROLLBACK TO SAVEPOINT my_savepoint');
      //   rethrow;
      // }
      return await operation(txn, 'dummy_savepoint');
    });
  }

  /// Force cleanup of any potentially stuck transactions (conceptual).
  Future<void> forceCleanupTransactions() async {
    // In a real-world scenario, this might involve checking for long-running
    // transactions and logging warnings. With Drift's managed transactions,
    // this is less of a manual concern.
    await _logger.logInfo(
      operation: 'TransactionManager.cleanup',
      message: 'Forcing cleanup check for transactions.',
    );
  }

  /// Executes a read-only database operation within a transaction.
  Future<T> executeReadOnlyTransaction<T>(
    String operationName,
    Future<T> Function(dynamic txn) operation,
  ) async {
    // For read-only operations, we can use the same transaction mechanism
    // but with additional logging to indicate it's read-only
    await _logger.logInfo(
      operation: 'TransactionManager.readOnly',
      message: 'Starting read-only transaction: $operationName',
    );

    return executeTransaction(operationName, operation);
  }

  /// Get statistics about transactions.
  Map<String, int> getTransactionStatistics() {
    return Map.unmodifiable(_transactionStats);
  }
}
