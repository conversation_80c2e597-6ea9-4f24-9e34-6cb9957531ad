import 'dart:developer' as developer;

/// Mixin that provides error handling capabilities to BLoCs and other classes
mixin ErrorHandlingMixin {
  /// Handles and logs errors with context information
  Future<void> handleError(
    String operation,
    Object error, {
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    developer.log(
      'Error in $operation: ${error.toString()}',
      name: '${runtimeType.toString()}_Error',
      level: 1000, // Error level
      error: error,
      stackTrace: stackTrace,
    );

    if (context != null && context.isNotEmpty) {
      developer.log(
        'Error context: ${context.toString()}',
        name: '${runtimeType.toString()}_Context',
        level: 1000,
      );
    }
  }



  /// Logs informational messages for operations (CalendarBloc compatible signature)
  Future<void> logInfo({
    required String operation,
    required String message,
    Map<String, dynamic>? context,
  }) async {
    developer.log(
      '$operation: $message',
      name: '${runtimeType.toString()}_Info',
      level: 800, // Info level
    );

    if (context != null && context.isNotEmpty) {
      developer.log(
        'Context: ${context.toString()}',
        name: '${runtimeType.toString()}_Context',
        level: 800,
      );
    }
  }

  /// Logs error messages with detailed information (CalendarBloc compatible signature)
  Future<void> logError({
    required String operation,
    required Exception exception,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) async {
    developer.log(
      'Error in $operation: ${exception.toString()}',
      name: '${runtimeType.toString()}_Error',
      level: 1000, // Error level
      error: exception,
      stackTrace: stackTrace,
    );

    if (context != null && context.isNotEmpty) {
      developer.log(
        'Error context: ${context.toString()}',
        name: '${runtimeType.toString()}_Context',
        level: 1000,
      );
    }
  }

  /// Records performance metrics for operations (CalendarBloc compatible signature)
  void recordPerformance(String operation, int durationMs) {
    developer.log(
      'Performance: $operation completed in ${durationMs}ms',
      name: '${runtimeType.toString()}_Performance',
      level: 500, // Debug level
    );

    // Log warning for slow operations (>1000ms)
    if (durationMs > 1000) {
      developer.log(
        'Slow operation detected: $operation took ${durationMs}ms',
        name: '${runtimeType.toString()}_SlowOp',
        level: 900, // Warning level
      );
    }
  }

  /// Handles recoverable errors with retry logic
  Future<T?> handleRecoverableError<T>(
    String operation,
    Future<T> Function() action, {
    int maxRetries = 3,
    Duration retryDelay = const Duration(milliseconds: 500),
  }) async {
    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await action();
      } catch (error, stackTrace) {
        if (attempt == maxRetries) {
          await logError(
            operation: '$operation.finalAttempt',
            exception: error is Exception ? error : Exception(error.toString()),
            stackTrace: stackTrace,
            context: {'attempt': attempt, 'maxRetries': maxRetries},
          );
          rethrow;
        }

        await logInfo(
          operation: '$operation.retry',
          message: 'Retrying after error (attempt $attempt/$maxRetries)',
          context: {
            'error': error.toString(),
            'attempt': attempt,
            'nextRetryIn': retryDelay.inMilliseconds,
          },
        );

        await Future.delayed(retryDelay);
      }
    }
    return null;
  }
}