import 'package:injectable/injectable.dart';

import '../../domain/domain.dart';
import '../datasources/local_database.dart';
import '../validation/data_validators.dart';
import '../validation/input_sanitizer.dart';

import 'exceptions/repository_exception_handler.dart';
import 'mappers/task_mapper.dart';

import '../error_handling/error_logger.dart';
import '../error_handling/transaction_manager.dart';
import 'exceptions/repository_exceptions.dart';

/// TaskRepository的具体实现
@LazySingleton(as: TaskRepository)
class TaskRepositoryImpl implements TaskRepository {
  final LocalDatabase _database;
  final ErrorLogger _logger = ErrorLogger.instance;
  late final TransactionManager _transactionManager;

  /// Cache for frequently accessed tasks
  final Map<String, dynamic> _taskCache = {};

  /// Maximum cache size to prevent memory issues
  static const int _maxCacheSize = 1000;

  TaskRepositoryImpl(this._database) {
    _transactionManager = TransactionManager(_database);
  }

  @override
  Future<void> createTask(Task task) async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'createTask',
      () async {
        // Sanitize and validate input
        final sanitizedTask = InputSanitizer.sanitizeTask(task);
        TaskValidationService.validateTask(sanitizedTask);

        // Additional data validation
        final validation = DataValidators.validateTaskData(
            TaskMapper.toTaskData(sanitizedTask));
        validation.throwIfInvalid();
        validation.logWarnings();

        // Log operation start
        await _logger.logInfo(
          operation: 'createTask',
          message: 'Starting task creation',
          context: {
            'task_id': sanitizedTask.id,
            'has_subtasks': sanitizedTask.subtasks.isNotEmpty,
            'subtask_count': sanitizedTask.subtasks.length,
          },
        );

        // Execute with enhanced transaction management
        await _transactionManager.executeTransaction(
          'createTask',
          (txn) async {
            // Convert to database format
            final taskCompanion =
                RepositoryExceptionHandler.handleDataTransformation(
              'task_to_companion',
              () => TaskMapper.toTasksCompanion(sanitizedTask),
            );

            await txn.into(_database.tasks).insert(taskCompanion);

            // Create subtasks with validation
            if (sanitizedTask.subtasks.isNotEmpty) {
              // Validate subtask hierarchy
              final hierarchyValidation = DataValidators.validateTaskHierarchy(
                TaskMapper.toTaskData(sanitizedTask),
                sanitizedTask.subtasks.map(TaskMapper.toSubTaskData).toList(),
              );
              hierarchyValidation.throwIfInvalid();

              final subtaskCompanions = sanitizedTask.subtasks
                  .map((subtask) => TaskMapper.toSubTasksCompanion(subtask))
                  .toList();

              await txn.insertSubTasks(subtaskCompanions);
            }
          },
        );

        // Update cache
        _updateCache(sanitizedTask.id, sanitizedTask);

        // Log successful completion
        await _logger.logInfo(
          operation: 'createTask.success',
          message: 'Task created successfully',
          context: {
            'task_id': sanitizedTask.id,
            'cache_size': _taskCache.length,
          },
        );
      },
    );
  }

  @override
  Future<void> updateTask(Task task) async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'updateTask',
      () async {
        // Sanitize and validate input
        final sanitizedTask = InputSanitizer.sanitizeTask(task);
        TaskValidationService.validateTask(sanitizedTask);

        // Additional data validation
        final validation = DataValidators.validateTaskData(
            TaskMapper.toTaskData(sanitizedTask));
        validation.throwIfInvalid();
        validation.logWarnings();

        // Log operation start
        await _logger.logInfo(
          operation: 'updateTask',
          message: 'Starting task update',
          context: {
            'task_id': sanitizedTask.id,
            'has_subtasks': sanitizedTask.subtasks.isNotEmpty,
            'subtask_count': sanitizedTask.subtasks.length,
          },
        );

        // Check if task exists
        final existingTask = await _database.getTaskById(sanitizedTask.id);
        if (existingTask == null) {
          throw TaskNotFoundException(sanitizedTask.id);
        }

        // Execute with enhanced transaction management and savepoints
        await _transactionManager.executeWithSavepoint(
          'updateTask',
          (txn, savepoint) async {
            // Convert to database format
            final taskCompanion =
                RepositoryExceptionHandler.handleDataTransformation(
              'task_to_companion_update',
              () => TaskMapper.toTasksCompanion(sanitizedTask),
            );

            final updatedRows = await txn.update(_database.tasks).replace(taskCompanion);
            if (updatedRows == 0) {
              throw const DatabaseOperationException('Failed to update task');
            }

            // Handle subtasks efficiently with validation
            if (sanitizedTask.subtasks.isNotEmpty) {
              final hierarchyValidation = DataValidators.validateTaskHierarchy(
                TaskMapper.toTaskData(sanitizedTask),
                sanitizedTask.subtasks.map(TaskMapper.toSubTaskData).toList(),
              );
              hierarchyValidation.throwIfInvalid();
            }

            await _updateSubtasks(
                sanitizedTask.id, sanitizedTask.subtasks, txn);
          },
        );

        // Update cache
        _updateCache(sanitizedTask.id, sanitizedTask);

        // Log successful completion
        await _logger.logInfo(
          operation: 'updateTask.success',
          message: 'Task updated successfully',
          context: {
            'task_id': sanitizedTask.id,
            'cache_size': _taskCache.length,
          },
        );
      },
    );
  }

  @override
  Future<void> deleteTask(String taskId) async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'deleteTask',
      () async {
        // Sanitize task ID
        final sanitizedTaskId = InputSanitizer.sanitizeTaskId(taskId);

        // Log operation start
        await _logger.logInfo(
          operation: 'deleteTask',
          message: 'Starting task deletion',
          context: {'task_id': sanitizedTaskId},
        );

        // Check if task exists
        final existingTask = await _database.getTaskById(sanitizedTaskId);
        if (existingTask == null) {
          throw TaskNotFoundException(sanitizedTaskId);
        }

        // Execute with transaction management
        await _database.transaction(() async {
          // Delete with cascade (subtasks will be deleted automatically)
          final deletedRows = await (_database.delete(_database.tasks)
              ..where((tbl) => tbl.id.equals(sanitizedTaskId)))
              .go();
          if (deletedRows == 0) {
            throw const DatabaseOperationException('Failed to delete task');
          }
        });

        // Remove from cache
        _removeFromCache(sanitizedTaskId);

        // Log successful completion
        await _logger.logInfo(
          operation: 'deleteTask.success',
          message: 'Task deleted successfully',
          context: {
            'task_id': sanitizedTaskId,
            'cache_size': _taskCache.length,
          },
        );
      },
    );
  }

  @override
  Stream<List<Task>> watchTasksByDate(DateTime date) {
    final stream =
        _database.watchTasksByDate(date).asyncMap((taskDataList) async {
      return await RepositoryExceptionHandler.handleDatabaseOperation(
        'transform_tasks_by_date',
        () async {
          // Use efficient batch loading for subtasks
          final tasks = await _batchLoadTasksWithSubtasks(taskDataList);

          // Update cache for frequently accessed tasks
          for (final task in tasks) {
            _updateCache(task.id, task);
          }

          return tasks;
        },
      );
    });
    return RepositoryExceptionHandler.handleStreamOperation(
        'watchTasksByDate', stream);
  }

  @override
  Stream<List<Task>> watchTasksForMonth({
    required int year,
    required int month,
  }) {
    final stream = _database
        .watchTasksForMonth(year: year, month: month)
        .asyncMap((taskDataList) async {
      return await RepositoryExceptionHandler.handleDatabaseOperation(
        'transform_tasks_for_month',
        () async {
          // Use efficient batch loading for subtasks
          final tasks = await _batchLoadTasksWithSubtasks(taskDataList);

          // Update cache for frequently accessed tasks
          for (final task in tasks) {
            _updateCache(task.id, task);
          }

          return tasks;
        },
      );
    });
    return RepositoryExceptionHandler.handleStreamOperation(
        'watchTasksForMonth', stream);
  }

  @override
  Stream<Map<DateTime, int>> watchTaskLoadForMonth({
    required int year,
    required int month,
  }) {
    final stream = _database
        .watchTasksForMonth(year: year, month: month)
        .map((taskDataList) {
      return RepositoryExceptionHandler.handleDataTransformation(
        'calculate_task_load',
        () {
          // Use domain service for consistent calculation
          final tasksByDate = <DateTime, List<Task>>{};

          for (final taskData in taskDataList) {
            final dateKey = DateTime(
              taskData.dueDate.year,
              taskData.dueDate.month,
              taskData.dueDate.day,
            );

            // Convert to domain model for calculation
            final task = Task(
              id: taskData.id,
              title: taskData.title,
              notes: taskData.notes,
              creationDate: taskData.creationDate,
              dueDate: taskData.dueDate,
              isCompleted: taskData.isCompleted,
              completionDate: taskData.completionDate,
              priority: taskData.priority,
              subtasks: [], // Subtasks not needed for load calculation
            );

            tasksByDate.putIfAbsent(dateKey, () => []).add(task);
          }

          return TaskLoadCalculationService.calculateTaskLoadForDates(
              tasksByDate);
        },
      );
    });
    return RepositoryExceptionHandler.handleStreamOperation(
        'watchTaskLoadForMonth', stream);
  }

  // ==================== ADDITIONAL REPOSITORY METHODS ====================

  /// Get a single task by ID with caching
  @override
  Future<Task?> getTaskById(String taskId) async {
    // Check cache first
    if (_taskCache.containsKey(taskId)) {
      return _taskCache[taskId];
    }

    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'getTaskById',
      () async {
        final taskData = await _database.getTaskById(taskId);
        if (taskData == null) return null;

        final subtasks = await _database.getSubTasksForTask(taskId);
        final task = TaskMapper.fromTaskData(taskData, subtasks);

        // Update cache
        _updateCache(taskId, task);

        return task;
      },
    );
  }

  /// Get tasks by priority with pagination
  Future<List<Task>> getTasksByPriority(
    Priority priority, {
    int limit = 50,
    int offset = 0,
  }) async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'getTasksByPriority',
      () async {
        // Convert Priority enum to database string value
        String priorityValue;
        switch (priority) {
          case Priority.urgentImportant:
            priorityValue = 'urgent_important';
            break;
          case Priority.importantNotUrgent:
            priorityValue = 'important_not_urgent';
            break;
          case Priority.urgentNotImportant:
            priorityValue = 'urgent_not_important';
            break;
          case Priority.notUrgentNotImportant:
            priorityValue = 'not_urgent_not_important';
            break;
        }

        final taskDataList = await _database.getTasksByPriority(priorityValue);
        return await _batchLoadTasksWithSubtasks(taskDataList);
      },
    );
  }

  /// Search tasks with full-text search
  @override
  Future<List<Task>> searchTasks(String query) async {
    if (query.trim().isEmpty) return [];

    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'searchTasks',
      () async {
        // Sanitize and validate search query
        final sanitizedQuery = InputSanitizer.sanitizeSearchQuery(query);

        // Validate search query
        final validation = DataValidators.validateSearchQuery(sanitizedQuery);
        validation.throwIfInvalid();
        validation.logWarnings();

        // Log search operation
        await _logger.logInfo(
          operation: 'searchTasks',
          message: 'Executing search query',
          context: {
            'query_length': sanitizedQuery.length,
            'original_query_length': query.length,
          },
        );

        final startTime = DateTime.now();
        final taskDataList = await _database.searchTasks(sanitizedQuery);
        final searchDuration = DateTime.now().difference(startTime);

        // Log search performance
        await _logger.logInfo(
          operation: 'searchTasks.performance',
          message: 'Search completed',
          context: {
            'results_count': taskDataList.length,
            'search_duration_ms': searchDuration.inMilliseconds,
            'query_length': sanitizedQuery.length,
          },
        );

        // Performance warning for slow searches
        if (searchDuration.inMilliseconds > 1000) {
          await _logger.logWarning(
            operation: 'searchTasks.slow_query',
            message: 'Search query took longer than expected',
            context: {
              'duration_ms': searchDuration.inMilliseconds,
              'results_count': taskDataList.length,
            },
          );
        }

        return await _batchLoadTasksWithSubtasks(taskDataList);
      },
    );
  }

  /// Get overdue tasks
  Future<List<Task>> getOverdueTasks() async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'getOverdueTasks',
      () async {
        final taskDataList = await _database.getOverdueTasks();
        return await _batchLoadTasksWithSubtasks(taskDataList);
      },
    );
  }

  /// Batch create multiple tasks
  Future<void> createTasks(List<Task> tasks) async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'createTasks',
      () async {
        if (tasks.isEmpty) {
          throw const DataValidationException('Cannot create empty task list');
        }

        // Sanitize all tasks first
        final sanitizedTasks = tasks.map(InputSanitizer.sanitizeTask).toList();

        // Validate batch operation
        final batchValidation = DataValidators.validateBatchOperation(
          sanitizedTasks.map((task) => task.id).toList(),
          maxBatchSize: 500, // Reasonable limit for batch operations
        );
        batchValidation.throwIfInvalid();
        batchValidation.logWarnings();

        // Validate all tasks individually
        for (final task in sanitizedTasks) {
          TaskValidationService.validateTask(task);
        }

        // Log batch operation start
        await _logger.logInfo(
          operation: 'createTasks',
          message: 'Starting batch task creation',
          context: {
            'batch_size': sanitizedTasks.length,
            'total_subtasks': sanitizedTasks.fold(
                0, (sum, task) => sum + task.subtasks.length),
          },
        );

        // Execute with enhanced transaction management (long-running for large batches)
        await _transactionManager.executeTransaction(
          'createTasks',
          (txn) async {
            // Convert to database format
            final taskCompanions = sanitizedTasks
                .map((task) => TaskMapper.toTasksCompanion(task))
                .toList();

            await txn.batch((batch) {
              batch.insertAll(_database.tasks, taskCompanions);
            });

            // Insert all subtasks with validation
            for (final task in sanitizedTasks) {
              if (task.subtasks.isNotEmpty) {
                // Validate subtask hierarchy for each task
                final hierarchyValidation =
                    DataValidators.validateTaskHierarchy(
                  TaskMapper.toTaskData(task),
                  task.subtasks.map(TaskMapper.toSubTaskData).toList(),
                );
                hierarchyValidation.throwIfInvalid();

                final subtaskCompanions = task.subtasks
                    .map((subtask) => TaskMapper.toSubTasksCompanion(subtask))
                    .toList();

                await txn.insertSubTasks(subtaskCompanions);
              }
            }
          },
          isLongRunning: sanitizedTasks.length > 100,
        );

        // Update cache efficiently
        for (final task in sanitizedTasks) {
          _updateCache(task.id, task);
        }

        // Log successful completion
        await _logger.logInfo(
          operation: 'createTasks.success',
          message: 'Batch task creation completed',
          context: {
            'created_count': sanitizedTasks.length,
            'cache_size': _taskCache.length,
          },
        );
      },
    );
  }

  /// Batch delete multiple tasks
  @override
  Future<void> deleteTasks(List<String> taskIds) async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'deleteTasks',
      () async {
        if (taskIds.isEmpty) {
          throw const DataValidationException(
              'Cannot delete empty task ID list');
        }

        // Sanitize all task IDs
        final sanitizedTaskIds =
            taskIds.map(InputSanitizer.sanitizeTaskId).toList();

        // Validate batch size
        if (sanitizedTaskIds.length > 1000) {
          throw const DataValidationException(
              'Batch delete size cannot exceed 1000 tasks');
        }

        // Log batch operation start
        await _logger.logInfo(
          operation: 'deleteTasks',
          message: 'Starting batch task deletion',
          context: {'batch_size': sanitizedTaskIds.length},
        );

        // Execute with transaction management
        int deletedCount = 0;
        await _database.transaction(() async {
          deletedCount = await (_database.delete(_database.tasks)
              ..where((tbl) => tbl.id.isIn(sanitizedTaskIds)))
              .go();

          if (deletedCount != sanitizedTaskIds.length) {
            throw DatabaseOperationException(
                'Some tasks could not be deleted. Expected: ${sanitizedTaskIds.length}, Deleted: $deletedCount');
          }
        });

        // Remove from cache
        for (final taskId in sanitizedTaskIds) {
          _removeFromCache(taskId);
        }

        // Log successful completion
        await _logger.logInfo(
          operation: 'deleteTasks.success',
          message: 'Batch task deletion completed',
          context: {
            'deleted_count': deletedCount,
            'cache_size': _taskCache.length,
          },
        );
      },
    );
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /// Efficiently load tasks with their subtasks in batch
  Future<List<Task>> _batchLoadTasksWithSubtasks(List<TaskData> taskDataList,
      [dynamic db]) async {
    final database = db as LocalDatabase? ?? _database;
    if (taskDataList.isEmpty) return [];

    // Get all subtasks for all tasks in one query (more efficient)
    final allSubtasks = <String, List<SubTaskData>>{};

    for (final taskData in taskDataList) {
      final subtasks = await database.getSubTasksForTask(taskData.id);
      allSubtasks[taskData.id] = subtasks;
    }

    // Convert to domain models
    return taskDataList.map((taskData) {
      final subtasks = allSubtasks[taskData.id] ?? [];
      return TaskMapper.fromTaskData(taskData, subtasks);
    }).toList();
  }

  /// Update subtasks efficiently (only change what's needed)
  Future<void> _updateSubtasks(String taskId, List<SubTask> newSubtasks,
      [dynamic db]) async {
    final database = db as LocalDatabase? ?? _database;
    final existingSubtasks = await database.getSubTasksForTask(taskId);
    final existingIds = existingSubtasks.map((s) => s.id).toSet();
    final newIds = newSubtasks.map((s) => s.id).toSet();

    // Delete removed subtasks
    final toDelete = existingIds.difference(newIds);
    for (final subtaskId in toDelete) {
      await database.deleteSubTask(subtaskId);
    }

    // Add new subtasks
    final toAdd = newIds.difference(existingIds);
    final subtasksToAdd = newSubtasks.where((s) => toAdd.contains(s.id));

    for (final subtask in subtasksToAdd) {
      final companion = TaskMapper.toSubTasksCompanion(subtask);
      await database.insertSubTask(companion);
    }

    // Update existing subtasks
    final toUpdate = newIds.intersection(existingIds);
    for (final subtaskId in toUpdate) {
      final newSubtask = newSubtasks.firstWhere((s) => s.id == subtaskId);
      final companion = TaskMapper.toSubTasksCompanion(newSubtask);
      await database.updateSubTask(companion);
    }
  }

  /// Update task cache with size limit
  void _updateCache(String taskId, Task task) {
    // Remove oldest entries if cache is full
    if (_taskCache.length >= _maxCacheSize) {
      final oldestKey = _taskCache.keys.first;
      _taskCache.remove(oldestKey);
    }

    _taskCache[taskId] = task;
  }

  /// Update task list cache with size limit
  void _updateTaskListCache(String cacheKey, List<Task> tasks) {
    // Remove oldest entries if cache is full
    if (_taskCache.length >= _maxCacheSize) {
      final oldestKey = _taskCache.keys.first;
      _taskCache.remove(oldestKey);
    }

    _taskCache[cacheKey] = tasks;
  }

  /// Remove task from cache
  void _removeFromCache(String taskId) {
    _taskCache.remove(taskId);
  }

  /// Clear entire cache (useful for testing or memory management)
  void clearCache() {
    _taskCache.clear();
  }

  /// Get cache statistics for monitoring
  Map<String, int> getCacheStats() {
    return {
      'size': _taskCache.length,
      'max_size': _maxCacheSize,
      'hit_ratio_percent': _taskCache.isEmpty
          ? 0
          : ((_taskCache.length / _maxCacheSize) * 100).round(),
    };
  }

  /// Get comprehensive repository health status
  Future<Map<String, dynamic>> getHealthStatus() async {
    try {
      final startTime = DateTime.now();

      // Test basic database connectivity
      await _database.customSelect('SELECT 1').get();

      final dbResponseTime = DateTime.now().difference(startTime);

      // Get transaction statistics
      final transactionStats = _transactionManager.getTransactionStatistics();

      // Get error statistics
      final errorStats = _logger.getErrorStatistics();

      // Get cache statistics
      final cacheStats = getCacheStats();

      // Test a simple query performance
      final queryStartTime = DateTime.now();
      await _database.customSelect('SELECT COUNT(*) FROM tasks').get();
      final queryResponseTime = DateTime.now().difference(queryStartTime);

      return {
        'status': 'healthy',
        'timestamp': DateTime.now().toIso8601String(),
        'database': {
          'connection_status': 'connected',
          'response_time_ms': dbResponseTime.inMilliseconds,
          'query_performance_ms': queryResponseTime.inMilliseconds,
        },
        'transactions': transactionStats,
        'errors': errorStats,
        'cache': cacheStats,
        'performance': {
          'db_response_healthy': dbResponseTime.inMilliseconds < 100,
          'query_performance_healthy': queryResponseTime.inMilliseconds < 50,
          'error_rate_healthy':
              ((errorStats['error_counts'] as Map)['total_errors_last_hour'] ??
                      0) <
                  10,
        },
      };
    } catch (e, s) {
      await _logger.logError(
        operation: 'getHealthStatus',
        error: e,
        stackTrace: s,
        severity: ErrorSeverity.error,
      );

      return {
        'status': 'unhealthy',
        'timestamp': DateTime.now().toIso8601String(),
        'error': e.toString(),
        'cache': getCacheStats(),
      };
    }
  }

  @override
  Future<List<Task>> getTasksForDate(DateTime date) async {
    try {
      // Log operation start
      await _logger.logInfo(
        operation: 'getTasksForDate',
        message: 'Getting tasks for specific date',
        context: {
          'date': date.toIso8601String(),
        },
      );

      // Check cache first
      final cacheKey = '${date.year}-${date.month}-${date.day}';
      if (_taskCache.containsKey(cacheKey)) {
        final cachedTasks = _taskCache[cacheKey];
        if (cachedTasks is List<Task>) {
          return cachedTasks;
        }
      }

      // Query database directly with proper date range
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      final taskDataList =
          await _database.getTasksDueInRange(startOfDay, endOfDay);

      // Convert TaskData to Task with subtasks
      final tasks = <Task>[];
      for (final taskData in taskDataList) {
        final subtasks = await _database.getSubTasksForTask(taskData.id);
        final task = TaskMapper.fromTaskData(taskData, subtasks);
        tasks.add(task);
      }

      // Cache the result
      _updateTaskListCache(cacheKey, tasks);

      return tasks;
    } catch (e, s) {
      await _logger.logError(
        operation: 'getTasksForDate',
        error: e,
        stackTrace: s,
        severity: ErrorSeverity.error,
      );
      throw DatabaseOperationException('Failed to get tasks for date: $e');
    }
  }

  @override
  Future<List<Task>> getAllTasks() async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'getAllTasks',
      () async {
        // Log operation start
        await _logger.logInfo(
          operation: 'getAllTasks',
          message: 'Getting all tasks',
        );

        final taskDataList = await _database.getAllTasks();
        return await _batchLoadTasksWithSubtasks(taskDataList);
      },
    );
  }

  /// Perform repository maintenance tasks
  Future<Map<String, dynamic>> performMaintenance() async {
    final results = <String, dynamic>{};

    try {
      // Clear cache if it's getting too large
      if (_taskCache.length > _maxCacheSize * 0.9) {
        final oldSize = _taskCache.length;
        clearCache();
        results['cache_cleared'] = {
          'old_size': oldSize,
          'new_size': _taskCache.length,
        };
      }

      // Force cleanup stuck transactions
      await _transactionManager.forceCleanupTransactions();
      results['transaction_cleanup'] = 'completed';

      // Analyze database for optimization opportunities
      final analyzeStart = DateTime.now();
      await _database.customStatement('ANALYZE');
      final analyzeDuration = DateTime.now().difference(analyzeStart);

      results['database_analysis'] = {
        'completed': true,
        'duration_ms': analyzeDuration.inMilliseconds,
      };

      // Vacuum database if needed (this should be done carefully in production)
      // await _database.customStatement('VACUUM');

      results['maintenance_status'] = 'completed';
      results['timestamp'] = DateTime.now().toIso8601String();

      await _logger.logInfo(
        operation: 'performMaintenance',
        message: 'Repository maintenance completed',
        context: results,
      );
    } catch (e, s) {
      await _logger.logError(
        operation: 'performMaintenance',
        error: e,
        stackTrace: s,
        severity: ErrorSeverity.warning,
      );

      results['maintenance_status'] = 'failed';
      results['error'] = e.toString();
    }

    return results;
  }
}
