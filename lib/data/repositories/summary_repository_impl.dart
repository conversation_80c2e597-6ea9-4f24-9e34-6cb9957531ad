import 'package:injectable/injectable.dart';
import '../../domain/domain.dart';
import '../datasources/local_database.dart';
import 'mappers/task_mapper.dart';

import '../error_handling/repository_exception_handler.dart';
import '../error_handling/error_logger.dart';
import '../error_handling/transaction_manager.dart';
import '../validation/data_validators.dart';
import 'exceptions/repository_exceptions.dart';

/// SummaryRepository的具体实现
@LazySingleton(as: SummaryRepository)
class SummaryRepositoryImpl implements SummaryRepository {
  final LocalDatabase _database;
  final ErrorLogger _logger = ErrorLogger.instance;
  late final TransactionManager _transactionManager;

  SummaryRepositoryImpl(this._database) {
    _transactionManager = TransactionManager(_database);
  }

  @override
  Future<SummaryReport> getMonthlySummary({
    required int year,
    required int month,
  }) async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'getMonthlySummary',
      () async {
        // Validate input parameters
        if (year < 1900 || year > 2100) {
          throw const DataValidationException(
              'Year must be between 1900 and 2100');
        }
        if (month < 1 || month > 12) {
          throw const DataValidationException('Month must be between 1 and 12');
        }

        final startOfMonth = DateTime(year, month, 1);
        final endOfMonth = DateTime(year, month + 1, 1);

        // Validate date range
        final dateValidation =
            DataValidators.validateDateRange(startOfMonth, endOfMonth);
        dateValidation.throwIfInvalid();
        dateValidation.logWarnings();

        // Log operation start
        await _logger.logInfo(
          operation: 'getMonthlySummary',
          message: 'Generating monthly summary',
          context: {
            'year': year,
            'month': month,
            'date_range':
                '${startOfMonth.toIso8601String()} to ${endOfMonth.toIso8601String()}',
          },
        );

        // Execute with read-only transaction for consistency
        return await _transactionManager.executeReadOnlyTransaction(
          'getMonthlySummary',
          (txn) async {
            final operationStart = DateTime.now();

            // Get tasks created in the month
            final createdTasks = await _database.getTasksCreatedInRange(
                startOfMonth, endOfMonth);

            // Get tasks completed in the month
            final completedTasks = await _database.getCompletedTasksInRange(
                startOfMonth, endOfMonth);

            // Convert to domain models for analysis
            final createdTaskModels =
                await TaskMapper.fromTaskDataList(createdTasks, _database);
            final completedTaskModels =
                await TaskMapper.fromTaskDataList(completedTasks, _database);

            // Use domain services for calculations
            final totalCreated = createdTaskModels.length;
            final totalCompleted = completedTaskModels.length;
            final completionRate =
                TaskLoadCalculationService.calculateCompletionRate(
                    createdTaskModels);

            // Calculate quadrant distribution using domain service
            final quadrantDistribution =
                TaskLoadCalculationService.calculateQuadrantDistribution(
                    createdTaskModels);

            // Get highlights using domain service
            final highlights = HighlightsCalculationService.calculateHighlights(
                completedTaskModels);

            final operationDuration = DateTime.now().difference(operationStart);

            // Log performance metrics
            await _logger.logInfo(
              operation: 'getMonthlySummary.performance',
              message: 'Monthly summary generated',
              context: {
                'year': year,
                'month': month,
                'created_tasks': totalCreated,
                'completed_tasks': totalCompleted,
                'completion_rate': completionRate,
                'duration_ms': operationDuration.inMilliseconds,
              },
            );

            // Performance warning for slow operations
            if (operationDuration.inMilliseconds > 2000) {
              await _logger.logWarning(
                operation: 'getMonthlySummary.slow_operation',
                message: 'Monthly summary generation took longer than expected',
                context: {
                  'duration_ms': operationDuration.inMilliseconds,
                  'created_tasks': totalCreated,
                  'completed_tasks': totalCompleted,
                },
              );
            }

            return SummaryReport(
              period: '$year年$month月',
              totalTasksCreated: totalCreated,
              totalTasksCompleted: totalCompleted,
              completionRate: (completionRate * 100), // Convert to percentage
              quadrantDistribution: quadrantDistribution,
              highlights: highlights,
            );
          },
        );
      },
    );
  }

  @override
  Future<SummaryReport> getYearlySummary({required int year}) async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'getYearlySummary',
      () async {
        final startOfYear = DateTime(year, 1, 1);
        final endOfYear = DateTime(year + 1, 1, 1);

        // Get tasks created in the year
        final createdTasks =
            await _database.getTasksCreatedInRange(startOfYear, endOfYear);

        // Get tasks completed in the year
        final completedTasks =
            await _database.getCompletedTasksInRange(startOfYear, endOfYear);

        // Convert to domain models for analysis
        final createdTaskModels =
            await TaskMapper.fromTaskDataList(createdTasks, _database);
        final completedTaskModels =
            await TaskMapper.fromTaskDataList(completedTasks, _database);

        // Use domain services for calculations
        final totalCreated = createdTaskModels.length;
        final totalCompleted = completedTaskModels.length;
        final completionRate =
            TaskLoadCalculationService.calculateCompletionRate(
                createdTaskModels);

        // Calculate quadrant distribution using domain service
        final quadrantDistribution =
            TaskLoadCalculationService.calculateQuadrantDistribution(
                createdTaskModels);

        // Get highlights using domain service
        final highlights = HighlightsCalculationService.calculateHighlights(
            completedTaskModels);

        return SummaryReport(
          period: '$year年',
          totalTasksCreated: totalCreated,
          totalTasksCompleted: totalCompleted,
          completionRate: (completionRate * 100), // Convert to percentage
          quadrantDistribution: quadrantDistribution,
          highlights: highlights,
        );
      },
    );
  }

  // ==================== ADDITIONAL SUMMARY METHODS ====================

  /// Get detailed monthly statistics
  Future<Map<String, dynamic>> getDetailedMonthlyStats({
    required int year,
    required int month,
  }) async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'getDetailedMonthlyStats',
      () async {
        final startOfMonth = DateTime(year, month, 1);
        final endOfMonth = DateTime(year, month + 1, 1);

        // Get comprehensive statistics from database
        final stats = await _database.getTaskStatistics();
        final priorityDistribution = await _database.getPriorityDistribution();
        final dailyLoad =
            await _database.getDailyTaskLoad(startOfMonth, endOfMonth);

        return {
          'basic_stats': stats,
          'priority_distribution': priorityDistribution,
          'daily_task_load': dailyLoad,
          'period': '$year年$month月',
        };
      },
    );
  }

  /// Get productivity trends over time
  Future<List<Map<String, dynamic>>> getProductivityTrends({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'getProductivityTrends',
      () async {
        final trends = <Map<String, dynamic>>[];

        // Calculate monthly trends
        var currentDate = DateTime(startDate.year, startDate.month, 1);
        final endMonth = DateTime(endDate.year, endDate.month, 1);

        while (currentDate.isBefore(endMonth) ||
            currentDate.isAtSameMomentAs(endMonth)) {
          final monthStart = currentDate;
          final monthEnd = DateTime(currentDate.year, currentDate.month + 1, 1);

          final createdTasks =
              await _database.getTasksCreatedInRange(monthStart, monthEnd);
          final completedTasks =
              await _database.getCompletedTasksInRange(monthStart, monthEnd);

          final createdModels =
              await TaskMapper.fromTaskDataList(createdTasks, _database);
          final completedModels =
              await TaskMapper.fromTaskDataList(completedTasks, _database);

          final completionRate =
              TaskLoadCalculationService.calculateCompletionRate(createdModels);

          trends.add({
            'period':
                '${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}',
            'tasks_created': createdModels.length,
            'tasks_completed': completedModels.length,
            'completion_rate': completionRate,
            'quadrant_distribution':
                TaskLoadCalculationService.calculateQuadrantDistribution(
                    createdModels),
          });

          currentDate = DateTime(currentDate.year, currentDate.month + 1, 1);
        }

        return trends;
      },
    );
  }

  /// Get task completion patterns by day of week
  Future<Map<String, double>> getCompletionPatternsByDayOfWeek() async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'getCompletionPatternsByDayOfWeek',
      () async {
        // Get all completed tasks
        final now = DateTime.now();
        final threeMonthsAgo = DateTime(now.year, now.month - 3, now.day);

        final completedTasks =
            await _database.getCompletedTasksInRange(threeMonthsAgo, now);

        // Group by day of week
        final dayOfWeekCounts = <int, int>{};
        for (final task in completedTasks) {
          if (task.completionDate != null) {
            final dayOfWeek = task.completionDate!.weekday;
            dayOfWeekCounts[dayOfWeek] = (dayOfWeekCounts[dayOfWeek] ?? 0) + 1;
          }
        }

        // Convert to percentages
        final total =
            dayOfWeekCounts.values.fold(0, (sum, count) => sum + count);
        final patterns = <String, double>{};

        const dayNames = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday'
        ];

        for (int i = 1; i <= 7; i++) {
          final count = dayOfWeekCounts[i] ?? 0;
          final percentage = total > 0 ? (count / total) * 100 : 0.0;
          patterns[dayNames[i - 1]] = percentage;
        }

        return patterns;
      },
    );
  }

  /// Get performance metrics for dashboard
  Future<Map<String, dynamic>> getPerformanceMetrics() async {
    return await RepositoryExceptionHandler.handleDatabaseOperation(
      'getPerformanceMetrics',
      () async {
        final now = DateTime.now();

        final lastMonth = DateTime(now.year, now.month - 1, 1);

        // Current month stats
        final currentMonthSummary = await getMonthlySummary(
          year: now.year,
          month: now.month,
        );

        // Last month stats for comparison
        final lastMonthSummary = await getMonthlySummary(
          year: lastMonth.year,
          month: lastMonth.month,
        );

        // Calculate trends
        final completionRateTrend = currentMonthSummary.completionRate -
            lastMonthSummary.completionRate;
        final taskCreationTrend = currentMonthSummary.totalTasksCreated -
            lastMonthSummary.totalTasksCreated;

        // Get overdue tasks count
        final overdueTasks = await _database.getOverdueTasks();

        return {
          'current_month': {
            'completion_rate': currentMonthSummary.completionRate,
            'tasks_created': currentMonthSummary.totalTasksCreated,
            'tasks_completed': currentMonthSummary.totalTasksCompleted,
          },
          'trends': {
            'completion_rate_change': completionRateTrend,
            'task_creation_change': taskCreationTrend,
          },
          'alerts': {
            'overdue_tasks_count': overdueTasks.length,
          },
          'quadrant_distribution': currentMonthSummary.quadrantDistribution,
        };
      },
    );
  }
}
