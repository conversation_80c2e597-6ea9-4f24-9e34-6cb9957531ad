import 'dart:async';

import '../../../domain/exceptions/domain_exceptions.dart';
import '../../error_handling/error_logger.dart';
import 'repository_exceptions.dart';

/// A utility class to handle exceptions in the repository layer consistently.
class RepositoryExceptionHandler {
  static final _logger = ErrorLogger.instance;

  /// Handles a database operation that returns a Future.
  static Future<T> handleDatabaseOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    try {
      return await operation();
    } on DriftSqlException catch (e, s) {
      await _logger.logError(
        operation: operationName,
        error: e,
        stackTrace: s,
      );
      throw DatabaseOperationException(
          'A database error occurred during $operationName: $e');
    } on DataValidationException catch (e) {
      await _logger.logWarning(
        operation: operationName,
        message: 'Data validation failed: ${e.message}',
      );
      rethrow;
    } on TaskNotFoundException catch (e) {
      await _logger.logWarning(
        operation: operationName,
        message: 'Task not found: ${e.message}',
      );
      rethrow;
    } on SubTaskException catch (e) {
      await _logger.logWarning(
        operation: operationName,
        message: 'Subtask error: ${e.message}',
      );
      rethrow;
    } on DomainException catch (e) {
      await _logger.logWarning(
        operation: operationName,
        message: 'Domain validation failed: ${e.message}',
      );
      rethrow;
    } catch (e, s) {
      await _logger.logError(
        operation: operationName,
        error: e,
        stackTrace: s,
      );
      throw UnknownRepositoryException(
          'An unexpected error occurred during $operationName: $e');
    }
  }

  /// Handles a stream-based database operation.
  static Stream<T> handleStreamOperation<T>(
    String operationName,
    Stream<T> stream,
  ) {
    // Be resilient: log errors but do NOT throw, so UI won't crash on transient DB closures (e.g., during reset/import)
    return stream.handleError((error, stackTrace) async {
      await _logger.logError(
        operation: operationName,
        error: error,
        stackTrace: stackTrace,
      );
      // swallow the error to keep the stream alive; callers can re-subscribe on their side if needed
    });
  }

  /// Handles a data transformation operation.
  static T handleDataTransformation<T>(
    String operationName,
    T Function() transformation,
  ) {
    try {
      return transformation();
    } on Exception catch (e) {
      // We don't log this by default, as it might be an expected part of validation logic.
      // The caller can decide to log if needed.
      throw DataTransformationException(
          'Failed to transform data for $operationName: $e');
    }
  }
}
