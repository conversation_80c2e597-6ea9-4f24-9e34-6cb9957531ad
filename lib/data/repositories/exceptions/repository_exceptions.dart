/// Base class for all repository-related exceptions.
abstract class RepositoryException implements Exception {
  final String message;
  final String code;

  const RepositoryException(this.message, this.code);

  @override
  String toString() => 'RepositoryException ($code): $message';
}

/// Thrown when a database operation fails.
class DatabaseOperationException extends RepositoryException {
  const DatabaseOperationException(String message)
      : super(message, 'DB_OPERATION_ERROR');
}

/// Thrown when data validation fails before a repository operation.
class DataValidationException extends RepositoryException {
  const DataValidationException(String message)
      : super(message, 'DATA_VALIDATION_ERROR');
}

/// Thrown when a data transformation (e.g., mapping) fails.
class DataTransformationException extends RepositoryException {
  const DataTransformationException(String message)
      : super(message, 'DATA_TRANSFORMATION_ERROR');
}

/// Thrown for unexpected errors within the repository layer.
class UnknownRepositoryException extends RepositoryException {
  const UnknownRepositoryException(String message)
      : super(message, 'UNKNOWN_ERROR');
}

/// Thrown when a Drift SQL operation fails.
class DriftSqlException extends RepositoryException {
  const DriftSqlException(String message) : super(message, 'DRIFT_SQL_ERROR');
}
