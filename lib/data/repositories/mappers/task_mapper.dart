import 'package:drift/drift.dart';
import '../../../domain/domain.dart';
import '../../datasources/local_database.dart';

/// Mapper for converting between domain models and database entities
class TaskMapper {
  /// Convert Task domain model to TasksCompanion for database operations
  static TasksCompanion toTasksCompanion(Task task) {
    // Ensure due date is not before creation date
    DateTime? adjustedDueDate = task.dueDate;
    if (adjustedDueDate != null && adjustedDueDate.isBefore(task.creationDate)) {
      adjustedDueDate = task.creationDate;
    }

    return TasksCompanion(
      id: Value(task.id),
      title: Value(task.title),
      notes: Value(task.notes),
      creationDate: Value(task.creationDate),
      dueDate: Value(adjustedDueDate),
      isCompleted: Value(task.isCompleted),
      completionDate: Value(task.completionDate),
      priority: Value(task.priority),
    );
  }

  /// Convert TaskData from database to Task domain model
  static Task fromTaskData(TaskData taskData, List<SubTaskData> subtaskData) {
    final subtasks = subtaskData
        .map((subtask) => fromSubTaskData(subtask))
        .toList();

    return Task(
      id: taskData.id,
      title: taskData.title,
      notes: taskData.notes,
      creationDate: taskData.creationDate,
      dueDate: taskData.dueDate,
      isCompleted: taskData.isCompleted,
      completionDate: taskData.completionDate,
      priority: taskData.priority,
      subtasks: subtasks,
    );
  }

  /// Convert SubTask domain model to SubTasksCompanion for database operations
  static SubTasksCompanion toSubTasksCompanion(SubTask subtask) {
    return SubTasksCompanion(
      id: Value(subtask.id),
      parentTaskId: Value(subtask.parentTaskId),
      title: Value(subtask.title),
      isCompleted: Value(subtask.isCompleted),
    );
  }

  /// Convert SubTaskData from database to SubTask domain model
  static SubTask fromSubTaskData(SubTaskData subtaskData) {
    return SubTask(
      id: subtaskData.id,
      title: subtaskData.title,
      parentTaskId: subtaskData.parentTaskId,
      isCompleted: subtaskData.isCompleted,
    );
  }

  /// Convert list of TaskData to list of Task domain models
  static Future<List<Task>> fromTaskDataList(
    List<TaskData> taskDataList,
    LocalDatabase database,
  ) async {
    final tasks = <Task>[];

    for (final taskData in taskDataList) {
      final subtaskData = await database.getSubTasksForTask(taskData.id);
      final task = fromTaskData(taskData, subtaskData);
      tasks.add(task);
    }

    return tasks;
  }

  /// Convert Task for update operations (only changed fields)
  static TasksCompanion toUpdateCompanion(Task task, {
    String? title,
    String? notes,
    DateTime? dueDate,
    Priority? priority,
    bool? isCompleted,
    DateTime? completionDate,
  }) {
    // Ensure due date is not before creation date
    DateTime? adjustedDueDate = dueDate;
    if (adjustedDueDate != null && adjustedDueDate.isBefore(task.creationDate)) {
      adjustedDueDate = task.creationDate;
    }

    return TasksCompanion(
      id: Value(task.id),
      title: title != null ? Value(title) : const Value.absent(),
      notes: notes != null ? Value(notes) : const Value.absent(),
      dueDate: adjustedDueDate != null ? Value(adjustedDueDate) : const Value.absent(),
      priority: priority != null ? Value(priority) : const Value.absent(),
      isCompleted: isCompleted != null ? Value(isCompleted) : const Value.absent(),
      completionDate: completionDate != null
          ? Value(completionDate)
          : const Value.absent(),
    );
  }

  /// Validate task data before conversion
  static void validateTaskData(TaskData taskData) {
    if (taskData.title.trim().isEmpty) {
      throw const TaskValidationException('Task title cannot be empty');
    }

    // More flexible date validation - allow due date to be on the same day as creation date
    // or slightly before for past due tasks
    final creationDateOnly = DateTime(
      taskData.creationDate.year,
      taskData.creationDate.month,
      taskData.creationDate.day
    );
    final dueDateOnly = DateTime(
      taskData.dueDate.year,
      taskData.dueDate.month,
      taskData.dueDate.day
    );

    // Only throw error if due date is more than one day before creation date
    if (dueDateOnly.isBefore(creationDateOnly.subtract(const Duration(days: 1)))) {
      throw const DateValidationException('Due date cannot be more than one day before creation date');
    }

    if (taskData.isCompleted && taskData.completionDate == null) {
      throw const TaskValidationException('Completed task must have completion date');
    }

    if (!taskData.isCompleted && taskData.completionDate != null) {
      throw const TaskValidationException('Incomplete task cannot have completion date');
    }
  }

  /// Validate subtask data before conversion
  static void validateSubTaskData(SubTaskData subtaskData) {
    if (subtaskData.title.trim().isEmpty) {
      throw const SubTaskException('Subtask title cannot be empty');
    }

    if (subtaskData.parentTaskId.trim().isEmpty) {
      throw const SubTaskException('Subtask must have valid parent task ID');
    }
  }

  /// Convert Task domain model to TaskData for database operations
  static TaskData toTaskData(Task task) {
    return TaskData(
      id: task.id,
      title: task.title,
      notes: task.notes,
      creationDate: task.creationDate,
      dueDate: task.dueDate,
      isCompleted: task.isCompleted,
      completionDate: task.completionDate,
      priority: task.priority,
    );
  }

  /// Convert SubTask domain model to SubTaskData for database operations
  static SubTaskData toSubTaskData(SubTask subtask) {
    return SubTaskData(
      id: subtask.id,
      parentTaskId: subtask.parentTaskId,
      title: subtask.title,
      isCompleted: subtask.isCompleted,
    );
  }

  /// Convert TasksCompanion to TaskData
  static TaskData toTaskDataFromCompanion(TasksCompanion companion) {
    return TaskData(
      id: companion.id.value,
      title: companion.title.value,
      notes: companion.notes.value,
      creationDate: companion.creationDate.value,
      dueDate: companion.dueDate.value,
      isCompleted: companion.isCompleted.value,
      completionDate: companion.completionDate.value,
      priority: companion.priority.value,
    );
  }

  /// Convert SubTasksCompanion to SubTaskData
  static SubTaskData toSubTaskDataFromCompanion(SubTasksCompanion companion) {
    return SubTaskData(
      id: companion.id.value,
      parentTaskId: companion.parentTaskId.value,
      title: companion.title.value,
      isCompleted: companion.isCompleted.value,
    );
  }
}