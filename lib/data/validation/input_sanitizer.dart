import 'dart:convert';
import '../../domain/domain.dart';
import '../repositories/exceptions/repository_exceptions.dart';

/// Comprehensive input sanitization and security validation
class InputSanitizer {
  /// Maximum allowed string lengths for different fields
  static const Map<String, int> _maxLengths = {
    'task_title': 200,
    'task_notes': 10000,
    'subtask_title': 200,
    'search_query': 1000,
    'task_id': 100,
  };

  /// Patterns for detecting potentially malicious input
  static final RegExp _sqlInjectionPattern = RegExp(
    r"(;[\s]*(\bUNION\b|\bSELECT\b|\bINSERT\b|\bUPDATE\b|\bDELETE\b|\bDROP\b|\bCREATE\b|\bALTER\b))|(-{2,})|(/\*)|(\*/)|(\bUNION\s+SELECT\b)|(\bOR\s+1\s*=\s*1\b)|(\bAND\s+1\s*=\s*1\b)",
    caseSensitive: false,
  );

  static final RegExp _xssPattern = RegExp(
    r"(<script[^>]*>.*?</script>)|(<iframe[^>]*>.*?</iframe>)|(<object[^>]*>.*?</object>)|(<embed[^>]*>)|(<link[^>]*>)|(<meta[^>]*>)",
    caseSensitive: false,
  );

  static final RegExp _pathTraversalPattern = RegExp(
    r"(\.\./)|(\.\.\\/)|(%2e%2e%2f)|(%252e%252e%252f)|(%c0%ae%c0%ae%c0%af)",
    caseSensitive: false,
  );

  /// Sanitize and validate task title
  static String sanitizeTaskTitle(String title) {
    if (title.isEmpty) {
      throw const DataValidationException('Task title cannot be empty');
    }

    // Remove leading/trailing whitespace
    final trimmed = title.trim();

    if (trimmed.isEmpty) {
      throw const DataValidationException(
          'Task title cannot be only whitespace');
    }

    // Check length
    if (trimmed.length > _maxLengths['task_title']!) {
      throw DataValidationException(
          'Task title cannot exceed ${_maxLengths['task_title']} characters');
    }

    // Check for malicious patterns
    _checkForMaliciousPatterns(trimmed, 'task_title');

    // Normalize whitespace (replace multiple spaces with single space)
    final normalized = trimmed.replaceAll(RegExp(r'\s+'), ' ');

    // Remove control characters except newlines and tabs
    final cleaned =
        normalized.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');

    return cleaned;
  }

  /// Sanitize and validate task notes
  static String sanitizeTaskNotes(String notes) {
    // Notes can be empty
    if (notes.isEmpty) return '';

    // Remove leading/trailing whitespace
    final trimmed = notes.trim();

    // Check length
    if (trimmed.length > _maxLengths['task_notes']!) {
      throw DataValidationException(
          'Task notes cannot exceed ${_maxLengths['task_notes']} characters');
    }

    // Check for malicious patterns
    _checkForMaliciousPatterns(trimmed, 'task_notes');

    // Remove control characters except newlines and tabs
    final cleaned =
        trimmed.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');

    // Normalize line endings
    final normalized = cleaned.replaceAll(RegExp(r'\r\n|\r'), '\n');

    return normalized;
  }

  /// Sanitize and validate subtask title
  static String sanitizeSubtaskTitle(String title) {
    if (title.isEmpty) {
      throw const DataValidationException('Subtask title cannot be empty');
    }

    // Remove leading/trailing whitespace
    final trimmed = title.trim();

    if (trimmed.isEmpty) {
      throw const DataValidationException(
          'Subtask title cannot be only whitespace');
    }

    // Check length
    if (trimmed.length > _maxLengths['subtask_title']!) {
      throw DataValidationException(
          'Subtask title cannot exceed ${_maxLengths['subtask_title']} characters');
    }

    // Check for malicious patterns
    _checkForMaliciousPatterns(trimmed, 'subtask_title');

    // Normalize whitespace
    final normalized = trimmed.replaceAll(RegExp(r'\s+'), ' ');

    // Remove control characters
    final cleaned =
        normalized.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');

    return cleaned;
  }

  /// Sanitize and validate search query
  static String sanitizeSearchQuery(String query) {
    if (query.isEmpty) {
      throw const DataValidationException('Search query cannot be empty');
    }

    // Remove leading/trailing whitespace
    final trimmed = query.trim();

    if (trimmed.isEmpty) {
      throw const DataValidationException(
          'Search query cannot be only whitespace');
    }

    // Check length
    if (trimmed.length > _maxLengths['search_query']!) {
      throw DataValidationException(
          'Search query cannot exceed ${_maxLengths['search_query']} characters');
    }

    // Check for malicious patterns (more strict for search)
    _checkForMaliciousPatterns(trimmed, 'search_query');

    // Remove potentially dangerous characters for FTS
    final cleaned = trimmed.replaceAll(RegExp(r'[<>"\\]'), '');

    // Normalize whitespace
    final normalized = cleaned.replaceAll(RegExp(r'\s+'), ' ');

    return normalized;
  }

  /// Sanitize and validate task ID
  static String sanitizeTaskId(String id) {
    if (id.isEmpty) {
      throw const DataValidationException('Task ID cannot be empty');
    }

    // Remove leading/trailing whitespace
    final trimmed = id.trim();

    if (trimmed.isEmpty) {
      throw const DataValidationException('Task ID cannot be only whitespace');
    }

    // Check length
    if (trimmed.length > _maxLengths['task_id']!) {
      throw DataValidationException(
          'Task ID cannot exceed ${_maxLengths['task_id']} characters');
    }

    // Task IDs should only contain alphanumeric characters, hyphens, and underscores
    if (!RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(trimmed)) {
      throw const DataValidationException(
          'Task ID can only contain letters, numbers, hyphens, and underscores');
    }

    return trimmed;
  }

  /// Sanitize date input (validate and normalize)
  static DateTime sanitizeDate(DateTime date, String fieldName) {
    // Check for reasonable date ranges
    final minDate = DateTime(1900, 1, 1);
    final maxDate = DateTime(2100, 12, 31);

    if (date.isBefore(minDate)) {
      throw DataValidationException(
          '$fieldName cannot be before ${minDate.year}');
    }

    if (date.isAfter(maxDate)) {
      throw DataValidationException(
          '$fieldName cannot be after ${maxDate.year}');
    }

    // Normalize to remove microseconds for consistency
    return DateTime(
      date.year,
      date.month,
      date.day,
      date.hour,
      date.minute,
      date.second,
    );
  }

  /// Sanitize priority enum
  static Priority sanitizePriority(Priority priority) {
    // Validate that the priority is one of the allowed values
    if (!Priority.values.contains(priority)) {
      throw DataValidationException('Invalid priority value: $priority');
    }

    return priority;
  }

  /// Sanitize boolean values
  static bool sanitizeBoolean(bool value, String fieldName) {
    // Booleans are inherently safe, but we validate the field name
    if (fieldName.isEmpty) {
      throw const DataValidationException('Field name cannot be empty');
    }

    return value;
  }

  /// Sanitize integer values with range checking
  static int sanitizeInteger(
    int value,
    String fieldName, {
    int? min,
    int? max,
  }) {
    if (min != null && value < min) {
      throw DataValidationException('$fieldName cannot be less than $min');
    }

    if (max != null && value > max) {
      throw DataValidationException('$fieldName cannot be greater than $max');
    }

    return value;
  }

  /// Sanitize a complete Task object
  static Task sanitizeTask(Task task) {
    return Task(
      id: sanitizeTaskId(task.id),
      title: sanitizeTaskTitle(task.title),
      notes: sanitizeTaskNotes(task.notes),
      creationDate: sanitizeDate(task.creationDate, 'creation date'),
      dueDate: sanitizeDate(task.dueDate, 'due date'),
      isCompleted: sanitizeBoolean(task.isCompleted, 'completion status'),
      completionDate: task.completionDate != null
          ? sanitizeDate(task.completionDate!, 'completion date')
          : null,
      priority: sanitizePriority(task.priority),
      subtasks: task.subtasks.map(sanitizeSubTask).toList(),
    );
  }

  /// Sanitize a SubTask object
  static SubTask sanitizeSubTask(SubTask subtask) {
    return SubTask(
      id: sanitizeTaskId(subtask.id),
      parentTaskId: sanitizeTaskId(subtask.parentTaskId),
      title: sanitizeSubtaskTitle(subtask.title),
      isCompleted: sanitizeBoolean(subtask.isCompleted, 'completion status'),
    );
  }

  /// Sanitize JSON input for API endpoints
  static Map<String, dynamic> sanitizeJsonInput(String jsonString) {
    try {
      final decoded = jsonDecode(jsonString);

      if (decoded is! Map<String, dynamic>) {
        throw const DataValidationException('JSON input must be an object');
      }

      // Check for reasonable JSON size (prevent DoS attacks)
      if (jsonString.length > 1024 * 1024) {
        // 1MB limit
        throw const DataValidationException('JSON input too large');
      }

      // Recursively sanitize all string values in the JSON
      return _sanitizeJsonObject(decoded);
    } on FormatException catch (e) {
      throw DataValidationException('Invalid JSON format: ${e.message}');
    }
  }

  /// Get sanitization statistics for monitoring
  static Map<String, dynamic> getSanitizationStats() {
    return {
      'max_lengths': _maxLengths,
      'security_patterns': {
        'sql_injection_checks': 'enabled',
        'xss_checks': 'enabled',
        'path_traversal_checks': 'enabled',
      },
      'supported_field_types': [
        'task_title',
        'task_notes',
        'subtask_title',
        'search_query',
        'task_id',
        'dates',
        'priorities',
        'booleans',
        'integers',
      ],
    };
  }

  // ==================== PRIVATE METHODS ====================

  /// Check for malicious patterns in input
  static void _checkForMaliciousPatterns(String input, String fieldName) {
    // Check for SQL injection patterns
    if (_sqlInjectionPattern.hasMatch(input)) {
      throw DataValidationException(
          'Potentially malicious SQL pattern detected in $fieldName');
    }

    // Check for XSS patterns
    if (_xssPattern.hasMatch(input)) {
      throw DataValidationException(
          'Potentially malicious script pattern detected in $fieldName');
    }

    // Check for path traversal patterns
    if (_pathTraversalPattern.hasMatch(input)) {
      throw DataValidationException(
          'Potentially malicious path traversal pattern detected in $fieldName');
    }

    // Check for null bytes (can cause issues in some systems)
    if (input.contains('\x00')) {
      throw DataValidationException('Null bytes not allowed in $fieldName');
    }

    // Check for excessive special characters (potential encoding attacks)
    // For now, disable this check to allow Chinese characters
    // TODO: Implement proper Unicode character validation
    // final specialCharCount = RegExp(r'[^\w\s\-.,!?()[\]{}:;"]').allMatches(input).length;
    // if (specialCharCount > input.length * 0.3) {
    //   throw DataValidationException('Excessive special characters detected in $fieldName');
    // }
  }

  /// Recursively sanitize JSON object
  static Map<String, dynamic> _sanitizeJsonObject(Map<String, dynamic> obj) {
    final sanitized = <String, dynamic>{};

    for (final entry in obj.entries) {
      final key = _sanitizeJsonKey(entry.key);
      final value = _sanitizeJsonValue(entry.value);
      sanitized[key] = value;
    }

    return sanitized;
  }

  /// Sanitize JSON object key
  static String _sanitizeJsonKey(String key) {
    // Keys should be reasonable length and contain safe characters
    if (key.length > 100) {
      throw const DataValidationException('JSON key too long');
    }

    if (!RegExp(r'^[a-zA-Z0-9_-]+$').hasMatch(key)) {
      throw const DataValidationException(
          'JSON key contains invalid characters');
    }

    return key;
  }

  /// Sanitize JSON value recursively
  static dynamic _sanitizeJsonValue(dynamic value) {
    if (value is String) {
      // Basic string sanitization for JSON values
      if (value.length > 10000) {
        throw const DataValidationException('JSON string value too long');
      }

      // Remove control characters
      return value.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');
    } else if (value is Map<String, dynamic>) {
      return _sanitizeJsonObject(value);
    } else if (value is List) {
      if (value.length > 1000) {
        throw const DataValidationException('JSON array too large');
      }

      return value.map(_sanitizeJsonValue).toList();
    } else if (value is num || value is bool || value == null) {
      return value;
    } else {
      throw DataValidationException(
          'Unsupported JSON value type: ${value.runtimeType}');
    }
  }
}

// This local exception is no longer needed as we use the one from the repository exceptions file.
// We keep the file for the sanitization logic itself.
