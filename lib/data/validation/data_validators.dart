import 'package:mytodospace/data/datasources/local_database.dart';

// A simple validation result class.
class ValidationResult {
  final List<String> errors;
  final List<String> warnings;

  ValidationResult({this.errors = const [], this.warnings = const []});

  bool get isValid => errors.isEmpty;

  void throwIfInvalid() {
    if (!isValid) {
      // In a real app, you'd throw a specific validation exception.
      throw Exception(errors.join(', '));
    }
  }

  void logWarnings() {
    if (warnings.isNotEmpty) {
      // In a real app, you'd use a proper logger.
      // In a real app, you'd use a proper logger like: logger.w('Validation Warnings: ${warnings.join(', ')}');
    }
  }
}

class DataValidators {
  static ValidationResult validateTaskData(TaskData task) {
    final errors = <String>[];
    if (task.title.isEmpty) {
      errors.add('Task title cannot be empty.');
    }
    return ValidationResult(errors: errors);
  }

  static ValidationResult validateTaskHierarchy(
      TaskData parent, List<SubTaskData> children) {
    // For now, we'll just return a valid result.
    return ValidationResult();
  }

  static ValidationResult validateSearchQuery(String query) {
    final errors = <String>[];
    if (query.length > 100) {
      errors.add('Search query is too long.');
    }
    return ValidationResult(errors: errors);
  }

  /// Validates a date range for queries and operations
  static ValidationResult validateDateRange(
      DateTime startDate, DateTime endDate) {
    final errors = <String>[];
    final warnings = <String>[];

    if (startDate.isAfter(endDate)) {
      errors.add('Start date cannot be after end date');
    }

    final now = DateTime.now();
    final maxFutureDate = now.add(const Duration(days: 3650)); // 10 years

    if (endDate.isAfter(maxFutureDate)) {
      warnings.add('End date is more than 10 years in the future');
    }

    final minDate = DateTime(1900, 1, 1);
    if (startDate.isBefore(minDate)) {
      warnings.add('Start date is before year 1900');
    }

    return ValidationResult(errors: errors, warnings: warnings);
  }

  static ValidationResult validateBatchOperation(List<String> ids,
      {int maxBatchSize = 100}) {
    final errors = <String>[];
    if (ids.length > maxBatchSize) {
      errors.add('Batch size exceeds the limit of $maxBatchSize.');
    }
    return ValidationResult(errors: errors);
  }
}
