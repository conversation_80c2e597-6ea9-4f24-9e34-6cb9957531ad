import 'package:drift/drift.dart';
import 'package:drift/drift.dart' as drift;
import '../../domain/models/task_model.dart';
import 'database_migrations.dart';

import 'database_config.dart';

part 'local_database.g.dart';

/// 优先级转换器
class PriorityConverter extends TypeConverter<Priority, String> {
  @override
  Priority fromSql(String fromDb) {
    switch (fromDb) {
      case 'urgent_important':
        return Priority.urgentImportant;
      case 'important_not_urgent':
        return Priority.importantNotUrgent;
      case 'urgent_not_important':
        return Priority.urgentNotImportant;
      case 'not_urgent_not_important':
        return Priority.notUrgentNotImportant;
      default:
        return Priority.urgentImportant;
    }
  }

  @override
  String toSql(Priority value) {
    switch (value) {
      case Priority.urgentImportant:
        return 'urgent_important';
      case Priority.importantNotUrgent:
        return 'important_not_urgent';
      case Priority.urgentNotImportant:
        return 'urgent_not_important';
      case Priority.notUrgentNotImportant:
        return 'not_urgent_not_important';
    }
  }
}

/// 任务数据表定义
@DataClassName('TaskData')
class Tasks extends Table {
  TextColumn get id => text()();
  TextColumn get title => text().withLength(min: 1, max: 200)();
  TextColumn get notes => text().withDefault(const Constant(''))();
  DateTimeColumn get creationDate => dateTime()();
  DateTimeColumn get dueDate => dateTime()();
  BoolColumn get isCompleted => boolean().withDefault(const Constant(false))();
  DateTimeColumn get completionDate => dateTime().nullable()();
  TextColumn get priority => text()
      .map(PriorityConverter())
      .withDefault(const Constant('urgent_important'))();

  @override
  Set<Column> get primaryKey => {id};
}

/// 子任务数据表定义
@DataClassName('SubTaskData')
class SubTasks extends Table {
  TextColumn get id => text()();
  TextColumn get parentTaskId =>
      text().references(Tasks, #id, onDelete: KeyAction.cascade)();
  TextColumn get title => text().withLength(min: 1, max: 200)();
  BoolColumn get isCompleted => boolean().withDefault(const Constant(false))();

  @override
  Set<Column> get primaryKey => {id};
}

/// 本地数据库类
@DriftDatabase(tables: [Tasks, SubTasks])
class LocalDatabase extends _$LocalDatabase {
  LocalDatabase() : super(_openConnection());

  /// Test constructor for in-memory database
  LocalDatabase.forTesting(super.executor);

  /// Performance monitor instance
  // TODO: Fix type compatibility issue in Drift 2.x
  // late final DatabasePerformanceMonitor _performanceMonitor =
  //     DatabasePerformanceMonitor(this as QueryExecutor);

  @override
  int get schemaVersion => DatabaseMigrations.currentVersion;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) async {
        await m.createAll();
        await _createIndexes();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        await DatabaseMigrations.migrate(m.database, from, to);
      },
      // TODO: Fix type compatibility issue in Drift 2.x - beforeOpen expects OpeningDetails in Drift 2.x
      // beforeOpen: (QueryExecutor executor) async {
      //   // Enable foreign key constraints
      //   await executor.runCustom('PRAGMA foreign_keys = ON');
      //
      //   // Optimize SQLite settings for performance
      //   await executor.runCustom('PRAGMA journal_mode = WAL');
      //   await executor.runCustom('PRAGMA synchronous = NORMAL');
      //   await executor.runCustom('PRAGMA cache_size = 10000');
      //   await executor.runCustom('PRAGMA temp_store = MEMORY');
      //   await executor.runCustom('PRAGMA busy_timeout = 30000');
      //
      //   // Validate schema after opening
      //   final issues = await DatabaseMigrations.validateSchema(executor);
      //   if (issues.isNotEmpty) {
      //     print('Database schema validation issues: $issues');
      //   }
      // },
    );
  }

  /// Create database indexes for performance optimization
  Future<void> _createIndexes() async {
    // Index on due_date for calendar queries
    await customStatement('''
      CREATE INDEX IF NOT EXISTS idx_tasks_due_date
      ON tasks(due_date)
    ''');

    // Index on creation_date for analytics queries
    await customStatement('''
      CREATE INDEX IF NOT EXISTS idx_tasks_creation_date
      ON tasks(creation_date)
    ''');

    // Index on completion status and date for performance queries
    await customStatement('''
      CREATE INDEX IF NOT EXISTS idx_tasks_completion
      ON tasks(is_completed, completion_date)
    ''');

    // Index on priority for quadrant queries
    await customStatement('''
      CREATE INDEX IF NOT EXISTS idx_tasks_priority
      ON tasks(priority)
    ''');

    // Composite index for date range queries
    await customStatement('''
      CREATE INDEX IF NOT EXISTS idx_tasks_due_date_priority
      ON tasks(due_date, priority, is_completed)
    ''');

    // Index on parent_task_id for subtask queries
    await customStatement('''
      CREATE INDEX IF NOT EXISTS idx_subtasks_parent_id
      ON sub_tasks(parent_task_id)
    ''');

    // Full-text search index for task titles and notes
    await customStatement('''
      CREATE VIRTUAL TABLE IF NOT EXISTS tasks_fts USING fts5(
        id UNINDEXED,
        title,
        notes,
        content='tasks',
        content_rowid='rowid'
      )
    ''');

    // Triggers to keep FTS table in sync
    await customStatement('''
      CREATE TRIGGER IF NOT EXISTS tasks_fts_insert AFTER INSERT ON tasks BEGIN
        INSERT INTO tasks_fts(rowid, id, title, notes)
        VALUES (new.rowid, new.id, new.title, new.notes);
      END
    ''');

    await customStatement('''
      CREATE TRIGGER IF NOT EXISTS tasks_fts_delete AFTER DELETE ON tasks BEGIN
        INSERT INTO tasks_fts(tasks_fts, rowid, id, title, notes)
        VALUES('delete', old.rowid, old.id, old.title, old.notes);
      END
    ''');

    await customStatement('''
      CREATE TRIGGER IF NOT EXISTS tasks_fts_update AFTER UPDATE ON tasks BEGIN
        INSERT INTO tasks_fts(tasks_fts, rowid, id, title, notes)
        VALUES('delete', old.rowid, old.id, old.title, old.notes);
        INSERT INTO tasks_fts(rowid, id, title, notes)
        VALUES (new.rowid, new.id, new.title, new.notes);
      END
    ''');
  }

  // ==================== TASK OPERATIONS ====================

  /// 创建任务（带事务处理）
  Future<void> insertTask(TasksCompanion task) async {
    await transaction(() async {
      await into(tasks).insert(task);
    });
  }

  /// 批量创建任务
  Future<void> insertTasks(List<TasksCompanion> taskList) async {
    await transaction(() async {
      await batch((batch) {
        for (final task in taskList) {
          batch.insert(tasks, task);
        }
      });
    });
  }

  /// 更新任务
  Future<bool> updateTask(TasksCompanion task) async {
    final rowsAffected = await (update(tasks)
          ..where((t) => t.id.equals(task.id.value)))
        .write(task);
    return rowsAffected > 0;
  }

  /// 删除任务（级联删除子任务）
  Future<bool> deleteTask(String taskId) async {
    return await transaction(() async {
      // Delete subtasks first (though cascade should handle this)
      await deleteSubTasksForTask(taskId);

      // Delete the main task
      final rowsAffected =
          await (delete(tasks)..where((t) => t.id.equals(taskId))).go();

      return rowsAffected > 0;
    });
  }

  /// 批量删除任务
  Future<int> deleteTasks(List<String> taskIds) async {
    return await transaction(() async {
      int totalDeleted = 0;
      for (final taskId in taskIds) {
        final deleted = await deleteTask(taskId);
        if (deleted) totalDeleted++;
      }
      return totalDeleted;
    });
  }

  /// 获取单个任务
  Future<TaskData?> getTaskById(String taskId) {
    return (select(tasks)..where((t) => t.id.equals(taskId))).getSingleOrNull();
  }

  /// 获取所有任务
  Future<List<TaskData>> getAllTasks() {
    return (select(tasks)..orderBy([(t) => OrderingTerm.asc(t.dueDate)])).get();
  }

  /// 监听指定日期的任务列表（优化版）
  Stream<List<TaskData>> watchTasksByDate(DateTime date) {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));

    return (select(tasks)
          ..where((t) =>
              t.dueDate.isBiggerOrEqual(Variable(startOfDay)) &
              t.dueDate.isSmallerThan(Variable(endOfDay)))
          ..orderBy([(t) => OrderingTerm.asc(t.priority)]))
        .watch();
  }

  /// 监听指定月份的任务负荷数据（优化版）
  Stream<List<TaskData>> watchTasksForMonth({
    required int year,
    required int month,
  }) {
    final startOfMonth = DateTime(year, month, 1);
    final endOfMonth = DateTime(year, month + 1, 1);

    return (select(tasks)
          ..where((t) => t.dueDate.isBetweenValues(startOfMonth, endOfMonth))
          ..orderBy([(t) => OrderingTerm.asc(t.dueDate)]))
        .watch();
  }

  /// Get tasks created in a specific date range
  Future<List<TaskData>> getTasksCreatedInRange(DateTime start, DateTime end) {
    return (select(tasks)
          ..where((t) => t.creationDate.isBetweenValues(start, end)))
        .get();
  }

  /// 获取指定时间范围内的任务（按截止日期）
  Future<List<TaskData>> getTasksDueInRange(DateTime start, DateTime end) {
    return (select(tasks)
          ..where((t) => t.dueDate.isBetweenValues(start, end))
          ..orderBy([(t) => OrderingTerm.asc(t.dueDate)]))
        .get();
  }

  /// Get completed tasks in a specific date range
  Future<List<TaskData>> getCompletedTasksInRange(
      DateTime start, DateTime end) {
    return (select(tasks)
          ..where((t) =>
              t.isCompleted.equals(true) &
              t.completionDate.isBetweenValues(start, end)))
        .get();
  }

  /// 获取逾期任务
  Future<List<TaskData>> getOverdueTasks() {
    final now = DateTime.now();
    return (select(tasks)
          ..where((t) =>
              t.isCompleted.equals(false) & t.dueDate.isSmallerThanValue(now))
          ..orderBy([(t) => OrderingTerm.asc(t.dueDate)]))
        .get();
  }

  /// 按优先级获取任务
  Future<List<TaskData>> getTasksByPriority(String priority) {
    return (select(tasks)
          ..where((t) => t.priority.equals(priority))
          ..orderBy([(t) => OrderingTerm.asc(t.dueDate)]))
        .get();
  }

  /// Search tasks using full-text search
  Future<List<TaskData>> searchTasks(String query) async {
    if (query.trim().isEmpty) return [];

    // Use FTS table for search
    final searchResults = await customSelect('''
      SELECT t.* FROM tasks t
      JOIN tasks_fts fts ON t.rowid = fts.rowid
      WHERE tasks_fts MATCH ?
      ORDER BY rank
    ''', variables: [Variable.withString(query)]).get();

    // mapFromRow is not a standard method, using tasks.map(row.data) instead.
    return searchResults.map((row) => tasks.map(row.data)).toList();
  }

  // ==================== SUBTASK OPERATIONS ====================

  /// 创建子任务
  Future<void> insertSubTask(SubTasksCompanion subtask) =>
      into(subTasks).insert(subtask);

  /// 批量创建子任务
  Future<void> insertSubTasks(List<SubTasksCompanion> subtaskList) async {
    await batch((batch) {
      for (final subtask in subtaskList) {
        batch.insert(subTasks, subtask);
      }
    });
  }

  /// 更新子任务
  Future<bool> updateSubTask(SubTasksCompanion subtask) async {
    final rowsAffected = await (update(subTasks)
          ..where((s) => s.id.equals(subtask.id.value)))
        .write(subtask);
    return rowsAffected > 0;
  }

  /// 删除子任务
  Future<bool> deleteSubTask(String subtaskId) async {
    final rowsAffected =
        await (delete(subTasks)..where((s) => s.id.equals(subtaskId))).go();
    return rowsAffected > 0;
  }

  /// 获取任务的子任务列表
  Future<List<SubTaskData>> getSubTasksForTask(String taskId) {
    return (select(subTasks)
          ..where((s) => s.parentTaskId.equals(taskId))
          ..orderBy([(s) => OrderingTerm.asc(s.title)]))
        .get();
  }

  /// 监听任务的子任务列表
  Stream<List<SubTaskData>> watchSubTasksForTask(String taskId) {
    return (select(subTasks)
          ..where((s) => s.parentTaskId.equals(taskId))
          ..orderBy([(s) => OrderingTerm.asc(s.title)]))
        .watch();
  }

  /// 删除任务的所有子任务
  Future<int> deleteSubTasksForTask(String taskId) =>
      (delete(subTasks)..where((s) => s.parentTaskId.equals(taskId))).go();

  // ==================== ANALYTICS OPERATIONS ====================

  /// 获取任务统计信息
  Future<Map<String, int>> getTaskStatistics() async {
    final result = await customSelect('''
      SELECT
        COUNT(*) as total_tasks,
        COUNT(CASE WHEN is_completed = 1 THEN 1 END) as completed_tasks,
        COUNT(CASE WHEN is_completed = 0 THEN 1 END) as pending_tasks,
        COUNT(CASE WHEN is_completed = 0 AND due_date < datetime('now') THEN 1 END) as overdue_tasks
      FROM tasks
    ''').getSingle();

    return {
      'total_tasks': result.read<int>('total_tasks'),
      'completed_tasks': result.read<int>('completed_tasks'),
      'pending_tasks': result.read<int>('pending_tasks'),
      'overdue_tasks': result.read<int>('overdue_tasks'),
    };
  }

  /// 获取优先级分布
  Future<Map<String, int>> getPriorityDistribution() async {
    final results = await customSelect('''
      SELECT priority, COUNT(*) as count
      FROM tasks
      GROUP BY priority
    ''').get();

    final distribution = <String, int>{};
    for (final row in results) {
      distribution[row.read<String>('priority')] = row.read<int>('count');
    }

    return distribution;
  }

  /// 获取每日任务负荷（用于热力图）
  Future<Map<String, int>> getDailyTaskLoad(
      DateTime start, DateTime end) async {
    final results = await customSelect('''
      SELECT
        DATE(due_date, 'unixepoch') as date,
        COALESCE(SUM(
          CASE priority
            WHEN 'urgent_important' THEN 4
            WHEN 'important_not_urgent' THEN 3
            WHEN 'urgent_not_important' THEN 2
            WHEN 'not_urgent_not_important' THEN 1
            ELSE 1
          END
        ), 0) as load
      FROM tasks
      WHERE due_date BETWEEN ? AND ?
        AND is_completed = 0
      GROUP BY DATE(due_date, 'unixepoch')
      ORDER BY date
    ''', variables: [
      Variable.withInt(start.millisecondsSinceEpoch ~/ 1000), // Convert to seconds
      Variable.withInt(end.millisecondsSinceEpoch ~/ 1000),   // Convert to seconds
    ]).get();

    final loadMap = <String, int>{};
    for (final row in results) {
      final date = row.read<String?>('date');
      final load = row.read<int?>('load') ?? 0;
      if (date != null) {
        loadMap[date] = load;
      }
    }
    return loadMap;
  }

  /// Get task load data for a specific month
  Future<Map<DateTime, int>> getTaskLoadForMonth(int year, int month) async {
    final startOfMonth = DateTime(year, month, 1);
    final endOfMonth = DateTime(year, month + 1, 1);

    final tasksInMonth = await (select(tasks)
          ..where((t) => t.dueDate.isBetweenValues(startOfMonth, endOfMonth)))
        .get();

    final taskLoadByDate = <DateTime, int>{};
    for (final task in tasksInMonth) {
      final date =
          DateTime(task.dueDate.year, task.dueDate.month, task.dueDate.day);
      taskLoadByDate[date] = (taskLoadByDate[date] ?? 0) + 1;
    }

    return taskLoadByDate;
  }

  /// Get task load data for a specific year
  Future<Map<DateTime, int>> getTaskLoadForYear(int year) async {
    final startOfYear = DateTime(year, 1, 1);
    final endOfYear = DateTime(year + 1, 1, 1);

    final tasksInYear = await (select(tasks)
          ..where((t) => t.dueDate.isBetweenValues(startOfYear, endOfYear)))
        .get();

    final taskLoadByDate = <DateTime, int>{};
    for (final task in tasksInYear) {
      final date =
          DateTime(task.dueDate.year, task.dueDate.month, task.dueDate.day);
      taskLoadByDate[date] = (taskLoadByDate[date] ?? 0) + 1;
    }

    return taskLoadByDate;
  }

  // ==================== MAINTENANCE OPERATIONS ====================

  /// 清理已完成的旧任务（超过指定天数）
  Future<int> cleanupOldCompletedTasks(int daysOld) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));

    return await (delete(tasks)
          ..where((t) =>
              t.isCompleted.equals(true) &
              t.completionDate.isSmallerThanValue(cutoffDate)))
        .go();
  }

  /// 数据库完整性检查
  Future<bool> checkDatabaseIntegrity() async {
    try {
      final result = await customSelect('PRAGMA integrity_check').getSingle();
      return result.read<String>('integrity_check') == 'ok';
    } catch (e) {
      return false;
    }
  }

  /// 优化数据库
  Future<void> optimizeDatabase() async {
    await customStatement('VACUUM');
    await customStatement('ANALYZE');
  }

  /// 获取数据库大小信息
  Future<Map<String, int>> getDatabaseSize() async {
    final result = await customSelect('''
      SELECT
        page_count * page_size as size,
        page_count,
        page_size
      FROM pragma_page_count(), pragma_page_size()
    ''').getSingle();

    return {
      'size_bytes': result.read<int>('size'),
      'page_count': result.read<int>('page_count'),
      'page_size': result.read<int>('page_size'),
    };
  }

  /// Get performance metrics for database operations
  Future<Map<String, dynamic>> getPerformanceMetrics() async {
    // TODO: Fix type compatibility issue in Drift 2.x
    // return await _performanceMonitor.getPerformanceStats();
    return <String, dynamic>{}; // Return an empty map as a placeholder
  }

  /// Clean up old data (older than 10 years)
  Future<int> cleanupOldData() async {
    final cutoffDate = DateTime.now().subtract(const Duration(days: 3650));

    // Delete old completed tasks
    final deletedTasks = await (delete(tasks)
          ..where((t) =>
              t.isCompleted.equals(true) &
              t.completionDate.isSmallerThanValue(cutoffDate)))
        .go();

    // Delete old subtasks for deleted tasks
    await (delete(subTasks)
          ..where((st) => st.parentTaskId
              .isNotInQuery(selectOnly(tasks)..addColumns([tasks.id]))))
        .go();

    return deletedTasks;
  }

  // ==================== PERFORMANCE MONITORING ====================

  /// Get comprehensive performance statistics
  Future<Map<String, dynamic>> getPerformanceStats() =>
      // TODO: Fix type compatibility issue in Drift 2.x
      // _performanceMonitor.getPerformanceStats();
      Future.value(<String, dynamic>{}); // Return an empty map as a placeholder

  /// Monitor query performance
  Future<T> monitorQuery<T>(
    String queryName,
    Future<T> Function() query, {
    Duration slowQueryThreshold = const Duration(milliseconds: 100),
  }) async {
    // TODO: Fix type compatibility issue in Drift 2.x
    // final result = await _performanceMonitor.monitorQuery(queryName, query, slowQueryThreshold: slowQueryThreshold);
    // return result['result'] as T;
    return await query(); // Return the result of the query directly
  }

  /// Analyze query execution plan
  Future<List<Map<String, dynamic>>> analyzeQuery(String sql) =>
      // TODO: Fix type compatibility issue in Drift 2.x
      // _performanceMonitor.analyzeQuery(sql);
      Future.value(
          <Map<String, dynamic>>[]); // Return an empty list as a placeholder

  /// Get index usage statistics
  Future<List<Map<String, dynamic>>> getIndexUsageStats() =>
      // TODO: Fix type compatibility issue in Drift 2.x
      // _performanceMonitor.getIndexUsageStats();
      Future.value(
          <Map<String, dynamic>>[]); // Return an empty list as a placeholder

  /// Optimize database performance
  Future<Map<String, dynamic>> optimizePerformance() =>
      // TODO: Fix type compatibility issue in Drift 2.x
      // _performanceMonitor.optimizeDatabase();
      Future.value(<String, dynamic>{}); // Return an empty map as a placeholder

  /// Check for potential performance issues
  Future<List<String>> checkPerformanceIssues() =>
      // TODO: Fix type compatibility issue in Drift 2.x
      // _performanceMonitor.checkPerformanceIssues();
      Future.value(<String>[]); // Return an empty list as a placeholder

  /// Get database schema information
  Future<Map<String, dynamic>> getSchemaInfo() =>
      DatabaseMigrations.getSchemaInfo(this);

  /// Validate database schema
  Future<List<String>> validateSchema() =>
      DatabaseMigrations.validateSchema(this);
}

/// 数据库连接配置
LazyDatabase _openConnection() {
  // Use production configuration by default
  // In development, you can switch to DatabaseConfig.createDevelopmentDatabase()
  return DatabaseConfig.createProductionDatabase();
}
