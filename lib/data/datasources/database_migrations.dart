import 'package:drift/drift.dart';

/// Database migration utilities and version management
class DatabaseMigrations {
  /// Current database version
  static const int currentVersion = 2;
  
  /// Migration history and version information
  static const Map<int, String> versionHistory = {
    1: 'Initial database schema with tasks and subtasks',
    2: 'Added indexes, constraints, and full-text search',
  };

  /// Execute migration from one version to another
  static Future<void> migrate(
    GeneratedDatabase database,
    int fromVersion,
    int toVersion,
  ) async {
    for (int version = fromVersion + 1; version <= toVersion; version++) {
      await _executeMigration(database, version);
    }
  }

  /// Execute specific version migration
  static Future<void> _executeMigration(GeneratedDatabase database, int version) async {
    switch (version) {
      case 2:
        await _migrateToV2(database);
        break;
      default:
        throw UnsupportedError('Migration to version $version is not supported');
    }
  }

  /// Migration to version 2: Add indexes and constraints
  static Future<void> _migrateToV2(GeneratedDatabase database) async {
    // Add performance indexes
    await database.customStatement('''
      CREATE INDEX IF NOT EXISTS idx_tasks_due_date
      ON tasks(due_date)
    ''');

    await database.customStatement('''
      CREATE INDEX IF NOT EXISTS idx_tasks_creation_date
      ON tasks(creation_date)
    ''');

    await database.customStatement('''
      CREATE INDEX IF NOT EXISTS idx_tasks_completion
      ON tasks(is_completed, completion_date)
    ''');

    await database.customStatement('''
      CREATE INDEX IF NOT EXISTS idx_tasks_priority
      ON tasks(priority)
    ''');

    await database.customStatement('''
      CREATE INDEX IF NOT EXISTS idx_tasks_due_date_priority
      ON tasks(due_date, priority, is_completed)
    ''');
    
    await database.customStatement('''
      CREATE INDEX IF NOT EXISTS idx_subtasks_parent_id
      ON sub_tasks(parent_task_id)
    ''');

    // Create full-text search virtual table
    await database.customStatement('''
      CREATE VIRTUAL TABLE IF NOT EXISTS tasks_fts USING fts5(
        id UNINDEXED,
        title,
        notes,
        content='tasks',
        content_rowid='rowid'
      )
    ''');

    // Populate FTS table with existing data
    await database.customStatement('''
      INSERT INTO tasks_fts(rowid, id, title, notes)
      SELECT rowid, id, title, notes FROM tasks
    ''');

    // Create triggers to keep FTS table synchronized
    await _createFTSTriggers(database);

    // Add data validation triggers
    await _createValidationTriggers(database);
  }

  /// Create full-text search triggers
  static Future<void> _createFTSTriggers(GeneratedDatabase database) async {
    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS tasks_fts_insert AFTER INSERT ON tasks BEGIN
        INSERT INTO tasks_fts(rowid, id, title, notes)
        VALUES (new.rowid, new.id, new.title, new.notes);
      END
    ''');
    
    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS tasks_fts_delete AFTER DELETE ON tasks BEGIN
        INSERT INTO tasks_fts(tasks_fts, rowid, id, title, notes)
        VALUES('delete', old.rowid, old.id, old.title, old.notes);
      END
    ''');

    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS tasks_fts_update AFTER UPDATE ON tasks BEGIN
        INSERT INTO tasks_fts(tasks_fts, rowid, id, title, notes)
        VALUES('delete', old.rowid, old.id, old.title, old.notes);
        INSERT INTO tasks_fts(rowid, id, title, notes)
        VALUES (new.rowid, new.id, new.title, new.notes);
      END
    ''');
  }

  /// Create data validation triggers
  static Future<void> _createValidationTriggers(GeneratedDatabase database) async {
    // Trigger to validate task dates
    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS validate_task_dates
      BEFORE INSERT ON tasks
      FOR EACH ROW
      WHEN NEW.due_date < NEW.creation_date
      BEGIN
        SELECT RAISE(ABORT, 'Due date cannot be before creation date');
      END
    ''');

    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS validate_task_dates_update
      BEFORE UPDATE ON tasks
      FOR EACH ROW
      WHEN NEW.due_date < NEW.creation_date
      BEGIN
        SELECT RAISE(ABORT, 'Due date cannot be before creation date');
      END
    ''');

    // Trigger to validate completion date
    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS validate_completion_date
      BEFORE INSERT ON tasks
      FOR EACH ROW
      WHEN NEW.is_completed = 1 AND NEW.completion_date IS NULL
      BEGIN
        SELECT RAISE(ABORT, 'Completed task must have completion date');
      END
    ''');

    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS validate_completion_date_update
      BEFORE UPDATE ON tasks
      FOR EACH ROW
      WHEN NEW.is_completed = 1 AND NEW.completion_date IS NULL
      BEGIN
        SELECT RAISE(ABORT, 'Completed task must have completion date');
      END
    ''');

    // Trigger to validate task title length
    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS validate_task_title
      BEFORE INSERT ON tasks
      FOR EACH ROW
      WHEN LENGTH(TRIM(NEW.title)) = 0 OR LENGTH(NEW.title) > 200
      BEGIN
        SELECT RAISE(ABORT, 'Task title must be between 1 and 200 characters');
      END
    ''');

    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS validate_task_title_update
      BEFORE UPDATE ON tasks
      FOR EACH ROW
      WHEN LENGTH(TRIM(NEW.title)) = 0 OR LENGTH(NEW.title) > 200
      BEGIN
        SELECT RAISE(ABORT, 'Task title must be between 1 and 200 characters');
      END
    ''');

    // Trigger to validate subtask title length
    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS validate_subtask_title
      BEFORE INSERT ON sub_tasks
      FOR EACH ROW
      WHEN LENGTH(TRIM(NEW.title)) = 0 OR LENGTH(NEW.title) > 200
      BEGIN
        SELECT RAISE(ABORT, 'Subtask title must be between 1 and 200 characters');
      END
    ''');

    await database.customStatement('''
      CREATE TRIGGER IF NOT EXISTS validate_subtask_title_update
      BEFORE UPDATE ON sub_tasks
      FOR EACH ROW
      WHEN LENGTH(TRIM(NEW.title)) = 0 OR LENGTH(NEW.title) > 200
      BEGIN
        SELECT RAISE(ABORT, 'Subtask title must be between 1 and 200 characters');
      END
    ''');
  }

  /// Backup database schema information
  static Future<Map<String, dynamic>> getSchemaInfo(GeneratedDatabase database) async {
    final tables = await database.customSelect('''
      SELECT name, sql FROM sqlite_master
      WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    ''').get();

    final indexes = await database.customSelect('''
      SELECT name, sql FROM sqlite_master
      WHERE type='index' AND name NOT LIKE 'sqlite_%'
      ORDER BY name
    ''').get();

    final triggers = await database.customSelect('''
      SELECT name, sql FROM sqlite_master
      WHERE type='trigger'
      ORDER BY name
    ''').get();

    return {
      'version': currentVersion,
      'tables': tables.map((row) => {
        'name': row.read<String>('name'),
        'sql': row.read<String>('sql'),
      }).toList(),
      'indexes': indexes.map((row) => {
        'name': row.read<String>('name'),
        'sql': row.read<String>('sql'),
      }).toList(),
      'triggers': triggers.map((row) => {
        'name': row.read<String>('name'),
        'sql': row.read<String>('sql'),
      }).toList(),
    };
  }

  /// Validate database schema integrity
  static Future<List<String>> validateSchema(GeneratedDatabase database) async {
    final issues = <String>[];

    try {
      // Check if required tables exist
      final tables = await database.customSelect('''
        SELECT name FROM sqlite_master
        WHERE type='table' AND name IN ('tasks', 'sub_tasks', 'tasks_fts')
      ''').get();

      final tableNames = tables.map((row) => row.read<String>('name')).toSet();
      
      if (!tableNames.contains('tasks')) {
        issues.add('Missing required table: tasks');
      }
      
      if (!tableNames.contains('sub_tasks')) {
        issues.add('Missing required table: sub_tasks');
      }
      
      if (!tableNames.contains('tasks_fts')) {
        issues.add('Missing full-text search table: tasks_fts');
      }

      // Check if required indexes exist
      final indexes = await database.customSelect('''
        SELECT name FROM sqlite_master
        WHERE type='index' AND name LIKE 'idx_%'
      ''').get();

      final indexNames = indexes.map((row) => row.read<String>('name')).toSet();
      final requiredIndexes = [
        'idx_tasks_due_date',
        'idx_tasks_creation_date',
        'idx_tasks_completion',
        'idx_tasks_priority',
        'idx_subtasks_parent_id',
      ];

      for (final requiredIndex in requiredIndexes) {
        if (!indexNames.contains(requiredIndex)) {
          issues.add('Missing required index: $requiredIndex');
        }
      }

      // Check foreign key constraints
      final pragmaResult = await database.customSelect('PRAGMA foreign_key_check').get();
      if (pragmaResult.isNotEmpty) {
        issues.add('Foreign key constraint violations detected');
      }

    } catch (e) {
      issues.add('Error validating schema: $e');
    }

    return issues;
  }
}