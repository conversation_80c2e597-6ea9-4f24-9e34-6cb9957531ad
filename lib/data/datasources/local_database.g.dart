// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'local_database.dart';

// ignore_for_file: type=lint
class $TasksTable extends Tasks with drift.TableInfo<$TasksTable, TaskData> {
  @override
  final drift.GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TasksTable(this.attachedDatabase, [this._alias]);
  static const drift.VerificationMeta _idMeta =
      const drift.VerificationMeta('id');
  @override
  late final drift.GeneratedColumn<String> id = drift.GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const drift.VerificationMeta _titleMeta =
      const drift.VerificationMeta('title');
  @override
  late final drift.GeneratedColumn<String> title =
      drift.GeneratedColumn<String>('title', aliasedName, false,
          additionalChecks: GeneratedColumn.checkTextLength(
              minTextLength: 1, maxTextLength: 200),
          type: DriftSqlType.string,
          requiredDuringInsert: true);
  static const drift.VerificationMeta _notesMeta =
      const drift.VerificationMeta('notes');
  @override
  late final drift.GeneratedColumn<String> notes =
      drift.GeneratedColumn<String>('notes', aliasedName, false,
          type: DriftSqlType.string,
          requiredDuringInsert: false,
          defaultValue: const drift.Constant(''));
  static const drift.VerificationMeta _creationDateMeta =
      const drift.VerificationMeta('creationDate');
  @override
  late final drift.GeneratedColumn<DateTime> creationDate =
      drift.GeneratedColumn<DateTime>('creation_date', aliasedName, false,
          type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const drift.VerificationMeta _dueDateMeta =
      const drift.VerificationMeta('dueDate');
  @override
  late final drift.GeneratedColumn<DateTime> dueDate =
      drift.GeneratedColumn<DateTime>('due_date', aliasedName, false,
          type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const drift.VerificationMeta _isCompletedMeta =
      const drift.VerificationMeta('isCompleted');
  @override
  late final drift.GeneratedColumn<bool> isCompleted =
      drift.GeneratedColumn<bool>('is_completed', aliasedName, false,
          type: DriftSqlType.bool,
          requiredDuringInsert: false,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("is_completed" IN (0, 1))'),
          defaultValue: const drift.Constant(false));
  static const drift.VerificationMeta _completionDateMeta =
      const drift.VerificationMeta('completionDate');
  @override
  late final drift.GeneratedColumn<DateTime> completionDate =
      drift.GeneratedColumn<DateTime>('completion_date', aliasedName, true,
          type: DriftSqlType.dateTime, requiredDuringInsert: false);
  @override
  late final drift.GeneratedColumnWithTypeConverter<Priority, String> priority =
      drift.GeneratedColumn<String>('priority', aliasedName, false,
              type: DriftSqlType.string,
              requiredDuringInsert: false,
              defaultValue: const drift.Constant('urgent_important'))
          .withConverter<Priority>($TasksTable.$converterpriority);
  @override
  List<drift.GeneratedColumn> get $columns => [
        id,
        title,
        notes,
        creationDate,
        dueDate,
        isCompleted,
        completionDate,
        priority
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'tasks';
  @override
  drift.VerificationContext validateIntegrity(
      drift.Insertable<TaskData> instance,
      {bool isInserting = false}) {
    final context = drift.VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('notes')) {
      context.handle(
          _notesMeta, notes.isAcceptableOrUnknown(data['notes']!, _notesMeta));
    }
    if (data.containsKey('creation_date')) {
      context.handle(
          _creationDateMeta,
          creationDate.isAcceptableOrUnknown(
              data['creation_date']!, _creationDateMeta));
    } else if (isInserting) {
      context.missing(_creationDateMeta);
    }
    if (data.containsKey('due_date')) {
      context.handle(_dueDateMeta,
          dueDate.isAcceptableOrUnknown(data['due_date']!, _dueDateMeta));
    } else if (isInserting) {
      context.missing(_dueDateMeta);
    }
    if (data.containsKey('is_completed')) {
      context.handle(
          _isCompletedMeta,
          isCompleted.isAcceptableOrUnknown(
              data['is_completed']!, _isCompletedMeta));
    }
    if (data.containsKey('completion_date')) {
      context.handle(
          _completionDateMeta,
          completionDate.isAcceptableOrUnknown(
              data['completion_date']!, _completionDateMeta));
    }
    return context;
  }

  @override
  Set<drift.GeneratedColumn> get $primaryKey => {id};
  @override
  TaskData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TaskData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      notes: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}notes'])!,
      creationDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}creation_date'])!,
      dueDate: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}due_date'])!,
      isCompleted: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_completed'])!,
      completionDate: attachedDatabase.typeMapping.read(
          DriftSqlType.dateTime, data['${effectivePrefix}completion_date']),
      priority: $TasksTable.$converterpriority.fromSql(attachedDatabase
          .typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}priority'])!),
    );
  }

  @override
  $TasksTable createAlias(String alias) {
    return $TasksTable(attachedDatabase, alias);
  }

  static drift.TypeConverter<Priority, String> $converterpriority =
      PriorityConverter();
}

class TaskData extends drift.DataClass implements drift.Insertable<TaskData> {
  final String id;
  final String title;
  final String notes;
  final DateTime creationDate;
  final DateTime dueDate;
  final bool isCompleted;
  final DateTime? completionDate;
  final Priority priority;
  const TaskData(
      {required this.id,
      required this.title,
      required this.notes,
      required this.creationDate,
      required this.dueDate,
      required this.isCompleted,
      this.completionDate,
      required this.priority});
  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    map['id'] = drift.Variable<String>(id);
    map['title'] = drift.Variable<String>(title);
    map['notes'] = drift.Variable<String>(notes);
    map['creation_date'] = drift.Variable<DateTime>(creationDate);
    map['due_date'] = drift.Variable<DateTime>(dueDate);
    map['is_completed'] = drift.Variable<bool>(isCompleted);
    if (!nullToAbsent || completionDate != null) {
      map['completion_date'] = drift.Variable<DateTime>(completionDate);
    }
    {
      map['priority'] = drift.Variable<String>(
          $TasksTable.$converterpriority.toSql(priority));
    }
    return map;
  }

  TasksCompanion toCompanion(bool nullToAbsent) {
    return TasksCompanion(
      id: drift.Value(id),
      title: drift.Value(title),
      notes: drift.Value(notes),
      creationDate: drift.Value(creationDate),
      dueDate: drift.Value(dueDate),
      isCompleted: drift.Value(isCompleted),
      completionDate: completionDate == null && nullToAbsent
          ? const drift.Value.absent()
          : drift.Value(completionDate),
      priority: drift.Value(priority),
    );
  }

  factory TaskData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return TaskData(
      id: serializer.fromJson<String>(json['id']),
      title: serializer.fromJson<String>(json['title']),
      notes: serializer.fromJson<String>(json['notes']),
      creationDate: serializer.fromJson<DateTime>(json['creationDate']),
      dueDate: serializer.fromJson<DateTime>(json['dueDate']),
      isCompleted: serializer.fromJson<bool>(json['isCompleted']),
      completionDate: serializer.fromJson<DateTime?>(json['completionDate']),
      priority: serializer.fromJson<Priority>(json['priority']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'title': serializer.toJson<String>(title),
      'notes': serializer.toJson<String>(notes),
      'creationDate': serializer.toJson<DateTime>(creationDate),
      'dueDate': serializer.toJson<DateTime>(dueDate),
      'isCompleted': serializer.toJson<bool>(isCompleted),
      'completionDate': serializer.toJson<DateTime?>(completionDate),
      'priority': serializer.toJson<Priority>(priority),
    };
  }

  TaskData copyWith(
          {String? id,
          String? title,
          String? notes,
          DateTime? creationDate,
          DateTime? dueDate,
          bool? isCompleted,
          drift.Value<DateTime?> completionDate = const drift.Value.absent(),
          Priority? priority}) =>
      TaskData(
        id: id ?? this.id,
        title: title ?? this.title,
        notes: notes ?? this.notes,
        creationDate: creationDate ?? this.creationDate,
        dueDate: dueDate ?? this.dueDate,
        isCompleted: isCompleted ?? this.isCompleted,
        completionDate:
            completionDate.present ? completionDate.value : this.completionDate,
        priority: priority ?? this.priority,
      );
  TaskData copyWithCompanion(TasksCompanion data) {
    return TaskData(
      id: data.id.present ? data.id.value : this.id,
      title: data.title.present ? data.title.value : this.title,
      notes: data.notes.present ? data.notes.value : this.notes,
      creationDate: data.creationDate.present
          ? data.creationDate.value
          : this.creationDate,
      dueDate: data.dueDate.present ? data.dueDate.value : this.dueDate,
      isCompleted:
          data.isCompleted.present ? data.isCompleted.value : this.isCompleted,
      completionDate: data.completionDate.present
          ? data.completionDate.value
          : this.completionDate,
      priority: data.priority.present ? data.priority.value : this.priority,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TaskData(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('notes: $notes, ')
          ..write('creationDate: $creationDate, ')
          ..write('dueDate: $dueDate, ')
          ..write('isCompleted: $isCompleted, ')
          ..write('completionDate: $completionDate, ')
          ..write('priority: $priority')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, title, notes, creationDate, dueDate,
      isCompleted, completionDate, priority);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TaskData &&
          other.id == this.id &&
          other.title == this.title &&
          other.notes == this.notes &&
          other.creationDate == this.creationDate &&
          other.dueDate == this.dueDate &&
          other.isCompleted == this.isCompleted &&
          other.completionDate == this.completionDate &&
          other.priority == this.priority);
}

class TasksCompanion extends drift.UpdateCompanion<TaskData> {
  final drift.Value<String> id;
  final drift.Value<String> title;
  final drift.Value<String> notes;
  final drift.Value<DateTime> creationDate;
  final drift.Value<DateTime> dueDate;
  final drift.Value<bool> isCompleted;
  final drift.Value<DateTime?> completionDate;
  final drift.Value<Priority> priority;
  final drift.Value<int> rowid;
  const TasksCompanion({
    this.id = const drift.Value.absent(),
    this.title = const drift.Value.absent(),
    this.notes = const drift.Value.absent(),
    this.creationDate = const drift.Value.absent(),
    this.dueDate = const drift.Value.absent(),
    this.isCompleted = const drift.Value.absent(),
    this.completionDate = const drift.Value.absent(),
    this.priority = const drift.Value.absent(),
    this.rowid = const drift.Value.absent(),
  });
  TasksCompanion.insert({
    required String id,
    required String title,
    this.notes = const drift.Value.absent(),
    required DateTime creationDate,
    required DateTime dueDate,
    this.isCompleted = const drift.Value.absent(),
    this.completionDate = const drift.Value.absent(),
    this.priority = const drift.Value.absent(),
    this.rowid = const drift.Value.absent(),
  })  : id = drift.Value(id),
        title = drift.Value(title),
        creationDate = drift.Value(creationDate),
        dueDate = drift.Value(dueDate);
  static drift.Insertable<TaskData> custom({
    drift.Expression<String>? id,
    drift.Expression<String>? title,
    drift.Expression<String>? notes,
    drift.Expression<DateTime>? creationDate,
    drift.Expression<DateTime>? dueDate,
    drift.Expression<bool>? isCompleted,
    drift.Expression<DateTime>? completionDate,
    drift.Expression<String>? priority,
    drift.Expression<int>? rowid,
  }) {
    return drift.RawValuesInsertable({
      if (id != null) 'id': id,
      if (title != null) 'title': title,
      if (notes != null) 'notes': notes,
      if (creationDate != null) 'creation_date': creationDate,
      if (dueDate != null) 'due_date': dueDate,
      if (isCompleted != null) 'is_completed': isCompleted,
      if (completionDate != null) 'completion_date': completionDate,
      if (priority != null) 'priority': priority,
      if (rowid != null) 'rowid': rowid,
    });
  }

  TasksCompanion copyWith(
      {drift.Value<String>? id,
      drift.Value<String>? title,
      drift.Value<String>? notes,
      drift.Value<DateTime>? creationDate,
      drift.Value<DateTime>? dueDate,
      drift.Value<bool>? isCompleted,
      drift.Value<DateTime?>? completionDate,
      drift.Value<Priority>? priority,
      drift.Value<int>? rowid}) {
    return TasksCompanion(
      id: id ?? this.id,
      title: title ?? this.title,
      notes: notes ?? this.notes,
      creationDate: creationDate ?? this.creationDate,
      dueDate: dueDate ?? this.dueDate,
      isCompleted: isCompleted ?? this.isCompleted,
      completionDate: completionDate ?? this.completionDate,
      priority: priority ?? this.priority,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    if (id.present) {
      map['id'] = drift.Variable<String>(id.value);
    }
    if (title.present) {
      map['title'] = drift.Variable<String>(title.value);
    }
    if (notes.present) {
      map['notes'] = drift.Variable<String>(notes.value);
    }
    if (creationDate.present) {
      map['creation_date'] = drift.Variable<DateTime>(creationDate.value);
    }
    if (dueDate.present) {
      map['due_date'] = drift.Variable<DateTime>(dueDate.value);
    }
    if (isCompleted.present) {
      map['is_completed'] = drift.Variable<bool>(isCompleted.value);
    }
    if (completionDate.present) {
      map['completion_date'] = drift.Variable<DateTime>(completionDate.value);
    }
    if (priority.present) {
      map['priority'] = drift.Variable<String>(
          $TasksTable.$converterpriority.toSql(priority.value));
    }
    if (rowid.present) {
      map['rowid'] = drift.Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TasksCompanion(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('notes: $notes, ')
          ..write('creationDate: $creationDate, ')
          ..write('dueDate: $dueDate, ')
          ..write('isCompleted: $isCompleted, ')
          ..write('completionDate: $completionDate, ')
          ..write('priority: $priority, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $SubTasksTable extends SubTasks
    with drift.TableInfo<$SubTasksTable, SubTaskData> {
  @override
  final drift.GeneratedDatabase attachedDatabase;
  final String? _alias;
  $SubTasksTable(this.attachedDatabase, [this._alias]);
  static const drift.VerificationMeta _idMeta =
      const drift.VerificationMeta('id');
  @override
  late final drift.GeneratedColumn<String> id = drift.GeneratedColumn<String>(
      'id', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const drift.VerificationMeta _parentTaskIdMeta =
      const drift.VerificationMeta('parentTaskId');
  @override
  late final drift.GeneratedColumn<String> parentTaskId =
      drift.GeneratedColumn<String>('parent_task_id', aliasedName, false,
          type: DriftSqlType.string,
          requiredDuringInsert: true,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'REFERENCES tasks (id) ON DELETE CASCADE'));
  static const drift.VerificationMeta _titleMeta =
      const drift.VerificationMeta('title');
  @override
  late final drift.GeneratedColumn<String> title =
      drift.GeneratedColumn<String>('title', aliasedName, false,
          additionalChecks: GeneratedColumn.checkTextLength(
              minTextLength: 1, maxTextLength: 200),
          type: DriftSqlType.string,
          requiredDuringInsert: true);
  static const drift.VerificationMeta _isCompletedMeta =
      const drift.VerificationMeta('isCompleted');
  @override
  late final drift.GeneratedColumn<bool> isCompleted =
      drift.GeneratedColumn<bool>('is_completed', aliasedName, false,
          type: DriftSqlType.bool,
          requiredDuringInsert: false,
          defaultConstraints: GeneratedColumn.constraintIsAlways(
              'CHECK ("is_completed" IN (0, 1))'),
          defaultValue: const drift.Constant(false));
  @override
  List<drift.GeneratedColumn> get $columns =>
      [id, parentTaskId, title, isCompleted];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'sub_tasks';
  @override
  drift.VerificationContext validateIntegrity(
      drift.Insertable<SubTaskData> instance,
      {bool isInserting = false}) {
    final context = drift.VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('parent_task_id')) {
      context.handle(
          _parentTaskIdMeta,
          parentTaskId.isAcceptableOrUnknown(
              data['parent_task_id']!, _parentTaskIdMeta));
    } else if (isInserting) {
      context.missing(_parentTaskIdMeta);
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('is_completed')) {
      context.handle(
          _isCompletedMeta,
          isCompleted.isAcceptableOrUnknown(
              data['is_completed']!, _isCompletedMeta));
    }
    return context;
  }

  @override
  Set<drift.GeneratedColumn> get $primaryKey => {id};
  @override
  SubTaskData map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return SubTaskData(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      parentTaskId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}parent_task_id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      isCompleted: attachedDatabase.typeMapping
          .read(DriftSqlType.bool, data['${effectivePrefix}is_completed'])!,
    );
  }

  @override
  $SubTasksTable createAlias(String alias) {
    return $SubTasksTable(attachedDatabase, alias);
  }
}

class SubTaskData extends drift.DataClass
    implements drift.Insertable<SubTaskData> {
  final String id;
  final String parentTaskId;
  final String title;
  final bool isCompleted;
  const SubTaskData(
      {required this.id,
      required this.parentTaskId,
      required this.title,
      required this.isCompleted});
  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    map['id'] = drift.Variable<String>(id);
    map['parent_task_id'] = drift.Variable<String>(parentTaskId);
    map['title'] = drift.Variable<String>(title);
    map['is_completed'] = drift.Variable<bool>(isCompleted);
    return map;
  }

  SubTasksCompanion toCompanion(bool nullToAbsent) {
    return SubTasksCompanion(
      id: drift.Value(id),
      parentTaskId: drift.Value(parentTaskId),
      title: drift.Value(title),
      isCompleted: drift.Value(isCompleted),
    );
  }

  factory SubTaskData.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return SubTaskData(
      id: serializer.fromJson<String>(json['id']),
      parentTaskId: serializer.fromJson<String>(json['parentTaskId']),
      title: serializer.fromJson<String>(json['title']),
      isCompleted: serializer.fromJson<bool>(json['isCompleted']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= drift.driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'parentTaskId': serializer.toJson<String>(parentTaskId),
      'title': serializer.toJson<String>(title),
      'isCompleted': serializer.toJson<bool>(isCompleted),
    };
  }

  SubTaskData copyWith(
          {String? id,
          String? parentTaskId,
          String? title,
          bool? isCompleted}) =>
      SubTaskData(
        id: id ?? this.id,
        parentTaskId: parentTaskId ?? this.parentTaskId,
        title: title ?? this.title,
        isCompleted: isCompleted ?? this.isCompleted,
      );
  SubTaskData copyWithCompanion(SubTasksCompanion data) {
    return SubTaskData(
      id: data.id.present ? data.id.value : this.id,
      parentTaskId: data.parentTaskId.present
          ? data.parentTaskId.value
          : this.parentTaskId,
      title: data.title.present ? data.title.value : this.title,
      isCompleted:
          data.isCompleted.present ? data.isCompleted.value : this.isCompleted,
    );
  }

  @override
  String toString() {
    return (StringBuffer('SubTaskData(')
          ..write('id: $id, ')
          ..write('parentTaskId: $parentTaskId, ')
          ..write('title: $title, ')
          ..write('isCompleted: $isCompleted')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, parentTaskId, title, isCompleted);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is SubTaskData &&
          other.id == this.id &&
          other.parentTaskId == this.parentTaskId &&
          other.title == this.title &&
          other.isCompleted == this.isCompleted);
}

class SubTasksCompanion extends drift.UpdateCompanion<SubTaskData> {
  final drift.Value<String> id;
  final drift.Value<String> parentTaskId;
  final drift.Value<String> title;
  final drift.Value<bool> isCompleted;
  final drift.Value<int> rowid;
  const SubTasksCompanion({
    this.id = const drift.Value.absent(),
    this.parentTaskId = const drift.Value.absent(),
    this.title = const drift.Value.absent(),
    this.isCompleted = const drift.Value.absent(),
    this.rowid = const drift.Value.absent(),
  });
  SubTasksCompanion.insert({
    required String id,
    required String parentTaskId,
    required String title,
    this.isCompleted = const drift.Value.absent(),
    this.rowid = const drift.Value.absent(),
  })  : id = drift.Value(id),
        parentTaskId = drift.Value(parentTaskId),
        title = drift.Value(title);
  static drift.Insertable<SubTaskData> custom({
    drift.Expression<String>? id,
    drift.Expression<String>? parentTaskId,
    drift.Expression<String>? title,
    drift.Expression<bool>? isCompleted,
    drift.Expression<int>? rowid,
  }) {
    return drift.RawValuesInsertable({
      if (id != null) 'id': id,
      if (parentTaskId != null) 'parent_task_id': parentTaskId,
      if (title != null) 'title': title,
      if (isCompleted != null) 'is_completed': isCompleted,
      if (rowid != null) 'rowid': rowid,
    });
  }

  SubTasksCompanion copyWith(
      {drift.Value<String>? id,
      drift.Value<String>? parentTaskId,
      drift.Value<String>? title,
      drift.Value<bool>? isCompleted,
      drift.Value<int>? rowid}) {
    return SubTasksCompanion(
      id: id ?? this.id,
      parentTaskId: parentTaskId ?? this.parentTaskId,
      title: title ?? this.title,
      isCompleted: isCompleted ?? this.isCompleted,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, drift.Expression> toColumns(bool nullToAbsent) {
    final map = <String, drift.Expression>{};
    if (id.present) {
      map['id'] = drift.Variable<String>(id.value);
    }
    if (parentTaskId.present) {
      map['parent_task_id'] = drift.Variable<String>(parentTaskId.value);
    }
    if (title.present) {
      map['title'] = drift.Variable<String>(title.value);
    }
    if (isCompleted.present) {
      map['is_completed'] = drift.Variable<bool>(isCompleted.value);
    }
    if (rowid.present) {
      map['rowid'] = drift.Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('SubTasksCompanion(')
          ..write('id: $id, ')
          ..write('parentTaskId: $parentTaskId, ')
          ..write('title: $title, ')
          ..write('isCompleted: $isCompleted, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$LocalDatabase extends drift.GeneratedDatabase {
  _$LocalDatabase(QueryExecutor e) : super(e);
  $LocalDatabaseManager get managers => $LocalDatabaseManager(this);
  late final $TasksTable tasks = $TasksTable(this);
  late final $SubTasksTable subTasks = $SubTasksTable(this);
  @override
  Iterable<drift.TableInfo<drift.Table, Object?>> get allTables =>
      allSchemaEntities.whereType<drift.TableInfo<drift.Table, Object?>>();
  @override
  List<drift.DatabaseSchemaEntity> get allSchemaEntities => [tasks, subTasks];
  @override
  drift.StreamQueryUpdateRules get streamUpdateRules =>
      const StreamQueryUpdateRules(
        [
          drift.WritePropagation(
            on: drift.TableUpdateQuery.onTableName('tasks',
                limitUpdateKind: drift.UpdateKind.delete),
            result: [
              drift.TableUpdate('sub_tasks', kind: drift.UpdateKind.delete),
            ],
          ),
        ],
      );
}

typedef $$TasksTableCreateCompanionBuilder = TasksCompanion Function({
  required String id,
  required String title,
  drift.Value<String> notes,
  required DateTime creationDate,
  required DateTime dueDate,
  drift.Value<bool> isCompleted,
  drift.Value<DateTime?> completionDate,
  drift.Value<Priority> priority,
  drift.Value<int> rowid,
});
typedef $$TasksTableUpdateCompanionBuilder = TasksCompanion Function({
  drift.Value<String> id,
  drift.Value<String> title,
  drift.Value<String> notes,
  drift.Value<DateTime> creationDate,
  drift.Value<DateTime> dueDate,
  drift.Value<bool> isCompleted,
  drift.Value<DateTime?> completionDate,
  drift.Value<Priority> priority,
  drift.Value<int> rowid,
});

final class $$TasksTableReferences
    extends drift.BaseReferences<_$LocalDatabase, $TasksTable, TaskData> {
  $$TasksTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static drift.MultiTypedResultKey<$SubTasksTable, List<SubTaskData>>
      _subTasksRefsTable(_$LocalDatabase db) =>
          drift.MultiTypedResultKey.fromTable(db.subTasks,
              aliasName: drift.$_aliasNameGenerator(
                  db.tasks.id, db.subTasks.parentTaskId));

  $$SubTasksTableProcessedTableManager get subTasksRefs {
    final manager = $$SubTasksTableTableManager($_db, $_db.subTasks).filter(
        (f) => f.parentTaskId.id.sqlEquals($_itemColumn<String>('id')!));

    final cache = $_typedResult.readTableOrNull(_subTasksRefsTable($_db));
    return drift.ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$TasksTableFilterComposer
    extends drift.Composer<_$LocalDatabase, $TasksTable> {
  $$TasksTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get notes => $composableBuilder(
      column: $table.notes, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<DateTime> get creationDate => $composableBuilder(
      column: $table.creationDate,
      builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<DateTime> get dueDate => $composableBuilder(
      column: $table.dueDate, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<bool> get isCompleted => $composableBuilder(
      column: $table.isCompleted,
      builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<DateTime> get completionDate => $composableBuilder(
      column: $table.completionDate,
      builder: (column) => drift.ColumnFilters(column));

  drift.ColumnWithTypeConverterFilters<Priority, Priority, String>
      get priority => $composableBuilder(
          column: $table.priority,
          builder: (column) => drift.ColumnWithTypeConverterFilters(column));

  drift.Expression<bool> subTasksRefs(
      drift.Expression<bool> Function($$SubTasksTableFilterComposer f) f) {
    final $$SubTasksTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.subTasks,
        getReferencedColumn: (t) => t.parentTaskId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$SubTasksTableFilterComposer(
              $db: $db,
              $table: $db.subTasks,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$TasksTableOrderingComposer
    extends drift.Composer<_$LocalDatabase, $TasksTable> {
  $$TasksTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get notes => $composableBuilder(
      column: $table.notes, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<DateTime> get creationDate => $composableBuilder(
      column: $table.creationDate,
      builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<DateTime> get dueDate => $composableBuilder(
      column: $table.dueDate,
      builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<bool> get isCompleted => $composableBuilder(
      column: $table.isCompleted,
      builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<DateTime> get completionDate => $composableBuilder(
      column: $table.completionDate,
      builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get priority => $composableBuilder(
      column: $table.priority,
      builder: (column) => drift.ColumnOrderings(column));
}

class $$TasksTableAnnotationComposer
    extends drift.Composer<_$LocalDatabase, $TasksTable> {
  $$TasksTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  drift.GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  drift.GeneratedColumn<String> get notes =>
      $composableBuilder(column: $table.notes, builder: (column) => column);

  drift.GeneratedColumn<DateTime> get creationDate => $composableBuilder(
      column: $table.creationDate, builder: (column) => column);

  drift.GeneratedColumn<DateTime> get dueDate =>
      $composableBuilder(column: $table.dueDate, builder: (column) => column);

  drift.GeneratedColumn<bool> get isCompleted => $composableBuilder(
      column: $table.isCompleted, builder: (column) => column);

  drift.GeneratedColumn<DateTime> get completionDate => $composableBuilder(
      column: $table.completionDate, builder: (column) => column);

  drift.GeneratedColumnWithTypeConverter<Priority, String> get priority =>
      $composableBuilder(column: $table.priority, builder: (column) => column);

  drift.Expression<T> subTasksRefs<T extends Object>(
      drift.Expression<T> Function($$SubTasksTableAnnotationComposer a) f) {
    final $$SubTasksTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.subTasks,
        getReferencedColumn: (t) => t.parentTaskId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$SubTasksTableAnnotationComposer(
              $db: $db,
              $table: $db.subTasks,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$TasksTableTableManager extends drift.RootTableManager<
    _$LocalDatabase,
    $TasksTable,
    TaskData,
    $$TasksTableFilterComposer,
    $$TasksTableOrderingComposer,
    $$TasksTableAnnotationComposer,
    $$TasksTableCreateCompanionBuilder,
    $$TasksTableUpdateCompanionBuilder,
    (TaskData, $$TasksTableReferences),
    TaskData,
    drift.PrefetchHooks Function({bool subTasksRefs})> {
  $$TasksTableTableManager(_$LocalDatabase db, $TasksTable table)
      : super(drift.TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$TasksTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$TasksTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$TasksTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            drift.Value<String> id = const drift.Value.absent(),
            drift.Value<String> title = const drift.Value.absent(),
            drift.Value<String> notes = const drift.Value.absent(),
            drift.Value<DateTime> creationDate = const drift.Value.absent(),
            drift.Value<DateTime> dueDate = const drift.Value.absent(),
            drift.Value<bool> isCompleted = const drift.Value.absent(),
            drift.Value<DateTime?> completionDate = const drift.Value.absent(),
            drift.Value<Priority> priority = const drift.Value.absent(),
            drift.Value<int> rowid = const drift.Value.absent(),
          }) =>
              TasksCompanion(
            id: id,
            title: title,
            notes: notes,
            creationDate: creationDate,
            dueDate: dueDate,
            isCompleted: isCompleted,
            completionDate: completionDate,
            priority: priority,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String title,
            drift.Value<String> notes = const drift.Value.absent(),
            required DateTime creationDate,
            required DateTime dueDate,
            drift.Value<bool> isCompleted = const drift.Value.absent(),
            drift.Value<DateTime?> completionDate = const drift.Value.absent(),
            drift.Value<Priority> priority = const drift.Value.absent(),
            drift.Value<int> rowid = const drift.Value.absent(),
          }) =>
              TasksCompanion.insert(
            id: id,
            title: title,
            notes: notes,
            creationDate: creationDate,
            dueDate: dueDate,
            isCompleted: isCompleted,
            completionDate: completionDate,
            priority: priority,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$TasksTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({subTasksRefs = false}) {
            return drift.PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [if (subTasksRefs) db.subTasks],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (subTasksRefs)
                    await drift.$_getPrefetchedData<TaskData, $TasksTable,
                            SubTaskData>(
                        currentTable: table,
                        referencedTable:
                            $$TasksTableReferences._subTasksRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$TasksTableReferences(db, table, p0).subTasksRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.parentTaskId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$TasksTableProcessedTableManager = drift.ProcessedTableManager<
    _$LocalDatabase,
    $TasksTable,
    TaskData,
    $$TasksTableFilterComposer,
    $$TasksTableOrderingComposer,
    $$TasksTableAnnotationComposer,
    $$TasksTableCreateCompanionBuilder,
    $$TasksTableUpdateCompanionBuilder,
    (TaskData, $$TasksTableReferences),
    TaskData,
    drift.PrefetchHooks Function({bool subTasksRefs})>;
typedef $$SubTasksTableCreateCompanionBuilder = SubTasksCompanion Function({
  required String id,
  required String parentTaskId,
  required String title,
  drift.Value<bool> isCompleted,
  drift.Value<int> rowid,
});
typedef $$SubTasksTableUpdateCompanionBuilder = SubTasksCompanion Function({
  drift.Value<String> id,
  drift.Value<String> parentTaskId,
  drift.Value<String> title,
  drift.Value<bool> isCompleted,
  drift.Value<int> rowid,
});

final class $$SubTasksTableReferences
    extends drift.BaseReferences<_$LocalDatabase, $SubTasksTable, SubTaskData> {
  $$SubTasksTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $TasksTable _parentTaskIdTable(_$LocalDatabase db) =>
      db.tasks.createAlias(
          drift.$_aliasNameGenerator(db.subTasks.parentTaskId, db.tasks.id));

  $$TasksTableProcessedTableManager get parentTaskId {
    final $_column = $_itemColumn<String>('parent_task_id')!;

    final manager = $$TasksTableTableManager($_db, $_db.tasks)
        .filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_parentTaskIdTable($_db));
    if (item == null) return manager;
    return drift.ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$SubTasksTableFilterComposer
    extends drift.Composer<_$LocalDatabase, $SubTasksTable> {
  $$SubTasksTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => drift.ColumnFilters(column));

  drift.ColumnFilters<bool> get isCompleted => $composableBuilder(
      column: $table.isCompleted,
      builder: (column) => drift.ColumnFilters(column));

  $$TasksTableFilterComposer get parentTaskId {
    final $$TasksTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.parentTaskId,
        referencedTable: $db.tasks,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$TasksTableFilterComposer(
              $db: $db,
              $table: $db.tasks,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$SubTasksTableOrderingComposer
    extends drift.Composer<_$LocalDatabase, $SubTasksTable> {
  $$SubTasksTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => drift.ColumnOrderings(column));

  drift.ColumnOrderings<bool> get isCompleted => $composableBuilder(
      column: $table.isCompleted,
      builder: (column) => drift.ColumnOrderings(column));

  $$TasksTableOrderingComposer get parentTaskId {
    final $$TasksTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.parentTaskId,
        referencedTable: $db.tasks,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$TasksTableOrderingComposer(
              $db: $db,
              $table: $db.tasks,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$SubTasksTableAnnotationComposer
    extends drift.Composer<_$LocalDatabase, $SubTasksTable> {
  $$SubTasksTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  drift.GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  drift.GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  drift.GeneratedColumn<bool> get isCompleted => $composableBuilder(
      column: $table.isCompleted, builder: (column) => column);

  $$TasksTableAnnotationComposer get parentTaskId {
    final $$TasksTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.parentTaskId,
        referencedTable: $db.tasks,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$TasksTableAnnotationComposer(
              $db: $db,
              $table: $db.tasks,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$SubTasksTableTableManager extends drift.RootTableManager<
    _$LocalDatabase,
    $SubTasksTable,
    SubTaskData,
    $$SubTasksTableFilterComposer,
    $$SubTasksTableOrderingComposer,
    $$SubTasksTableAnnotationComposer,
    $$SubTasksTableCreateCompanionBuilder,
    $$SubTasksTableUpdateCompanionBuilder,
    (SubTaskData, $$SubTasksTableReferences),
    SubTaskData,
    drift.PrefetchHooks Function({bool parentTaskId})> {
  $$SubTasksTableTableManager(_$LocalDatabase db, $SubTasksTable table)
      : super(drift.TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$SubTasksTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$SubTasksTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$SubTasksTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            drift.Value<String> id = const drift.Value.absent(),
            drift.Value<String> parentTaskId = const drift.Value.absent(),
            drift.Value<String> title = const drift.Value.absent(),
            drift.Value<bool> isCompleted = const drift.Value.absent(),
            drift.Value<int> rowid = const drift.Value.absent(),
          }) =>
              SubTasksCompanion(
            id: id,
            parentTaskId: parentTaskId,
            title: title,
            isCompleted: isCompleted,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String parentTaskId,
            required String title,
            drift.Value<bool> isCompleted = const drift.Value.absent(),
            drift.Value<int> rowid = const drift.Value.absent(),
          }) =>
              SubTasksCompanion.insert(
            id: id,
            parentTaskId: parentTaskId,
            title: title,
            isCompleted: isCompleted,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$SubTasksTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({parentTaskId = false}) {
            return drift.PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends drift.TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (parentTaskId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.parentTaskId,
                    referencedTable:
                        $$SubTasksTableReferences._parentTaskIdTable(db),
                    referencedColumn:
                        $$SubTasksTableReferences._parentTaskIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$SubTasksTableProcessedTableManager = drift.ProcessedTableManager<
    _$LocalDatabase,
    $SubTasksTable,
    SubTaskData,
    $$SubTasksTableFilterComposer,
    $$SubTasksTableOrderingComposer,
    $$SubTasksTableAnnotationComposer,
    $$SubTasksTableCreateCompanionBuilder,
    $$SubTasksTableUpdateCompanionBuilder,
    (SubTaskData, $$SubTasksTableReferences),
    SubTaskData,
    drift.PrefetchHooks Function({bool parentTaskId})>;

class $LocalDatabaseManager {
  final _$LocalDatabase _db;
  $LocalDatabaseManager(this._db);
  $$TasksTableTableManager get tasks =>
      $$TasksTableTableManager(_db, _db.tasks);
  $$SubTasksTableTableManager get subTasks =>
      $$SubTasksTableTableManager(_db, _db.subTasks);
}
