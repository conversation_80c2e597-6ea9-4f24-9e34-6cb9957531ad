import 'package:drift/drift.dart';

/// Performance monitoring for database operations
class DatabasePerformanceMonitor {
  final QueryExecutor _executor;

  DatabasePerformanceMonitor(this._executor);

  /// Get comprehensive performance statistics
  Future<Map<String, dynamic>> getPerformanceStats() async {
    final stats = <String, dynamic>{};

    try {
      // Get cache statistics
      final cacheStats = await _executor.runSelect('''
        SELECT 
          cache_hit_ratio,
          cache_hit,
          cache_miss
        FROM pragma_cache_stats()
      ''', []);

      if (cacheStats.isNotEmpty) {
        final row = cacheStats.first;
        stats['cache_hit_ratio'] = row['hit_ratio'];
        stats['cache_hits'] = row['cache_hit'];
        stats['cache_misses'] = row['cache_miss'];
      }

      // Get page statistics
      final pageStats = await _executor.runSelect('''
        SELECT 
          page_count,
          page_size,
          freelist_count,
          (page_count * page_size) as total_size,
          (freelist_count * page_size) as free_space
        FROM pragma_page_count(), pragma_page_size(), pragma_freelist_count()
      ''', []);

      if (pageStats.isNotEmpty) {
        final row = pageStats.first;
        stats['total_pages'] = row['page_count'];
        stats['page_size'] = row['page_size'];
        stats['free_pages'] = row['freelist_count'];
        stats['total_size_bytes'] = row['total_size'];
        stats['free_space_bytes'] = row['free_space'];
      }

      // Get WAL mode statistics
      final walStats =
          await _executor.runSelect('PRAGMA wal_checkpoint(PASSIVE)', []);
      if (walStats.isNotEmpty) {
        final row = walStats.first;
        stats['wal_busy'] = row['busy'];
        stats['wal_log'] = row['log'];
        stats['wal_checkpointed'] = row['checkpointed'];
      }

      // Get table statistics
      final tableStats = await _getTableStatistics();
      stats['tables'] = tableStats;
    } catch (e) {
      stats['error'] = 'Failed to collect performance stats: $e';
    }

    return stats;
  }

  /// Get statistics for each table
  Future<Map<String, Map<String, dynamic>>> _getTableStatistics() async {
    final tableStats = <String, Map<String, dynamic>>{};

    try {
      // Get table names
      final tables = await _executor.runSelect('''
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      ''', []);

      for (final table in tables) {
        final tableName = table['name'] as String;

        // Get row count
        final countResult = await _executor.runSelect(
          'SELECT COUNT(*) as count FROM "$tableName"',
          [],
        );

        final rowCount =
            countResult.isNotEmpty ? countResult.first['count'] as int : 0;

        // Get table size
        final sizeResult = await _executor.runSelect('''
          SELECT 
            pgsize,
            unused
          FROM dbstat 
          WHERE name = ?
        ''', [tableName]);

        int totalSize = 0;
        int unusedSize = 0;

        for (final row in sizeResult) {
          totalSize += row['pgsize'] as int;
          unusedSize += row['unused'] as int;
        }

        tableStats[tableName] = {
          'row_count': rowCount,
          'total_size_bytes': totalSize,
          'unused_size_bytes': unusedSize,
          'efficiency_percent': totalSize > 0
              ? ((totalSize - unusedSize) / totalSize * 100).round()
              : 100,
        };
      }
    } catch (e) {
      // dbstat might not be available in all SQLite builds
      // logger.e('Could not get detailed table statistics: $e');
    }

    return tableStats;
  }

  /// Analyze query execution plans
  Future<List<Map<String, dynamic>>> analyzeQuery(String sql) async {
    try {
      final results = await _executor.runSelect('EXPLAIN QUERY PLAN $sql', []);
      return results.map((row) => row).toList();
    } catch (e) {
      return [
        {'error': 'Failed to analyze query: $e'}
      ];
    }
  }

  /// Get index usage statistics
  Future<List<Map<String, dynamic>>> getIndexUsageStats() async {
    try {
      final results = await _executor.runSelect('''
        SELECT 
          name,
          tbl,
          CASE 
            WHEN stat IS NOT NULL THEN 'Used'
            ELSE 'Unused'
          END as usage_status
        FROM sqlite_master sm
        LEFT JOIN sqlite_stat1 s1 ON sm.name = s1.idx
        WHERE sm.type = 'index' 
          AND sm.name NOT LIKE 'sqlite_%'
        ORDER BY sm.name
      ''', []);

      return results.map((row) => row).toList();
    } catch (e) {
      return [
        {'error': 'Failed to get index usage stats: $e'}
      ];
    }
  }

  /// Optimize database performance
  Future<Map<String, dynamic>> optimizeDatabase() async {
    final results = <String, dynamic>{};
    final stopwatch = Stopwatch()..start();

    try {
      // Update statistics
      await _executor.runCustom('ANALYZE');
      results['analyze_completed'] = true;

      // Vacuum database
      await _executor.runCustom('VACUUM');
      results['vacuum_completed'] = true;

      // Checkpoint WAL
      final walResult =
          await _executor.runSelect('PRAGMA wal_checkpoint(TRUNCATE)', []);
      if (walResult.isNotEmpty) {
        results['wal_checkpoint'] = walResult.first;
      }

      stopwatch.stop();
      results['optimization_time_ms'] = stopwatch.elapsedMilliseconds;
      results['success'] = true;
    } catch (e) {
      stopwatch.stop();
      results['error'] = 'Optimization failed: $e';
      results['optimization_time_ms'] = stopwatch.elapsedMilliseconds;
      results['success'] = false;
    }

    return results;
  }

  /// Monitor query performance
  Future<Map<String, dynamic>> monitorQuery(
    String queryName,
    Future<dynamic> Function() query, {
    Duration? slowQueryThreshold,
  }) async {
    final stopwatch = Stopwatch()..start();
    final threshold = slowQueryThreshold ?? const Duration(milliseconds: 100);

    try {
      final result = await query();
      stopwatch.stop();

      final duration = stopwatch.elapsed;
      final isSlow = duration > threshold;

      if (isSlow) {
        // logger.w('Slow query detected: $queryName took ${duration.inMilliseconds}ms');
      }

      return {
        'query_name': queryName,
        'duration_ms': duration.inMilliseconds,
        'is_slow': isSlow,
        'threshold_ms': threshold.inMilliseconds,
        'success': true,
        'result': result,
      };
    } catch (e) {
      stopwatch.stop();

      return {
        'query_name': queryName,
        'duration_ms': stopwatch.elapsed.inMilliseconds,
        'is_slow': false,
        'threshold_ms': threshold.inMilliseconds,
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Check for potential performance issues
  Future<List<String>> checkPerformanceIssues() async {
    final issues = <String>[];

    try {
      // Check for missing indexes on foreign keys
      final foreignKeys = await _executor.runSelect('''
        SELECT DISTINCT m.name as table_name, p.from as column_name
        FROM sqlite_master m, pragma_foreign_key_list(m.name) p
        WHERE m.type = 'table'
      ''', []);

      for (final fk in foreignKeys) {
        final tableName = fk['table_name'] as String;
        final columnName = fk['column_name'] as String;

        final indexExists = await _executor.runSelect('''
          SELECT COUNT(*) as count
          FROM sqlite_master 
          WHERE type = 'index' 
            AND sql LIKE '%$tableName%' 
            AND sql LIKE '%$columnName%'
        ''', []);

        if (indexExists.isNotEmpty && indexExists.first['count'] == 0) {
          issues.add('Missing index on foreign key: $tableName.$columnName');
        }
      }

      // Check for large tables without proper indexes
      final stats = await getPerformanceStats();
      if (stats['tables'] is Map) {
        final tables = stats['tables'] as Map<String, Map<String, dynamic>>;

        for (final entry in tables.entries) {
          final tableName = entry.key;
          final tableStats = entry.value;
          final rowCount = tableStats['row_count'] as int? ?? 0;

          if (rowCount > 1000) {
            // Check if table has adequate indexes
            final indexes = await _executor.runSelect('''
              SELECT COUNT(*) as count
              FROM sqlite_master 
              WHERE type = 'index' AND tbl_name = ?
            ''', [tableName]);

            final indexCount =
                indexes.isNotEmpty ? indexes.first['count'] as int : 0;

            if (indexCount < 2) {
              // At least primary key + one other index
              issues.add(
                  'Large table $tableName ($rowCount rows) may need more indexes');
            }
          }
        }
      }

      // Check cache hit ratio
      if (stats['cache_hit_ratio'] is num) {
        final hitRatio = stats['cache_hit_ratio'] as num;
        if (hitRatio < 90) {
          issues.add('Low cache hit ratio: ${hitRatio.toStringAsFixed(1)}%');
        }
      }

      // Check for fragmentation
      if (stats['free_space_bytes'] is int &&
          stats['total_size_bytes'] is int) {
        final freeSpace = stats['free_space_bytes'] as int;
        final totalSize = stats['total_size_bytes'] as int;

        if (totalSize > 0) {
          final fragmentationPercent = (freeSpace / totalSize) * 100;
          if (fragmentationPercent > 25) {
            issues.add(
                'High fragmentation: ${fragmentationPercent.toStringAsFixed(1)}% free space');
          }
        }
      }
    } catch (e) {
      issues.add('Error checking performance issues: $e');
    }

    return issues;
  }
}
