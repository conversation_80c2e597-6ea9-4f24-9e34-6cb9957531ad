import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

/// Database configuration for different environments
class DatabaseConfig {
  /// Database file name
  static const String databaseName = 'mytodospace.db';

  /// Default cache size (in pages)
  static const int defaultCacheSize = 10000;

  /// Default busy timeout (in milliseconds)
  static const int defaultBusyTimeout = 30000;

  /// Performance optimization settings
  static const Map<String, String> performanceSettings = {
    'journal_mode': 'WAL',
    'synchronous': 'NORMAL',
    'temp_store': 'MEMORY',
    'mmap_size': '268435456', // 256MB
    'cache_spill': 'false',
  };

  /// Create production database connection
  static LazyDatabase createProductionDatabase() {
    return LazyDatabase(() async {
      // Ensure Flutter binding is initialized before accessing path_provider
      try {
        final dbFolder = await getApplicationDocumentsDirectory();
        final file = File(p.join(dbFolder.path, databaseName));

        return NativeDatabase.createInBackground(
          file,
          logStatements: false, // Disable in production
          setup: (database) async {
            // Performance settings are temporarily skipped to avoid compatibility issues.
          },
        );
      } catch (e) {
        // Fallback to a temporary directory if path_provider fails
        final tempDir = Directory.systemTemp;
        final file = File(p.join(tempDir.path, 'fallback_$databaseName'));

        return NativeDatabase.createInBackground(
          file,
          logStatements: true, // Enable logging for fallback
          setup: (database) async {
            // Performance settings are temporarily skipped to avoid compatibility issues.
          },
        );
      }
    });
  }

  /// Create development database connection
  static LazyDatabase createDevelopmentDatabase() {
    return LazyDatabase(() async {
      try {
        final dbFolder = await getApplicationDocumentsDirectory();
        final file = File(p.join(dbFolder.path, 'dev_$databaseName'));

        return NativeDatabase.createInBackground(
          file,
          logStatements: true, // Enable logging in development
          setup: (database) async {
            // Performance settings are temporarily skipped to avoid compatibility issues.
          },
        );
      } catch (e) {
        // Fallback to a temporary directory if path_provider fails
        final tempDir = Directory.systemTemp;
        final file = File(p.join(tempDir.path, 'dev_fallback_$databaseName'));

        return NativeDatabase.createInBackground(
          file,
          logStatements: true, // Enable logging for fallback
          setup: (database) async {
            // Performance settings are temporarily skipped to avoid compatibility issues.
          },
        );
      }
    });
  }

  /// Create test database connection (in-memory)
  static QueryExecutor createTestDatabase() {
    return NativeDatabase.memory(
      logStatements: true,
      setup: (database) async {
        // Performance settings are temporarily skipped to avoid compatibility issues.
      },
    );
  }

  /// Get database file path for backup/restore operations
  static Future<String> getDatabasePath() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    return p.join(dbFolder.path, databaseName);
  }

  /// Check if database file exists
  static Future<bool> databaseExists() async {
    final path = await getDatabasePath();
    return File(path).exists();
  }

  /// Get database file size in bytes
  static Future<int> getDatabaseFileSize() async {
    final path = await getDatabasePath();
    final file = File(path);

    if (await file.exists()) {
      return await file.length();
    }

    return 0;
  }

  /// Backup database to specified path
  static Future<void> backupDatabase(String backupPath) async {
    final sourcePath = await getDatabasePath();
    final sourceFile = File(sourcePath);

    if (await sourceFile.exists()) {
      await sourceFile.copy(backupPath);
    } else {
      throw Exception('Database file does not exist');
    }
  }

  /// Restore database from backup
  static Future<void> restoreDatabase(String backupPath) async {
    final targetPath = await getDatabasePath();
    final backupFile = File(backupPath);

    if (await backupFile.exists()) {
      await backupFile.copy(targetPath);
    } else {
      throw Exception('Backup file does not exist');
    }
  }

  /// Delete database file (for testing or reset)
  static Future<void> deleteDatabase() async {
    final path = await getDatabasePath();
    final file = File(path);

    if (await file.exists()) {
      await file.delete();
    }

    // Also delete WAL and SHM files if they exist
    final walFile = File('$path-wal');
    final shmFile = File('$path-shm');

    if (await walFile.exists()) {
      await walFile.delete();
    }

    if (await shmFile.exists()) {
      await shmFile.delete();
    }
  }
}
