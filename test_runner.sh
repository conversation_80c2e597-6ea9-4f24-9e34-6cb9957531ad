#!/bin/bash

# MyToDoSpace 自动化测试脚本
# 用于运行所有测试并生成报告

echo "🚀 开始运行 MyToDoSpace 自动化测试套件..."
echo "=================================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 函数：运行测试并统计结果
run_test() {
    local test_name=$1
    local test_command=$2
    
    echo -e "${BLUE}📋 运行 $test_name...${NC}"
    
    if eval $test_command; then
        echo -e "${GREEN}✅ $test_name 通过${NC}"
        ((PASSED_TESTS++))
    else
        echo -e "${RED}❌ $test_name 失败${NC}"
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
    echo ""
}

# 1. 检查依赖
echo -e "${YELLOW}🔍 检查项目依赖...${NC}"
flutter pub get
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 依赖安装失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 依赖检查完成${NC}"
echo ""

# 2. 代码生成
echo -e "${YELLOW}🔧 运行代码生成...${NC}"
dart run build_runner build --delete-conflicting-outputs
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 代码生成失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 代码生成完成${NC}"
echo ""

# 3. 静态分析
echo -e "${YELLOW}🔍 运行静态代码分析...${NC}"
flutter analyze --no-fatal-infos
if [ $? -ne 0 ]; then
    echo -e "${YELLOW}⚠️  静态分析发现问题，但继续运行测试${NC}"
else
    echo -e "${GREEN}✅ 静态分析通过${NC}"
fi
echo ""

# 4. 单元测试
echo -e "${BLUE}🧪 运行单元测试...${NC}"
echo "=================================================="

# Domain层测试
run_test "Task Model 测试" "flutter test test/unit/domain/models/task_model_test.dart"
run_test "Summary Report Model 测试" "flutter test test/unit/domain/models/summary_report_model_test.dart"

# Data层测试
run_test "Task Repository 测试" "flutter test test/unit/data/repositories/task_repository_impl_test.dart"

# Presentation层测试
run_test "TaskList BLoC 测试" "flutter test test/unit/features/tasks/bloc/task_list_bloc_test.dart"
run_test "Calendar BLoC 测试" "flutter test test/unit/features/calendar/bloc/calendar_bloc_test.dart"
run_test "Summary BLoC 测试" "flutter test test/unit/features/summary/bloc/summary_bloc_test.dart"

# 5. Widget测试
echo -e "${BLUE}🎨 运行Widget测试...${NC}"
echo "=================================================="
run_test "Calendar Month View 测试" "flutter test test/widget/calendar_month_view_test.dart"

# 6. 集成测试（可选，需要模拟器）
echo -e "${BLUE}🔗 检查集成测试...${NC}"
echo "=================================================="
if command -v flutter &> /dev/null && flutter devices | grep -q "macos"; then
    run_test "应用集成测试" "flutter test integration_test/app_integration_test.dart"
else
    echo -e "${YELLOW}⚠️  跳过集成测试（需要macOS设备）${NC}"
fi

# 7. 测试覆盖率（可选）
echo -e "${BLUE}📊 生成测试覆盖率报告...${NC}"
echo "=================================================="
flutter test --coverage
if [ $? -eq 0 ] && [ -f "coverage/lcov.info" ]; then
    echo -e "${GREEN}✅ 覆盖率报告已生成: coverage/lcov.info${NC}"
    
    # 如果安装了lcov，生成HTML报告
    if command -v genhtml &> /dev/null; then
        genhtml coverage/lcov.info -o coverage/html
        echo -e "${GREEN}📈 HTML覆盖率报告: coverage/html/index.html${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  覆盖率报告生成失败${NC}"
fi
echo ""

# 8. 构建测试
echo -e "${BLUE}🏗️  测试应用构建...${NC}"
echo "=================================================="
run_test "macOS构建测试" "flutter build macos --debug"

# 9. 性能测试（基础检查）
echo -e "${BLUE}⚡ 基础性能检查...${NC}"
echo "=================================================="

# 检查包大小
if [ -d "build/macos" ]; then
    APP_SIZE=$(du -sh build/macos | cut -f1)
    echo -e "${BLUE}📦 应用大小: $APP_SIZE${NC}"
fi

# 检查依赖数量
DEP_COUNT=$(grep -c "^  [a-zA-Z]" pubspec.yaml)
echo -e "${BLUE}📚 依赖数量: $DEP_COUNT${NC}"

# 检查代码行数
if command -v find &> /dev/null; then
    CODE_LINES=$(find lib -name "*.dart" -exec wc -l {} + | tail -1 | awk '{print $1}')
    echo -e "${BLUE}📝 代码行数: $CODE_LINES${NC}"
fi

echo ""

# 10. 最终报告
echo "=================================================="
echo -e "${BLUE}📋 测试总结报告${NC}"
echo "=================================================="
echo -e "总测试数: ${TOTAL_TESTS}"
echo -e "${GREEN}通过: ${PASSED_TESTS}${NC}"
echo -e "${RED}失败: ${FAILED_TESTS}${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}🎉 所有测试通过！应用质量良好。${NC}"
    exit 0
else
    echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查并修复。${NC}"
    exit 1
fi