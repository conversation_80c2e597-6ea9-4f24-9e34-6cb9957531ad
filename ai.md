请你用简体中文来告诉我目前的项目状态、错误个数、以及你接下来要做的事情，毕竟我需要能预计的计划而不是一直看似很忙碌的样子，可以吗？？？别忘了要严格遵守index.md的所有指令和你的身份，另外：

所有问题请你精心梳理、分析、执行，另外再按照你的安排继进行，别忘了要严格遵守index.md的所有指令和你的身份 @/Users/<USER>/pro-test-space/mytodospace/index.md @/Users/<USER>/pro-test-space/mytodospace/.kiro/specs/enterprise-todo-app/tasks.md @/

有几个问题需要优化：
1. 日期内的点击more之后内容溢出，可以做到内容多了就在内部可滚动即可
2. 移除“新建任务”，顶部有了就没有必要加这个
3. 目前tab的选中高亮没有做
4. 日期内的内容太小，请合适处理
5. 在右侧的内容列表右下方悬浮一个icon按钮，点击后当前tab的当前列表置顶
6. 另外因为 “2.”的执行，整体右侧内容区的宽度还可以缩小，

减少这个留白的距离就行，
7. 内容区的列表中的字体字号请你调整，目前字号都很大，做关联性调整到紧凑！紧凑！紧凑！紧凑！

1. 日期内的右上角的数字角标数据未居中
2. 任务列表和搜索和新建任务全部移除，即这一行都移除，下面的整体上移填充
3. 我纵向滚动了一下，然后找个任务点击详情，然后操作子任务状态，然后会发现列表闪了一下且滚动了顶部，我希望不要滚动，不要闪烁，另外其他操作还有编辑之后等等操作都会闪烁一下、滚动顶部，我希望都不要，要无感！！
4. 另外我希望日期内只能显示四条（包括more），这意味着字体字号大小可以大一号，点击more之后如果内容多，则最多展示5条数据然后可纵向滚动，目前留白太多，所以才要求上面的描述变动

接下来详情和编辑都有几个共同的问题，我们一并修复优化好： 
1.  创建时间、截止时间、子任务进度、最后更新，这四个最好类似标签一样尽可能的紧凑，一小条一小条的排版好，不要太占地方，毕竟是辅助效果，不是主要内容，显示！但是不是主要视觉效果
2. 顶部的完成状态和关闭不在同一行上
3. 子任务只要有一个没完成，那主任务就是没完成，全部子任务完成了才可以是完成状态
4. 子任务列表也需要紧凑！紧凑！紧凑！
5. 不要太多留白空间
6. 弹窗我希望布局内容尽可能的紧凑！紧凑！紧凑！

1.  创建时间、截止时间、子任务进度、最后更新，这四个最好类似标签一样尽可能的紧凑，一排排好！难道一排排不下吗？，一小条一小条的排版好，不要太占地方，毕竟是辅助效果，不是主要内容，显示！但是不是主要视觉效果
2. 子任务只要有一个没完成，那主任务就是没完成，全部子任务完成了才可以是完成状态
3. 子任务列表也需要紧凑！紧凑！紧凑！
4. 上面说的那个更新子任务状态后，列表还是会闪烁一下！！！禁止闪烁，我要求无感！！
要求的事极高的交互和ui

1. 创建时间、截止时间、子任务进度、最后更新，目前没有了文字标注，我觉得还是要加上，也是一行排列，字体不要太小！，例如：每块上下展示：
上面：创建时间 ；下面：2025-09-03 10:00
2. 右侧tabs统计的应该是当前选中日期的对应象限数量，还有内容区的展示列表也是对应日期的对应象限的事项，请你以最专业的技术修复一下，别忘了你的定位，你在做什么，不允许有冗余方案、冗余代码，需要每个函数都要测试通过后再进行下一个函数的实现，必须要采用高端极强性能的算法而不是随便实现！
3. 你看图片上，这些同级的标题不要字体、背景色、字号大小、颜色、样式不一样，这么基础的你不要再犯错了，整体的这种基础的排版你在仔细的专业的优化一下
4. 详情、编辑、新增，弹窗内的各个块要在视觉上有所辨识度，不然哪个字段是哪个块上的都容易混淆
5. 编辑的截止日期要现实年月日，选择器要专业的本地化模式（简体中文）

1. 你的详情页（新增、编辑）字体又小、间距又没有，在干嘛？紧凑就是这种紧凑？？？以狭小视觉体验为代价？？？重新设计！！！
2. 右侧tabs统计的应该是当前选中日期的对应象限数量，还有内容区的展示列表也是对应日期的对应象限的事项


请你梳理所有问题，一一进行ui高质量优化 @/Users/<USER>/pro-test-space/mytodospace/index.md 
别忘了你的定位，你在做什么，不允许有冗余方案、冗余代码，需要每个函数都要测试通过后再进行下一个函数的实现，必须要采用高端极强性能的算法而不是随便实现！

You are working on a todo application project located at `/Users/<USER>/pro-test-space/mytodospace`. This is a critical UI/UX optimization task that requires absolute precision and thoroughness. The primary focus is on resolving styling issues, improving visual design, and enhancing element coordination WITHOUT modifying existing functionality.

**CRITICAL CONSTRAINTS:**
- NO modification of existing functionality is permitted
- NO creation of new features or behavioral changes
- Focus EXCLUSIVELY on UI/UX improvements: styling, layout, spacing, visual hierarchy, and design coordination
- Every change must maintain 100% backward compatibility with existing functionality
- All function signatures, input parameters, output parameters, and behavior must remain unchanged

**Step 1: Mandatory Project Documentation Analysis**
- Use `view` tool to read and analyze ALL instructions in `index.md` and referenced files
- Use `codebase-retrieval` to identify all documentation files containing UI/UX requirements, design standards, and styling guidelines
- Document your understanding of the design system, color schemes, typography, and layout principles
- These design standards are binding for all styling work

**Step 2: Current UI/UX State Assessment**
- Use `view` tool to systematically examine the entire project directory structure, focusing on UI components and styling files
- Use `codebase-retrieval` to understand the current styling architecture, CSS/styling patterns, and component design
- Create a detailed inventory of existing UI elements including:
  - Component styling and layout patterns
  - Color usage and consistency
  - Typography implementation
  - Spacing and padding systems
  - Responsive design patterns
  - Visual hierarchy and element coordination

**Step 3: Design Specification Analysis**
- Use `view` tool to read ALL content in `prompt/tasks.md` with focus on UI/UX related tasks
- Examine the entire `/prompt` directory for design specifications, mockups, and styling guidelines
- Use `codebase-retrieval` to cross-reference design specifications with actual styling implementation
- Identify specific UI/UX issues: compact layouts, unsightly design elements, poor element coordination

**Step 4: UI/UX Gap Analysis and Issue Identification**
- Compare design specifications against actual UI implementation using `codebase-retrieval` and `view` tools
- Identify specific styling issues:
  - Overly compact layouts that reduce usability
  - Inconsistent spacing and padding
  - Poor visual hierarchy
  - Uncoordinated element positioning
  - Color scheme inconsistencies
  - Typography problems
  - Responsive design failures
- Use `diagnostics` tool to check for styling-related errors or warnings
- Document each issue with specific file locations and proposed solutions

**Step 5: Styling Optimization Implementation**
- Use `str-replace-editor` to implement ONLY styling and layout improvements
- Focus on:
  - Improving spacing and padding for better visual breathing room
  - Enhancing visual hierarchy through better typography and color usage
  - Coordinating element positioning and alignment
  - Ensuring consistent design patterns across components
  - Optimizing responsive behavior
- Verify that NO functional code is modified - only CSS, styling classes, and layout properties

**Step 6: Visual Validation and Quality Assurance**
- Use `view` tool to examine updated styling implementations
- Compare final styling against design specifications pixel-by-pixel
- Test responsive behavior across different screen sizes
- Verify that all functional behavior remains unchanged
- Document all styling improvements with before/after comparisons

**ABSOLUTE REQUIREMENTS:**
- ZERO modification of JavaScript/TypeScript logic, API calls, data handling, or business logic
- ZERO changes to function signatures, parameters, or return values
- ONLY modify: CSS files, styling classes, layout properties, spacing, colors, typography, and visual elements
- Every styling change must improve usability and visual appeal
- Perfect adherence to established design system and patterns
- No assumptions about design preferences - follow existing design specifications exactly

**Quality Assurance Protocol:**
- Every styling change must be validated to ensure no functional regression
- All modifications must maintain existing accessibility standards
- Changes must be consistent with the overall design language
- No introduction of new dependencies or breaking changes

Begin by acknowledging your understanding of these UI/UX optimization requirements and your commitment to improving visual design while maintaining absolute functional integrity.






You are working on a Flutter todo application with strict UI/UX optimization constraints. Complete the following three tasks while maintaining 100% functional integrity:

**CRITICAL CONSTRAINTS FOR ALL TASKS:**
- NO modification of existing functionality or business logic
- NO changes to BLoC events, state management, or data flow
- Only UI/UX styling, layout, and visual improvements permitted
- All changes must use existing AppTheme design tokens from lib/app/theme.dart
- Maintain backward compatibility with existing component signatures
- Test all changes thoroughly before completion

**TASK 1: Year View Layout Optimization**
File: `lib/features/calendar/presentation/widgets/calendar_year_view.dart`

Requirements:
- Remove scrolling behavior - display all 12 months in a fixed grid layout that fits the available screen space
- Remove the right sidebar (today's tasks panel) from year view - year view should occupy full width
- Remove the top toolbar/header elements - show only the year grid
- Implement click-to-zoom functionality:
  - Clicking any month should zoom/expand that month for detailed interaction
  - Clicking individual dates within the expanded month should be easily accessible
  - Add a "return to year view" button/mechanism to restore the full year grid
- Ensure the year grid uses responsive layout that adapts to different screen sizes
- Use AppTheme spacing tokens for all padding, margins, and gaps

**TASK 2: Month View Task Display Bug Fix**
Files: `lib/features/calendar/presentation/widgets/calendar_month_view.dart` and related calendar navigation logic

Problem: When navigating from summary/report views back to month view, tasks within date cells disappear completely.

Requirements:
- Identify and fix ONLY the task visibility/loading issue when returning to month view
- DO NOT modify any other working functionality in the calendar system
- Ensure tasks are properly loaded and displayed in month view date cells after navigation
- Verify the fix works for all navigation paths: summary→month, report→month, year→month
- Implement comprehensive function testing to verify:
  - Task loading and display works correctly
  - Navigation between views maintains state properly
  - No regression in existing calendar functionality
  - All task CRUD operations continue to work as expected

**TASK 3: Dialog UI/UX Enhancement**
Files: 
- `lib/features/tasks/presentation/widgets/task_detail_dialog.dart`
- `lib/features/tasks/presentation/task_editor_dialog.dart`

Current Issues: Both dialogs have poor visual organization, inconsistent element sizing, inappropriate font sizes, poor color hierarchy, and inconsistent spacing.

Requirements:
- Improve visual hierarchy using AppTheme color tokens (textPrimary, textSecondary, textMuted)
- Standardize font sizes using consistent typography scale
- Implement proper spacing using AppTheme.spacing tokens (spacing1 through spacing6)
- Organize content with clear visual groupings and logical information architecture
- Ensure proper contrast ratios and accessibility compliance
- Maintain all existing form validation, data handling, and user interaction functionality
- Use AppTheme border and surface colors for consistent visual design
- Implement responsive layout that works across different dialog sizes

**DELIVERABLES:**
1. Complete each task with detailed before/after documentation
2. Provide comprehensive testing results for all modified functionality
3. Confirm no functional regressions in any existing features
4. Document all AppTheme tokens used and styling decisions made

Begin with Task 1 (Year View Layout), then proceed to Task 2 (Month View Bug Fix), and finally Task 3 (Dialog Enhancement).

--------------------

1. 本月总结、j本年总结，不用弄两个入口，直接合并一个入口，名为：本月/年总结，然后点击进去之后左边月、右边年，且不开新页面，而是左侧边栏的右侧的内容区显示即可
2. 四象限视图的标题和统计占空间太大了，现在移除他们，在右侧的内容区的底部将统计（含label）作为底部状态栏中的一部分，这样就不会占用内容空间了
3. 年视图的月视图内点击日期后，和2一样的规则处理，目前存在一个问题就是四象限内每个象限内容超出后无法滚动
4. 请你将年视图、月视图内的相关字体元素的字号大小调整一下，太小了，我要是近视，根本看不清写的啥，请你注意字号、字体、颜色、间距！！！！！！
I need you to make the following UI improvements to the todo application:

1. **Merge Monthly/Yearly Summary Entries**: 
   - Combine the separate "本月总结" and "本年总结" menu items into a single entry called "本月/年总结"
   - When clicked, display the content in the right content area (not a new page)
   - Show monthly summary on the left side and yearly summary on the right side within the same view

2. **Optimize Quadrant View Layout**:
   - Remove the large title and statistics sections from the quadrant view to save space
   - Move all statistics (including labels) to a bottom status bar in the right content area
   - This will free up content space while keeping the statistics accessible

3. **Fix Quadrant View Scrolling Issues**:
   - When clicking on dates in the year view's month view, apply the same layout optimization as item #2
   - Fix the current issue where quadrant content cannot scroll when it overflows within each quadrant
   - Ensure each quadrant has proper scrollable content areas

4. **Improve Typography and Readability**:
   - Increase font sizes across year view and month view components - current sizes are too small for users with vision difficulties
   - Pay attention to:
     - Font size (make significantly larger)
     - Font weight and family
     - Text color contrast
     - Line spacing and padding between elements
   - Ensure all text elements are clearly readable and accessible

Please focus on maintaining the existing functionality while improving the visual hierarchy and usability of these views.
---------------------

