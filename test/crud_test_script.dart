import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/core/di/injection.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/domain/repositories/task_repository.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_event.dart';

/// 自动化CRUD测试脚本
/// 测试所有任务的增删改查操作
void main() {
  group('CRUD操作全面测试', () {
    late TaskRepository taskRepository;
    late TaskListBloc taskListBloc;

    setUpAll(() async {
      print('🚀 开始CRUD测试初始化...');
      configureDependencies();
      taskRepository = getIt<TaskRepository>();
      taskListBloc = getIt<TaskListBloc>();
      print('✅ CRUD测试初始化完成');
    });

    tearDownAll(() async {
      print('🧹 清理CRUD测试资源...');
      await taskListBloc.close();
      print('✅ CRUD测试清理完成');
    });

    test('1. 创建任务测试', () async {
      print('\n🔄 开始测试：创建任务');
      
      final task = Task.create(
        title: 'CRUD测试任务',
        notes: '这是一个用于测试CRUD操作的任务',
        priority: Priority.importantNotUrgent,
        dueDate: DateTime.now().add(const Duration(days: 1)),
        subtasks: [],
      );

      // 创建任务后再添加子任务，确保有正确的parentTaskId
      final taskWithSubtasks = task.copyWith(
        subtasks: [
          SubTask.create(title: '子任务1', parentTaskId: task.id),
          SubTask.create(title: '子任务2', parentTaskId: task.id),
        ],
      );

      print('📝 任务数据: ${taskWithSubtasks.title}');
      print('📝 子任务数量: ${taskWithSubtasks.subtasks.length}');

      try {
        await taskRepository.createTask(taskWithSubtasks);
        print('✅ 任务创建成功');
        
        // 验证任务是否真的被创建
        final createdTask = await taskRepository.getTaskById(taskWithSubtasks.id);
        expect(createdTask, isNotNull);
        expect(createdTask!.title, equals(taskWithSubtasks.title));
        print('✅ 任务创建验证通过');
      } catch (e) {
        print('❌ 任务创建失败: $e');
        rethrow;
      }
    });

    test('2. 更新任务测试', () async {
      print('\n🔄 开始测试：更新任务');
      
      // 先创建一个任务
      final originalTask = Task.create(
        title: '原始任务',
        notes: '原始备注',
        priority: Priority.notUrgentNotImportant,
        dueDate: DateTime.now().add(const Duration(days: 1)),
      );

      await taskRepository.createTask(originalTask);
      print('📝 原始任务创建完成');

      // 更新任务
      final updatedTask = originalTask.copyWith(
        title: '更新后的任务',
        notes: '更新后的备注',
        priority: Priority.urgentImportant,
        isCompleted: true,
        completionDate: DateTime.now(),
      );

      print('📝 更新数据: ${updatedTask.title}');
      print('📝 完成状态: ${updatedTask.isCompleted}');

      try {
        await taskRepository.updateTask(updatedTask);
        print('✅ 任务更新成功');
        
        // 验证更新是否生效
        final retrievedTask = await taskRepository.getTaskById(updatedTask.id);
        expect(retrievedTask, isNotNull);
        expect(retrievedTask!.title, equals(updatedTask.title));
        expect(retrievedTask.isCompleted, equals(true));
        print('✅ 任务更新验证通过');
      } catch (e) {
        print('❌ 任务更新失败: $e');
        rethrow;
      }
    });

    test('3. 删除任务测试', () async {
      print('\n🔄 开始测试：删除任务');
      
      // 先创建一个任务
      final taskToDelete = Task.create(
        title: '待删除任务',
        notes: '这个任务将被删除',
        dueDate: DateTime.now().add(const Duration(days: 1)),
      );

      await taskRepository.createTask(taskToDelete);
      print('📝 待删除任务创建完成: ${taskToDelete.id}');

      try {
        await taskRepository.deleteTask(taskToDelete.id);
        print('✅ 任务删除成功');
        
        // 验证任务是否真的被删除
        final deletedTask = await taskRepository.getTaskById(taskToDelete.id);
        expect(deletedTask, isNull);
        print('✅ 任务删除验证通过');
      } catch (e) {
        print('❌ 任务删除失败: $e');
        rethrow;
      }
    });

    test('4. 子任务操作测试', () async {
      print('\n🔄 开始测试：子任务操作');
      
      final baseTask = Task.create(
        title: '带子任务的任务',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        subtasks: [],
      );

      final taskWithSubtasks = baseTask.copyWith(
        subtasks: [
          SubTask.create(title: '子任务A', parentTaskId: baseTask.id),
          SubTask.create(title: '子任务B', parentTaskId: baseTask.id),
          SubTask.create(title: '子任务C', parentTaskId: baseTask.id),
        ],
      );

      await taskRepository.createTask(taskWithSubtasks);
      print('📝 带子任务的任务创建完成，子任务数量: ${taskWithSubtasks.subtasks.length}');

      // 更新子任务状态
      final updatedSubtasks = taskWithSubtasks.subtasks.map((subtask) {
        if (subtask.title == '子任务A') {
          return subtask.copyWith(isCompleted: true);
        }
        return subtask;
      }).toList();

      final updatedTask = taskWithSubtasks.copyWith(subtasks: updatedSubtasks);

      try {
        await taskRepository.updateTask(updatedTask);
        print('✅ 子任务状态更新成功');
        
        // 验证子任务状态
        final retrievedTask = await taskRepository.getTaskById(updatedTask.id);
        expect(retrievedTask, isNotNull);
        final completedSubtasks = retrievedTask!.subtasks.where((s) => s.isCompleted).toList();
        expect(completedSubtasks.length, equals(1));
        print('✅ 子任务状态验证通过');
      } catch (e) {
        print('❌ 子任务操作失败: $e');
        rethrow;
      }
    });

    test('5. BLoC事件测试', () async {
      print('\n🔄 开始测试：BLoC事件');
      
      final testTask = Task.create(
        title: 'BLoC测试任务',
        notes: '用于测试BLoC事件的任务',
        dueDate: DateTime.now().add(const Duration(days: 1)),
      );

      try {
        // 测试任务创建事件
        print('📡 发送任务创建事件...');
        taskListBloc.add(TaskListEvent.taskCreated(testTask));

        // 等待更长时间让事件处理完成，包括自动刷新
        await Future.delayed(const Duration(milliseconds: 1500));
        print('✅ 任务创建事件处理完成');

        // 额外刷新BLoC状态以确保包含新创建的任务
        print('📡 发送额外刷新请求事件...');
        taskListBloc.add(const TaskListEvent.refreshRequested());
        await Future.delayed(const Duration(milliseconds: 1000));
        print('✅ 额外刷新请求事件处理完成');

        // 测试完成状态切换事件
        print('📡 发送完成状态切换事件...');
        taskListBloc.add(TaskListEvent.completionToggled(
          taskId: testTask.id,
          isCompleted: true,
        ));
        
        await Future.delayed(const Duration(milliseconds: 500));
        print('✅ 完成状态切换事件处理完成');

        // 测试任务更新事件
        final updatedTask = testTask.copyWith(title: 'BLoC测试任务（已更新）');
        print('📡 发送任务更新事件...');
        taskListBloc.add(TaskListEvent.taskUpdated(updatedTask));
        
        await Future.delayed(const Duration(milliseconds: 500));
        print('✅ 任务更新事件处理完成');

        print('✅ 所有BLoC事件测试通过');
      } catch (e) {
        print('❌ BLoC事件测试失败: $e');
        rethrow;
      }
    });

    test('6. 错误处理测试', () async {
      print('\n🔄 开始测试：错误处理');
      
      try {
        // 测试更新不存在的任务
        final nonExistentTask = Task(
          id: 'non_existent_id_12345',
          title: '不存在的任务',
          notes: '',
          isCompleted: false,
          priority: Priority.notUrgentNotImportant,
          dueDate: DateTime.now().add(const Duration(days: 1)),
          creationDate: DateTime.now(),
          subtasks: [],
        );

        print('📝 尝试更新不存在的任务...');
        await taskRepository.updateTask(nonExistentTask);
        
        // 如果没有抛出异常，测试失败
        fail('应该抛出TaskNotFoundException');
      } catch (e) {
        print('✅ 正确捕获到异常: ${e.runtimeType}');
        expect(e.toString().contains('not found') || e.toString().contains('TaskNotFoundException'), isTrue);
      }

      try {
        // 测试删除不存在的任务
        print('📝 尝试删除不存在的任务...');
        await taskRepository.deleteTask('non_existent_id_67890');
        
        // 如果没有抛出异常，测试失败
        fail('应该抛出TaskNotFoundException');
      } catch (e) {
        print('✅ 正确捕获到异常: ${e.runtimeType}');
        expect(e.toString().contains('not found') || e.toString().contains('TaskNotFoundException'), isTrue);
      }

      print('✅ 错误处理测试通过');
    });
  });
}
