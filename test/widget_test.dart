  // This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:mytodospace/main.dart';
import 'test_config.dart';
import 'test_config.dart';

void main() {
  testWidgets('Todo app loads correctly', (WidgetTester tester) async {
    // 设置测试环境
    await TestConfig.setupTestEnvironment();
    
    try {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const MyToDoApp());

      // Verify that the app starts up without errors
      await tester.pumpAndSettle();
      
      // Check that basic UI elements are present
      expect(find.byType(MaterialApp), findsOneWidget);
    } finally {
      // 清理测试环境
      await TestConfig.tearDownTestEnvironment();
    }
  });
}
