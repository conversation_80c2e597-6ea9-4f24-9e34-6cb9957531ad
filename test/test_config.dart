import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/domain/models/summary_report_model.dart';

import 'package:mytodospace/domain/repositories/task_repository.dart';
import 'package:mocktail/mocktail.dart';

class MockTaskRepository extends Mock implements TaskRepository {}

void setupTestFallbacks() {
  registerFallbackValue(DateTime(2025));
  registerFallbackValue(
      Task.create(title: 'fallback', dueDate: DateTime.now()));
  registerFallbackValue(Priority.urgentImportant);
}

/// 测试配置和工具类
class TestConfig {
  /// 设置测试环境
  static Future<void> setupTestEnvironment() async {
    // 初始化测试环境
    TestWidgetsFlutterBinding.ensureInitialized();
  }

  /// 清理测试环境
  static Future<void> tearDownTestEnvironment() async {
    // 清理测试资源
  }
}

/// 测试数据工厂类
class TestDataFactory {
  /// 创建测试用的Task
  static Task createTestTask({
    String? id,
    String? title,
    String? notes,
    DateTime? dueDate,
    Priority? priority,
    bool? isCompleted,
    DateTime? creationDate,
    DateTime? completionDate,
    List<SubTask>? subtasks,
  }) {
    return Task(
      id: id ?? 'task_${DateTime.now().millisecondsSinceEpoch}',
      title: title ?? '测试任务',
      notes: notes ?? '这是一个测试任务',
      creationDate: creationDate ?? DateTime.now(),
      dueDate: dueDate ?? DateTime.now().add(const Duration(days: 1)),
      priority: priority ?? Priority.urgentImportant,
      isCompleted: isCompleted ?? false,
      completionDate: completionDate,
      subtasks: subtasks ?? [],
    );
  }

  /// 创建测试用的子任务
  static SubTask createTestSubTask({
    String? id,
    String? title,
    String? parentTaskId,
    bool? isCompleted,
  }) {
    return SubTask(
      id: id ?? 'subtask_${DateTime.now().millisecondsSinceEpoch}',
      parentTaskId: parentTaskId ?? 'parent_task_id',
      title: title ?? '测试子任务',
      isCompleted: isCompleted ?? false,
    );
  }

  /// 创建测试用的SummaryReport
  static SummaryReport createTestSummaryReport({
    String? period,
    int? totalTasksCreated,
    int? totalTasksCompleted,
    double? completionRate,
    Map<Priority, int>? quadrantDistribution,
    List<Task>? highlights,
  }) {
    return SummaryReport(
      period: period ?? '2025年9月',
      totalTasksCreated: totalTasksCreated ?? 10,
      totalTasksCompleted: totalTasksCompleted ?? 7,
      completionRate: completionRate ?? 70.0,
      quadrantDistribution: quadrantDistribution ??
          {
            Priority.urgentImportant: 2,
            Priority.importantNotUrgent: 5,
            Priority.urgentNotImportant: 3,
            Priority.notUrgentNotImportant: 0,
          },
      highlights: highlights ?? [],
    );
  }

  /// 创建测试任务列表
  static List<Task> createTestTaskList({int count = 5}) {
    return List.generate(
        count,
        (index) => createTestTask(
              id: 'task_$index',
              title: '测试任务 $index',
              priority: Priority.values[index % Priority.values.length],
            ));
  }

  /// 创建带子任务的测试任务列表
  static List<Task> createTestTasksWithSubtasks(
      {int taskCount = 3, int subtaskCount = 2}) {
    final tasks = <Task>[];

    for (int i = 0; i < taskCount; i++) {
      final subtasks = <SubTask>[];

      for (int j = 0; j < subtaskCount; j++) {
        final subtask = createTestSubTask(
          id: 'subtask_${i}_$j',
          title: '子任务 ${i}_$j',
          parentTaskId: 'parent_task_$i',
        );
        subtasks.add(subtask);
      }

      final parentTask = createTestTask(
        id: 'parent_task_$i',
        title: '父任务 $i',
        subtasks: subtasks,
      );
      tasks.add(parentTask);
    }

    return tasks;
  }
}
