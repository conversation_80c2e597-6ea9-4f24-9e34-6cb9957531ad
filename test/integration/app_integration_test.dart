import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mytodospace/main.dart' as app;
import 'package:mytodospace/core/di/injection.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Integration Tests', () {
    setUpAll(() async {
      // Initialize dependencies
      configureDependencies();
    });

    testWidgets('should launch app and display calendar view', (tester) async {
      // Launch the app
      app.main();
      await tester.pumpAndSettle();

      // Verify that the calendar view is displayed
      expect(find.byType(Scaffold), findsOneWidget);

      // Verify toolbar elements
      expect(find.byIcon(Icons.chevron_left), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);
      expect(find.byIcon(Icons.today), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);

      // Verify calendar grid is present
      expect(find.text('日'), findsOneWidget);
      expect(find.text('一'), findsOneWidget);
      expect(find.text('二'), findsOneWidget);
      expect(find.text('三'), findsOneWidget);
      expect(find.text('四'), findsOneWidget);
      expect(find.text('五'), findsOneWidget);
      expect(find.text('六'), findsOneWidget);
    });

    testWidgets('should navigate to summary page', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Find and tap the monthly summary button in sidebar
      final monthlySummaryButton = find.text('本月总结');
      expect(monthlySummaryButton, findsOneWidget);

      await tester.tap(monthlySummaryButton);
      await tester.pumpAndSettle();

      // Verify we're on the summary page
      expect(find.text('统计概览'), findsOneWidget);
      expect(find.text('象限分布'), findsOneWidget);
    });

    testWidgets('should create a new task via quick add', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Find a date cell and double tap it
      final dateCell = find.text('15').first;
      await tester.tap(dateCell, warnIfMissed: false);
      await tester.pump();
      await tester.tap(dateCell, warnIfMissed: false);
      await tester.pumpAndSettle();

      // Verify task input appears in the task panel
      expect(find.text('重要且紧急'), findsOneWidget);
    });

    testWidgets('should open task editor dialog', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Tap the add button in toolbar
      final addButton = find.byIcon(Icons.add);
      await tester.tap(addButton);
      await tester.pumpAndSettle();

      // Verify task editor dialog opens
      expect(find.text('新建任务'), findsOneWidget);
      expect(find.text('任务标题'), findsOneWidget);
      expect(find.text('优先级'), findsOneWidget);
      expect(find.text('截止日期'), findsOneWidget);
    });

    testWidgets('should switch between different views', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Test switching to year view
      final yearViewButton = find.text('年视图');
      await tester.tap(yearViewButton);
      await tester.pumpAndSettle();

      // Test switching to quadrant view
      final quadrantViewButton = find.text('四象限视图');
      await tester.tap(quadrantViewButton);
      await tester.pumpAndSettle();

      // Switch back to month view
      final monthViewButton = find.text('月视图');
      await tester.tap(monthViewButton);
      await tester.pumpAndSettle();
    });

    testWidgets('should handle month navigation', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to next month
      final nextButton = find.byIcon(Icons.chevron_right);
      await tester.tap(nextButton);
      await tester.pumpAndSettle();

      // Navigate to previous month
      final prevButton = find.byIcon(Icons.chevron_left);
      await tester.tap(prevButton);
      await tester.pumpAndSettle();

      // Navigate to today
      final todayButton = find.byIcon(Icons.today);
      await tester.tap(todayButton);
      await tester.pumpAndSettle();
    });
  });
}
