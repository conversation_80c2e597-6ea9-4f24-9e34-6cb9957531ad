import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mytodospace/core/di/injection.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/domain/repositories/task_repository.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_event.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_state.dart';

void main() {
  group('Task Creation Integration Tests', () {
    late TaskRepository taskRepository;
    late TaskListBloc taskListBloc;

    setUpAll(() async {
      // Initialize Flutter binding for tests
      TestWidgetsFlutterBinding.ensureInitialized();

      // Wait for binding to be fully ready
      await Future.delayed(Duration.zero);

      // Initialize dependency injection
      configureDependencies();
      taskRepository = getIt<TaskRepository>();
      taskListBloc = getIt<TaskListBloc>();
    });

    tearDownAll(() async {
      await getIt.reset();
    });

    test('should create task through repository directly', () async {
      final task = Task.create(
        title: 'Integration Test Task',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.urgentImportant,
      );

      // This should work without throwing
      await taskRepository.createTask(task);

      // Verify task was created
      final retrievedTask = await taskRepository.getTaskById(task.id);
      expect(retrievedTask, isNotNull);
      expect(retrievedTask!.title, 'Integration Test Task');
    });

    test('should create task through BLoC', () async {
      final task = Task.create(
        title: 'BLoC Integration Test Task',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.importantNotUrgent,
      );

      // Listen to state changes
      final states = <TaskListState>[];
      final subscription = taskListBloc.stream.listen(states.add);

      // Trigger task creation
      taskListBloc.add(TaskListEvent.taskCreated(task));

      // Wait for processing
      await Future.delayed(const Duration(milliseconds: 500));

      subscription.cancel();

      // Check if any error occurred
      final hasError = states.any((state) => state.status == TaskListStatus.failure);
      if (hasError) {
        final errorState = states.firstWhere((state) => state.status == TaskListStatus.failure);
        fail('Task creation failed: ${errorState.errorMessage}');
      }

      // Verify task was created
      final retrievedTask = await taskRepository.getTaskById(task.id);
      expect(retrievedTask, isNotNull);
      expect(retrievedTask!.title, 'BLoC Integration Test Task');
    });

    test('should handle task creation with subtasks', () async {
      final task = Task.create(
        title: 'Task with Subtasks',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.urgentNotImportant,
      ).addSubtask('Subtask 1').addSubtask('Subtask 2');

      // Test through BLoC
      final states = <TaskListState>[];
      final subscription = taskListBloc.stream.listen(states.add);

      taskListBloc.add(TaskListEvent.taskCreated(task));
      await Future.delayed(const Duration(milliseconds: 500));

      subscription.cancel();

      // Check for errors
      final hasError = states.any((state) => state.status == TaskListStatus.failure);
      if (hasError) {
        final errorState = states.firstWhere((state) => state.status == TaskListStatus.failure);
        fail('Task creation with subtasks failed: ${errorState.errorMessage}');
      }

      // Verify task and subtasks were created
      final retrievedTask = await taskRepository.getTaskById(task.id);
      expect(retrievedTask, isNotNull);
      expect(retrievedTask!.subtasks.length, 2);
      expect(retrievedTask.subtasks[0].title, 'Subtask 1');
      expect(retrievedTask.subtasks[1].title, 'Subtask 2');

      // Verify subtask parent IDs are correctly set
      expect(retrievedTask.subtasks[0].parentTaskId, task.id);
      expect(retrievedTask.subtasks[1].parentTaskId, task.id);
    });

    test('should handle complex task with all fields and subtasks', () async {
      final now = DateTime.now();
      final dueDate = DateTime(now.year, now.month, now.day + 2, 15, 30); // Day after tomorrow at 3:30 PM

      final task = Task.create(
        title: 'Complex Task with All Fields',
        notes: 'This is a detailed task with notes and multiple subtasks',
        dueDate: dueDate,
        priority: Priority.importantNotUrgent,
        subtasks: [
          SubTask(
            id: 'temp_subtask_${DateTime.now().millisecondsSinceEpoch}_1',
            parentTaskId: 'temp_parent',
            title: 'First subtask',
            isCompleted: false,
          ),
          SubTask(
            id: 'temp_subtask_${DateTime.now().millisecondsSinceEpoch}_2',
            parentTaskId: 'temp_parent',
            title: 'Second subtask',
            isCompleted: false,
          ),
        ],
      );

      // Test through BLoC
      final states = <TaskListState>[];
      final subscription = taskListBloc.stream.listen(states.add);

      taskListBloc.add(TaskListEvent.taskCreated(task));
      await Future.delayed(const Duration(milliseconds: 500));

      subscription.cancel();

      // Check for errors
      final hasError = states.any((state) => state.status == TaskListStatus.failure);
      if (hasError) {
        final errorState = states.firstWhere((state) => state.status == TaskListStatus.failure);
        fail('Complex task creation failed: ${errorState.errorMessage}');
      }

      // Verify all fields were saved correctly
      final retrievedTask = await taskRepository.getTaskById(task.id);
      expect(retrievedTask, isNotNull);
      expect(retrievedTask!.title, 'Complex Task with All Fields');
      expect(retrievedTask.notes, 'This is a detailed task with notes and multiple subtasks');
      expect(retrievedTask.priority, Priority.importantNotUrgent);
      expect(retrievedTask.subtasks.length, 2);

      // Verify subtasks have correct parent IDs (not temp_parent)
      for (final subtask in retrievedTask.subtasks) {
        expect(subtask.parentTaskId, task.id);
        expect(subtask.parentTaskId, isNot('temp_parent'));
      }

      // Verify date handling
      expect(retrievedTask.dueDate.year, dueDate.year);
      expect(retrievedTask.dueDate.month, dueDate.month);
      expect(retrievedTask.dueDate.day, dueDate.day);
    });

    test('should handle task with today as due date', () async {
      final today = DateTime.now();
      final todayDate = DateTime(today.year, today.month, today.day);

      final task = Task.create(
        title: 'Task Due Today',
        dueDate: todayDate, // Only date, no time
        priority: Priority.urgentImportant,
      );

      // Test through BLoC
      final states = <TaskListState>[];
      final subscription = taskListBloc.stream.listen(states.add);

      taskListBloc.add(TaskListEvent.taskCreated(task));
      await Future.delayed(const Duration(milliseconds: 500));

      subscription.cancel();

      // Check for errors
      final hasError = states.any((state) => state.status == TaskListStatus.failure);
      if (hasError) {
        final errorState = states.firstWhere((state) => state.status == TaskListStatus.failure);
        fail('Today task creation failed: ${errorState.errorMessage}');
      }

      // Verify task was created with proper date handling
      final retrievedTask = await taskRepository.getTaskById(task.id);
      expect(retrievedTask, isNotNull);
      expect(retrievedTask!.dueDate.year, today.year);
      expect(retrievedTask.dueDate.month, today.month);
      expect(retrievedTask.dueDate.day, today.day);
      // Due date should be set to end of day (23:59:59)
      expect(retrievedTask.dueDate.hour, 23);
      expect(retrievedTask.dueDate.minute, 59);
    });
  });
}
