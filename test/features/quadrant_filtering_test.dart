import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:mytodospace/features/calendar/presentation/widgets/calendar_quadrant_view.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_bloc.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_state.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_state.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/app/theme.dart';

import 'quadrant_filtering_test.mocks.dart';

@GenerateMocks([CalendarBloc, TaskListBloc])
void main() {
  group('Quadrant Filtering and Statistics Tests', () {
    late MockCalendarBloc mockCalendarBloc;
    late MockTaskListBloc mockTaskListBloc;
    late DateTime testDate;
    late List<Task> testTasks;

    setUp(() {
      mockCalendarBloc = MockCalendarBloc();
      mockTaskListBloc = MockTaskListBloc();
      testDate = DateTime(2024, 1, 15);
      
      // Create test tasks for different dates and priorities
      testTasks = [
        // Tasks for test date
        Task(
          id: 'task1',
          title: 'Urgent Important Task',
          priority: Priority.urgentImportant,
          dueDate: testDate,
          creationDate: testDate,
          isCompleted: false,
          notes: '',
          subtasks: [],
        ),
        Task(
          id: 'task2',
          title: 'Important Not Urgent Task',
          priority: Priority.importantNotUrgent,
          dueDate: testDate,
          creationDate: testDate,
          isCompleted: false,
          notes: '',
          subtasks: [],
        ),
        Task(
          id: 'task3',
          title: 'Urgent Not Important Task',
          priority: Priority.urgentNotImportant,
          dueDate: testDate,
          creationDate: testDate,
          isCompleted: true,
          notes: '',
          subtasks: [],
        ),
        // Tasks for different date (should be filtered out)
        Task(
          id: 'task4',
          title: 'Different Date Task',
          priority: Priority.urgentImportant,
          dueDate: testDate.add(const Duration(days: 1)),
          creationDate: testDate.add(const Duration(days: 1)),
          isCompleted: false,
          notes: '',
          subtasks: [],
        ),
      ];
    });

    group('Date Filtering Tests', () {
      testWidgets('should filter tasks by selected date correctly', (tester) async {
        // Setup mock states
        when(mockCalendarBloc.state).thenReturn(
          CalendarState.initial().copyWith(selectedDate: testDate),
        );
        when(mockTaskListBloc.state).thenReturn(
          TaskListState.initial().copyWith(
            tasks: testTasks,
            status: TaskListStatus.success,
          ),
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: MultiBlocProvider(
              providers: [
                BlocProvider<CalendarBloc>.value(value: mockCalendarBloc),
                BlocProvider<TaskListBloc>.value(value: mockTaskListBloc),
              ],
              child: const Scaffold(
                body: CalendarQuadrantView(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify that only tasks from the selected date are shown
        // Task from different date should not be visible
        expect(find.text('Different Date Task'), findsNothing);
        
        // Tasks from selected date should be visible
        expect(find.text('Urgent Important Task'), findsOneWidget);
        expect(find.text('Important Not Urgent Task'), findsOneWidget);
        expect(find.text('Urgent Not Important Task'), findsOneWidget);
      });

      testWidgets('should handle empty task list gracefully', (tester) async {
        when(mockCalendarBloc.state).thenReturn(
          CalendarState.initial().copyWith(selectedDate: testDate),
        );
        when(mockTaskListBloc.state).thenReturn(
          TaskListState.initial().copyWith(
            tasks: [],
            status: TaskListStatus.success,
          ),
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: MultiBlocProvider(
              providers: [
                BlocProvider<CalendarBloc>.value(value: mockCalendarBloc),
                BlocProvider<TaskListBloc>.value(value: mockTaskListBloc),
              ],
              child: const Scaffold(
                body: CalendarQuadrantView(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Should show empty states for all quadrants
        expect(find.text('暂无任务'), findsWidgets);
      });
    });

    group('Quadrant Statistics Tests', () {
      testWidgets('should display correct quadrant statistics', (tester) async {
        when(mockCalendarBloc.state).thenReturn(
          CalendarState.initial().copyWith(selectedDate: testDate),
        );
        when(mockTaskListBloc.state).thenReturn(
          TaskListState.initial().copyWith(
            tasks: testTasks,
            status: TaskListStatus.success,
          ),
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: MultiBlocProvider(
              providers: [
                BlocProvider<CalendarBloc>.value(value: mockCalendarBloc),
                BlocProvider<TaskListBloc>.value(value: mockTaskListBloc),
              ],
              child: const Scaffold(
                body: CalendarQuadrantView(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Check statistics cards show correct counts
        // Should show 1 for urgent important (task1)
        expect(find.text('1'), findsWidgets);
        
        // Should show 0 for not urgent not important (no tasks)
        expect(find.text('0'), findsWidgets);
      });

      testWidgets('should update statistics when date changes', (tester) async {
        final newDate = testDate.add(const Duration(days: 1));
        
        // Initially show testDate
        when(mockCalendarBloc.state).thenReturn(
          CalendarState.initial().copyWith(selectedDate: testDate),
        );
        when(mockTaskListBloc.state).thenReturn(
          TaskListState.initial().copyWith(
            tasks: testTasks,
            status: TaskListStatus.success,
          ),
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: MultiBlocProvider(
              providers: [
                BlocProvider<CalendarBloc>.value(value: mockCalendarBloc),
                BlocProvider<TaskListBloc>.value(value: mockTaskListBloc),
              ],
              child: const Scaffold(
                body: CalendarQuadrantView(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Change to new date
        when(mockCalendarBloc.state).thenReturn(
          CalendarState.initial().copyWith(selectedDate: newDate),
        );

        await tester.pumpAndSettle();

        // Statistics should update to reflect new date
        // (task4 is on the new date)
        expect(find.text('Different Date Task'), findsOneWidget);
      });
    });

    group('Performance Tests', () {
      testWidgets('should handle large task lists efficiently', (tester) async {
        // Create a large list of tasks
        final largeTasks = List.generate(1000, (index) => Task(
          id: 'task_$index',
          title: 'Task $index',
          priority: Priority.values[index % 4],
          dueDate: testDate,
          creationDate: testDate,
          isCompleted: index % 2 == 0,
          notes: '',
          subtasks: [],
        ));

        when(mockCalendarBloc.state).thenReturn(
          CalendarState.initial().copyWith(selectedDate: testDate),
        );
        when(mockTaskListBloc.state).thenReturn(
          TaskListState.initial().copyWith(
            tasks: largeTasks,
            status: TaskListStatus.success,
          ),
        );

        final stopwatch = Stopwatch()..start();

        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: MultiBlocProvider(
              providers: [
                BlocProvider<CalendarBloc>.value(value: mockCalendarBloc),
                BlocProvider<TaskListBloc>.value(value: mockTaskListBloc),
              ],
              child: const Scaffold(
                body: CalendarQuadrantView(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();
        stopwatch.stop();

        // Should render within reasonable time (less than 1 second)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));

        // Should still show correct statistics
        expect(find.text('250'), findsWidgets); // 1000/4 = 250 per quadrant
      });
    });

    group('Date Display Tests', () {
      testWidgets('should show "今日任务" for today', (tester) async {
        final today = DateTime.now();
        
        when(mockCalendarBloc.state).thenReturn(
          CalendarState.initial().copyWith(selectedDate: today),
        );
        when(mockTaskListBloc.state).thenReturn(
          TaskListState.initial().copyWith(
            tasks: [],
            status: TaskListStatus.success,
          ),
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: MultiBlocProvider(
              providers: [
                BlocProvider<CalendarBloc>.value(value: mockCalendarBloc),
                BlocProvider<TaskListBloc>.value(value: mockTaskListBloc),
              ],
              child: const Scaffold(
                body: CalendarQuadrantView(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('今日任务'), findsOneWidget);
      });

      testWidgets('should show "明日任务" for tomorrow', (tester) async {
        final tomorrow = DateTime.now().add(const Duration(days: 1));
        
        when(mockCalendarBloc.state).thenReturn(
          CalendarState.initial().copyWith(selectedDate: tomorrow),
        );
        when(mockTaskListBloc.state).thenReturn(
          TaskListState.initial().copyWith(
            tasks: [],
            status: TaskListStatus.success,
          ),
        );

        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: MultiBlocProvider(
              providers: [
                BlocProvider<CalendarBloc>.value(value: mockCalendarBloc),
                BlocProvider<TaskListBloc>.value(value: mockTaskListBloc),
              ],
              child: const Scaffold(
                body: CalendarQuadrantView(),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        expect(find.text('明日任务'), findsOneWidget);
      });
    });
  });
}
