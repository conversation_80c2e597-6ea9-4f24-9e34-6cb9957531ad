// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in mytodospace/test/features/quadrant_filtering_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:flutter_bloc/flutter_bloc.dart' as _i7;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mytodospace/features/calendar/bloc/calendar_bloc.dart' as _i4;
import 'package:mytodospace/features/calendar/bloc/calendar_event.dart' as _i6;
import 'package:mytodospace/features/calendar/bloc/calendar_state.dart' as _i2;
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart' as _i8;
import 'package:mytodospace/features/tasks/bloc/task_list_event.dart' as _i9;
import 'package:mytodospace/features/tasks/bloc/task_list_state.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCalendarState_0 extends _i1.SmartFake implements _i2.CalendarState {
  _FakeCalendarState_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeTaskListState_1 extends _i1.SmartFake implements _i3.TaskListState {
  _FakeTaskListState_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [CalendarBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockCalendarBloc extends _i1.Mock implements _i4.CalendarBloc {
  MockCalendarBloc() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.CalendarState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeCalendarState_0(
          this,
          Invocation.getter(#state),
        ),
      ) as _i2.CalendarState);

  @override
  _i5.Stream<_i2.CalendarState> get stream => (super.noSuchMethod(
        Invocation.getter(#stream),
        returnValue: _i5.Stream<_i2.CalendarState>.empty(),
      ) as _i5.Stream<_i2.CalendarState>);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
      ) as bool);

  @override
  _i5.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void add(_i6.CalendarEvent? event) => super.noSuchMethod(
        Invocation.method(
          #add,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onEvent(_i6.CalendarEvent? event) => super.noSuchMethod(
        Invocation.method(
          #onEvent,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void emit(_i2.CalendarState? state) => super.noSuchMethod(
        Invocation.method(
          #emit,
          [state],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void on<E extends _i6.CalendarEvent>(
    _i7.EventHandler<E, _i2.CalendarState>? handler, {
    _i7.EventTransformer<E>? transformer,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #on,
          [handler],
          {#transformer: transformer},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onTransition(
          _i7.Transition<_i6.CalendarEvent, _i2.CalendarState>? transition) =>
      super.noSuchMethod(
        Invocation.method(
          #onTransition,
          [transition],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onChange(_i7.Change<_i2.CalendarState>? change) => super.noSuchMethod(
        Invocation.method(
          #onChange,
          [change],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addError(
    Object? error, [
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #addError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onError(
    Object? error,
    StackTrace? stackTrace,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #onError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<void> handleError(
    String? operation,
    Object? error, {
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #handleError,
          [
            operation,
            error,
          ],
          {
            #stackTrace: stackTrace,
            #context: context,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> logInfo({
    required String? operation,
    required String? message,
    Map<String, dynamic>? context,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logInfo,
          [],
          {
            #operation: operation,
            #message: message,
            #context: context,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  _i5.Future<void> logError({
    required String? operation,
    required Exception? exception,
    StackTrace? stackTrace,
    Map<String, dynamic>? context,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #logError,
          [],
          {
            #operation: operation,
            #exception: exception,
            #stackTrace: stackTrace,
            #context: context,
          },
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void recordPerformance(
    String? operation,
    int? durationMs,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #recordPerformance,
          [
            operation,
            durationMs,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  _i5.Future<T?> handleRecoverableError<T>(
    String? operation,
    _i5.Future<T> Function()? action, {
    int? maxRetries = 3,
    Duration? retryDelay = const Duration(milliseconds: 500),
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #handleRecoverableError,
          [
            operation,
            action,
          ],
          {
            #maxRetries: maxRetries,
            #retryDelay: retryDelay,
          },
        ),
        returnValue: _i5.Future<T?>.value(),
      ) as _i5.Future<T?>);
}

/// A class which mocks [TaskListBloc].
///
/// See the documentation for Mockito's code generation for more information.
class MockTaskListBloc extends _i1.Mock implements _i8.TaskListBloc {
  MockTaskListBloc() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.TaskListState get state => (super.noSuchMethod(
        Invocation.getter(#state),
        returnValue: _FakeTaskListState_1(
          this,
          Invocation.getter(#state),
        ),
      ) as _i3.TaskListState);

  @override
  _i5.Stream<_i3.TaskListState> get stream => (super.noSuchMethod(
        Invocation.getter(#stream),
        returnValue: _i5.Stream<_i3.TaskListState>.empty(),
      ) as _i5.Stream<_i3.TaskListState>);

  @override
  bool get isClosed => (super.noSuchMethod(
        Invocation.getter(#isClosed),
        returnValue: false,
      ) as bool);

  @override
  _i5.Future<void> close() => (super.noSuchMethod(
        Invocation.method(
          #close,
          [],
        ),
        returnValue: _i5.Future<void>.value(),
        returnValueForMissingStub: _i5.Future<void>.value(),
      ) as _i5.Future<void>);

  @override
  void add(_i9.TaskListEvent? event) => super.noSuchMethod(
        Invocation.method(
          #add,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onEvent(_i9.TaskListEvent? event) => super.noSuchMethod(
        Invocation.method(
          #onEvent,
          [event],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void emit(_i3.TaskListState? state) => super.noSuchMethod(
        Invocation.method(
          #emit,
          [state],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void on<E extends _i9.TaskListEvent>(
    _i7.EventHandler<E, _i3.TaskListState>? handler, {
    _i7.EventTransformer<E>? transformer,
  }) =>
      super.noSuchMethod(
        Invocation.method(
          #on,
          [handler],
          {#transformer: transformer},
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onTransition(
          _i7.Transition<_i9.TaskListEvent, _i3.TaskListState>? transition) =>
      super.noSuchMethod(
        Invocation.method(
          #onTransition,
          [transition],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onChange(_i7.Change<_i3.TaskListState>? change) => super.noSuchMethod(
        Invocation.method(
          #onChange,
          [change],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void addError(
    Object? error, [
    StackTrace? stackTrace,
  ]) =>
      super.noSuchMethod(
        Invocation.method(
          #addError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );

  @override
  void onError(
    Object? error,
    StackTrace? stackTrace,
  ) =>
      super.noSuchMethod(
        Invocation.method(
          #onError,
          [
            error,
            stackTrace,
          ],
        ),
        returnValueForMissingStub: null,
      );
}
