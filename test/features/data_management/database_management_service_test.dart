import 'dart:io';

import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/features/data_management/services/database_file_ops.dart';
import 'package:mytodospace/features/data_management/services/database_management_service.dart';
import 'package:mytodospace/features/data_management/services/markdown_exporter.dart';
import 'package:mytodospace/core/di/injection.dart';
import 'package:mytodospace/data/datasources/database_config.dart';
import 'package:mytodospace/data/datasources/local_database.dart';

class _FakeFileOps implements DatabaseFileOps {
  bool deleted = false;
  String? backedUpTo;
  String? restoredFrom;

  @override
  Future<void> backupDatabase(String backupPath) async {
    backedUpTo = backupPath;
    // simulate a write by creating the file
    final f = File(backupPath);
    await f.parent.create(recursive: true);
    await f.writeAsString('db');
  }

  @override
  Future<void> deleteDatabase() async {
    deleted = true;
  }

  @override
  Future<String> getDatabasePath() async => '/tmp/fake.db';

  @override
  Future<void> restoreDatabase(String sourcePath) async {
    restoredFrom = sourcePath;
  }
}

class _NoopReinitializer implements DatabaseReinitializer {
  int calls = 0;
  @override
  Future<void> reinitialize(oldDb) async {
    calls++;
  }
}

class _TestableService extends DatabaseManagementService {
  final List<Task> tasks;
  _TestableService({required this.tasks, required super.fileOps, required super.reinitializer, super.markdownExporter});

  @override
  Future<List<Task>> getTasksDueInRange(DateTime start, DateTime end) async => tasks;
}

void main() {
  setUp(() async {
    // Ensure a LocalDatabase is registered in getIt for service default constructor
    if (getIt.isRegistered<LocalDatabase>()) {
      await getIt.unregister<LocalDatabase>();
    }
    getIt.registerSingleton<LocalDatabase>(
      LocalDatabase.forTesting(DatabaseConfig.createTestDatabase()),
    );
  });

  tearDown(() async {
    if (getIt.isRegistered<LocalDatabase>()) {
      await getIt<LocalDatabase>().close();
      await getIt.unregister<LocalDatabase>();
    }
  });

  group('DatabaseManagementService', () {
    test('resetDatabase deletes and reinitializes', () async {
      final ops = _FakeFileOps();
      final reiniter = _NoopReinitializer();
      final service = DatabaseManagementService(fileOps: ops, reinitializer: reiniter);

      await service.resetDatabase();

      expect(ops.deleted, isTrue);
      // LocalDatabase should be available after reset
      expect(getIt.isRegistered<LocalDatabase>(), isTrue);
    });

    test('exportDatabaseTo copies file to destination', () async {
      final ops = _FakeFileOps();
      final reiniter = _NoopReinitializer();
      final service = DatabaseManagementService(fileOps: ops, reinitializer: reiniter);
      final dest = '${Directory.systemTemp.path}/db/export_test/mytodospace.db';

      await service.exportDatabaseTo(dest);

      expect(ops.backedUpTo, dest);
      expect(File(dest).existsSync(), isTrue);
    });

    test('importDatabaseFrom restores and reinitializes', () async {
      final ops = _FakeFileOps();
      final reiniter = _NoopReinitializer();
      final service = DatabaseManagementService(fileOps: ops, reinitializer: reiniter);

      final src = '${Directory.systemTemp.path}/db/import_src/mytodospace.db';
      await File(src).parent.create(recursive: true);
      await File(src).writeAsString('db');

      await service.importDatabaseFrom(src);

      expect(ops.restoredFrom, src);
      // LocalDatabase should be available after import
      expect(getIt.isRegistered<LocalDatabase>(), isTrue);
    });

    test('exportMarkdownTo writes markdown file', () async {
      final ops = _FakeFileOps();
      final reiniter = _NoopReinitializer();
      final tasks = [
        Task(
          id: 't1',
          title: 'A',
          notes: '',
          creationDate: DateTime(2025, 1, 1, 8),
          dueDate: DateTime(2025, 1, 1, 18),
          isCompleted: false,
          priority: Priority.urgentImportant,
          subtasks: const [],
        )
      ];
      final service = _TestableService(
        tasks: tasks,
        fileOps: ops,
        reinitializer: reiniter,
        markdownExporter: const MarkdownExporter(),
      );

      final dest = '${Directory.systemTemp.path}/md/export_test/tasks.md';
      await service.exportMarkdownTo(
        start: DateTime(2025, 1, 1),
        end: DateTime(2025, 1, 2),
        destinationPath: dest,
        title: 'MD Test',
      );

      final content = await File(dest).readAsString();
      expect(content, contains('# MD Test'));
      expect(content, contains('**A**'));
    });
  });
}

