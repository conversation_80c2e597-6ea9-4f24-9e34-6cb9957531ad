import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/features/data_management/presentation/database_management_dialog.dart';
import 'package:mytodospace/core/di/injection.dart';
import 'package:mytodospace/data/datasources/local_database.dart';
import 'package:mytodospace/data/datasources/database_config.dart';



void main() {
  setUp(() async {
    if (getIt.isRegistered<LocalDatabase>()) {
      await getIt.unregister<LocalDatabase>();
    }
    getIt.registerSingleton<LocalDatabase>(
      LocalDatabase.forTesting(DatabaseConfig.createTestDatabase()),
    );
  });

  tearDown(() async {
    if (getIt.isRegistered<LocalDatabase>()) {
      await getIt<LocalDatabase>().close();
      await getIt.unregister<LocalDatabase>();
    }
  });


  testWidgets('DatabaseManagementDialog renders core actions', (tester) async {
    await tester.pumpWidget(const MaterialApp(
      home: Scaffold(
        body: Center(child: DatabaseManagementDialog()),
      ),
    ));

    expect(find.text('数据库管理'), findsOneWidget);
    expect(find.text('DB初始化'), findsOneWidget);
    expect(find.text('DB文件导出'), findsOneWidget);
    expect(find.text('DB文件导入'), findsOneWidget);
    expect(find.text('md文件导出'), findsOneWidget);
  });
}

