import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/features/data_management/services/markdown_exporter.dart';

void main() {
  group('MarkdownExporter', () {
    test('exports tasks grouped by due date with headers and details', () {
      final exporter = const MarkdownExporter();
      final start = DateTime(2025, 1, 1);
      final end = DateTime(2025, 1, 3);

      final t1 = Task(
        id: '1',
        title: 'Task A',
        notes: 'Note A',
        creationDate: DateTime(2025, 1, 1, 8),
        dueDate: DateTime(2025, 1, 1, 18),
        isCompleted: false,
        priority: Priority.urgentImportant,
        subtasks: const [],
      );
      final t2 = Task(
        id: '2',
        title: 'Task B',
        notes: '',
        creationDate: DateTime(2025, 1, 1, 9),
        dueDate: DateTime(2025, 1, 2, 12),
        isCompleted: true,
        priority: Priority.importantNotUrgent,
        subtasks: const [],
      );

      final md = exporter.exportTasks(tasks: [t1, t2], start: start, end: end, title: 'Test Export');

      expect(md, contains('# Test Export'));
      expect(md, contains('> 导出范围：2025-01-01 至 2025-01-03'));
      expect(md, contains('## 2025-01-01'));
      expect(md, contains('## 2025-01-02'));
      expect(md, contains('**Task A**'));
      expect(md, contains('**Task B**'));
      expect(md, contains('> Note A'));
    });
  });
}

