import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:mytodospace/features/tasks/presentation/task_editor_dialog.dart';
import 'package:mytodospace/features/tasks/presentation/widgets/task_detail_dialog.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/app/theme.dart';

import 'ui_optimization_test.mocks.dart';

@GenerateMocks([TaskListBloc])
void main() {
  group('UI Optimization Tests', () {
    late MockTaskListBloc mockTaskListBloc;
    late Task testTask;

    setUp(() {
      mockTaskListBloc = MockTaskListBloc();
      testTask = Task(
        id: 'test-id',
        title: 'Test Task',
        notes: 'Test notes',
        priority: Priority.urgentImportant,
        dueDate: DateTime.now(),
        creationDate: DateTime.now(),
        isCompleted: false,
        subtasks: [],
      );
    });

    group('TaskEditorDialog UI Tests', () {
      testWidgets('should have proper spacing and typography', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: BlocProvider<TaskListBloc>.value(
              value: mockTaskListBloc,
              child: const Scaffold(
                body: TaskEditorDialog(),
              ),
            ),
          ),
        );

        // Test dialog container size
        final dialogContainer = tester.widget<Container>(
          find.descendant(
            of: find.byType(Dialog),
            matching: find.byType(Container),
          ).first,
        );
        expect(dialogContainer.constraints?.maxWidth, 580);
        expect(dialogContainer.constraints?.maxHeight, 720);

        // Test header padding
        final headerContainer = find.descendant(
          of: find.byType(TaskEditorDialog),
          matching: find.byWidgetPredicate((widget) =>
              widget is Container &&
              widget.padding == const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing6,
                vertical: AppTheme.spacing5,
              )),
        );
        expect(headerContainer, findsOneWidget);

        // Test title input font size by inspecting the inner EditableText
        final editable = tester.widget<EditableText>(
          find.descendant(
            of: find.byType(TaskEditorDialog),
            matching: find.byType(EditableText),
          ).first,
        );
        expect(editable.style.fontSize, 16);
      });

      testWidgets('should have improved button styling', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: BlocProvider<TaskListBloc>.value(
              value: mockTaskListBloc,
              child: const Scaffold(
                body: TaskEditorDialog(),
              ),
            ),
          ),
        );

        // Test cancel button
        final cancelButton = tester.widget<OutlinedButton>(
          find.widgetWithText(OutlinedButton, '取消'),
        );
        expect(cancelButton.style?.minimumSize?.resolve({}), const Size(100, 44));

        // Test save button
        final saveButton = tester.widget<FilledButton>(
          find.widgetWithText(FilledButton, '创建任务'),
        );
        expect(saveButton.style?.minimumSize?.resolve({}), const Size(0, 44));
      });

      testWidgets('should have proper content padding', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: BlocProvider<TaskListBloc>.value(
              value: mockTaskListBloc,
              child: const Scaffold(
                body: TaskEditorDialog(),
              ),
            ),
          ),
        );

        // Test content padding
        final scrollView = tester.widget<SingleChildScrollView>(
          find.descendant(
            of: find.byType(TaskEditorDialog),
            matching: find.byType(SingleChildScrollView),
          ),
        );
        expect(
          scrollView.padding,
          const EdgeInsets.fromLTRB(
            AppTheme.spacing6,
            AppTheme.spacing4,
            AppTheme.spacing6,
            AppTheme.spacing6,
          ),
        );
      });
    });

    group('TaskDetailDialog UI Tests', () {
      testWidgets('should have improved layout and spacing', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: BlocProvider<TaskListBloc>.value(
              value: mockTaskListBloc,
              child: Scaffold(
                body: TaskDetailDialog(task: testTask),
              ),
            ),
          ),
        );

        // Test dialog container constraints
        final dialogContainer = tester.widget<Container>(
          find.descendant(
            of: find.byType(Dialog),
            matching: find.byType(Container),
          ).first,
        );
        expect(dialogContainer.constraints?.maxWidth, 620);
        expect(dialogContainer.constraints?.maxHeight, 760);

        // Test header styling
        final headerContainer = find.descendant(
          of: find.byType(TaskDetailDialog),
          matching: find.byWidgetPredicate((widget) =>
              widget is Container &&
              widget.decoration is BoxDecoration &&
              (widget.decoration as BoxDecoration).color == 
                  AppTheme.urgentImportantColor.withOpacity(0.05)),
        );
        expect(headerContainer, findsOneWidget);
      });

      testWidgets('should have improved priority chip styling', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: BlocProvider<TaskListBloc>.value(
              value: mockTaskListBloc,
              child: Scaffold(
                body: TaskDetailDialog(task: testTask),
              ),
            ),
          ),
        );

        // Test priority chip container
        final priorityChip = find.descendant(
          of: find.byType(TaskDetailDialog),
          matching: find.byWidgetPredicate((widget) =>
              widget is Container &&
              widget.padding == const EdgeInsets.symmetric(
                horizontal: AppTheme.spacing3,
                vertical: AppTheme.spacing1_5,
              )),
        );
        expect(priorityChip, findsOneWidget);
      });

      testWidgets('should have larger task title font', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: BlocProvider<TaskListBloc>.value(
              value: mockTaskListBloc,
              child: Scaffold(
                body: TaskDetailDialog(task: testTask),
              ),
            ),
          ),
        );

        // Test task title styling
        final titleText = tester.widget<Text>(
          find.text(testTask.title),
        );
        expect(titleText.style?.fontSize, 20);
        expect(titleText.style?.fontWeight, FontWeight.w600);
      });
    });

    group('Theme Consistency Tests', () {
      testWidgets('should use consistent spacing throughout', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: BlocProvider<TaskListBloc>.value(
              value: mockTaskListBloc,
              child: const Scaffold(
                body: TaskEditorDialog(),
              ),
            ),
          ),
        );

        // Test that spacing constants are used consistently
        final spacingElements = find.byWidgetPredicate((widget) =>
            widget is SizedBox &&
            (widget.height == AppTheme.spacing5 ||
             widget.width == AppTheme.spacing4 ||
             widget.height == AppTheme.spacing3));
        
        expect(spacingElements, findsWidgets);
      });

      testWidgets('should use theme colors consistently', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: BlocProvider<TaskListBloc>.value(
              value: mockTaskListBloc,
              child: const Scaffold(
                body: TaskEditorDialog(),
              ),
            ),
          ),
        );

        // Test that theme colors are used
        final themedElements = find.byWidgetPredicate((widget) =>
            widget is Container &&
            widget.decoration is BoxDecoration &&
            ((widget.decoration as BoxDecoration).color == AppTheme.cardLight ||
             (widget.decoration as BoxDecoration).border?.top.color == AppTheme.borderLight));
        
        expect(themedElements, findsWidgets);
      });
    });

    group('Accessibility Tests', () {
      testWidgets('should have proper semantic labels', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: BlocProvider<TaskListBloc>.value(
              value: mockTaskListBloc,
              child: const Scaffold(
                body: TaskEditorDialog(),
              ),
            ),
          ),
        );

        // Test that buttons have proper tooltips
        final closeButton = find.byTooltip('关闭 (Esc)');
        expect(closeButton, findsOneWidget);
      });

      testWidgets('should have minimum touch target sizes', (tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: BlocProvider<TaskListBloc>.value(
              value: mockTaskListBloc,
              child: const Scaffold(
                body: TaskEditorDialog(),
              ),
            ),
          ),
        );

        // Test button minimum sizes
        final buttons = find.byType(IconButton);
        for (final button in buttons.evaluate()) {
          final buttonWidget = button.widget as IconButton;
          final size = buttonWidget.style?.minimumSize?.resolve({});
          if (size != null) {
            expect(size.width, greaterThanOrEqualTo(40));
            expect(size.height, greaterThanOrEqualTo(40));
          }
        }
      });
    });
  });
}
