import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/features/tasks/presentation/task_list_panel.dart';

/// 高性能TaskListPanel性能测试
/// 
/// 验证我们的O(n)算法在大数据量下的性能表现
void main() {
  group('TaskListPanel Performance Tests', () {
    
    test('任务分组算法性能测试 - O(n)时间复杂度', () {
      // 创建大量测试数据
      final largeTasks = _generateLargeTasks(10000);
      
      // 测试分组性能
      final stopwatch = Stopwatch()..start();
      
      // 使用反射访问私有方法进行测试
      final panel = _TaskListPanelTestHelper();
      final groupedTasks = panel.groupTasksByPriority(largeTasks);
      
      stopwatch.stop();
      
      // 验证性能：10000个任务应该在100ms内完成分组
      expect(stopwatch.elapsedMilliseconds, lessThan(100));
      
      // 验证正确性
      expect(groupedTasks.length, equals(4));
      
      int totalTasks = 0;
      for (final tasks in groupedTasks.values) {
        totalTasks += tasks.length;
      }
      expect(totalTasks, equals(10000));
      
      print('✅ 分组10000个任务耗时: ${stopwatch.elapsedMilliseconds}ms');
    });

    test('任务排序算法性能测试', () {
      // 创建混乱顺序的任务
      final tasks = _generateMixedTasks(5000);
      
      final stopwatch = Stopwatch()..start();
      
      // 执行排序
      tasks.sort((a, b) {
        if (a.isCompleted != b.isCompleted) {
          return a.isCompleted ? 1 : -1;
        }
        return a.dueDate.compareTo(b.dueDate);
      });
      
      stopwatch.stop();
      
      // 验证性能：5000个任务排序应该在50ms内完成
      expect(stopwatch.elapsedMilliseconds, lessThan(50));
      
      // 验证排序正确性
      bool isCorrectlySorted = true;
      for (int i = 0; i < tasks.length - 1; i++) {
        final current = tasks[i];
        final next = tasks[i + 1];
        
        if (current.isCompleted && !next.isCompleted) {
          isCorrectlySorted = false;
          break;
        }
        
        if (current.isCompleted == next.isCompleted &&
            current.dueDate.isAfter(next.dueDate)) {
          isCorrectlySorted = false;
          break;
        }
      }
      
      expect(isCorrectlySorted, isTrue);
      print('✅ 排序5000个任务耗时: ${stopwatch.elapsedMilliseconds}ms');
    });

    test('网格Tab布局性能测试', () {
      final tasks = _generateLargeTasks(1000);
      
      final stopwatch = Stopwatch()..start();
      
      // 模拟网格Tab的计算过程
      final helper = _TaskListPanelTestHelper();
      final groupedTasks = helper.groupTasksByPriority(tasks);
      
      // 计算每个优先级的统计信息
      final stats = <Priority, Map<String, int>>{};
      for (final priority in Priority.values) {
        final priorityTasks = groupedTasks[priority] ?? [];
        final incompleteCount = priorityTasks.where((t) => !t.isCompleted).length;
        stats[priority] = {
          'incomplete': incompleteCount,
          'total': priorityTasks.length,
        };
      }
      
      stopwatch.stop();
      
      // 验证性能：1000个任务的统计计算应该在20ms内完成
      expect(stopwatch.elapsedMilliseconds, lessThan(20));
      
      // 验证统计正确性
      expect(stats.length, equals(4));
      
      int totalIncomplete = 0;
      int totalTasks = 0;
      for (final stat in stats.values) {
        totalIncomplete += stat['incomplete']!;
        totalTasks += stat['total']!;
      }
      
      expect(totalTasks, equals(1000));
      print('✅ 计算1000个任务统计耗时: ${stopwatch.elapsedMilliseconds}ms');
    });

    test('内存使用效率测试', () {
      // 测试大数据量下的内存使用
      final tasks = _generateLargeTasks(50000);
      
      final helper = _TaskListPanelTestHelper();
      final groupedTasks = helper.groupTasksByPriority(tasks);
      
      // 验证没有重复数据
      int totalGroupedTasks = 0;
      for (final group in groupedTasks.values) {
        totalGroupedTasks += group.length;
      }
      
      expect(totalGroupedTasks, equals(50000));
      
      // 验证引用完整性（没有创建额外的Task对象）
      final originalTask = tasks.first;
      final groupedTask = groupedTasks[originalTask.priority]!
          .firstWhere((t) => t.id == originalTask.id);
      
      expect(identical(originalTask, groupedTask), isTrue);
      
      print('✅ 50000个任务内存使用测试通过');
    });
  });
}

/// 测试辅助类 - 用于访问TaskListPanel的私有方法
class _TaskListPanelTestHelper {
  /// 模拟TaskListPanel的分组算法
  Map<Priority, List<Task>> groupTasksByPriority(List<Task> tasks) {
    final groups = <Priority, List<Task>>{
      for (final priority in Priority.values) priority: <Task>[],
    };

    for (final task in tasks) {
      groups[task.priority]!.add(task);
    }

    // 对每个组内的任务进行排序：未完成在前，按截止日期排序
    for (final group in groups.values) {
      group.sort((a, b) {
        if (a.isCompleted != b.isCompleted) {
          return a.isCompleted ? 1 : -1;
        }
        return a.dueDate.compareTo(b.dueDate);
      });
    }

    return groups;
  }
}

/// 生成大量测试任务
List<Task> _generateLargeTasks(int count) {
  final tasks = <Task>[];
  final priorities = Priority.values;
  final baseDate = DateTime.now();
  
  for (int i = 0; i < count; i++) {
    tasks.add(Task(
      id: 'task_$i',
      title: '测试任务 $i',
      notes: '这是第$i个测试任务',
      creationDate: baseDate.subtract(Duration(days: i % 30)),
      dueDate: baseDate.add(Duration(days: i % 60)),
      priority: priorities[i % priorities.length],
      isCompleted: i % 3 == 0, // 1/3的任务已完成
    ));
  }
  
  return tasks;
}

/// 生成混乱顺序的测试任务
List<Task> _generateMixedTasks(int count) {
  final tasks = _generateLargeTasks(count);
  
  // 打乱顺序
  tasks.shuffle();
  
  return tasks;
}
