import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:mytodospace/features/tasks/presentation/widgets/task_detail_dialog.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/domain/repositories/task_repository.dart';

import 'task_detail_dialog_test.mocks.dart';

@GenerateMocks([TaskListBloc, TaskRepository])
void main() {
  group('TaskDetailDialog 紧凑化布局测试', () {
    late MockTaskListBloc mockTaskListBloc;
    late MockTaskRepository mockTaskRepository;
    late Task testTask;

    setUp(() {
      mockTaskListBloc = MockTaskListBloc();
      mockTaskRepository = MockTaskRepository();

      testTask = Task.create(
        title: '测试任务',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.urgentImportant,
        notes: '这是一个测试备注',
      );

      when(mockTaskListBloc.stream).thenAnswer((_) => const Stream.empty());
    });

    testWidgets('应该显示紧凑的信息标签', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: TaskDetailDialog(
              task: testTask,
              onTaskUpdated: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证四个信息标签都存在
      expect(find.text('创建时间'), findsOneWidget);
      expect(find.text('截止时间'), findsOneWidget);
      expect(find.text('子任务进度'), findsOneWidget);
      expect(find.text('最后更新'), findsOneWidget);
    });

    testWidgets('应该显示完成状态和关闭按钮在同一行', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: TaskDetailDialog(
              task: testTask,
              onTaskUpdated: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证完成状态复选框存在
      expect(find.byType(Checkbox), findsAtLeastNWidgets(1));
      
      // 验证关闭按钮存在
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('应该显示紧凑的备注部分', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: TaskDetailDialog(
              task: testTask,
              onTaskUpdated: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证备注标题和内容
      expect(find.text('备注'), findsOneWidget);
      expect(find.text('这是一个测试备注'), findsOneWidget);
    });

    testWidgets('应该显示紧凑的操作按钮', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: TaskDetailDialog(
              task: testTask,
              onTaskUpdated: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证编辑和删除按钮
      expect(find.text('编辑任务'), findsOneWidget);
      expect(find.text('删除'), findsOneWidget);
    });
  });

  group('子任务完成状态逻辑测试', () {
    late MockTaskListBloc mockTaskListBloc;
    late MockTaskRepository mockTaskRepository;
    late Task taskWithSubtasks;

    setUp(() {
      mockTaskListBloc = MockTaskListBloc();
      mockTaskRepository = MockTaskRepository();

      taskWithSubtasks = Task.create(
        title: '主任务',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.urgentImportant,
      ).copyWith(
        subtasks: [
          SubTask.create(title: '子任务1', parentTaskId: 'test-task-id'),
          SubTask.create(title: '子任务2', parentTaskId: 'test-task-id'),
          SubTask.create(title: '子任务3', parentTaskId: 'test-task-id'),
        ],
      );

      when(mockTaskListBloc.stream).thenAnswer((_) => const Stream.empty());
    });

    testWidgets('应该显示子任务进度', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: TaskDetailDialog(
              task: taskWithSubtasks,
              onTaskUpdated: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证子任务进度显示
      expect(find.text('子任务 (0/3)'), findsOneWidget);
      expect(find.text('0/3'), findsOneWidget);
    });

    testWidgets('应该显示紧凑的子任务列表', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: TaskDetailDialog(
              task: taskWithSubtasks,
              onTaskUpdated: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证子任务项
      expect(find.text('子任务1'), findsOneWidget);
      expect(find.text('子任务2'), findsOneWidget);
      expect(find.text('子任务3'), findsOneWidget);
      
      // 验证子任务复选框
      expect(find.byType(Checkbox), findsAtLeastNWidgets(3));
    });

    testWidgets('主任务完成状态应该依赖子任务', (tester) async {
      // 创建部分完成的任务
      final partiallyCompletedTask = taskWithSubtasks.copyWith(
        subtasks: [
          SubTask.create(title: '子任务1', parentTaskId: 'test-task-id').copyWith(isCompleted: true),
          SubTask.create(title: '子任务2', parentTaskId: 'test-task-id'),
          SubTask.create(title: '子任务3', parentTaskId: 'test-task-id'),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: TaskDetailDialog(
              task: partiallyCompletedTask,
              onTaskUpdated: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证子任务进度更新
      expect(find.text('子任务 (1/3)'), findsOneWidget);
      expect(find.text('1/3'), findsOneWidget);
    });
  });

  group('紧凑化布局性能测试', () {
    late MockTaskListBloc mockTaskListBloc;

    setUp(() {
      mockTaskListBloc = MockTaskListBloc();
      when(mockTaskListBloc.stream).thenAnswer((_) => const Stream.empty());
    });

    testWidgets('应该快速渲染紧凑布局', (tester) async {
      final task = Task.create(
        title: '性能测试任务',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.urgentImportant,
        notes: '测试备注内容',
      );

      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: TaskDetailDialog(
              task: task,
              onTaskUpdated: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      stopwatch.stop();

      // 验证渲染时间合理
      expect(stopwatch.elapsedMilliseconds, lessThan(200));
    });

    testWidgets('应该高效处理大量子任务', (tester) async {
      final taskWithManySubtasks = Task.create(
        title: '大量子任务测试',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.urgentImportant,
      ).copyWith(
        subtasks: List.generate(50, (index) =>
          SubTask.create(title: '子任务${index + 1}', parentTaskId: 'test-task-id')
        ),
      );

      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: TaskDetailDialog(
              task: taskWithManySubtasks,
              onTaskUpdated: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      stopwatch.stop();

      // 验证大量子任务渲染时间合理
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
      
      // 验证子任务数量显示正确
      expect(find.text('子任务 (0/50)'), findsOneWidget);
    });
  });
}
