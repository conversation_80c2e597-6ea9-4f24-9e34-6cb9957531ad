// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in mytodospace/test/features/tasks/presentation/task_editor_dialog_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mytodospace/domain/models/task_model.dart' as _i4;
import 'package:mytodospace/domain/repositories/task_repository.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [TaskRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockTaskRepository extends _i1.Mock implements _i2.TaskRepository {
  MockTaskRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i3.Future<void> createTask(_i4.Task? task) => (super.noSuchMethod(
        Invocation.method(
          #createTask,
          [task],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> updateTask(_i4.Task? task) => (super.noSuchMethod(
        Invocation.method(
          #updateTask,
          [task],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> deleteTask(String? taskId) => (super.noSuchMethod(
        Invocation.method(
          #deleteTask,
          [taskId],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Future<void> deleteTasks(List<String>? taskIds) => (super.noSuchMethod(
        Invocation.method(
          #deleteTasks,
          [taskIds],
        ),
        returnValue: _i3.Future<void>.value(),
        returnValueForMissingStub: _i3.Future<void>.value(),
      ) as _i3.Future<void>);

  @override
  _i3.Stream<List<_i4.Task>> watchTasksByDate(DateTime? date) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchTasksByDate,
          [date],
        ),
        returnValue: _i3.Stream<List<_i4.Task>>.empty(),
      ) as _i3.Stream<List<_i4.Task>>);

  @override
  _i3.Stream<List<_i4.Task>> watchTasksForMonth({
    required int? year,
    required int? month,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchTasksForMonth,
          [],
          {
            #year: year,
            #month: month,
          },
        ),
        returnValue: _i3.Stream<List<_i4.Task>>.empty(),
      ) as _i3.Stream<List<_i4.Task>>);

  @override
  _i3.Stream<Map<DateTime, int>> watchTaskLoadForMonth({
    required int? year,
    required int? month,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #watchTaskLoadForMonth,
          [],
          {
            #year: year,
            #month: month,
          },
        ),
        returnValue: _i3.Stream<Map<DateTime, int>>.empty(),
      ) as _i3.Stream<Map<DateTime, int>>);

  @override
  _i3.Future<List<_i4.Task>> getTasksForDate(DateTime? date) =>
      (super.noSuchMethod(
        Invocation.method(
          #getTasksForDate,
          [date],
        ),
        returnValue: _i3.Future<List<_i4.Task>>.value(<_i4.Task>[]),
      ) as _i3.Future<List<_i4.Task>>);

  @override
  _i3.Future<List<_i4.Task>> getAllTasks() => (super.noSuchMethod(
        Invocation.method(
          #getAllTasks,
          [],
        ),
        returnValue: _i3.Future<List<_i4.Task>>.value(<_i4.Task>[]),
      ) as _i3.Future<List<_i4.Task>>);

  @override
  _i3.Future<_i4.Task?> getTaskById(String? taskId) => (super.noSuchMethod(
        Invocation.method(
          #getTaskById,
          [taskId],
        ),
        returnValue: _i3.Future<_i4.Task?>.value(),
      ) as _i3.Future<_i4.Task?>);

  @override
  _i3.Future<List<_i4.Task>> searchTasks(String? query) => (super.noSuchMethod(
        Invocation.method(
          #searchTasks,
          [query],
        ),
        returnValue: _i3.Future<List<_i4.Task>>.value(<_i4.Task>[]),
      ) as _i3.Future<List<_i4.Task>>);
}
