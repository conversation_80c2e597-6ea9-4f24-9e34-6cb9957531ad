import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:mytodospace/features/tasks/presentation/task_editor_dialog.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/domain/repositories/task_repository.dart';

import 'task_editor_dialog_test.mocks.dart';

@GenerateMocks([TaskRepository])
void main() {
  group('TaskEditorDialog 紧凑化布局测试', () {
    late MockTaskRepository mockTaskRepository;

    setUp(() {
      mockTaskRepository = MockTaskRepository();
    });

    testWidgets('应该显示紧凑的信息行', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            selectedDate: DateTime.now(),
            initialPriority: Priority.urgentImportant,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证优先级和日期选择器在同一行
      expect(find.text('优先级'), findsOneWidget);
      expect(find.text('截止日期'), findsOneWidget);
      
      // 验证下拉选择器
      expect(find.byType(DropdownButton<Priority>), findsOneWidget);
    });

    testWidgets('应该显示紧凑的子任务部分', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            selectedDate: DateTime.now(),
            initialPriority: Priority.urgentImportant,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证子任务标题和添加按钮
      expect(find.text('子任务 (0)'), findsOneWidget);
      expect(find.text('添加'), findsOneWidget);
      expect(find.text('暂无子任务'), findsOneWidget);
    });

    testWidgets('应该显示紧凑的操作按钮', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            selectedDate: DateTime.now(),
            initialPriority: Priority.urgentImportant,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证取消和创建按钮
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('创建'), findsOneWidget);
    });

    testWidgets('编辑模式应该显示保存按钮', (tester) async {
      final existingTask = Task.create(
        title: '现有任务',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.urgentImportant,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            existingTask: existingTask,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证保存按钮
      expect(find.text('保存'), findsOneWidget);
      
      // 验证任务标题已填充
      expect(find.text('现有任务'), findsOneWidget);
    });

    testWidgets('应该正确显示优先级选项', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            selectedDate: DateTime.now(),
            initialPriority: Priority.urgentImportant,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击优先级下拉菜单
      await tester.tap(find.byType(DropdownButton<Priority>));
      await tester.pumpAndSettle();

      // 验证所有优先级选项
      expect(find.text('重要且紧急'), findsWidgets);
      expect(find.text('重要不紧急'), findsOneWidget);
      expect(find.text('紧急不重要'), findsOneWidget);
      expect(find.text('不重要不紧急'), findsOneWidget);
    });
  });

  group('紧凑日期格式化测试', () {
    testWidgets('应该正确格式化今天的日期', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            selectedDate: DateTime.now(),
            initialPriority: Priority.urgentImportant,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证今天的紧凑格式
      expect(find.text('今天'), findsOneWidget);
    });

    testWidgets('应该正确格式化明天的日期', (tester) async {
      final tomorrow = DateTime.now().add(const Duration(days: 1));
      
      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            selectedDate: tomorrow,
            initialPriority: Priority.urgentImportant,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证明天的紧凑格式
      expect(find.text('明天'), findsOneWidget);
    });
  });

  group('子任务管理测试', () {
    testWidgets('应该能够添加子任务', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            selectedDate: DateTime.now(),
            initialPriority: Priority.urgentImportant,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击添加子任务按钮
      await tester.tap(find.text('添加'));
      await tester.pumpAndSettle();

      // 验证子任务对话框出现
      expect(find.byType(AlertDialog), findsOneWidget);
    });

    testWidgets('应该显示子任务数量', (tester) async {
      final taskWithSubtasks = Task.create(
        title: '有子任务的任务',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.urgentImportant,
      ).copyWith(
        subtasks: [
          SubTask.create(title: '子任务1', parentTaskId: 'test-task-id'),
          SubTask.create(title: '子任务2', parentTaskId: 'test-task-id'),
        ],
      );

      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            existingTask: taskWithSubtasks,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证子任务数量显示
      expect(find.text('子任务 (2)'), findsOneWidget);
    });
  });

  group('性能测试', () {
    testWidgets('应该快速渲染编辑弹窗', (tester) async {
      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            selectedDate: DateTime.now(),
            initialPriority: Priority.urgentImportant,
          ),
        ),
      );

      await tester.pumpAndSettle();
      stopwatch.stop();

      // 验证渲染时间合理
      expect(stopwatch.elapsedMilliseconds, lessThan(200));
    });

    testWidgets('应该高效处理复杂任务编辑', (tester) async {
      final complexTask = Task.create(
        title: '复杂任务',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.urgentImportant,
        notes: '这是一个很长的备注内容，用于测试性能。' * 10,
      ).copyWith(
        subtasks: List.generate(20, (index) =>
          SubTask.create(title: '子任务${index + 1}', parentTaskId: 'test-task-id')
        ),
      );

      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            existingTask: complexTask,
          ),
        ),
      );

      await tester.pumpAndSettle();
      stopwatch.stop();

      // 验证复杂任务渲染时间合理
      expect(stopwatch.elapsedMilliseconds, lessThan(300));
      
      // 验证子任务数量正确
      expect(find.text('子任务 (20)'), findsOneWidget);
    });
  });

  group('表单验证测试', () {
    testWidgets('应该验证任务标题不为空', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            selectedDate: DateTime.now(),
            initialPriority: Priority.urgentImportant,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 尝试保存空标题的任务
      await tester.tap(find.text('创建'));
      await tester.pumpAndSettle();

      // 验证错误提示
      expect(find.text('请输入任务标题'), findsOneWidget);
    });

    testWidgets('应该正确填充现有任务数据', (tester) async {
      final existingTask = Task.create(
        title: '现有任务标题',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.importantNotUrgent,
        notes: '现有备注',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: TaskEditorDialog(
            existingTask: existingTask,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证表单字段已正确填充
      expect(find.text('现有任务标题'), findsOneWidget);
      expect(find.text('现有备注'), findsOneWidget);
    });
  });
}
