import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:mytodospace/features/tasks/presentation/task_list_panel.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_state.dart';
import 'package:mytodospace/domain/models/task_model.dart';

import 'task_list_panel_test.mocks.dart';

@GenerateMocks([TaskListBloc])
void main() {
  group('TaskListPanel Basic Tests', () {
    late MockTaskListBloc mockTaskListBloc;

    setUp(() {
      mockTaskListBloc = MockTaskListBloc();
    });

    testWidgets('显示加载状态', (WidgetTester tester) async {
      // Arrange
      when(mockTaskListBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.loading,
          currentOperation: TaskListOperation.none,
          tasks: [],
          filteredTasks: [],
          date: DateTime.now(),
        ),
      );
      when(mockTaskListBloc.stream).thenAnswer((_) => Stream.empty());

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: const TaskListPanel(),
          ),
        ),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
      expect(find.text('正在加载任务...'), findsOneWidget);
    });

    testWidgets('显示空状态', (WidgetTester tester) async {
      // Arrange
      when(mockTaskListBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: [],
          filteredTasks: [],
          date: DateTime.now(),
        ),
      );
      when(mockTaskListBloc.stream).thenAnswer((_) => Stream.empty());

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: const TaskListPanel(),
          ),
        ),
      );

      // Assert
      expect(find.text('暂无任务'), findsOneWidget);
      expect(find.text('点击右上角按钮创建新任务'), findsOneWidget);
    });

    testWidgets('显示任务列表和网格tabs', (WidgetTester tester) async {
      // Arrange
      final tasks = [
        Task(
          id: '1',
          title: '紧急重要任务',
          notes: '',
          creationDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 1)),
          priority: Priority.urgentImportant,
        ),
        Task(
          id: '2',
          title: '重要不紧急任务',
          notes: '',
          creationDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 2)),
          priority: Priority.importantNotUrgent,
        ),
        Task(
          id: '3',
          title: '紧急不重要任务',
          notes: '',
          creationDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 3)),
          priority: Priority.urgentNotImportant,
        ),
        Task(
          id: '4',
          title: '不紧急不重要任务',
          notes: '',
          creationDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 4)),
          priority: Priority.notUrgentNotImportant,
        ),
      ];

      when(mockTaskListBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: tasks,
          filteredTasks: tasks,
          date: DateTime.now(),
        ),
      );
      when(mockTaskListBloc.stream).thenAnswer((_) => Stream.empty());

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: const TaskListPanel(),
          ),
        ),
      );

      // Assert
      expect(find.byType(TabBarView), findsOneWidget);

      // 验证网格tab标签（完整文字，不省略）
      expect(find.text('紧急重要'), findsOneWidget);
      expect(find.text('重要不紧急'), findsOneWidget);
      expect(find.text('紧急不重要'), findsOneWidget);
      expect(find.text('不紧急不重要'), findsOneWidget);

      // 验证任务计数格式（不带括号）
      expect(find.text('1/1'), findsNWidgets(4));
    });

    testWidgets('测试任务分组功能', (WidgetTester tester) async {
      // Arrange
      final tasks = [
        Task(
          id: '1',
          title: '紧急重要任务1',
          notes: '',
          creationDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 1)),
          priority: Priority.urgentImportant,
          isCompleted: false,
        ),
        Task(
          id: '2',
          title: '紧急重要任务2',
          notes: '',
          creationDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 1)),
          priority: Priority.urgentImportant,
          isCompleted: true,
        ),
        Task(
          id: '3',
          title: '重要不紧急任务',
          notes: '',
          creationDate: DateTime.now(),
          dueDate: DateTime.now().add(const Duration(days: 2)),
          priority: Priority.importantNotUrgent,
          isCompleted: false,
        ),
      ];

      when(mockTaskListBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: tasks,
          filteredTasks: tasks,
          date: DateTime.now(),
        ),
      );
      when(mockTaskListBloc.stream).thenAnswer((_) => Stream.empty());

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: const TaskListPanel(),
          ),
        ),
      );

      // Assert
      // 验证紧急重要tab显示正确的计数：1个未完成/2个总数
      expect(find.text('1/2'), findsOneWidget);
      // 验证重要不紧急tab显示正确的计数：1个未完成/1个总数
      expect(find.text('1/1'), findsOneWidget);
      // 验证其他两个tab显示0个任务
      expect(find.text('0/0'), findsNWidgets(2));
    });

    testWidgets('测试错误状态显示', (WidgetTester tester) async {
      // Arrange
      when(mockTaskListBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.failure,
          currentOperation: TaskListOperation.none,
          tasks: [],
          filteredTasks: [],
          date: DateTime.now(),
          errorMessage: '网络连接失败',
        ),
      );
      when(mockTaskListBloc.stream).thenAnswer((_) => Stream.empty());

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: const TaskListPanel(),
          ),
        ),
      );

      // Assert
      expect(find.text('网络连接失败'), findsOneWidget);
      expect(find.text('重试'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('测试任务列表显示', (WidgetTester tester) async {
      // Arrange
      when(mockTaskListBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: [],
          filteredTasks: [],
          date: DateTime.now(),
        ),
      );
      when(mockTaskListBloc.stream).thenAnswer((_) => Stream.empty());

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: const TaskListPanel(),
          ),
        ),
      );

      // Assert
      // 验证任务列表正常显示（AppBar已移除）
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(Column), findsAtLeastNWidgets(1));
    });

    testWidgets('测试置顶按钮功能', (WidgetTester tester) async {
      // Arrange - 创建足够多的任务来触发滚动
      final tasks = List.generate(20, (index) => Task(
        id: 'task_$index',
        title: '测试任务 $index',
        notes: '',
        creationDate: DateTime.now(),
        dueDate: DateTime.now().add(Duration(days: index)),
        priority: Priority.urgentImportant,
      ));

      when(mockTaskListBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: tasks,
          filteredTasks: tasks,
          date: DateTime.now(),
        ),
      );
      when(mockTaskListBloc.stream).thenAnswer((_) => Stream.empty());

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: const TaskListPanel(),
          ),
        ),
      );

      // Assert
      // 验证置顶按钮相关组件存在
      expect(find.byType(TabBarView), findsOneWidget);
      expect(find.byType(Stack), findsAtLeastNWidgets(1));
    });
  });

  group('TaskListPanel Scroll Position Preservation Tests', () {
    late MockTaskListBloc mockTaskListBloc;
    late List<Task> testTasks;

    setUp(() {
      mockTaskListBloc = MockTaskListBloc();
      testTasks = [
        Task.create(
          title: 'Test Task 1',
          dueDate: DateTime.now().add(const Duration(days: 1)),
          priority: Priority.urgentImportant,
        ),
        Task.create(
          title: 'Test Task 2',
          dueDate: DateTime.now().add(const Duration(days: 2)),
          priority: Priority.importantNotUrgent,
        ),
        Task.create(
          title: 'Test Task 3',
          dueDate: DateTime.now().add(const Duration(days: 3)),
          priority: Priority.urgentNotImportant,
        ),
      ];

      // Setup default state
      when(mockTaskListBloc.state).thenReturn(
        TaskListState.initial().copyWith(
          status: TaskListStatus.success,
          tasks: testTasks,
        ),
      );
      when(mockTaskListBloc.stream).thenAnswer(
        (_) => Stream.value(mockTaskListBloc.state),
      );
    });

    testWidgets('should preserve scroll position when task status changes', (tester) async {
      // Arrange
      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: const TaskListPanel(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      // Find the scroll controller for urgent important tasks
      final scrollableFinder = find.byType(Scrollable).first;
      expect(scrollableFinder, findsOneWidget);

      // Simulate scrolling down
      await tester.drag(scrollableFinder, const Offset(0, -200));
      await tester.pumpAndSettle();

      // Get the current scroll position
      final scrollable = tester.widget<Scrollable>(scrollableFinder);
      final initialOffset = scrollable.controller?.offset ?? 0.0;

      // Act - simulate task status change
      final updatedTasks = testTasks.map((task) {
        if (task.id == testTasks.first.id) {
          return task.copyWith(isCompleted: true);
        }
        return task;
      }).toList();

      when(mockTaskListBloc.state).thenReturn(
        TaskListState.initial().copyWith(
          status: TaskListStatus.success,
          tasks: updatedTasks,
        ),
      );

      // Trigger state update
      await tester.pumpAndSettle();

      // Assert - scroll position should be preserved
      final finalOffset = scrollable.controller?.offset ?? 0.0;
      expect(finalOffset, equals(initialOffset));
    });

    testWidgets('should not rebuild unnecessarily on minor task changes', (tester) async {
      int buildCount = 0;

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: Builder(
              builder: (context) {
                buildCount++;
                return const TaskListPanel();
              },
            ),
          ),
        ),
      );
      await tester.pumpAndSettle();

      final initialBuildCount = buildCount;

      // Simulate minor task change that shouldn't trigger rebuild
      final minorUpdatedTasks = testTasks.map((task) {
        if (task.id == testTasks.first.id) {
          return task.copyWith(notes: 'Updated notes');
        }
        return task;
      }).toList();

      when(mockTaskListBloc.state).thenReturn(
        TaskListState.initial().copyWith(
          status: TaskListStatus.success,
          tasks: minorUpdatedTasks,
        ),
      );

      await tester.pumpAndSettle();

      // Should not rebuild for minor changes
      expect(buildCount, equals(initialBuildCount));
    });
  });

  group('TaskListPanel Performance Tests', () {
    late MockTaskListBloc mockTaskListBloc;

    setUp(() {
      mockTaskListBloc = MockTaskListBloc();
    });

    testWidgets('should handle large task lists efficiently', (tester) async {
      // Create a large list of tasks
      final largeTasks = List.generate(100, (index) =>
        Task.create(
          title: 'Task $index',
          dueDate: DateTime.now().add(Duration(days: index + 1)),
          priority: Priority.values[index % Priority.values.length],
        ),
      );

      when(mockTaskListBloc.state).thenReturn(
        TaskListState.initial().copyWith(
          status: TaskListStatus.success,
          tasks: largeTasks,
        ),
      );
      when(mockTaskListBloc.stream).thenAnswer(
        (_) => Stream.value(mockTaskListBloc.state),
      );

      // Measure build time
      final stopwatch = Stopwatch()..start();

      await tester.pumpWidget(
        MaterialApp(
          home: BlocProvider<TaskListBloc>.value(
            value: mockTaskListBloc,
            child: const TaskListPanel(),
          ),
        ),
      );
      await tester.pumpAndSettle();

      stopwatch.stop();

      // Assert that build time is reasonable (less than 500ms for tests)
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
    });
  });
}
