import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mocktail/mocktail.dart';

import 'package:mytodospace/features/tasks/presentation/widgets/quadrant_section.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/app/theme.dart';

class MockTaskListBloc extends Mock implements TaskListBloc {}

void main() {
  group('QuadrantSection', () {
    late MockTaskListBloc mockTaskListBloc;
    late List<Task> testTasks;

    setUp(() {
      mockTaskListBloc = MockTaskListBloc();
      testTasks = [
        Task(
          id: '1',
          title: 'Test Task 1',
          notes: 'Test notes',
          priority: Priority.urgentImportant,
          creationDate: DateTime.now(),
          dueDate: DateTime.now(),
          isCompleted: false,
          subtasks: [],
        ),
        Task(
          id: '2',
          title: 'Test Task 2',
          notes: 'Test notes 2',
          priority: Priority.urgentImportant,
          creationDate: DateTime.now(),
          dueDate: DateTime.now(),
          isCompleted: true,
          subtasks: [],
        ),
      ];
    });

    Widget createTestWidget({
      String title = 'Test Quadrant',
      Color color = Colors.red,
      List<Task>? tasks,
      Priority priority = Priority.urgentImportant,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider<TaskListBloc>(
            create: (context) => mockTaskListBloc,
            child: QuadrantSection(
              title: title,
              color: color,
              tasks: tasks ?? testTasks,
              priority: priority,
            ),
          ),
        ),
      );
    }

    testWidgets('should display quadrant title and task count', (tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Test Quadrant'), findsOneWidget);
      expect(find.text('2'), findsOneWidget); // Task count badge
    });

    testWidgets('should display expand/collapse icon', (tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byIcon(Icons.expand_more), findsOneWidget);
    });

    testWidgets('should toggle expansion when expand icon is tapped', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find the expand/collapse button
      final expandButton = find.byIcon(Icons.expand_more);
      expect(expandButton, findsOneWidget);

      // Initially, tasks should be visible (expanded state)
      expect(find.text('Test Task 1'), findsOneWidget);
      expect(find.text('Test Task 2'), findsOneWidget);

      // Tap the expand/collapse button to collapse
      await tester.tap(expandButton);
      await tester.pumpAndSettle();

      // Tasks should be hidden after collapse animation
      expect(find.text('Test Task 1'), findsNothing);
      expect(find.text('Test Task 2'), findsNothing);

      // Tap again to expand
      await tester.tap(expandButton);
      await tester.pumpAndSettle();

      // Tasks should be visible again
      expect(find.text('Test Task 1'), findsOneWidget);
      expect(find.text('Test Task 2'), findsOneWidget);
    });

    testWidgets('should show add task button', (tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byIcon(Icons.add), findsOneWidget);
    });

    testWidgets('should display empty state when no tasks', (tester) async {
      await tester.pumpWidget(createTestWidget(tasks: []));

      expect(find.text('暂无任务'), findsOneWidget);
    });

    testWidgets('should apply correct priority color', (tester) async {
      const testColor = Colors.blue;
      await tester.pumpWidget(createTestWidget(color: testColor));

      // Find containers with the priority color
      final containers = find.byType(Container);
      expect(containers, findsWidgets);

      // Verify that the color is applied correctly
      final widget = tester.widget<QuadrantSection>(find.byType(QuadrantSection));
      expect(widget.color, equals(testColor));
    });

    testWidgets('should animate icon rotation during expansion toggle', (tester) async {
      await tester.pumpWidget(createTestWidget());

      final expandButton = find.byIcon(Icons.expand_more);
      
      // Tap to collapse
      await tester.tap(expandButton);
      await tester.pump(const Duration(milliseconds: 150)); // Mid-animation
      
      // Icon should be rotating
      final transform = tester.widget<Transform>(
        find.ancestor(
          of: find.byIcon(Icons.expand_more),
          matching: find.byType(Transform),
        ),
      );
      expect(transform.transform, isNotNull);
      
      await tester.pumpAndSettle();
    });

    testWidgets('should handle priority descriptions correctly', (tester) async {
      await tester.pumpWidget(createTestWidget(
        priority: Priority.urgentImportant,
      ));

      // Should display the priority description
      expect(find.textContaining('立即处理'), findsOneWidget);
    });

    testWidgets('should maintain state during widget rebuilds', (tester) async {
      await tester.pumpWidget(createTestWidget());

      // Collapse the section
      await tester.tap(find.byIcon(Icons.expand_more));
      await tester.pumpAndSettle();

      // Rebuild the widget
      await tester.pumpWidget(createTestWidget(title: 'Updated Title'));

      // State should be maintained (still collapsed)
      expect(find.text('Test Task 1'), findsNothing);
      expect(find.text('Test Task 2'), findsNothing);
    });

    testWidgets('should handle rapid tap gestures correctly', (tester) async {
      await tester.pumpWidget(createTestWidget());

      final expandButton = find.byIcon(Icons.expand_more);
      
      // Rapid taps
      await tester.tap(expandButton);
      await tester.pump(const Duration(milliseconds: 50));
      await tester.tap(expandButton);
      await tester.pump(const Duration(milliseconds: 50));
      await tester.tap(expandButton);
      
      await tester.pumpAndSettle();
      
      // Should handle rapid taps gracefully without errors
      expect(tester.takeException(), isNull);
    });
  });
}
