import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloc_test/bloc_test.dart';

import 'package:mytodospace/features/calendar/presentation/widgets/calendar_month_view.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_bloc.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_state.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_event.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_state.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_event.dart';
import 'package:mytodospace/domain/models/task_model.dart';

class MockCalendarBloc extends MockBloc<CalendarEvent, CalendarState> implements CalendarBloc {}
class MockTaskListBloc extends MockBloc<TaskListEvent, TaskListState> implements TaskListBloc {}

void main() {
  group('CalendarMonthView Task Display', () {
    late MockCalendarBloc mockCalendarBloc;
    late MockTaskListBloc mockTaskListBloc;

    setUp(() {
      mockCalendarBloc = MockCalendarBloc();
      mockTaskListBloc = MockTaskListBloc();

      // Setup initial states
      whenListen(
        mockCalendarBloc,
        Stream.value(CalendarState.initial().copyWith(
          displayMonthDate: DateTime(2024, 1, 1),
          selectedDate: DateTime(2024, 1, 15),
        )),
        initialState: CalendarState.initial().copyWith(
          displayMonthDate: DateTime(2024, 1, 1),
          selectedDate: DateTime(2024, 1, 15),
        ),
      );

      whenListen(
        mockTaskListBloc,
        Stream.value(TaskListState.initial()),
        initialState: TaskListState.initial(),
      );
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: MultiBlocProvider(
            providers: [
              BlocProvider<CalendarBloc>(create: (context) => mockCalendarBloc),
              BlocProvider<TaskListBloc>(create: (context) => mockTaskListBloc),
            ],
            child: const CalendarMonthView(),
          ),
        ),
      );
    }

    testWidgets('should render calendar month view without errors', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify the widget renders successfully
      expect(find.byType(CalendarMonthView), findsOneWidget);
    });

    testWidgets('should handle state changes gracefully', (tester) async {
      await tester.pumpWidget(createTestWidget());
      await tester.pumpAndSettle();

      // Verify no exceptions are thrown during state changes
      expect(tester.takeException(), isNull);
    });
  });
}
