import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get_it/get_it.dart';
import 'package:mytodospace/core/di/injection.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/features/tasks/presentation/task_editor_dialog.dart';

void main() {
  group('Task Creation Widget Tests', () {
    setUpAll(() async {
      TestWidgetsFlutterBinding.ensureInitialized();
      await Future.delayed(Duration.zero);
      configureDependencies();
    });

    tearDownAll(() async {
      await GetIt.instance.reset();
    });

    testWidgets('should display task editor dialog', (WidgetTester tester) async {
      Task? savedTask;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => TaskEditorDialog(
                      selectedDate: DateTime.now(),
                      onTaskSaved: (task) {
                        savedTask = task;
                      },
                    ),
                  );
                },
                child: const Text('Create Task'),
              ),
            ),
          ),
        ),
      );

      // Tap the button to show dialog
      await tester.tap(find.text('Create Task'));
      await tester.pumpAndSettle();

      // Verify dialog is shown
      expect(find.text('创建新任务'), findsOneWidget);
      expect(find.text('任务标题'), findsOneWidget);

      // Enter task title
      await tester.enterText(find.byType(TextFormField).first, 'Test Task Title');
      await tester.pumpAndSettle();

      // Tap save button
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();

      // Verify task was saved
      expect(savedTask, isNotNull);
      expect(savedTask!.title, 'Test Task Title');
    });

    testWidgets('should validate required fields', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => TaskEditorDialog(
                      selectedDate: DateTime.now(),
                      onTaskSaved: (task) {},
                    ),
                  );
                },
                child: const Text('Create Task'),
              ),
            ),
          ),
        ),
      );

      // Tap the button to show dialog
      await tester.tap(find.text('Create Task'));
      await tester.pumpAndSettle();

      // Try to save without entering title
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();

      // Verify validation error is shown
      expect(find.text('请输入任务标题'), findsOneWidget);
    });

    testWidgets('should handle priority selection', (WidgetTester tester) async {
      Task? savedTask;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => TaskEditorDialog(
                      selectedDate: DateTime.now(),
                      initialPriority: Priority.importantNotUrgent,
                      onTaskSaved: (task) {
                        savedTask = task;
                      },
                    ),
                  );
                },
                child: const Text('Create Task'),
              ),
            ),
          ),
        ),
      );

      // Tap the button to show dialog
      await tester.tap(find.text('Create Task'));
      await tester.pumpAndSettle();

      // Enter task title
      await tester.enterText(find.byType(TextFormField).first, 'Priority Test Task');
      await tester.pumpAndSettle();

      // Save the task
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();

      // Verify task was saved with correct priority
      expect(savedTask, isNotNull);
      expect(savedTask!.priority, Priority.importantNotUrgent);
    });
  });
}
