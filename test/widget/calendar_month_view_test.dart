import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mytodospace/features/calendar/presentation/widgets/calendar_month_view.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_bloc.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_state.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_state.dart';

// Generate mocks
@GenerateMocks([CalendarBloc, TaskListBloc])
import 'calendar_month_view_test.mocks.dart';

void main() {
  group('CalendarMonthView Widget Tests', () {
    late MockCalendarBloc mockCalendarBloc;
    late MockTaskListBloc mockTaskListBloc;

    setUp(() {
      mockCalendarBloc = MockCalendarBloc();
      mockTaskListBloc = MockTaskListBloc();
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: MultiBlocProvider(
          providers: [
            BlocProvider<CalendarBloc>.value(value: mockCalendarBloc),
            BlocProvider<TaskListBloc>.value(value: mockTaskListBloc),
          ],
          child: const Scaffold(
            body: CalendarMonthView(),
          ),
        ),
      );
    }

    testWidgets('should display calendar grid with weekday headers',
        (tester) async {
      // Arrange
      when(mockCalendarBloc.state).thenReturn(CalendarState.initial());
      when(mockCalendarBloc.stream)
          .thenAnswer((_) => Stream.value(CalendarState.initial()));
      when(mockTaskListBloc.state).thenReturn(TaskListState.initial());
      when(mockTaskListBloc.stream)
          .thenAnswer((_) => Stream.value(TaskListState.initial()));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('日'), findsOneWidget);
      expect(find.text('一'), findsOneWidget);
      expect(find.text('二'), findsOneWidget);
      expect(find.text('三'), findsOneWidget);
      expect(find.text('四'), findsOneWidget);
      expect(find.text('五'), findsOneWidget);
      expect(find.text('六'), findsOneWidget);
    });

    testWidgets('should display date numbers in grid', (tester) async {
      // Arrange
      when(mockCalendarBloc.state).thenReturn(CalendarState.initial());
      when(mockCalendarBloc.stream)
          .thenAnswer((_) => Stream.value(CalendarState.initial()));
      when(mockTaskListBloc.state).thenReturn(TaskListState.initial());
      when(mockTaskListBloc.stream)
          .thenAnswer((_) => Stream.value(TaskListState.initial()));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      // Should find at least some date numbers
      expect(find.text('1'), findsAtLeastNWidgets(1));
      expect(find.text('15'), findsAtLeastNWidgets(1));
    });

    testWidgets('should highlight today\'s date', (tester) async {
      // Arrange
      final today = DateTime.now();
      final state = CalendarState.initial().copyWith(
        status: CalendarStatus.success,
        selectedDate: today,
        displayMonthDate: DateTime(today.year, today.month, 1),
        taskLoadByDate: {},
      );

      when(mockCalendarBloc.state).thenReturn(state);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(state));
      when(mockTaskListBloc.state).thenReturn(TaskListState.initial());
      when(mockTaskListBloc.stream)
          .thenAnswer((_) => Stream.value(TaskListState.initial()));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      // Today's date should be highlighted
      expect(find.text('${today.day}'), findsAtLeastNWidgets(1));
    });

    testWidgets('should show task indicators when tasks exist', (tester) async {
      // Arrange
      final today = DateTime.now();
      final taskLoadMap = {
        DateTime(today.year, today.month, 1): 3,
        DateTime(today.year, today.month, 15): 5,
      };

      final state = CalendarState.initial().copyWith(
        status: CalendarStatus.success,
        selectedDate: today,
        displayMonthDate: DateTime(today.year, today.month, 1),
        taskLoadByDate: taskLoadMap,
      );

      when(mockCalendarBloc.state).thenReturn(state);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(state));
      when(mockTaskListBloc.state).thenReturn(TaskListState.initial());
      when(mockTaskListBloc.stream)
          .thenAnswer((_) => Stream.value(TaskListState.initial()));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      // Should display task indicators
      expect(find.byType(Container), findsAtLeastNWidgets(1));
    });

    testWidgets('should handle date cell tap', (tester) async {
      // Arrange
      when(mockCalendarBloc.state).thenReturn(CalendarState.initial());
      when(mockCalendarBloc.stream)
          .thenAnswer((_) => Stream.value(CalendarState.initial()));
      when(mockTaskListBloc.state).thenReturn(TaskListState.initial());
      when(mockTaskListBloc.stream)
          .thenAnswer((_) => Stream.value(TaskListState.initial()));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Find and tap a date cell
      final dateCell = find.text('15').first;
      await tester.tap(dateCell);
      await tester.pump();

      // Assert
      // Verify that the bloc received the date selection event
      verify(mockCalendarBloc.add(any)).called(1);
    });

    testWidgets('should handle double tap for quick task creation',
        (tester) async {
      // Arrange
      when(mockCalendarBloc.state).thenReturn(CalendarState.initial());
      when(mockCalendarBloc.stream)
          .thenAnswer((_) => Stream.value(CalendarState.initial()));
      when(mockTaskListBloc.state).thenReturn(TaskListState.initial());
      when(mockTaskListBloc.stream)
          .thenAnswer((_) => Stream.value(TaskListState.initial()));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Find and double tap a date cell
      final dateCell = find.text('15').first;
      await tester.tap(dateCell);
      await tester.pump();
      await tester.tap(dateCell);
      await tester.pump();

      // Assert
      // Verify that the bloc received events
      verify(mockCalendarBloc.add(any)).called(greaterThan(0));
    });

    testWidgets('should display loading state', (tester) async {
      // Arrange
      final loadingState = CalendarState.initial().copyWith(
        status: CalendarStatus.loading,
        selectedDate: DateTime.now(),
        displayMonthDate: DateTime.now(),
        taskLoadByDate: {},
      );

      when(mockCalendarBloc.state).thenReturn(loadingState);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(loadingState));
      when(mockTaskListBloc.state).thenReturn(TaskListState.initial());
      when(mockTaskListBloc.stream)
          .thenAnswer((_) => Stream.value(TaskListState.initial()));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      // Should still display the calendar structure even when loading
      expect(find.text('日'), findsOneWidget);
    });

    testWidgets('should display error state', (tester) async {
      // Arrange
      final errorState = CalendarState.initial().copyWith(
        status: CalendarStatus.failure,
        selectedDate: DateTime.now(),
        displayMonthDate: DateTime.now(),
        taskLoadByDate: {},
        errorMessage: 'Failed to load tasks',
      );

      when(mockCalendarBloc.state).thenReturn(errorState);
      when(mockCalendarBloc.stream).thenAnswer((_) => Stream.value(errorState));
      when(mockTaskListBloc.state).thenReturn(TaskListState.initial());
      when(mockTaskListBloc.stream)
          .thenAnswer((_) => Stream.value(TaskListState.initial()));

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      // Should still display the calendar even in error state
      expect(find.text('日'), findsOneWidget);
    });
  });
}
