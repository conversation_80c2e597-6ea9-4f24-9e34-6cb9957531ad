import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mytodospace/main.dart' as app;

/// 应用性能测试套件
/// 
/// 验证：
/// - 启动时间
/// - 页面切换性能
/// - 大量数据处理性能
/// - 内存使用情况
/// - 滚动性能
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('应用性能测试', () {
    testWidgets('应用启动性能测试', (WidgetTester tester) async {
      final stopwatch = Stopwatch()..start();
      
      // 启动应用
      app.main();
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      final startupTime = stopwatch.elapsedMilliseconds;
      
      // 验证启动时间在合理范围内（小于3秒）
      expect(startupTime, lessThan(3000), 
             reason: '应用启动时间应该小于3秒，实际: ${startupTime}ms');
      
      print('应用启动时间: ${startupTime}ms');
    });

    testWidgets('页面切换性能测试', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      final tabs = ['任务', '日历', '四象限', '总结'];
      final switchTimes = <int>[];

      for (final tab in tabs) {
        final tabFinder = find.text(tab);
        if (tabFinder.evaluate().isNotEmpty) {
          final stopwatch = Stopwatch()..start();
          
          await tester.tap(tabFinder);
          await tester.pumpAndSettle();
          
          stopwatch.stop();
          final switchTime = stopwatch.elapsedMilliseconds;
          switchTimes.add(switchTime);
          
          // 验证页面切换时间在合理范围内（小于500ms）
          expect(switchTime, lessThan(500), 
                 reason: '页面切换时间应该小于500ms，$tab页面实际: ${switchTime}ms');
          
          print('$tab页面切换时间: ${switchTime}ms');
        }
      }

      final averageSwitchTime = switchTimes.reduce((a, b) => a + b) / switchTimes.length;
      expect(averageSwitchTime, lessThan(300), 
             reason: '平均页面切换时间应该小于300ms，实际: ${averageSwitchTime.toStringAsFixed(1)}ms');
      
      print('平均页面切换时间: ${averageSwitchTime.toStringAsFixed(1)}ms');
    });

    testWidgets('大量任务数据处理性能测试', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 创建大量测试任务
      final stopwatch = Stopwatch()..start();
      
      for (int i = 0; i < 100; i++) {
        await _createTestTaskQuick(tester, '性能测试任务 $i');
        
        // 每10个任务检查一次性能
        if (i % 10 == 9) {
          await tester.pumpAndSettle();
          final currentTime = stopwatch.elapsedMilliseconds;
          print('创建${i + 1}个任务耗时: ${currentTime}ms');
          
          // 验证创建任务的性能不会随数量增加而显著下降
          expect(currentTime, lessThan((i + 1) * 100), 
                 reason: '创建任务性能下降过多');
        }
      }
      
      stopwatch.stop();
      final totalTime = stopwatch.elapsedMilliseconds;
      
      expect(totalTime, lessThan(30000), 
             reason: '创建100个任务应该在30秒内完成，实际: ${totalTime}ms');
      
      print('创建100个任务总耗时: ${totalTime}ms');
    });

    testWidgets('滚动性能测试', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 创建足够的任务以测试滚动
      for (int i = 0; i < 50; i++) {
        await _createTestTaskQuick(tester, '滚动测试任务 $i');
      }
      await tester.pumpAndSettle();

      // 查找可滚动组件
      final scrollable = find.byType(Scrollable);
      if (scrollable.evaluate().isNotEmpty) {
        final stopwatch = Stopwatch()..start();
        
        // 执行多次滚动操作
        for (int i = 0; i < 10; i++) {
          await tester.drag(scrollable.first, const Offset(0, -300));
          await tester.pump();
        }
        
        await tester.pumpAndSettle();
        stopwatch.stop();
        
        final scrollTime = stopwatch.elapsedMilliseconds;
        expect(scrollTime, lessThan(2000), 
               reason: '滚动操作应该流畅，耗时应小于2秒，实际: ${scrollTime}ms');
        
        print('滚动性能测试耗时: ${scrollTime}ms');
      }
    });

    testWidgets('内存使用监控测试', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 记录初始内存使用情况
      final initialMemory = _getCurrentMemoryUsage();
      print('初始内存使用: ${initialMemory}MB');

      // 执行一系列操作
      for (int i = 0; i < 20; i++) {
        await _createTestTaskQuick(tester, '内存测试任务 $i');
        
        // 切换页面
        final tabs = ['日历', '四象限', '总结', '任务'];
        for (final tab in tabs) {
          final tabFinder = find.text(tab);
          if (tabFinder.evaluate().isNotEmpty) {
            await tester.tap(tabFinder);
            await tester.pumpAndSettle();
          }
        }
      }

      // 记录操作后内存使用情况
      final finalMemory = _getCurrentMemoryUsage();
      final memoryIncrease = finalMemory - initialMemory;
      
      print('最终内存使用: ${finalMemory}MB');
      print('内存增长: ${memoryIncrease}MB');

      // 验证内存增长在合理范围内（小于50MB）
      expect(memoryIncrease, lessThan(50), 
             reason: '内存增长应该控制在50MB以内，实际增长: ${memoryIncrease}MB');
    });

    testWidgets('数据库操作性能测试', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 测试批量插入性能
      final insertStopwatch = Stopwatch()..start();
      for (int i = 0; i < 50; i++) {
        await _createTestTaskQuick(tester, '数据库测试任务 $i');
      }
      insertStopwatch.stop();
      
      final insertTime = insertStopwatch.elapsedMilliseconds;
      expect(insertTime, lessThan(15000), 
             reason: '插入50个任务应该在15秒内完成，实际: ${insertTime}ms');
      
      print('批量插入50个任务耗时: ${insertTime}ms');

      // 测试查询性能
      final queryStopwatch = Stopwatch()..start();
      
      // 切换到不同视图触发查询
      final views = ['月视图', '四象限', '总结'];
      for (final view in views) {
        final viewFinder = find.text(view);
        if (viewFinder.evaluate().isNotEmpty) {
          await tester.tap(viewFinder);
          await tester.pumpAndSettle();
        }
      }
      
      queryStopwatch.stop();
      final queryTime = queryStopwatch.elapsedMilliseconds;
      
      expect(queryTime, lessThan(3000), 
             reason: '数据查询应该在3秒内完成，实际: ${queryTime}ms');
      
      print('数据查询性能测试耗时: ${queryTime}ms');
    });

    testWidgets('UI渲染性能测试', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 创建复杂的UI场景
      for (int i = 0; i < 30; i++) {
        await _createTestTaskQuick(tester, '渲染测试任务 $i');
      }

      // 测试复杂UI的渲染性能
      final renderStopwatch = Stopwatch()..start();
      
      // 切换到四象限视图（最复杂的布局）
      final quadrantTab = find.text('四象限');
      if (quadrantTab.evaluate().isNotEmpty) {
        await tester.tap(quadrantTab);
        await tester.pumpAndSettle();
      }
      
      renderStopwatch.stop();
      final renderTime = renderStopwatch.elapsedMilliseconds;
      
      expect(renderTime, lessThan(2000), 
             reason: '复杂UI渲染应该在2秒内完成，实际: ${renderTime}ms');
      
      print('复杂UI渲染耗时: ${renderTime}ms');
    });
  });

  group('压力测试', () {
    testWidgets('极限数据量测试', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 创建大量任务测试系统极限
      final stopwatch = Stopwatch()..start();
      
      for (int i = 0; i < 500; i++) {
        await _createTestTaskQuick(tester, '极限测试任务 $i');
        
        // 每100个任务检查一次
        if (i % 100 == 99) {
          await tester.pumpAndSettle();
          final currentTime = stopwatch.elapsedMilliseconds;
          print('创建${i + 1}个任务耗时: ${currentTime}ms');
          
          // 验证应用仍然响应
          expect(find.byType(MaterialApp), findsOneWidget, 
                 reason: '应用应该保持响应状态');
        }
      }
      
      stopwatch.stop();
      final totalTime = stopwatch.elapsedMilliseconds;
      
      print('创建500个任务总耗时: ${totalTime}ms');
      
      // 验证应用在极限数据量下仍能正常工作
      expect(find.byType(MaterialApp), findsOneWidget, 
             reason: '应用在极限数据量下应该保持稳定');
    });

    testWidgets('长时间运行稳定性测试', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // 模拟长时间使用场景
      for (int cycle = 0; cycle < 10; cycle++) {
        print('执行第${cycle + 1}轮操作循环');
        
        // 创建任务
        for (int i = 0; i < 5; i++) {
          await _createTestTaskQuick(tester, '循环${cycle}_任务$i');
        }
        
        // 切换页面
        final tabs = ['日历', '四象限', '总结', '任务'];
        for (final tab in tabs) {
          final tabFinder = find.text(tab);
          if (tabFinder.evaluate().isNotEmpty) {
            await tester.tap(tabFinder);
            await tester.pumpAndSettle();
          }
        }
        
        // 验证应用仍然稳定
        expect(find.byType(MaterialApp), findsOneWidget, 
               reason: '应用在第${cycle + 1}轮循环后应该保持稳定');
      }
      
      print('长时间运行稳定性测试完成');
    });
  });
}

// 辅助方法
Future<void> _createTestTaskQuick(WidgetTester tester, String title) async {
  // 简化的任务创建，用于性能测试
  final addButton = find.byIcon(Icons.add);
  if (addButton.evaluate().isNotEmpty) {
    await tester.tap(addButton);
    await tester.pump(); // 使用pump而不是pumpAndSettle以提高速度

    final titleField = find.byType(TextField).first;
    await tester.enterText(titleField, title);
    
    final saveButton = find.text('保存');
    if (saveButton.evaluate().isNotEmpty) {
      await tester.tap(saveButton);
      await tester.pump();
    }
  }
}

double _getCurrentMemoryUsage() {
  // 这里应该实现实际的内存监控逻辑
  // 由于Flutter测试环境的限制，这里返回模拟值
  return 50.0; // MB
}