import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:bloc_test/bloc_test.dart';

import 'package:mytodospace/features/tasks/presentation/widgets/quadrant_section.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_state.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_event.dart';
import 'package:mytodospace/domain/models/task_model.dart';

class MockTaskListBloc extends MockBloc<TaskListEvent, TaskListState> implements TaskListBloc {}

void main() {
  group('Expansion Performance Tests', () {
    late MockTaskListBloc mockTaskListBloc;

    setUp(() {
      mockTaskListBloc = MockTaskListBloc();
      whenListen(
        mockTaskListBloc,
        Stream.value(TaskListState.initial()),
        initialState: TaskListState.initial(),
      );
    });

    // 生成大量测试任务
    List<Task> generateLargeTasks(int count) {
      return List.generate(count, (index) => Task(
        id: 'task_$index',
        title: 'Performance Test Task ${index + 1}',
        notes: 'Test notes for performance task $index',
        priority: Priority.values[index % Priority.values.length],
        creationDate: DateTime.now(),
        dueDate: DateTime.now().add(Duration(days: index)),
        isCompleted: index % 3 == 0,
        subtasks: [],
      ));
    }

    Widget createTestWidget(List<Task> tasks) {
      return MaterialApp(
        home: Scaffold(
          body: BlocProvider<TaskListBloc>(
            create: (context) => mockTaskListBloc,
            child: QuadrantSection(
              title: 'Performance Test',
              color: Colors.blue,
              tasks: tasks,
              priority: Priority.urgentImportant,
            ),
          ),
        ),
      );
    }

    testWidgets('should handle 50 tasks efficiently', (tester) async {
      final largeTasks = generateLargeTasks(50);
      
      final stopwatch = Stopwatch()..start();
      
      await tester.pumpWidget(createTestWidget(largeTasks));
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      
      // 性能断言：渲染50个任务应该在300ms内完成（调整为更现实的期望）
      expect(stopwatch.elapsedMilliseconds, lessThan(300));
      
      // 验证功能正常
      expect(find.text('50'), findsOneWidget); // Task count
      expect(find.byIcon(Icons.expand_more), findsOneWidget);
    });

    testWidgets('should handle rapid expansion/collapse efficiently', (tester) async {
      final largeTasks = generateLargeTasks(30);
      
      await tester.pumpWidget(createTestWidget(largeTasks));
      await tester.pumpAndSettle();
      
      final expandButton = find.byIcon(Icons.expand_more);
      
      final stopwatch = Stopwatch()..start();
      
      // 执行10次快速展开/收起操作
      for (int i = 0; i < 10; i++) {
        await tester.tap(expandButton);
        await tester.pump(const Duration(milliseconds: 10));
      }
      
      await tester.pumpAndSettle();
      stopwatch.stop();
      
      // 性能断言：10次操作应该在500ms内完成
      expect(stopwatch.elapsedMilliseconds, lessThan(500));
      
      // 验证没有异常
      expect(tester.takeException(), isNull);
    });

    testWidgets('should efficiently render large task lists with ListView', (tester) async {
      final largeTasks = generateLargeTasks(100);
      
      await tester.pumpWidget(createTestWidget(largeTasks));
      await tester.pumpAndSettle();
      
      // 验证使用了ListView（通过查找Scrollable）
      expect(find.byType(Scrollable), findsOneWidget);
      
      // 验证任务数量显示正确
      expect(find.text('100'), findsOneWidget);
      
      // 验证展开收起功能正常
      final expandButton = find.byIcon(Icons.expand_more);
      await tester.tap(expandButton);
      await tester.pumpAndSettle();
      
      // 收起后任务应该隐藏
      expect(find.text('Performance Test Task 1'), findsNothing);
    });

    testWidgets('should maintain performance with frequent state changes', (tester) async {
      final tasks = generateLargeTasks(20);
      
      await tester.pumpWidget(createTestWidget(tasks));
      await tester.pumpAndSettle();
      
      final stopwatch = Stopwatch()..start();
      
      // 模拟频繁的状态变化
      for (int i = 0; i < 5; i++) {
        // 展开
        await tester.tap(find.byIcon(Icons.expand_more));
        await tester.pump(const Duration(milliseconds: 50));
        
        // 收起
        await tester.tap(find.byIcon(Icons.expand_more));
        await tester.pump(const Duration(milliseconds: 50));
      }
      
      await tester.pumpAndSettle();
      stopwatch.stop();
      
      // 性能断言：频繁状态变化应该在300ms内完成
      expect(stopwatch.elapsedMilliseconds, lessThan(300));
      
      // 验证最终状态正确（任务可能可见或不可见，取决于最后的状态）
      // 主要验证没有异常发生
      expect(tester.takeException(), isNull);
    });

    testWidgets('should handle memory efficiently with large datasets', (tester) async {
      // 测试内存效率：创建、销毁、重建大量任务
      for (int iteration = 0; iteration < 3; iteration++) {
        final tasks = generateLargeTasks(50);
        
        await tester.pumpWidget(createTestWidget(tasks));
        await tester.pumpAndSettle();
        
        // 执行一些操作
        await tester.tap(find.byIcon(Icons.expand_more));
        await tester.pumpAndSettle();
        
        await tester.tap(find.byIcon(Icons.expand_more));
        await tester.pumpAndSettle();
        
        // 清理
        await tester.pumpWidget(Container());
        await tester.pumpAndSettle();
      }
      
      // 如果没有内存泄漏，测试应该正常完成
      expect(tester.takeException(), isNull);
    });
  });
}
