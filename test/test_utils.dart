import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

/// 测试工具类
class TestUtils {
  /// 等待异步操作完成
  static Future<void> waitForAsync([Duration? duration]) async {
    await Future.delayed(duration ?? const Duration(milliseconds: 100));
  }

  /// 等待微任务完成
  static Future<void> pumpAndSettle(WidgetTester tester) async {
    await tester.pumpAndSettle();
  }

  /// 等待指定时间
  static Future<void> wait(Duration duration) async {
    await Future.delayed(duration);
  }

  /// 查找并点击按钮
  static Future<void> tapButton(WidgetTester tester, String text) async {
    final button = find.text(text);
    expect(button, findsOneWidget);
    await tester.tap(button);
    await tester.pumpAndSettle();
  }

  /// 输入文本
  static Future<void> enterText(WidgetTester tester, String text, String value) async {
    final textField = find.text(text);
    expect(textField, findsOneWidget);
    await tester.enterText(textField, value);
    await tester.pumpAndSettle();
  }

  /// 验证文本存在
  static void expectText(String text) {
    expect(find.text(text), findsOneWidget);
  }

  /// 验证文本不存在
  static void expectNoText(String text) {
    expect(find.text(text), findsNothing);
  }
}