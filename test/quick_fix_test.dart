import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/domain/exceptions/domain_exceptions.dart';

void main() {
  group('Quick Fix Verification', () {
    test('should create task with subtasks using Task.create', () {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final subtasks = [
        SubTask(
          id: 'temp_subtask_${timestamp}_1',
          parentTaskId: 'temp_parent',
          title: 'First subtask',
          isCompleted: false,
        ),
        SubTask(
          id: 'temp_subtask_${timestamp}_2',
          parentTaskId: 'temp_parent',
          title: 'Second subtask',
          isCompleted: false,
        ),
      ];

      final task = Task.create(
        title: 'Test Task with Subtasks',
        notes: 'This is a test task with notes',
        dueDate: DateTime.now().add(const Duration(days: 1)),
        priority: Priority.importantNotUrgent,
        subtasks: subtasks,
      );

      // Verify task was created successfully
      expect(task.title, 'Test Task with Subtasks');
      expect(task.notes, 'This is a test task with notes');
      expect(task.priority, Priority.importantNotUrgent);
      expect(task.subtasks.length, 2);

      // Verify subtask parent IDs were fixed
      for (final subtask in task.subtasks) {
        expect(subtask.parentTaskId, task.id);
        expect(subtask.parentTaskId, isNot('temp_parent'));
      }

      print('✅ Task created successfully with ID: ${task.id}');
      print('✅ Subtasks parent IDs fixed: ${task.subtasks.map((s) => s.parentTaskId).toSet()}');
    });

    test('should handle today due date correctly', () {
      final today = DateTime.now();
      final todayDate = DateTime(today.year, today.month, today.day);

      final task = Task.create(
        title: 'Task Due Today',
        dueDate: todayDate,
        priority: Priority.urgentImportant,
      );

      // Verify due date was normalized to end of day
      expect(task.dueDate.year, today.year);
      expect(task.dueDate.month, today.month);
      expect(task.dueDate.day, today.day);
      expect(task.dueDate.hour, 23);
      expect(task.dueDate.minute, 59);

      print('✅ Today due date normalized: ${task.dueDate}');
    });

    test('should handle empty parentTaskId validation', () {
      // This should NOT throw an exception anymore
      expect(() {
        SubTask.validated(
          id: 'test_subtask',
          parentTaskId: 'temp_parent', // This should be valid
          title: 'Test Subtask',
          isCompleted: false,
        );
      }, returnsNormally);

      // This SHOULD throw an exception
      expect(() {
        SubTask.validated(
          id: 'test_subtask',
          parentTaskId: '', // Empty string should fail
          title: 'Test Subtask',
          isCompleted: false,
        );
      }, throwsA(isA<SubTaskException>()));

      print('✅ SubTask validation working correctly');
    });
  });
}
