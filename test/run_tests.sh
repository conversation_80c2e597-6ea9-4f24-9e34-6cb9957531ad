#!/bin/bash

# 测试运行脚本
# 用于自动化执行所有测试并生成报告

set -e

echo "🧪 开始执行自动化测试..."
echo "=================================="

# 设置测试环境
export FLUTTER_TEST=true
export TEST_MODE=automated

# 创建测试报告目录
mkdir -p test_reports

# 记录开始时间
START_TIME=$(date +%s)

# 1. 运行单元测试
echo "📋 执行单元测试..."
flutter test test/unit/ \
  --reporter=expanded \
  --coverage \
  --coverage-path=coverage/unit \
  > test_reports/unit_tests.log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 单元测试通过"
else
    echo "❌ 单元测试失败，查看日志: test_reports/unit_tests.log"
    exit 1
fi

# 2. 运行集成测试
echo "🔗 执行集成测试..."
flutter test test/integration/ \
  --reporter=expanded \
  --coverage \
  --coverage-path=coverage/integration \
  > test_reports/integration_tests.log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 集成测试通过"
else
    echo "❌ 集成测试失败，查看日志: test_reports/integration_tests.log"
    exit 1
fi

# 3. 运行Widget测试
echo "🎨 执行Widget测试..."
flutter test test/unit/widgets/ \
  --reporter=expanded \
  --coverage \
  --coverage-path=coverage/widgets \
  > test_reports/widget_tests.log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ Widget测试通过"
else
    echo "❌ Widget测试失败，查看日志: test_reports/widget_tests.log"
    exit 1
fi

# 4. 生成覆盖率报告
echo "📊 生成测试覆盖率报告..."
if command -v genhtml &> /dev/null; then
    genhtml coverage/unit/lcov.info -o test_reports/coverage_report
    echo "✅ 覆盖率报告生成完成: test_reports/coverage_report/index.html"
else
    echo "⚠️  genhtml未安装，跳过覆盖率报告生成"
fi

# 5. 运行性能测试
echo "⚡ 执行性能测试..."
flutter test test/performance/ \
  --reporter=expanded \
  > test_reports/performance_tests.log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 性能测试通过"
else
    echo "❌ 性能测试失败，查看日志: test_reports/performance_tests.log"
fi

# 6. 运行压力测试
echo "💪 执行压力测试..."
flutter test test/stress/ \
  --reporter=expanded \
  > test_reports/stress_tests.log 2>&1

if [ $? -eq 0 ]; then
    echo "✅ 压力测试通过"
else
    echo "❌ 压力测试失败，查看日志: test_reports/stress_tests.log"
fi

# 记录结束时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))

# 生成测试摘要
echo "=================================="
echo "🎉 所有测试执行完成！"
echo "⏱️  总耗时: ${DURATION}秒"
echo "📁 测试报告位置: test_reports/"
echo "📊 覆盖率报告: test_reports/coverage_report/"

# 统计测试结果
echo ""
echo "📈 测试统计:"
echo "   - 单元测试: $(grep -c "PASSED\|FAILED" test_reports/unit_tests.log 2>/dev/null || echo "N/A")"
echo "   - 集成测试: $(grep -c "PASSED\|FAILED" test_reports/integration_tests.log 2>/dev/null || echo "N/A")"
echo "   - Widget测试: $(grep -c "PASSED\|FAILED" test_reports/widget_tests.log 2>/dev/null || echo "N/A")"

echo ""
echo "🔍 查看详细日志:"
echo "   - 单元测试: cat test_reports/unit_tests.log"
echo "   - 集成测试: cat test_reports/integration_tests.log"
echo "   - Widget测试: cat test_reports/widget_tests.log"

# 检查是否有失败的测试
FAILED_TESTS=$(grep -r "FAILED\|ERROR" test_reports/ || true)
if [ -n "$FAILED_TESTS" ]; then
    echo ""
    echo "❌ 发现失败的测试:"
    echo "$FAILED_TESTS"
    exit 1
else
    echo ""
    echo "✅ 所有测试都通过了！"
    exit 0
fi
