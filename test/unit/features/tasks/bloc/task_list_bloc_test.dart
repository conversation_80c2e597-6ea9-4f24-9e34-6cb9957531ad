import 'dart:async';
import 'package:bloc_test/bloc_test.dart';
import 'package:clock/clock.dart';

import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:mytodospace/domain/domain.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_event.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_state.dart';

class MockTaskRepository extends Mock implements TaskRepository {}

void main() {
  final fixedTime = DateTime(2024, 1, 15, 10, 0, 0);
  final fixedClock = Clock.fixed(fixedTime);

  withClock(fixedClock, () {
    group('TaskListBloc', () {
      late TaskRepository mockTaskRepository;
      late TaskListBloc taskListBloc;
      late StreamController<List<Task>> tasksStreamController;

      setUpAll(() {
        registerFallbackValue(
            Task.create(title: 'fallback', dueDate: DateTime(2024)));
      });

      // Test data
      final testDate = DateTime(2024, 1, 15);
      final testTask1 = Task(
        id: 'task1',
        title: 'Test Task 1',
        notes: 'Test notes 1',
        creationDate: DateTime(2024, 1, 14),
        dueDate: testDate,
        priority: Priority.urgentImportant,
        isCompleted: false,
        subtasks: [],
      );

      final testTask2 = Task(
        id: 'task2',
        title: 'Test Task 2',
        notes: 'Test notes 2',
        creationDate: DateTime(2024, 1, 14),
        dueDate: testDate,
        priority: Priority.importantNotUrgent,
        isCompleted: true,
        completionDate: DateTime(2024, 1, 15),
        subtasks: [],
      );

      final testTasks = [testTask1, testTask2];

      setUp(() {
        mockTaskRepository = MockTaskRepository();
        tasksStreamController = StreamController<List<Task>>.broadcast();

        // Setup default mock behavior
        when(() => mockTaskRepository.watchTasksByDate(any()))
            .thenAnswer((_) => tasksStreamController.stream);
        when(() => mockTaskRepository.searchTasks(any()))
            .thenAnswer((_) async => []);

        taskListBloc = TaskListBloc(mockTaskRepository, fixedClock);
      });

      tearDown(() {
        tasksStreamController.close();
        taskListBloc.close();
      });

      test('initial state is correct', () {
        expect(
          taskListBloc.state,
          TaskListState.initial().copyWith(date: fixedTime),
        );
      });

      group('subscriptionRequested', () {
        blocTest<TaskListBloc, TaskListState>(
          'emits loading then success when subscription succeeds',
          build: () => taskListBloc,
          setUp: () {
            when(() => mockTaskRepository.watchTasksByDate(any()))
                .thenAnswer((_) => tasksStreamController.stream);
          },
          act: (bloc) async {
            bloc.add(TaskListEvent.subscriptionRequested(testDate));
            // Wait a bit for the subscription to be set up
            await Future.delayed(const Duration(milliseconds: 10));
            // Simulate stream emitting data
            tasksStreamController.add(testTasks);
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.loading),
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.success)
                .having((s) => s.tasks, 'tasks', testTasks)
                .having((s) => s.totalTasks, 'totalTasks', 2)
                .having((s) => s.completedTasks, 'completedTasks', 1),
          ],
          verify: (_) {
            verify(() => mockTaskRepository.watchTasksByDate(testDate))
                .called(1);
          },
        );

        blocTest<TaskListBloc, TaskListState>(
          'emits loading then failure when subscription fails',
          build: () => taskListBloc,
          setUp: () {
            when(() => mockTaskRepository.watchTasksByDate(any()))
                .thenAnswer((_) => Stream.error(Exception('Database error')));
          },
          act: (bloc) =>
              bloc.add(TaskListEvent.subscriptionRequested(testDate)),
          expect: () => [
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.loading),
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.failure)
                .having((s) => s.errorMessage, 'errorMessage', isNotNull),
          ],
        );
      });

      group('completionToggled', () {
        blocTest<TaskListBloc, TaskListState>(
          'successfully toggles task completion',
          build: () => taskListBloc,
          seed: () => TaskListState.initial().copyWith(
            tasks: testTasks,
            filteredTasks: testTasks,
          ),
          setUp: () {
            when(() => mockTaskRepository.updateTask(any()))
                .thenAnswer((_) async {});
          },
          act: (bloc) => bloc.add(
            const TaskListEvent.completionToggled(
              taskId: 'task1',
              isCompleted: true,
            ),
          ),
          expect: () => [
            isA<TaskListState>().having((s) => s.currentOperation,
                'currentOperation', TaskListOperation.togglingCompletion),
            isA<TaskListState>().having((s) => s.currentOperation,
                'currentOperation', TaskListOperation.none),
          ],
          verify: (_) {
            final captured =
                verify(() => mockTaskRepository.updateTask(captureAny()))
                    .captured;
            final updatedTask = captured.first as Task;
            expect(updatedTask.isCompleted, isTrue);
            expect(updatedTask.completionDate, isNotNull);
          },
        );

        blocTest<TaskListBloc, TaskListState>(
          'emits failure when task not found',
          build: () => taskListBloc,
          seed: () => TaskListState.initial().copyWith(
            tasks: testTasks,
            filteredTasks: testTasks,
          ),
          act: (bloc) => bloc.add(
            const TaskListEvent.completionToggled(
              taskId: 'nonexistent',
              isCompleted: true,
            ),
          ),
          expect: () => [
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.initial)
                .having((s) => s.currentOperation, 'currentOperation',
                    TaskListOperation.togglingCompletion)
                .having((s) => s.taskLoadingStates, 'taskLoadingStates',
                    {'nonexistent': true}),
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.failure)
                .having((s) => s.lastException, 'lastException',
                    isA<TaskNotFoundException>()),
          ],
        );
      });

      group('taskDeleted', () {
        blocTest<TaskListBloc, TaskListState>(
          'successfully deletes task',
          build: () => taskListBloc,
          seed: () => TaskListState.initial().copyWith(
            tasks: testTasks,
            filteredTasks: testTasks,
          ),
          setUp: () {
            when(() => mockTaskRepository.deleteTask(any()))
                .thenAnswer((_) async {});
          },
          act: (bloc) => bloc.add(
            const TaskListEvent.taskDeleted(taskId: 'task1'),
          ),
          expect: () => [
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.deleting),
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.success),
          ],
          verify: (_) {
            verify(() => mockTaskRepository.deleteTask('task1')).called(1);
          },
        );
      });

      group('taskCreated', () {
        blocTest<TaskListBloc, TaskListState>(
          'successfully creates task',
          build: () => taskListBloc,
          setUp: () {
            when(() => mockTaskRepository.createTask(any()))
                .thenAnswer((_) async {});
          },
          act: (bloc) => bloc.add(TaskListEvent.taskCreated(testTask1)),
          expect: () => [
            isA<TaskListState>().having((s) => s.currentOperation,
                'currentOperation', TaskListOperation.creatingTask),
            isA<TaskListState>().having((s) => s.currentOperation,
                'currentOperation', TaskListOperation.none),
          ],
          verify: (_) {
            verify(() => mockTaskRepository.createTask(testTask1)).called(1);
          },
        );
      });

      group('searchRequested', () {
        blocTest<TaskListBloc, TaskListState>(
          'filters tasks based on search query',
          build: () => taskListBloc,
          setUp: () {
            // Mock searchTasks to return testTask1 when searching for 'Task 1'
            when(() => mockTaskRepository.searchTasks('Task 1'))
                .thenAnswer((_) async => [testTask1]);
          },
          seed: () => TaskListState.initial().copyWith(
            tasks: testTasks,
            filteredTasks: testTasks,
          ),
          act: (bloc) =>
              bloc.add(const TaskListEvent.searchRequested('Task 1')),
          wait: const Duration(milliseconds: 400), // Wait for debounce
          expect: () => [
            isA<TaskListState>()
                .having((s) => s.searchQuery, 'searchQuery', 'Task 1')
                .having((s) => s.filteredTasks, 'filteredTasks', [testTask1]),
          ],
        );
      });

      group('filterByPriority', () {
        blocTest<TaskListBloc, TaskListState>(
          'filters tasks by priority',
          build: () => taskListBloc,
          seed: () => TaskListState.initial().copyWith(
            tasks: testTasks,
            filteredTasks: testTasks,
          ),
          act: (bloc) => bloc.add(
            const TaskListEvent.filterByPriority(Priority.urgentImportant),
          ),
          expect: () => [
            isA<TaskListState>()
                .having((s) => s.priorityFilter, 'priorityFilter',
                    Priority.urgentImportant)
                .having((s) => s.filteredTasks, 'filteredTasks', [testTask1]),
          ],
        );
      });

      group('filterByCompletion', () {
        blocTest<TaskListBloc, TaskListState>(
          'filters tasks by completion status',
          build: () => taskListBloc,
          seed: () => TaskListState.initial().copyWith(
            tasks: testTasks,
            filteredTasks: testTasks,
          ),
          act: (bloc) => bloc.add(
            const TaskListEvent.filterByCompletion(true),
          ),
          expect: () => [
            isA<TaskListState>()
                .having((s) => s.completionFilter, 'completionFilter', true)
                .having((s) => s.filteredTasks, 'filteredTasks', [testTask2]),
          ],
        );
      });

      group('filtersCleared', () {
        blocTest<TaskListBloc, TaskListState>(
          'clears all filters',
          build: () => taskListBloc,
          seed: () => TaskListState.initial().copyWith(
            tasks: testTasks,
            filteredTasks: [testTask1],
            searchQuery: 'test',
            priorityFilter: Priority.urgentImportant,
            completionFilter: false,
            hasActiveFilters: true,
          ),
          act: (bloc) => bloc.add(const TaskListEvent.filtersCleared()),
          expect: () => [
            isA<TaskListState>()
                .having((s) => s.hasActiveFilters, 'hasActiveFilters', false)
                .having((s) => s.searchQuery, 'searchQuery', '')
                .having((s) => s.priorityFilter, 'priorityFilter', null)
                .having((s) => s.completionFilter, 'completionFilter', null),
          ],
        );
      });

      group('retryRequested', () {
        blocTest<TaskListBloc, TaskListState>(
          'retries failed operation',
          build: () => taskListBloc,
          setUp: () {
            when(() => mockTaskRepository.watchTasksByDate(any()))
                .thenAnswer((_) => tasksStreamController.stream);
          },
          seed: () => TaskListState.initial().copyWith(
            status: TaskListStatus.failure,
            errorMessage: 'Some error',
            canRetry: true,
            date: testDate,
          ),
          act: (bloc) async {
            bloc.add(const TaskListEvent.retryRequested());
            // Wait a bit for the subscription to be set up
            await Future.delayed(const Duration(milliseconds: 10));
            // Simulate successful retry
            tasksStreamController.add(testTasks);
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.loading),
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.loading),
            isA<TaskListState>()
                .having((s) => s.status, 'status', TaskListStatus.success)
                .having((s) => s.tasks, 'tasks', testTasks),
          ],
        );
      });

      group('state extensions', () {
        test('isLoading returns correct value', () {
          expect(
            TaskListState.initial()
                .copyWith(status: TaskListStatus.loading)
                .isLoading,
            isTrue,
          );
          expect(
            TaskListState.initial()
                .copyWith(status: TaskListStatus.success)
                .isLoading,
            isFalse,
          );
        });

        test('hasError returns correct value', () {
          expect(
            TaskListState.initial()
                .copyWith(status: TaskListStatus.failure)
                .hasError,
            isTrue,
          );
          expect(
            TaskListState.initial()
                .copyWith(status: TaskListStatus.success)
                .hasError,
            isFalse,
          );
        });

        test('completionRate calculates correctly', () {
          final state = TaskListState.initial().copyWith(
            totalTasks: 4,
            completedTasks: 2,
          );
          expect(state.completionRate, equals(50.0));
        });

        test('tasksByPriority groups correctly', () {
          final state = TaskListState.initial().copyWith(
            filteredTasks: testTasks,
          );

          final grouped = state.tasksByPriority;
          expect(grouped[Priority.urgentImportant], contains(testTask1));
          expect(grouped[Priority.importantNotUrgent], contains(testTask2));
        });
      });
    });
  });
}
