// Mocks generated by Mockito 5.4.6 from annotations
// in mytodospace/test/unit/features/summary/bloc/summary_bloc_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mytodospace/domain/models/summary_report_model.dart' as _i2;
import 'package:mytodospace/domain/repositories/summary_repository.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeSummaryReport_0 extends _i1.SmartFake implements _i2.SummaryReport {
  _FakeSummaryReport_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [SummaryRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockSummaryRepository extends _i1.Mock implements _i3.SummaryRepository {
  MockSummaryRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.SummaryReport> getMonthlySummary({
    required int? year,
    required int? month,
  }) =>
      (super.noSuchMethod(
        Invocation.method(
          #getMonthlySummary,
          [],
          {
            #year: year,
            #month: month,
          },
        ),
        returnValue: _i4.Future<_i2.SummaryReport>.value(_FakeSummaryReport_0(
          this,
          Invocation.method(
            #getMonthlySummary,
            [],
            {
              #year: year,
              #month: month,
            },
          ),
        )),
      ) as _i4.Future<_i2.SummaryReport>);

  @override
  _i4.Future<_i2.SummaryReport> getYearlySummary({required int? year}) =>
      (super.noSuchMethod(
        Invocation.method(
          #getYearlySummary,
          [],
          {#year: year},
        ),
        returnValue: _i4.Future<_i2.SummaryReport>.value(_FakeSummaryReport_0(
          this,
          Invocation.method(
            #getYearlySummary,
            [],
            {#year: year},
          ),
        )),
      ) as _i4.Future<_i2.SummaryReport>);
}
