import 'package:bloc_test/bloc_test.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:mytodospace/features/summary/bloc/summary_bloc.dart';
import 'package:mytodospace/features/summary/bloc/summary_event.dart';
import 'package:mytodospace/features/summary/bloc/summary_state.dart';
import 'package:mytodospace/domain/repositories/summary_repository.dart';
import 'package:mytodospace/domain/models/summary_report_model.dart';
import 'package:mytodospace/domain/models/task_model.dart';

// Generate mocks
@GenerateMocks([SummaryRepository])
import 'summary_bloc_test.mocks.dart';

void main() {
  group('SummaryBloc Tests', () {
    late SummaryBloc bloc;
    late MockSummaryRepository mockRepository;

    setUp(() {
      mockRepository = MockSummaryRepository();
      bloc = SummaryBloc(mockRepository);
    });

    tearDown(() {
      bloc.close();
    });

    test('initial state should be correct', () {
      final initialState = bloc.state;
      expect(initialState.status, equals(SummaryStatus.initial));
      expect(initialState.currentOperation, equals(SummaryOperation.none));
      expect(initialState.report, isNull);
      expect(initialState.summaryData, isNull);
    });

    group('monthlyRequested', () {
      const year = 2025;
      const month = 9;

      const testReport = SummaryReport(
        period: '2025年9月',
        totalTasksCreated: 20,
        totalTasksCompleted: 15,
        completionRate: 75.0,
        quadrantDistribution: {
          Priority.urgentImportant: 5,
          Priority.importantNotUrgent: 8,
          Priority.urgentNotImportant: 4,
          Priority.notUrgentNotImportant: 3,
        },
        highlights: [],
      );

      blocTest<SummaryBloc, SummaryState>(
        'should emit loading then success when monthly summary requested',
        build: () {
          when(mockRepository.getMonthlySummary(
            year: year,
            month: month,
          )).thenAnswer((_) async => testReport);
          return bloc;
        },
        act: (bloc) => bloc.add(
          const SummaryEvent.monthlyRequested(year: year, month: month),
        ),
        expect: () => [
          isA<SummaryState>()
              .having((s) => s.status, 'status', SummaryStatus.loading)
              .having((s) => s.currentOperation, 'currentOperation',
                  SummaryOperation.loadingReport),
          isA<SummaryState>()
              .having((s) => s.status, 'status', SummaryStatus.success)
              .having((s) => s.currentOperation, 'currentOperation',
                  SummaryOperation.none)
              .having((s) => s.report, 'report', testReport),
        ],
        verify: (_) {
          verify(mockRepository.getMonthlySummary(
            year: year,
            month: month,
          )).called(1);
        },
      );

      blocTest<SummaryBloc, SummaryState>(
        'should emit failure when repository throws error',
        build: () {
          when(mockRepository.getMonthlySummary(
            year: year,
            month: month,
          )).thenThrow(Exception('Database error'));
          return bloc;
        },
        act: (bloc) => bloc.add(
          const SummaryEvent.monthlyRequested(year: year, month: month),
        ),
        expect: () => [
          isA<SummaryState>()
              .having((s) => s.status, 'status', SummaryStatus.loading)
              .having((s) => s.currentOperation, 'currentOperation',
                  SummaryOperation.loadingReport),
          isA<SummaryState>()
              .having((s) => s.status, 'status', SummaryStatus.failure)
              .having((s) => s.currentOperation, 'currentOperation',
                  SummaryOperation.none)
              .having((s) => s.errorMessage, 'errorMessage',
                  'Exception: Database error'),
        ],
      );
    });

    group('yearlyRequested', () {
      const year = 2025;

      const testReport = SummaryReport(
        period: '2025年',
        totalTasksCreated: 240,
        totalTasksCompleted: 180,
        completionRate: 75.0,
        quadrantDistribution: {
          Priority.urgentImportant: 60,
          Priority.importantNotUrgent: 96,
          Priority.urgentNotImportant: 48,
          Priority.notUrgentNotImportant: 36,
        },
        highlights: [],
      );

      blocTest<SummaryBloc, SummaryState>(
        'should emit loading then success when yearly summary requested',
        build: () {
          when(mockRepository.getYearlySummary(year: year))
              .thenAnswer((_) async => testReport);
          return bloc;
        },
        act: (bloc) => bloc.add(
          const SummaryEvent.yearlyRequested(year: year),
        ),
        expect: () => [
          isA<SummaryState>()
              .having((s) => s.status, 'status', SummaryStatus.loading)
              .having((s) => s.currentOperation, 'currentOperation',
                  SummaryOperation.loadingReport),
          isA<SummaryState>()
              .having((s) => s.status, 'status', SummaryStatus.success)
              .having((s) => s.currentOperation, 'currentOperation',
                  SummaryOperation.none)
              .having((s) => s.report, 'report', testReport),
        ],
        verify: (_) {
          verify(mockRepository.getYearlySummary(year: year)).called(1);
        },
      );

      blocTest<SummaryBloc, SummaryState>(
        'should emit failure when repository throws error',
        build: () {
          when(mockRepository.getYearlySummary(year: year))
              .thenThrow(Exception('Network error'));
          return bloc;
        },
        act: (bloc) => bloc.add(
          const SummaryEvent.yearlyRequested(year: year),
        ),
        expect: () => [
          isA<SummaryState>()
              .having((s) => s.status, 'status', SummaryStatus.loading)
              .having((s) => s.currentOperation, 'currentOperation',
                  SummaryOperation.loadingReport),
          isA<SummaryState>()
              .having((s) => s.status, 'status', SummaryStatus.failure)
              .having((s) => s.currentOperation, 'currentOperation',
                  SummaryOperation.none)
              .having((s) => s.errorMessage, 'errorMessage',
                  'Exception: Network error'),
        ],
      );
    });
  });
}
