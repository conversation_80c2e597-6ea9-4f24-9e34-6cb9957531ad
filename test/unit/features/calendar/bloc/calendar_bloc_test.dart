import 'dart:async';
import 'package:bloc_test/bloc_test.dart';

import 'package:clock/clock.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:mytodospace/domain/domain.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_bloc.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_event.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_state.dart';

class MockTaskRepository extends Mock implements TaskRepository {}

class MockSummaryRepository extends Mock implements SummaryRepository {}

class FakeTask extends Fake implements Task {}

void main() {
  final fixedTime = DateTime(2024, 1, 15, 10, 0, 0);
  final fixedClock = Clock.fixed(fixedTime);

  withClock(fixedClock, () {
    setUpAll(() {
      registerFallbackValue(FakeTask());
    });

    group('CalendarBloc', () {
      late TaskRepository mockTaskRepository;
      late CalendarBloc calendarBloc;

      late StreamController<Map<DateTime, int>> taskLoadStreamController;

      // Test data
      final testDate = DateTime(2024, 1, 15);
      final testMonth = DateTime(2024, 1, 1);
      final testTaskLoad = {
        DateTime(2024, 1, 15): 3,
        DateTime(2024, 1, 16): 1,
        DateTime(2024, 1, 17): 2,
      };

      setUp(() {
        mockTaskRepository = MockTaskRepository();
        taskLoadStreamController =
            StreamController<Map<DateTime, int>>.broadcast();

        // Setup default mock behavior
        when(() => mockTaskRepository.watchTaskLoadForMonth(
              year: any(named: 'year'),
              month: any(named: 'month'),
            )).thenAnswer((_) => taskLoadStreamController.stream);
        when(() => mockTaskRepository.watchTasksByDate(any()))
            .thenAnswer((_) => Stream.value([]));
        when(() => mockTaskRepository.createTask(any()))
            .thenAnswer((_) async {});
        when(() => mockTaskRepository.updateTask(any()))
            .thenAnswer((_) async {});

        calendarBloc = CalendarBloc(mockTaskRepository, fixedClock);
        when(() => mockTaskRepository.getTasksForDate(any()))
            .thenAnswer((_) async => []);
      });

      tearDown(() {
        taskLoadStreamController.close();
        calendarBloc.close();
      });

      // Helper function to create initial state with fixed test date
      CalendarState createInitialState() {
        return CalendarState(
          status: CalendarStatus.initial,
          currentOperation: CalendarOperation.none,
          viewType: CalendarViewType.month,
          selectedDate: fixedTime,
          displayMonthDate: DateTime(fixedTime.year, fixedTime.month, 1),
          displayYear: fixedTime.year,
          taskLoadByDate: {},
          yearTaskLoadByDate: {},
          canNavigatePrevious: true,
          canNavigateNext: true,
        );
      }

      test('initial state is correct', () {
        final expectedState = CalendarState(
          status: CalendarStatus.initial,
          currentOperation: CalendarOperation.none,
          viewType: CalendarViewType.month,
          selectedDate: fixedTime,
          displayMonthDate: DateTime(fixedTime.year, fixedTime.month, 1),
          displayYear: fixedTime.year,
          taskLoadByDate: {},
          yearTaskLoadByDate: {},
          canNavigatePrevious: true,
          canNavigateNext: true,
        );
        expect(calendarBloc.state, equals(expectedState));
      });

      group('started', () {
        blocTest<CalendarBloc, CalendarState>(
          'emits loading then success when initialization succeeds',
          build: () => calendarBloc,
          act: (bloc) async {
            bloc.add(const CalendarEvent.started());
            // Wait a bit for the subscription to be set up
            await Future.delayed(const Duration(milliseconds: 10));
            // Simulate stream emitting data
            taskLoadStreamController.add(testTaskLoad);
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            CalendarState(
              status: CalendarStatus.loading,
              currentOperation: CalendarOperation.loadingData,
              viewType: CalendarViewType.month,
              selectedDate: fixedTime,
              displayMonthDate: DateTime(fixedTime.year, fixedTime.month, 1),
              displayYear: fixedTime.year,
              taskLoadByDate: {},
              yearTaskLoadByDate: {},
              canNavigatePrevious: true,
              canNavigateNext: true,
            ),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', {}),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', testTaskLoad)
                .having((s) => s.loadDurationMs, 'loadDurationMs', isA<int>()),
          ],
          verify: (_) {
            verify(() => mockTaskRepository.watchTaskLoadForMonth(
                  year: any(named: 'year'),
                  month: any(named: 'month'),
                )).called(1);
          },
        );

        blocTest<CalendarBloc, CalendarState>(
          'emits loading then failure when initialization fails',
          build: () => calendarBloc,
          setUp: () {
            when(() => mockTaskRepository.watchTaskLoadForMonth(
                  year: any(named: 'year'),
                  month: any(named: 'month'),
                )).thenThrow(Exception('Database error'));
          },
          act: (bloc) => bloc.add(const CalendarEvent.started()),
          expect: () => [
            createInitialState().copyWith(
              status: CalendarStatus.loading,
              currentOperation: CalendarOperation.loadingData,
            ),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.failure)
                .having((s) => s.errorMessage, 'errorMessage', isNotNull)
                .having(
                    (s) => s.lastException, 'lastException', isA<Exception>()),
          ],
        );
      });

      group('dateSelected', () {
        blocTest<CalendarBloc, CalendarState>(
          'updates selected date',
          build: () => calendarBloc,
          act: (bloc) => bloc.add(CalendarEvent.dateSelected(testDate)),
          expect: () => [
            createInitialState().copyWith(
              selectedDate: testDate,
              currentOperation: CalendarOperation.none,
            ),
          ],
        );

        blocTest<CalendarBloc, CalendarState>(
          'navigates to month when date is in different month',
          build: () => calendarBloc,
          seed: () => createInitialState().copyWith(
            displayMonthDate: DateTime(2024, 2, 1), // Different month
          ),
          act: (bloc) async {
            bloc.add(CalendarEvent.dateSelected(testDate));
            // Wait a bit for the navigation to be set up
            await Future.delayed(const Duration(milliseconds: 10));
            // Simulate month change completion
            taskLoadStreamController.add(testTaskLoad);
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            createInitialState().copyWith(
              displayMonthDate: DateTime(2024, 2, 1),
              selectedDate: testDate,
              currentOperation: CalendarOperation.none,
            ),
            createInitialState().copyWith(
              displayMonthDate: testMonth,
              selectedDate: testDate,
              status: CalendarStatus.loading,
              currentOperation: CalendarOperation.navigating,
            ),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', {}),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having(
                    (s) => s.displayMonthDate, 'displayMonthDate', testMonth)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', testTaskLoad)
                .having((s) => s.loadDurationMs, 'loadDurationMs', isA<int>()),
          ],
        );
      });

      group('monthChanged', () {
        blocTest<CalendarBloc, CalendarState>(
          'successfully changes month and loads data',
          build: () => calendarBloc,
          act: (bloc) async {
            bloc.add(CalendarEvent.monthChanged(testMonth));
            // Wait a bit for the navigation to be set up
            await Future.delayed(const Duration(milliseconds: 10));
            taskLoadStreamController.add(testTaskLoad);
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            createInitialState().copyWith(
              status: CalendarStatus.loading,
              currentOperation: CalendarOperation.navigating,
              displayMonthDate: testMonth,
            ),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', {}),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having(
                    (s) => s.displayMonthDate, 'displayMonthDate', testMonth)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', testTaskLoad)
                .having((s) => s.loadDurationMs, 'loadDurationMs', isA<int>()),
          ],
          verify: (_) {
            verify(() => mockTaskRepository.watchTaskLoadForMonth(
                  year: testMonth.year,
                  month: testMonth.month,
                )).called(1);
          },
        );
      });

      group('yearChanged', () {
        blocTest<CalendarBloc, CalendarState>(
          'successfully changes year',
          build: () => calendarBloc,
          act: (bloc) => bloc.add(const CalendarEvent.yearChanged(2025)),
          expect: () => [
            createInitialState().copyWith(
              status: CalendarStatus.loading,
              currentOperation: CalendarOperation.navigating,
              displayYear: 2025,
            ),
          ],
        );
      });

      group('todayNavigated', () {
        blocTest<CalendarBloc, CalendarState>(
          'navigates to today',
          build: () => calendarBloc,
          act: (bloc) async {
            bloc.add(const CalendarEvent.todayNavigated());
            // Wait a bit for the navigation to be set up
            await Future.delayed(const Duration(milliseconds: 10));
            taskLoadStreamController.add(testTaskLoad);
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            // Date selection
            createInitialState().copyWith(
              selectedDate: fixedTime,
              currentOperation: CalendarOperation.none,
            ),
            // Month navigation
            createInitialState().copyWith(
              selectedDate: fixedTime,
              status: CalendarStatus.loading,
              currentOperation: CalendarOperation.navigating,
              displayMonthDate: DateTime(fixedTime.year, fixedTime.month, 1),
            ),
            // Intermediate success state (empty data)
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', {}),
            // Final success state (with task load data)
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', testTaskLoad)
                .having((s) => s.loadDurationMs, 'loadDurationMs', isA<int>()),
          ],
        );
      });

      group('quickTaskAdded', () {
        blocTest<CalendarBloc, CalendarState>(
          'successfully creates quick task',
          build: () => calendarBloc,
          act: (bloc) => bloc.add(CalendarEvent.quickTaskAdded(
            title: 'Test Task',
            date: testDate,
            priority: Priority.urgentImportant,
          )),
          expect: () => [
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.creatingTask)
                .having(
                    (s) => s.creatingTaskIds, 'creatingTaskIds', isNotEmpty),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.creatingTaskIds, 'creatingTaskIds', isEmpty),
          ],
          verify: (_) {
            verify(() => mockTaskRepository.createTask(any())).called(1);
          },
        );

        blocTest<CalendarBloc, CalendarState>(
          'emits failure when task creation fails',
          build: () => calendarBloc,
          setUp: () {
            when(() => mockTaskRepository.createTask(any()))
                .thenThrow(Exception('Creation failed'));
          },
          act: (bloc) => bloc.add(CalendarEvent.quickTaskAdded(
            title: 'Test Task',
            date: testDate,
            priority: Priority.urgentImportant,
          )),
          expect: () => [
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.creatingTask)
                .having(
                    (s) => s.creatingTaskIds, 'creatingTaskIds', isNotEmpty),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.failure)
                .having((s) => s.errorMessage, 'errorMessage', isNotNull)
                .having(
                    (s) => s.lastException, 'lastException', isA<Exception>())
                .having((s) => s.creatingTaskIds, 'creatingTaskIds', isEmpty),
          ],
        );
      });

      group('bulkQuickTasksAdded', () {
        blocTest<CalendarBloc, CalendarState>(
          'successfully creates multiple quick tasks',
          build: () => calendarBloc,
          act: (bloc) => bloc.add(CalendarEvent.bulkQuickTasksAdded(
            titles: ['Task 1', 'Task 2', 'Task 3'],
            date: testDate,
            priority: Priority.importantNotUrgent,
          )),
          expect: () => [
            createInitialState().copyWith(
              status: CalendarStatus.creatingTask,
              currentOperation: CalendarOperation.creatingQuickTask,
            ),
            createInitialState().copyWith(
              status: CalendarStatus.success,
              currentOperation: CalendarOperation.none,
            ),
          ],
          verify: (_) {
            verify(() => mockTaskRepository.createTask(any())).called(3);
          },
        );
      });

      group('viewTypeChanged', () {
        blocTest<CalendarBloc, CalendarState>(
          'changes view type to year',
          build: () => calendarBloc,
          act: (bloc) async {
            bloc.add(const CalendarEvent.viewTypeChanged(CalendarViewType.year));
            // Provide data for year view loading
            await Future.delayed(const Duration(milliseconds: 10));
            // Add data for each month to prevent "No element" error
            for (int i = 0; i < 12; i++) {
              taskLoadStreamController.add(testTaskLoad);
            }
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            createInitialState().copyWith(
              viewType: CalendarViewType.year,
              currentOperation: CalendarOperation.changingView,
            ),
            isA<CalendarState>()
                .having((s) => s.viewType, 'viewType', CalendarViewType.year)
                .having((s) => s.currentOperation, 'currentOperation',
                    CalendarOperation.none),
          ],
        );
      });

      group('viewToggled', () {
        blocTest<CalendarBloc, CalendarState>(
          'toggles from month to year view',
          build: () => calendarBloc,
          seed: () => createInitialState().copyWith(
            viewType: CalendarViewType.month,
          ),
          act: (bloc) async {
            bloc.add(const CalendarEvent.viewToggled());
            // Provide data for year view loading
            await Future.delayed(const Duration(milliseconds: 10));
            // Add data for each month to prevent "No element" error
            for (int i = 0; i < 12; i++) {
              taskLoadStreamController.add(testTaskLoad);
            }
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            createInitialState().copyWith(
              viewType: CalendarViewType.year,
              currentOperation: CalendarOperation.changingView,
            ),
            isA<CalendarState>()
                .having((s) => s.viewType, 'viewType', CalendarViewType.year)
                .having((s) => s.currentOperation, 'currentOperation',
                    CalendarOperation.none),
          ],
        );
      });

      group('priorityFilterChanged', () {
        blocTest<CalendarBloc, CalendarState>(
          'applies priority filter',
          build: () => calendarBloc,
          act: (bloc) => bloc.add(
            const CalendarEvent.priorityFilterChanged(Priority.urgentImportant),
          ),
          expect: () => [
            createInitialState().copyWith(
              priorityFilter: Priority.urgentImportant,
              hasActiveFilters: true,
              currentOperation: CalendarOperation.filtering,
            ),
            createInitialState().copyWith(
              priorityFilter: Priority.urgentImportant,
              hasActiveFilters: true,
              currentOperation: CalendarOperation.none,
            ),
          ],
        );
      });

      group('completionFilterChanged', () {
        blocTest<CalendarBloc, CalendarState>(
          'applies completion filter',
          build: () => calendarBloc,
          act: (bloc) => bloc.add(
            const CalendarEvent.completionFilterChanged(true),
          ),
          expect: () => [
            createInitialState().copyWith(
              completionFilter: true,
              hasActiveFilters: true,
              currentOperation: CalendarOperation.filtering,
            ),
            createInitialState().copyWith(
              completionFilter: true,
              hasActiveFilters: true,
              currentOperation: CalendarOperation.none,
            ),
          ],
        );
      });

      group('filtersCleared', () {
        blocTest<CalendarBloc, CalendarState>(
          'clears all filters',
          build: () => calendarBloc,
          seed: () => createInitialState().copyWith(
            priorityFilter: Priority.urgentImportant,
            completionFilter: true,
            hasActiveFilters: true,
          ),
          act: (bloc) => bloc.add(const CalendarEvent.filtersCleared()),
          expect: () => [
            createInitialState().copyWith(
              priorityFilter: null,
              completionFilter: null,
              hasActiveFilters: false,
              currentOperation: CalendarOperation.filtering,
            ),
            createInitialState().copyWith(
              priorityFilter: null,
              completionFilter: null,
              hasActiveFilters: false,
              currentOperation: CalendarOperation.none,
            ),
          ],
        );
      });

      group('weekendsToggled', () {
        blocTest<CalendarBloc, CalendarState>(
          'toggles weekend visibility',
          build: () => calendarBloc,
          act: (bloc) => bloc.add(const CalendarEvent.weekendsToggled(false)),
          expect: () => [
            createInitialState().copyWith(showWeekends: false),
          ],
        );
      });

      group('firstDayOfWeekChanged', () {
        blocTest<CalendarBloc, CalendarState>(
          'changes first day of week',
          build: () => calendarBloc,
          act: (bloc) =>
              bloc.add(const CalendarEvent.firstDayOfWeekChanged(7)), // Sunday
          expect: () => [
            createInitialState().copyWith(firstDayOfWeek: 7),
          ],
        );
      });

      group('taskPreviewToggled', () {
        blocTest<CalendarBloc, CalendarState>(
          'toggles task preview',
          build: () => calendarBloc,
          act: (bloc) =>
              bloc.add(const CalendarEvent.taskPreviewToggled(false)),
          expect: () => [
            createInitialState().copyWith(taskPreviewEnabled: false),
          ],
        );
      });

      group('keyboardNavigation', () {
        blocTest<CalendarBloc, CalendarState>(
          'navigates to next day with keyboard',
          build: () => calendarBloc,
          seed: () => createInitialState().copyWith(
            selectedDate: testDate,
          ),
          act: (bloc) => bloc.add(const CalendarEvent.keyboardNavigation(
            action: CalendarKeyAction.nextDay,
          )),
          expect: () => [
            createInitialState().copyWith(
              selectedDate: testDate.add(const Duration(days: 1)),
              currentOperation: CalendarOperation.none,
            ),
          ],
        );

        blocTest<CalendarBloc, CalendarState>(
          'navigates to today with keyboard',
          build: () => calendarBloc,
          act: (bloc) async {
            bloc.add(const CalendarEvent.keyboardNavigation(
              action: CalendarKeyAction.goToToday,
            ));
            // Wait a bit for the navigation to be set up
            await Future.delayed(const Duration(milliseconds: 10));
            taskLoadStreamController.add(testTaskLoad);
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            // Date selection for today
            isA<CalendarState>()
                .having((s) => s.selectedDate, 'selectedDate', isA<DateTime>()),
            // Month navigation
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.loading)
                .having((s) => s.displayMonthDate, 'displayMonthDate',
                    isA<DateTime>()),
            // Success without task data
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.loadDurationMs, 'loadDurationMs', isA<int>()),
            // Success with task data
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', testTaskLoad),
          ],
        );
      });

      group('refreshRequested', () {
        blocTest<CalendarBloc, CalendarState>(
          'refreshes calendar data',
          build: () => calendarBloc,
          act: (bloc) async {
            bloc.add(const CalendarEvent.refreshRequested());
            // Wait a bit for the refresh to be set up
            await Future.delayed(const Duration(milliseconds: 10));
            taskLoadStreamController.add(testTaskLoad);
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            createInitialState().copyWith(
              status: CalendarStatus.refreshing,
              currentOperation: CalendarOperation.refreshing,
            ),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.refreshing)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', testTaskLoad)
                .having((s) => s.lastUpdated, 'lastUpdated', isNotNull),
          ],
        );
      });

      group('retryRequested', () {
        blocTest<CalendarBloc, CalendarState>(
          'retries failed operation',
          build: () => calendarBloc,
          seed: () => createInitialState().copyWith(
            status: CalendarStatus.failure,
            errorMessage: 'Some error',
            canRetry: true,
          ),
          act: (bloc) async {
            bloc.add(const CalendarEvent.retryRequested());
            // Wait a bit for the retry to be set up
            await Future.delayed(const Duration(milliseconds: 10));
            taskLoadStreamController.add(testTaskLoad);
          },
          wait: const Duration(milliseconds: 100),
          expect: () => [
            createInitialState().copyWith(
              status: CalendarStatus.loading,
            ),
            createInitialState().copyWith(
              status: CalendarStatus.loading,
              currentOperation: CalendarOperation.loadingData,
            ),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', {}),
            isA<CalendarState>()
                .having((s) => s.status, 'status', CalendarStatus.success)
                .having((s) => s.taskLoadByDate, 'taskLoadByDate', testTaskLoad)
                .having((s) => s.loadDurationMs, 'loadDurationMs', isA<int>()),
          ],
        );
      });

      group('state extensions', () {
        test('isLoading returns correct value', () {
          expect(
            createInitialState()
                .copyWith(status: CalendarStatus.loading)
                .isLoading,
            isTrue,
          );
          expect(
            createInitialState()
                .copyWith(status: CalendarStatus.success)
                .isLoading,
            isFalse,
          );
        });

        test('hasError returns correct value', () {
          expect(
            createInitialState()
                .copyWith(status: CalendarStatus.failure)
                .hasError,
            isTrue,
          );
          expect(
            createInitialState()
                .copyWith(status: CalendarStatus.success)
                .hasError,
            isFalse,
          );
        });

        test('getTaskLoadForDate returns correct value', () {
          final state = createInitialState().copyWith(
            taskLoadByDate: testTaskLoad,
          );

          expect(state.getTaskLoadForDate(DateTime(2024, 1, 15)), equals(3));
          expect(state.getTaskLoadForDate(DateTime(2024, 1, 20)), equals(0));
        });

        test('isDateSelected returns correct value', () {
          final state = createInitialState().copyWith(
            selectedDate: testDate,
          );

          expect(state.isDateSelected(testDate), isTrue);
          expect(state.isDateSelected(DateTime(2024, 1, 16)), isFalse);
        });

        test('isToday returns correct value', () {
          final state = createInitialState();
          // Since we're using a fixed clock in tests, "today" is the current date (2025-09-04)
          // not the fixed test date (2024-01-15)
          final today = DateTime.now();
          final todayDateOnly = DateTime(today.year, today.month, today.day);
          expect(state.isToday(todayDateOnly), isTrue);
          expect(state.isToday(DateTime(2020, 1, 1)), isFalse);
        });

        test('monthCompletionRate calculates correctly', () {
          final state = createInitialState().copyWith(
            totalTasksInMonth: 10,
            completedTasksInMonth: 7,
          );

          expect(state.monthCompletionRate, equals(70.0));
        });

        test('getTaskLoadIntensity calculates correctly', () {
          final state = createInitialState().copyWith(
            taskLoadByDate: testTaskLoad,
            maxTaskLoad: 3,
          );

          expect(
              state.getTaskLoadIntensity(DateTime(2024, 1, 15)), equals(1.0));
          expect(state.getTaskLoadIntensity(DateTime(2024, 1, 16)),
              closeTo(0.33, 0.01));
        });
    });
  });
  }); // Close withClock
}
