import 'package:flutter_test/flutter_test.dart';
import 'package:drift/native.dart';
import 'package:mytodospace/domain/domain.dart';
import 'package:mytodospace/data/datasources/local_database.dart';
import 'package:mytodospace/data/repositories/task_repository_impl.dart';
import 'package:mytodospace/data/repositories/exceptions/repository_exceptions.dart';

void main() {
  group('TaskRepositoryImpl Tests', () {
    late LocalDatabase database;
    late TaskRepositoryImpl repository;

    setUp(() {
      database = LocalDatabase.forTesting(NativeDatabase.memory());
      repository = TaskRepositoryImpl(database);
    });

    tearDown(() async {
      await database.close();
    });

    group('Task CRUD Operations', () {
      test('should create task successfully', () async {
        final task = Task.create(
          title: 'Test Task',
          dueDate: DateTime.now().add(const Duration(days: 1)),
          priority: Priority.urgentImportant,
          notes: 'Test notes',
        );

        await repository.createTask(task);

        final retrievedTask = await repository.getTaskById(task.id);
        expect(retrievedTask, isNotNull);
        expect(retrievedTask!.title, 'Test Task');
        expect(retrievedTask.notes, 'Test notes');
        expect(retrievedTask.priority, Priority.urgentImportant);
      });

      test('should create task with subtasks', () async {
        final task = Task.create(
          title: 'Parent Task',
          dueDate: DateTime.now().add(const Duration(days: 1)),
        ).addSubtask('Subtask 1').addSubtask('Subtask 2');

        await repository.createTask(task);

        final retrievedTask = await repository.getTaskById(task.id);
        expect(retrievedTask!.subtasks.length, 2);
        expect(retrievedTask.subtasks[0].title, 'Subtask 1');
        expect(retrievedTask.subtasks[1].title, 'Subtask 2');
      });

      test('should update task successfully', () async {
        final task = Task.create(
          title: 'Original Title',
          dueDate: DateTime.now().add(const Duration(days: 1)),
        );

        await repository.createTask(task);

        final updatedTask = task.updateWith(
          title: 'Updated Title',
          notes: 'Updated notes',
        );

        await repository.updateTask(updatedTask);

        final retrievedTask = await repository.getTaskById(task.id);
        expect(retrievedTask!.title, 'Updated Title');
        expect(retrievedTask.notes, 'Updated notes');
      });

      test('should delete task successfully', () async {
        final task = Task.create(
          title: 'Task to Delete',
          dueDate: DateTime.now().add(const Duration(days: 1)),
        );

        await repository.createTask(task);
        await repository.deleteTask(task.id);

        final retrievedTask = await repository.getTaskById(task.id);
        expect(retrievedTask, isNull);
      });

      test('should throw exception when updating non-existent task', () async {
        final task = Task.create(
          title: 'Non-existent Task',
          dueDate: DateTime.now().add(const Duration(days: 1)),
        );

        expect(
          () => repository.updateTask(task),
          throwsA(isA<TaskNotFoundException>()),
        );
      });

      test('should throw exception when deleting non-existent task', () async {
        expect(
          () => repository.deleteTask('non_existent_id'),
          throwsA(isA<TaskNotFoundException>()),
        );
      });
    });

    group('Task Queries', () {
      test('should watch tasks by date', () async {
        final testDate = DateTime(2025, 1, 15);
        
        final task1 = Task.create(
          title: 'Task 1',
          dueDate: testDate,
        );

        final task2 = Task.create(
          title: 'Task 2',
          dueDate: testDate.add(const Duration(days: 1)),
        );

        await repository.createTask(task1);
        await repository.createTask(task2);

        final tasksStream = repository.watchTasksByDate(testDate);
        final tasks = await tasksStream.first;

        expect(tasks.length, 1);
        expect(tasks.first.title, 'Task 1');
      });

      test('should calculate task load for month', () async {
        final testDate = DateTime(2025, 1, 15);
        
        final urgentTask = Task.create(
          title: 'Urgent Task',
          dueDate: testDate,
          priority: Priority.urgentImportant,
        );

        final importantTask = Task.create(
          title: 'Important Task',
          dueDate: testDate,
          priority: Priority.importantNotUrgent,
        );

        await repository.createTask(urgentTask);
        await repository.createTask(importantTask);

        final loadStream = repository.watchTaskLoadForMonth(
          year: 2025,
          month: 1,
        );
        final loadMap = await loadStream.first;

        final dateKey = DateTime(2025, 1, 15);
        expect(loadMap[dateKey], 7); // 4 + 3 = 7
      });

      test('should search tasks', () async {
        final task1 = Task.create(
          title: 'Flutter Development',
          dueDate: DateTime.now().add(const Duration(days: 1)),
          notes: 'Mobile app development',
        );

        final task2 = Task.create(
          title: 'Backend API',
          dueDate: DateTime.now().add(const Duration(days: 1)),
          notes: 'Server development',
        );

        await repository.createTask(task1);
        await repository.createTask(task2);

        // Wait for FTS indexing
        await Future.delayed(const Duration(milliseconds: 100));

        final searchResults = await repository.searchTasks('Flutter');
        expect(searchResults.length, 1);
        expect(searchResults.first.title, 'Flutter Development');
      });

      test('should get overdue tasks', () async {
        final overdueTask = Task.create(
          title: 'Overdue Task',
          dueDate: DateTime.now().subtract(const Duration(days: 1)),
        );

        final futureTask = Task.create(
          title: 'Future Task',
          dueDate: DateTime.now().add(const Duration(days: 1)),
        );

        await repository.createTask(overdueTask);
        await repository.createTask(futureTask);

        final overdueTasks = await repository.getOverdueTasks();
        expect(overdueTasks.length, 1);
        expect(overdueTasks.first.title, 'Overdue Task');
      });

      test('should get tasks by priority', () async {
        final urgentTask = Task.create(
          title: 'Urgent Task',
          dueDate: DateTime.now().add(const Duration(days: 1)),
          priority: Priority.urgentImportant,
        );

        final normalTask = Task.create(
          title: 'Normal Task',
          dueDate: DateTime.now().add(const Duration(days: 1)),
          priority: Priority.notUrgentNotImportant,
        );

        await repository.createTask(urgentTask);
        await repository.createTask(normalTask);

        final urgentTasks = await repository.getTasksByPriority(Priority.urgentImportant);
        expect(urgentTasks.length, 1);
        expect(urgentTasks.first.title, 'Urgent Task');
      });
    });

    group('Batch Operations', () {
      test('should create multiple tasks', () async {
        final tasks = [
          Task.create(
            title: 'Task 1',
            dueDate: DateTime.now().add(const Duration(days: 1)),
          ),
          Task.create(
            title: 'Task 2',
            dueDate: DateTime.now().add(const Duration(days: 2)),
          ),
          Task.create(
            title: 'Task 3',
            dueDate: DateTime.now().add(const Duration(days: 3)),
          ),
        ];

        await repository.createTasks(tasks);

        for (final task in tasks) {
          final retrievedTask = await repository.getTaskById(task.id);
          expect(retrievedTask, isNotNull);
          expect(retrievedTask!.title, task.title);
        }
      });

      test('should delete multiple tasks', () async {
        final tasks = [
          Task.create(
            title: 'Task 1',
            dueDate: DateTime.now().add(const Duration(days: 1)),
          ),
          Task.create(
            title: 'Task 2',
            dueDate: DateTime.now().add(const Duration(days: 2)),
          ),
        ];

        await repository.createTasks(tasks);

        final taskIds = tasks.map((t) => t.id).toList();
        await repository.deleteTasks(taskIds);

        for (final taskId in taskIds) {
          final retrievedTask = await repository.getTaskById(taskId);
          expect(retrievedTask, isNull);
        }
      });
    });

    group('Caching', () {
      test('should cache frequently accessed tasks', () async {
        final task = Task.create(
          title: 'Cached Task',
          dueDate: DateTime.now().add(const Duration(days: 1)),
        );

        await repository.createTask(task);

        // First access - should load from database
        final firstAccess = await repository.getTaskById(task.id);
        expect(firstAccess, isNotNull);

        // Second access - should load from cache
        final secondAccess = await repository.getTaskById(task.id);
        expect(secondAccess, isNotNull);
        expect(secondAccess!.title, task.title);

        // Check cache stats
        final cacheStats = repository.getCacheStats();
        expect(cacheStats['size'], greaterThan(0));
      });

      test('should clear cache', () async {
        final task = Task.create(
          title: 'Task for Cache Test',
          dueDate: DateTime.now().add(const Duration(days: 1)),
        );

        await repository.createTask(task);
        await repository.getTaskById(task.id); // Load into cache

        repository.clearCache();

        final cacheStats = repository.getCacheStats();
        expect(cacheStats['size'], 0);
      });
    });

    group('Error Handling', () {
      test('should handle validation errors', () async {
        expect(
          () => Task.create(title: '', dueDate: DateTime.now()),
          throwsA(isA<TaskValidationException>()),
        );
      });

      test('should handle database errors gracefully', () async {
        // Close database to simulate error
        await database.close();

        final task = Task.create(
          title: 'Test Task',
          dueDate: DateTime.now().add(const Duration(days: 1)),
        );

        // The repository should handle the error gracefully
        try {
          await repository.createTask(task);
          fail('Expected an exception to be thrown');
        } catch (e) {
          expect(e, isA<Exception>());
        }
      });
    });
  });
}