import 'package:flutter_test/flutter_test.dart';
import 'package:drift/native.dart';
import 'package:mytodospace/domain/domain.dart';
import 'package:mytodospace/data/datasources/local_database.dart';
import 'package:mytodospace/data/repositories/summary_repository_impl.dart';
import 'package:mytodospace/data/repositories/task_repository_impl.dart';
import 'package:mytodospace/core/errors/exceptions.dart';

void main() {
  group('SummaryRepositoryImpl Tests', () {
    late LocalDatabase database;
    late SummaryRepositoryImpl summaryRepository;
    late TaskRepositoryImpl taskRepository;

    setUp(() {
      database = LocalDatabase.forTesting(NativeDatabase.memory());
      summaryRepository = SummaryRepositoryImpl(database);
      taskRepository = TaskRepositoryImpl(database);
    });

    tearDown(() async {
      await database.close();
    });

    group('Monthly Summary', () {
      test('should generate monthly summary correctly', () async {
        final testDate = DateTime(2025, 1, 15);

        // Create test tasks with unique IDs
        final tasks = [
          Task(
            id: 'test_task_monthly_1_${DateTime.now().millisecondsSinceEpoch}',
            title: 'Completed Task 1',
            creationDate: testDate.subtract(const Duration(days: 1)),
            dueDate: testDate,
            priority: Priority.urgentImportant,
            isCompleted: true,
            completionDate: testDate,
            notes: '',
            subtasks: [],
          ),
          Task(
            id: 'test_task_monthly_2_${DateTime.now().millisecondsSinceEpoch + 1}',
            title: 'Completed Task 2',
            creationDate: testDate.subtract(const Duration(days: 1)),
            dueDate: testDate,
            priority: Priority.importantNotUrgent,
            isCompleted: true,
            completionDate: testDate,
            notes: '',
            subtasks: [],
          ),
          Task(
            id: 'test_task_monthly_3_${DateTime.now().millisecondsSinceEpoch + 2}',
            title: 'Pending Task',
            creationDate: testDate.subtract(const Duration(days: 1)),
            dueDate: testDate,
            priority: Priority.urgentNotImportant,
            isCompleted: false,
            completionDate: null,
            notes: '',
            subtasks: [],
          ),
        ];

        for (final task in tasks) {
          await taskRepository.createTask(task);
        }

        final summary = await summaryRepository.getMonthlySummary(
          year: 2025,
          month: 1,
        );

        expect(summary.period, '2025年1月');
        expect(summary.totalTasksCreated, 3);
        expect(summary.totalTasksCompleted, 2);
        expect(summary.completionRate, closeTo(66.67, 0.1));

        expect(summary.quadrantDistribution[Priority.urgentImportant], 1);
        expect(summary.quadrantDistribution[Priority.importantNotUrgent], 1);
        expect(summary.quadrantDistribution[Priority.urgentNotImportant], 1);
        expect(summary.quadrantDistribution[Priority.notUrgentNotImportant], 0);

        expect(summary.highlights.length, 2);
      });

      test('should handle empty month correctly', () async {
        final summary = await summaryRepository.getMonthlySummary(
          year: 2025,
          month: 12,
        );

        expect(summary.period, '2025年12月');
        expect(summary.totalTasksCreated, 0);
        expect(summary.totalTasksCompleted, 0);
        expect(summary.completionRate, 0.0);
        expect(summary.highlights, isEmpty);
      });
    });

    group('Yearly Summary', () {
      test('should generate yearly summary correctly', () async {
        final testDate = DateTime(2025, 6, 15);

        // Create test tasks across different months with unique IDs
        final baseTime = DateTime.now().millisecondsSinceEpoch;
        final tasks = [
          Task(
            id: 'test_task_yearly_1_$baseTime',
            title: 'Q1 Task',
            creationDate: DateTime.now().subtract(const Duration(days: 60)),
            dueDate: DateTime.now().subtract(const Duration(days: 59)),
            priority: Priority.urgentImportant,
            isCompleted: true,
            completionDate: DateTime.now().subtract(const Duration(days: 59)),
            notes: '',
            subtasks: [],
          ),
          Task(
            id: 'test_task_yearly_2_${baseTime + 1}',
            title: 'Q2 Task',
            creationDate: testDate.subtract(const Duration(days: 1)),
            dueDate: testDate,
            priority: Priority.importantNotUrgent,
            isCompleted: true,
            completionDate: testDate,
            notes: '',
            subtasks: [],
          ),
          Task(
            id: 'test_task_yearly_3_${baseTime + 2}',
            title: 'Q3 Task',
            creationDate: DateTime.now().subtract(const Duration(days: 10)),
            dueDate: DateTime.now().subtract(const Duration(days: 9)),
            priority: Priority.urgentNotImportant,
            isCompleted: false,
            completionDate: null,
            notes: '',
            subtasks: [],
          ),
        ];

        for (final task in tasks) {
          await taskRepository.createTask(task);
        }

        final summary = await summaryRepository.getYearlySummary(year: 2025);

        expect(summary.period, '2025年');
        expect(summary.totalTasksCreated, 3);
        expect(summary.totalTasksCompleted, 2);
        expect(summary.completionRate, closeTo(66.67, 0.1));
        expect(summary.highlights.length, 2);
      });
    });

    group('Detailed Statistics', () {
      test('should get detailed monthly statistics', () async {
        final testDate = DateTime(2025, 1, 15);

        final task = Task.create(
          title: 'Test Task',
          dueDate: testDate,
          priority: Priority.urgentImportant,
        );

        await taskRepository.createTask(task);

        final stats = await summaryRepository.getDetailedMonthlyStats(
          year: 2025,
          month: 1,
        );

        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('basic_stats'), true);
        expect(stats.containsKey('priority_distribution'), true);
        expect(stats.containsKey('daily_task_load'), true);
        expect(stats['period'], '2025年1月');
      });

      test('should get productivity trends', () async {
        final startDate = DateTime(2025, 1, 1);
        final endDate = DateTime(2025, 3, 31);

        // Create tasks in different months with unique IDs
        final baseTime = DateTime.now().millisecondsSinceEpoch;
        final tasks = [
          Task(
            id: 'test_task_trends_1_$baseTime',
            title: 'January Task',
            creationDate: DateTime(2025, 1, 14),
            dueDate: DateTime(2025, 1, 15),
            priority: Priority.urgentImportant,
            isCompleted: true,
            completionDate: DateTime(2025, 1, 15),
            notes: '',
            subtasks: [],
          ),
          Task(
            id: 'test_task_trends_2_${baseTime + 1}',
            title: 'February Task',
            creationDate: DateTime(2025, 2, 14),
            dueDate: DateTime(2025, 2, 15),
            priority: Priority.urgentImportant,
            isCompleted: true,
            completionDate: DateTime(2025, 2, 15),
            notes: '',
            subtasks: [],
          ),
          Task(
            id: 'test_task_trends_3_${baseTime + 2}',
            title: 'March Task',
            creationDate: DateTime(2025, 3, 14),
            dueDate: DateTime(2025, 3, 15),
            priority: Priority.urgentImportant,
            isCompleted: false,
            completionDate: null,
            notes: '',
            subtasks: [],
          ),
        ];

        for (final task in tasks) {
          await taskRepository.createTask(task);
        }

        final trends = await summaryRepository.getProductivityTrends(
          startDate: startDate,
          endDate: endDate,
        );

        expect(trends.length, 3); // 3 months
        expect(trends[0]['period'], '2025-01');
        expect(trends[1]['period'], '2025-02');
        expect(trends[2]['period'], '2025-03');

        for (final trend in trends) {
          expect(trend.containsKey('tasks_created'), true);
          expect(trend.containsKey('tasks_completed'), true);
          expect(trend.containsKey('completion_rate'), true);
          expect(trend.containsKey('quadrant_distribution'), true);
        }
      });

      test('should get completion patterns by day of week', () async {
        // Create completed tasks on different days with unique IDs
        final now = DateTime.now();
        final baseTime = DateTime.now().millisecondsSinceEpoch;
        final tasks = [
          Task(
            id: 'test_task_patterns_1_$baseTime',
            title: 'Monday Task',
            creationDate: now.subtract(const Duration(days: 1)),
            dueDate: now,
            priority: Priority.urgentImportant,
            isCompleted: true,
            completionDate: now,
            notes: '',
            subtasks: [],
          ),
          Task(
            id: 'test_task_patterns_2_${baseTime + 1}',
            title: 'Tuesday Task',
            creationDate: now.subtract(const Duration(days: 1)),
            dueDate: now,
            priority: Priority.urgentImportant,
            isCompleted: true,
            completionDate: now,
            notes: '',
            subtasks: [],
          ),
        ];

        for (final task in tasks) {
          await taskRepository.createTask(task);
        }

        final patterns =
            await summaryRepository.getCompletionPatternsByDayOfWeek();

        expect(patterns, isA<Map<String, double>>());
        expect(patterns.containsKey('Monday'), true);
        expect(patterns.containsKey('Tuesday'), true);
        expect(patterns.containsKey('Wednesday'), true);
        expect(patterns.containsKey('Thursday'), true);
        expect(patterns.containsKey('Friday'), true);
        expect(patterns.containsKey('Saturday'), true);
        expect(patterns.containsKey('Sunday'), true);

        // All percentages should sum to 100 (or close to it)
        final totalPercentage =
            patterns.values.fold(0.0, (sum, value) => sum + value);
        expect(totalPercentage, closeTo(100.0, 0.1));
      });

      test('should get performance metrics', () async {
        final now = DateTime.now();

        // Create current month tasks with unique IDs
        final baseTime = DateTime.now().millisecondsSinceEpoch;
        final currentMonthTask = Task(
          id: 'test_task_metrics_1_$baseTime',
          title: 'Current Month Task',
          creationDate: now.subtract(const Duration(days: 1)),
          dueDate: now,
          priority: Priority.urgentImportant,
          isCompleted: true,
          completionDate: now,
          notes: '',
          subtasks: [],
        );

        // Create overdue task
        final overdueTask = Task(
          id: 'test_task_metrics_2_${baseTime + 1}',
          title: 'Overdue Task',
          creationDate: now.subtract(const Duration(days: 2)),
          dueDate: now.subtract(const Duration(days: 1)),
          priority: Priority.urgentImportant,
          isCompleted: false,
          completionDate: null,
          notes: '',
          subtasks: [],
        );

        await taskRepository.createTask(currentMonthTask);
        await taskRepository.createTask(overdueTask);

        final metrics = await summaryRepository.getPerformanceMetrics();

        expect(metrics, isA<Map<String, dynamic>>());
        expect(metrics.containsKey('current_month'), true);
        expect(metrics.containsKey('trends'), true);
        expect(metrics.containsKey('alerts'), true);
        expect(metrics.containsKey('quadrant_distribution'), true);

        expect(
            metrics['current_month']['tasks_created'], greaterThanOrEqualTo(1));
        expect(
            metrics['alerts']['overdue_tasks_count'], greaterThanOrEqualTo(1));
      });
    });

    group('Error Handling', () {
      test('should handle database errors gracefully', () async {
        // Close database to simulate error
        await database.close();

        // The repository should handle the error gracefully and return empty results
        final summary = await summaryRepository.getMonthlySummary(year: 2025, month: 1);

        expect(summary.totalTasksCreated, 0);
        expect(summary.totalTasksCompleted, 0);
        expect(summary.completionRate, 0.0);
      });

      test('should handle invalid date ranges', () async {
        // Test with future dates (should return empty results, not error)
        final summary = await summaryRepository.getMonthlySummary(
          year: 2030,
          month: 12,
        );

        expect(summary.totalTasksCreated, 0);
        expect(summary.totalTasksCompleted, 0);
        expect(summary.completionRate, 0.0);
      });
    });

    group('Highlights Calculation', () {
      test('should prioritize important tasks in highlights', () async {
        final now = DateTime.now();

        // Create tasks with different priorities and complexities with unique IDs
        final baseTime = DateTime.now().millisecondsSinceEpoch;
        final tasks = [
          Task(
            id: 'test_task_highlights_1_$baseTime',
            title: 'Simple Urgent Task',
            creationDate: now.subtract(const Duration(days: 1)),
            dueDate: now,
            priority: Priority.urgentImportant,
            isCompleted: true,
            completionDate: now,
            notes: '',
            subtasks: [],
          ),
          Task(
            id: 'test_task_highlights_2_${baseTime + 1}',
            title: 'Complex Important Task',
            creationDate: now.subtract(const Duration(days: 1)),
            dueDate: now,
            priority: Priority.importantNotUrgent,
            isCompleted: true,
            completionDate: now,
            notes: '',
            subtasks: [
              SubTask(id: 'sub1_${baseTime + 1}', parentTaskId: 'test_task_highlights_2_${baseTime + 1}', title: 'Subtask 1', isCompleted: true),
              SubTask(id: 'sub2_${baseTime + 1}', parentTaskId: 'test_task_highlights_2_${baseTime + 1}', title: 'Subtask 2', isCompleted: true),
              SubTask(id: 'sub3_${baseTime + 1}', parentTaskId: 'test_task_highlights_2_${baseTime + 1}', title: 'Subtask 3', isCompleted: true),
            ],
          ),
          Task(
            id: 'test_task_highlights_3_${baseTime + 2}',
            title: 'Simple Low Priority Task',
            creationDate: now.subtract(const Duration(days: 1)),
            dueDate: now,
            priority: Priority.notUrgentNotImportant,
            isCompleted: true,
            completionDate: now,
            notes: '',
            subtasks: [],
          ),
        ];

        for (final task in tasks) {
          await taskRepository.createTask(task);
        }

        final summary = await summaryRepository.getMonthlySummary(
          year: now.year,
          month: now.month,
        );

        expect(summary.highlights.length, 3);

        // The complex important task should be ranked higher due to subtasks
        // even though it's not urgent
        final firstHighlight = summary.highlights.first;
        expect(firstHighlight.priority, Priority.importantNotUrgent);
        expect(firstHighlight.subtasks.length, 3);
      });

      test('should limit highlights to specified count', () async {
        final now = DateTime.now();

        // Create more completed tasks than the highlight limit
        final tasks = List.generate(
            10,
            (index) => Task.create(
                  title: 'Task ${index + 1}',
                  dueDate: now,
                  priority: Priority.urgentImportant,
                ).markAsCompleted());

        for (final task in tasks) {
          await taskRepository.createTask(task);
        }

        final summary = await summaryRepository.getMonthlySummary(
          year: now.year,
          month: now.month,
        );

        // Should be limited to 5 highlights (as defined in HighlightsCalculationService)
        expect(summary.highlights.length, lessThanOrEqualTo(5));
      });
    });
  });
}
