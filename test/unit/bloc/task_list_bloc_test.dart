import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:clock/clock.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_event.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_state.dart';

import 'package:mytodospace/domain/models/task_model.dart';

import '../../test_config.dart';

void main() {
  group('TaskListBloc', () {
    late TaskListBloc taskListBloc;
    late MockTaskRepository mockTaskRepository;
    late StreamController<List<Task>> tasksStreamController;

    // Mock a TaskRepository from test_config.dart
    // This is a bit of a hack to get around the fact that
    // we can't import test_config.dart in a test file.

    setUpAll(() {
      // Register fallback values for Mocktail
      // setupTestFallbacks();
    });

    setUp(() {
      mockTaskRepository = MockTaskRepository();
      tasksStreamController = StreamController<List<Task>>();
      // Register fallback values for Mocktail
      setupTestFallbacks();
      taskListBloc = TaskListBloc(mockTaskRepository, Clock.fixed(DateTime(2024, 1, 15)));
    });

    tearDown(() {
      tasksStreamController.close();
      taskListBloc.close();
    });

    blocTest<TaskListBloc, TaskListState>(
      'emits [loading, success] when subscriptionRequested event is added',
      build: () => taskListBloc,
      act: (bloc) async {
        bloc.add(TaskListEvent.subscriptionRequested(DateTime(2025, 9, 15)));
        // Wait a bit for the subscription to be set up
        await Future.delayed(const Duration(milliseconds: 10));
        // Simulate stream emitting data
        tasksStreamController.add([TestDataFactory.createTestTask()]);
      },
      expect: () => [
        isA<TaskListState>()
            .having((state) => state.status, 'status', TaskListStatus.loading),
        isA<TaskListState>()
            .having((state) => state.status, 'status', TaskListStatus.success),
      ],
      setUp: () {
        when(() => mockTaskRepository.watchTasksByDate(any()))
            .thenAnswer((_) => tasksStreamController.stream);
      },
    );

    blocTest<TaskListBloc, TaskListState>(
      'emits [creating, success] when taskCreated event is added',
      build: () => taskListBloc,
      act: (bloc) =>
          bloc.add(TaskListEvent.taskCreated(TestDataFactory.createTestTask())),
      expect: () => [
        isA<TaskListState>().having((state) => state.currentOperation,
            'operation', TaskListOperation.creatingTask),
        isA<TaskListState>().having((state) => state.currentOperation,
            'operation', TaskListOperation.none),
      ],
      setUp: () {
        when(() => mockTaskRepository.createTask(any())).thenAnswer(
          (_) => Future.value(null),
        );
      },
    );

    blocTest<TaskListBloc, TaskListState>(
      'emits [updating, success] when taskUpdated event is added',
      build: () => taskListBloc,
      act: (bloc) =>
          bloc.add(TaskListEvent.taskUpdated(TestDataFactory.createTestTask())),
      expect: () => [
        isA<TaskListState>()
            .having((state) => state.status, 'status', TaskListStatus.updating),
        isA<TaskListState>()
            .having((state) => state.status, 'status', TaskListStatus.success),
      ],
      setUp: () {
        when(() => mockTaskRepository.updateTask(any())).thenAnswer(
          (invocation) async => invocation.positionalArguments.first as Task,
        );
      },
    );

    blocTest<TaskListBloc, TaskListState>(
      'emits [deleting, success] when taskDeleted event is added',
      build: () => taskListBloc,
      act: (bloc) =>
          bloc.add(const TaskListEvent.taskDeleted(taskId: 'test-task-id')),
      expect: () => [
        isA<TaskListState>()
            .having((state) => state.status, 'status', TaskListStatus.deleting),
        isA<TaskListState>()
            .having((state) => state.status, 'status', TaskListStatus.success),
      ],
      setUp: () {
        when(() => mockTaskRepository.deleteTask(any()))
            .thenAnswer((_) async => true);
      },
    );

    blocTest<TaskListBloc, TaskListState>(
      'emits [toggling, failure] when completionToggled event is added with invalid task',
      build: () => taskListBloc,
      act: (bloc) => bloc.add(const TaskListEvent.completionToggled(
          taskId: 'test-task-id', isCompleted: true)),
      expect: () => [
        isA<TaskListState>().having((state) => state.currentOperation,
            'operation', TaskListOperation.togglingCompletion),
        isA<TaskListState>()
            .having((state) => state.status, 'status', TaskListStatus.failure)
            .having((state) => state.currentOperation, 'operation', TaskListOperation.none),
      ],
      setUp: () {
        when(() => mockTaskRepository.updateTask(any())).thenAnswer(
          (invocation) async => invocation.positionalArguments.first as Task,
        );
      },
    );

    blocTest<TaskListBloc, TaskListState>(
      'emits [loading, failure] when repository throws error',
      build: () => taskListBloc,
      act: (bloc) =>
          bloc.add(TaskListEvent.subscriptionRequested(DateTime(2025, 9, 15))),
      expect: () => [
        isA<TaskListState>()
            .having((state) => state.status, 'status', TaskListStatus.loading),
        isA<TaskListState>()
            .having((state) => state.status, 'status', TaskListStatus.failure),
      ],
      setUp: () {
        when(() => mockTaskRepository.watchTasksByDate(any()))
            .thenAnswer((_) => Stream.error(Exception('Database error')));
      },
    );

    blocTest<TaskListBloc, TaskListState>(
      'emits [filtered] when filterByPriority event is added',
      build: () => taskListBloc,
      act: (bloc) => bloc
          .add(const TaskListEvent.filterByPriority(Priority.urgentImportant)),
      expect: () => [
        isA<TaskListState>().having((state) => state.priorityFilter,
            'priorityFilter', Priority.urgentImportant),
      ],
    );

    blocTest<TaskListBloc, TaskListState>(
      'emits [filtered] when filterByCompletion event is added',
      build: () => taskListBloc,
      act: (bloc) => bloc.add(const TaskListEvent.filterByCompletion(true)),
      expect: () => [
        isA<TaskListState>().having(
            (state) => state.completionFilter, 'completionFilter', true),
      ],
    );

    test('initial state should be correct', () {
      expect(taskListBloc.state.status, TaskListStatus.initial);
      expect(taskListBloc.state.tasks, isEmpty);
      expect(taskListBloc.state.currentOperation, TaskListOperation.none);
    });
  });
}
