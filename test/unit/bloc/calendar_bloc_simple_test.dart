import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:clock/clock.dart';

import 'package:mytodospace/features/calendar/bloc/calendar_bloc.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_event.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_state.dart';
import 'package:mytodospace/domain/repositories/task_repository.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import '../../test_config.dart';

class MockTaskRepository extends Mock implements TaskRepository {}

void main() {
  group('CalendarBloc Simple Tests', () {
    late CalendarBloc calendarBloc;
    late MockTaskRepository mockTaskRepository;

    setUp(() {
      mockTaskRepository = MockTaskRepository();
      
      // 设置基本的mock行为
      when(() => mockTaskRepository.getTasksForDate(any())).thenAnswer(
        (_) async => <Task>[],
      );
      when(() => mockTaskRepository.watchTaskLoadForMonth(
        year: any(named: 'year'),
        month: any(named: 'month'),
      )).thenAnswer((_) => Stream.value(<DateTime, int>{}));
      
      calendarBloc = CalendarBloc(mockTaskRepository, Clock.fixed(DateTime(2024, 1, 15)));
    });

    tearDown(() {
      calendarBloc.close();
    });

    test('should create CalendarBloc', () {
      expect(calendarBloc, isNotNull);
      expect(calendarBloc.state, isA<CalendarState>());
    });

    test('should have initial state', () {
      final state = calendarBloc.state;
      expect(state.status, equals(CalendarStatus.initial));
      expect(state.viewType, equals(CalendarViewType.month));
      expect(state.selectedDate, isNotNull);
    });

    test('should handle date selection', () async {
      final newDate = DateTime(2025, 9, 15);
      
      calendarBloc.add(CalendarEvent.dateSelected(newDate));
      
      // 等待事件处理
      await Future.delayed(const Duration(milliseconds: 100));
      
      expect(calendarBloc.state.selectedDate, equals(newDate));
    });

    test('should handle view type change', () async {
      calendarBloc.add(CalendarEvent.viewTypeChanged(CalendarViewType.year));
      
      // 等待事件处理
      await Future.delayed(const Duration(milliseconds: 100));
      
      expect(calendarBloc.state.viewType, equals(CalendarViewType.year));
    });

    test('should handle month change', () async {
      final newMonth = DateTime(2025, 10, 1);
      
      calendarBloc.add(CalendarEvent.monthChanged(newMonth));
      
      // 等待事件处理
      await Future.delayed(const Duration(milliseconds: 100));
      
      expect(calendarBloc.state.displayMonthDate.month, equals(10));
    });
  });
}
