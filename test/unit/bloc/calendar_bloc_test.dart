import 'package:flutter_test/flutter_test.dart';
import 'package:bloc_test/bloc_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:clock/clock.dart';

import 'package:mytodospace/features/calendar/bloc/calendar_bloc.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_event.dart';
import 'package:mytodospace/features/calendar/bloc/calendar_state.dart';
import 'package:mytodospace/domain/repositories/task_repository.dart';
import 'package:mytodospace/domain/repositories/summary_repository.dart';
import 'package:mytodospace/domain/models/summary_report_model.dart';
import '../../test_config.dart';
import '../../test_utils.dart';

class MockTaskRepository extends Mock implements TaskRepository {}

class MockSummaryRepository extends Mock implements SummaryRepository {}

void main() {
  group('CalendarBloc', () {
    late CalendarBloc calendarBloc;
    late MockTaskRepository mockTaskRepository;
    late MockSummaryRepository mockSummaryRepository;

    final fixedTime = DateTime(2024, 1, 15, 10, 0, 0);

    setUp(() async {
      mockTaskRepository = MockTaskRepository();
      mockSummaryRepository = MockSummaryRepository();

      // 设置默认行为
      when(() => mockTaskRepository.getTasksForDate(any())).thenAnswer(
        (_) async => TestDataFactory.createTestTaskList(count: 3),
      );
      when(() => mockTaskRepository.watchTaskLoadForMonth(
            year: any(named: 'year'),
            month: any(named: 'month'),
          )).thenAnswer((_) => Stream.value(<DateTime, int>{}));
      when(() => mockSummaryRepository.getMonthlySummary(
            year: any(named: 'year'),
            month: any(named: 'month'),
          )).thenAnswer((_) async => SummaryReport.empty());

      calendarBloc = CalendarBloc(mockTaskRepository, Clock.fixed(fixedTime));
    });

    tearDown(() {
      calendarBloc.close();
    });

    group('CalendarEvent.started', () {
      blocTest<CalendarBloc, CalendarState>(
        'emits [loading, success] when started event is added',
        build: () => calendarBloc,
        act: (bloc) => bloc.add(const CalendarEvent.started()),
        expect: () => [
          isA<CalendarState>().having(
            (state) => state.status,
            'status',
            CalendarStatus.loading,
          ),
          isA<CalendarState>().having(
            (state) => state.status,
            'status',
            CalendarStatus.success,
          ),
        ],
        verify: (bloc) {
          expect(bloc.state.selectedDate, isNotNull);
          expect(bloc.state.displayMonthDate, isNotNull);
          expect(bloc.state.viewType, equals(CalendarViewType.month));
        },
      );

      test('should not emit after event handler completes', () async {
        // 测试BLoC不会在事件处理器完成后继续emit
        final states = <CalendarState>[];
        calendarBloc.stream.listen(states.add);

        calendarBloc.add(const CalendarEvent.started());

        // 等待事件处理完成
        await Future.delayed(const Duration(milliseconds: 200));

        // 验证状态数量合理（不应该无限emit）
        expect(states.length, lessThanOrEqualTo(3));

        // 验证最终状态
        expect(states.last.status, equals(CalendarStatus.success));
      });
    });

    group('CalendarEvent.monthChanged', () {
      blocTest<CalendarBloc, CalendarState>(
        'emits updated state with new month when monthChanged event is added',
        build: () => calendarBloc,
        seed: () => CalendarState.initial(),
        act: (bloc) => bloc.add(
          CalendarEvent.monthChanged(DateTime(2025, 10, 1)),
        ),
        expect: () => [
          isA<CalendarState>().having(
            (state) => state.displayMonthDate.month,
            'displayMonthDate.month',
            10,
          ),
        ],
        verify: (bloc) {
          expect(bloc.state.displayMonthDate.month, equals(10));
          expect(bloc.state.displayMonthDate.year, equals(2025));
        },
      );

      test('should handle month navigation correctly', () async {
        final states = <CalendarState>[];
        calendarBloc.stream.listen(states.add);

        // 导航到下个月
        calendarBloc.add(CalendarEvent.monthChanged(DateTime(2025, 10, 1)));
        await TestUtils.waitForAsync();

        expect(calendarBloc.state.displayMonthDate.month, equals(10));

        // 导航到上个月
        calendarBloc.add(CalendarEvent.monthChanged(DateTime(2025, 8, 1)));
        await TestUtils.waitForAsync();

        expect(calendarBloc.state.displayMonthDate.month, equals(8));
      });
    });

    group('CalendarEvent.viewTypeChanged', () {
      blocTest<CalendarBloc, CalendarState>(
        'emits updated state with new view type when viewTypeChanged event is added',
        build: () => calendarBloc,
        seed: () => CalendarState.initial(),
        act: (bloc) => bloc.add(
          const CalendarEvent.viewTypeChanged(CalendarViewType.year),
        ),
        expect: () => [
          isA<CalendarState>().having(
            (state) => state.viewType,
            'viewType',
            CalendarViewType.year,
          ),
        ],
        verify: (bloc) {
          expect(bloc.state.viewType, equals(CalendarViewType.year));
        },
      );

      test('should handle all view type changes', () async {
        const viewTypes = CalendarViewType.values;

        for (final viewType in viewTypes) {
          calendarBloc.add(CalendarEvent.viewTypeChanged(viewType));
          await TestUtils.waitForAsync();

          expect(calendarBloc.state.viewType, equals(viewType));
        }
      });
    });

    group('CalendarEvent.dateSelected', () {
      blocTest<CalendarBloc, CalendarState>(
        'emits updated state with selected date when dateSelected event is added',
        build: () => calendarBloc,
        seed: () => CalendarState.initial(),
        act: (bloc) => bloc.add(
          CalendarEvent.dateSelected(DateTime(2025, 9, 15)),
        ),
        expect: () => [
          isA<CalendarState>().having(
            (state) => state.selectedDate,
            'selectedDate',
            DateTime(2025, 9, 15),
          ),
        ],
        verify: (bloc) {
          expect(bloc.state.selectedDate.day, equals(15));
          expect(bloc.state.selectedDate.month, equals(9));
        },
      );
    });

    group('CalendarEvent.taskDropped', () {
      blocTest<CalendarBloc, CalendarState>(
        'emits updated state when taskDropped event is added',
        build: () => calendarBloc,
        seed: () => CalendarState.initial(),
        act: (bloc) => bloc.add(
          CalendarEvent.taskDropped(
            taskId: TestDataFactory.createTestTask().id,
            newDate: DateTime(2025, 9, 20),
          ),
        ),
        expect: () => [
          isA<CalendarState>(),
        ],
        verify: (bloc) {
          // 验证任务被正确处理
          expect(bloc.state.status, isNot(equals(CalendarStatus.failure)));
        },
      );
    });

    group('Error handling', () {
      test('should handle repository errors gracefully', () async {
        // 模拟仓库抛出异常
        when(() => mockTaskRepository.getTasksForDate(any())).thenThrow(
          Exception('Database error'),
        );

        final states = <CalendarState>[];
        calendarBloc.stream.listen(states.add);

        calendarBloc.add(const CalendarEvent.started());
        await TestUtils.waitForAsync();

        // 验证错误被正确处理
        expect(states.last.status, equals(CalendarStatus.failure));
        expect(states.last.errorMessage, isNotNull);
      });

      test('should not crash on invalid events', () async {
        final states = <CalendarState>[];
        calendarBloc.stream.listen(states.add);

        // 测试无效的日期
        calendarBloc.add(CalendarEvent.monthChanged(DateTime(9999, 13, 32)));
        await TestUtils.waitForAsync();

        // 验证应用没有崩溃
        expect(states.last.status, isNot(equals(CalendarStatus.failure)));
      });
    });

    group('State consistency', () {
      test('should maintain consistent state across events', () async {
        final states = <CalendarState>[];
        calendarBloc.stream.listen(states.add);

        // 执行一系列事件
        calendarBloc.add(const CalendarEvent.started());
        await TestUtils.waitForAsync();

        calendarBloc
            .add(const CalendarEvent.viewTypeChanged(CalendarViewType.year));
        await TestUtils.waitForAsync();

        calendarBloc.add(CalendarEvent.monthChanged(DateTime(2025, 12, 1)));
        await TestUtils.waitForAsync();

        // 验证状态一致性
        final finalState = states.last;
        expect(finalState.viewType, equals(CalendarViewType.year));
        expect(finalState.displayMonthDate.month, equals(12));
        expect(finalState.displayMonthDate.year, equals(2025));
        expect(finalState.status, equals(CalendarStatus.success));
      });

      test('should not emit duplicate states', () async {
        final states = <CalendarState>[];
        calendarBloc.stream.listen(states.add);

        // 连续发送相同事件
        calendarBloc.add(const CalendarEvent.started());
        calendarBloc.add(const CalendarEvent.started());
        calendarBloc.add(const CalendarEvent.started());

        await TestUtils.waitForAsync();

        // 验证没有重复状态
        final uniqueStates = states.toSet();
        expect(states.length, equals(uniqueStates.length));
      });
    });
  });
}
