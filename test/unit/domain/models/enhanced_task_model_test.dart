import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/domain.dart';

void main() {
  group('Enhanced Task Model Tests', () {
    late DateTime testDate;
    late DateTime futureDate;

    setUp(() {
      testDate = DateTime.now();
      futureDate = testDate.add(const Duration(days: 7));
    });

    group('Task.create', () {
      test('should create a valid task with required fields', () {
        final task = Task.create(
          title: 'Test Task',
          dueDate: futureDate,
        );

        expect(task.title, 'Test Task');
        expect(task.dueDate, futureDate);
        expect(task.priority, Priority.urgentImportant);
        expect(task.isCompleted, false);
        expect(task.notes, '');
        expect(task.subtasks, isEmpty);
        expect(task.id, isNotEmpty);
      });

      test('should create task with all optional fields', () {
        final subtasks = [
          SubTask.create(parentTaskId: 'parent', title: 'Subtask 1'),
        ];

        final task = Task.create(
          title: 'Complete Task',
          dueDate: futureDate,
          notes: 'Important notes',
          priority: Priority.importantNotUrgent,
          subtasks: subtasks,
        );

        expect(task.title, 'Complete Task');
        expect(task.notes, 'Important notes');
        expect(task.priority, Priority.importantNotUrgent);
        expect(task.subtasks.length, 1);
      });

      test('should throw exception for empty title', () {
        expect(
          () => Task.create(title: '', dueDate: futureDate),
          throwsA(isA<TaskValidationException>()),
        );
      });

      test('should throw exception for title too long', () {
        final longTitle = 'a' * 201;
        expect(
          () => Task.create(title: longTitle, dueDate: futureDate),
          throwsA(isA<TaskValidationException>()),
        );
      });
    });

    group('Task completion', () {
      test('should mark task as completed', () {
        final task = Task.create(title: 'Test Task', dueDate: futureDate);
        final completedTask = task.markAsCompleted();

        expect(completedTask.isCompleted, true);
        expect(completedTask.completionDate, isNotNull);
        expect(completedTask.completionDate!.isBefore(DateTime.now().add(const Duration(seconds: 1))), true);
      });

      test('should mark task as incomplete', () {
        final task = Task.create(title: 'Test Task', dueDate: futureDate)
            .markAsCompleted();
        final incompleteTask = task.markAsIncomplete();

        expect(incompleteTask.isCompleted, false);
        expect(incompleteTask.completionDate, isNull);
      });

      test('should throw exception when marking completed task as completed', () {
        final task = Task.create(title: 'Test Task', dueDate: futureDate)
            .markAsCompleted();

        expect(
          () => task.markAsCompleted(),
          throwsA(isA<TaskBusinessRuleException>()),
        );
      });
    });

    group('Subtask management', () {
      test('should add subtask to task', () {
        final task = Task.create(title: 'Test Task', dueDate: futureDate);
        final updatedTask = task.addSubtask('New Subtask');

        expect(updatedTask.subtasks.length, 1);
        expect(updatedTask.subtasks.first.title, 'New Subtask');
        expect(updatedTask.subtasks.first.parentTaskId, task.id);
      });

      test('should remove subtask from task', () {
        final task = Task.create(title: 'Test Task', dueDate: futureDate)
            .addSubtask('Subtask 1')
            .addSubtask('Subtask 2');
        
        final subtaskId = task.subtasks.first.id;
        final updatedTask = task.removeSubtask(subtaskId);

        expect(updatedTask.subtasks.length, 1);
        expect(updatedTask.subtasks.first.title, 'Subtask 2');
      });

      test('should update subtask', () {
        final task = Task.create(title: 'Test Task', dueDate: futureDate)
            .addSubtask('Original Title');
        
        final subtaskId = task.subtasks.first.id;
        final updatedTask = task.updateSubtask(
          subtaskId,
          title: 'Updated Title',
          isCompleted: true,
        );

        expect(updatedTask.subtasks.first.title, 'Updated Title');
        expect(updatedTask.subtasks.first.isCompleted, true);
      });

      test('should calculate subtask completion rate', () {
        final task = Task.create(title: 'Test Task', dueDate: futureDate)
            .addSubtask('Subtask 1')
            .addSubtask('Subtask 2');
        
        // Complete one subtask
        final subtaskId = task.subtasks.first.id;
        final updatedTask = task.updateSubtask(subtaskId, isCompleted: true);

        expect(updatedTask.subtaskCompletionRate, 0.5);
      });
    });

    group('Task properties', () {
      test('should detect overdue task', () {
        final pastDate = testDate.subtract(const Duration(days: 1));
        final task = Task.create(title: 'Test Task', dueDate: pastDate);

        expect(task.isOverdue, true);
      });

      test('should not detect completed task as overdue', () {
        final pastDate = testDate.subtract(const Duration(days: 1));
        final task = Task.create(title: 'Test Task', dueDate: pastDate)
            .markAsCompleted();

        expect(task.isOverdue, false);
      });

      test('should return correct priority weight', () {
        final urgentImportant = Task.create(
          title: 'Test',
          dueDate: futureDate,
          priority: Priority.urgentImportant,
        );
        final notUrgentNotImportant = Task.create(
          title: 'Test',
          dueDate: futureDate,
          priority: Priority.notUrgentNotImportant,
        );

        expect(urgentImportant.priorityWeight, 4);
        expect(notUrgentNotImportant.priorityWeight, 1);
      });
    });
  });

  group('SubTask Tests', () {
    test('should create valid subtask', () {
      final subtask = SubTask.create(
        parentTaskId: 'parent123',
        title: 'Test Subtask',
      );

      expect(subtask.title, 'Test Subtask');
      expect(subtask.parentTaskId, 'parent123');
      expect(subtask.isCompleted, false);
      expect(subtask.id, isNotEmpty);
    });

    test('should toggle completion', () {
      final subtask = SubTask.create(
        parentTaskId: 'parent123',
        title: 'Test Subtask',
      );

      final completed = subtask.toggleCompletion();
      expect(completed.isCompleted, true);

      final incomplete = completed.toggleCompletion();
      expect(incomplete.isCompleted, false);
    });

    test('should update title', () {
      final subtask = SubTask.create(
        parentTaskId: 'parent123',
        title: 'Original Title',
      );

      final updated = subtask.updateTitle('New Title');
      expect(updated.title, 'New Title');
    });

    test('should throw exception for empty title', () {
      expect(
        () => SubTask.create(parentTaskId: 'parent123', title: ''),
        throwsA(isA<SubTaskException>()),
      );
    });
  });
}