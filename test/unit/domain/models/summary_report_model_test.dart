import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/models/summary_report_model.dart';
import 'package:mytodospace/domain/models/task_model.dart';

void main() {
  group('SummaryReport Model Tests', () {
    test('should create a summary report with all required fields', () {
      // Arrange
      final highlights = [
        Task(
          id: 'task-1',
          title: 'Important Task',
          notes: '',
          creationDate: DateTime.now(),
          dueDate: DateTime.now(),
          isCompleted: true,
          completionDate: DateTime.now(),
          priority: Priority.importantNotUrgent,
          subtasks: [],
        ),
      ];

      final distribution = {
        Priority.urgentImportant: 5,
        Priority.importantNotUrgent: 10,
        Priority.urgentNotImportant: 3,
        Priority.notUrgentNotImportant: 2,
      };

      final report = SummaryReport(
        period: '2025年9月',
        totalTasksCreated: 20,
        totalTasksCompleted: 15,
        completionRate: 75.0,
        quadrantDistribution: distribution,
        highlights: highlights,
      );

      // Assert
      expect(report.period, '2025年9月');
      expect(report.totalTasksCreated, 20);
      expect(report.totalTasksCompleted, 15);
      expect(report.completionRate, 75.0);
      expect(report.quadrantDistribution.length, 4);
      expect(report.highlights.length, 1);
    });

    test('should calculate completion rate correctly', () {
      // Test different completion rates
      final testCases = [
        {'created': 10, 'completed': 10, 'expected': 100.0},
        {'created': 10, 'completed': 5, 'expected': 50.0},
        {'created': 10, 'completed': 0, 'expected': 0.0},
        {'created': 0, 'completed': 0, 'expected': 0.0},
      ];

      for (final testCase in testCases) {
        final report = SummaryReport(
          period: 'Test Period',
          totalTasksCreated: testCase['created'] as int,
          totalTasksCompleted: testCase['completed'] as int,
          completionRate: testCase['expected'] as double,
          quadrantDistribution: {},
          highlights: [],
        );

        expect(report.completionRate, testCase['expected']);
      }
    });

    test('should handle empty highlights and distribution', () {
      // Arrange
      final report = SummaryReport(
        period: '2025年',
        totalTasksCreated: 0,
        totalTasksCompleted: 0,
        completionRate: 0.0,
        quadrantDistribution: {},
        highlights: [],
      );

      // Assert
      expect(report.quadrantDistribution, isEmpty);
      expect(report.highlights, isEmpty);
      expect(report.totalTasksCreated, 0);
      expect(report.totalTasksCompleted, 0);
    });

    test('should handle all priority distributions', () {
      // Arrange
      final distribution = {
        Priority.urgentImportant: 1,
        Priority.importantNotUrgent: 2,
        Priority.urgentNotImportant: 3,
        Priority.notUrgentNotImportant: 4,
      };

      final report = SummaryReport(
        period: 'Test',
        totalTasksCreated: 10,
        totalTasksCompleted: 5,
        completionRate: 50.0,
        quadrantDistribution: distribution,
        highlights: [],
      );

      // Assert
      expect(report.quadrantDistribution[Priority.urgentImportant], 1);
      expect(report.quadrantDistribution[Priority.importantNotUrgent], 2);
      expect(report.quadrantDistribution[Priority.urgentNotImportant], 3);
      expect(report.quadrantDistribution[Priority.notUrgentNotImportant], 4);
    });
  });
}