import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/models/task_model.dart';

void main() {
  group('Task Model Tests', () {
    test('should create a task with all required fields', () {
      // Arrange
      final now = DateTime.now();
      final task = Task(
        id: 'test-id',
        title: 'Test Task',
        notes: 'Test notes',
        creationDate: now,
        dueDate: now,
        isCompleted: false,
        completionDate: null,
        priority: Priority.urgentImportant,
        subtasks: [],
      );

      // Assert
      expect(task.id, 'test-id');
      expect(task.title, 'Test Task');
      expect(task.notes, 'Test notes');
      expect(task.isCompleted, false);
      expect(task.priority, Priority.urgentImportant);
      expect(task.subtasks, isEmpty);
    });

    test('should create a completed task with completion date', () {
      // Arrange
      final now = DateTime.now();
      final completionDate = now.add(const Duration(hours: 1));
      
      final task = Task(
        id: 'test-id',
        title: 'Completed Task',
        notes: '',
        creationDate: now,
        dueDate: now,
        isCompleted: true,
        completionDate: completionDate,
        priority: Priority.importantNotUrgent,
        subtasks: [],
      );

      // Assert
      expect(task.isCompleted, true);
      expect(task.completionDate, completionDate);
      expect(task.priority, Priority.importantNotUrgent);
    });

    test('should handle subtasks correctly', () {
      // Arrange
      final subtask1 = SubTask(
        id: 'sub-1',
        parentTaskId: 'task-1',
        title: 'Subtask 1',
        isCompleted: false,
      );
      
      final subtask2 = SubTask(
        id: 'sub-2',
        parentTaskId: 'task-1',
        title: 'Subtask 2',
        isCompleted: true,
      );

      final task = Task(
        id: 'task-1',
        title: 'Task with subtasks',
        notes: '',
        creationDate: DateTime.now(),
        dueDate: DateTime.now(),
        isCompleted: false,
        completionDate: null,
        priority: Priority.urgentNotImportant,
        subtasks: [subtask1, subtask2],
      );

      // Assert
      expect(task.subtasks.length, 2);
      expect(task.subtasks[0].title, 'Subtask 1');
      expect(task.subtasks[0].isCompleted, false);
      expect(task.subtasks[1].title, 'Subtask 2');
      expect(task.subtasks[1].isCompleted, true);
    });

    test('should test all priority levels', () {
      final priorities = [
        Priority.urgentImportant,
        Priority.importantNotUrgent,
        Priority.urgentNotImportant,
        Priority.notUrgentNotImportant,
      ];

      for (final priority in priorities) {
        final task = Task(
          id: 'test-${priority.name}',
          title: 'Task ${priority.name}',
          notes: '',
          creationDate: DateTime.now(),
          dueDate: DateTime.now(),
          isCompleted: false,
          completionDate: null,
          priority: priority,
          subtasks: [],
        );

        expect(task.priority, priority);
      }
    });
  });

  group('SubTask Model Tests', () {
    test('should create a subtask with all required fields', () {
      // Arrange
      final subtask = SubTask(
        id: 'sub-1',
        parentTaskId: 'parent-1',
        title: 'Test Subtask',
        isCompleted: false,
      );

      // Assert
      expect(subtask.id, 'sub-1');
      expect(subtask.parentTaskId, 'parent-1');
      expect(subtask.title, 'Test Subtask');
      expect(subtask.isCompleted, false);
    });

    test('should create a completed subtask', () {
      // Arrange
      final subtask = SubTask(
        id: 'sub-1',
        parentTaskId: 'parent-1',
        title: 'Completed Subtask',
        isCompleted: true,
      );

      // Assert
      expect(subtask.isCompleted, true);
    });
  });
}