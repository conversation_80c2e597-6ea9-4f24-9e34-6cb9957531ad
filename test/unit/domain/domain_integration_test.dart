import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/domain.dart';

void main() {
  group('Domain Layer Integration Tests', () {
    test('should create and manipulate tasks with all domain services', () {
      // Create a task using the enhanced factory method
      final task = Task.create(
        title: 'Integration Test Task',
        dueDate: DateTime.now().add(const Duration(days: 7)),
        priority: Priority.urgentImportant,
        notes: 'This is a test task for integration testing',
      );

      // Verify task creation
      expect(task.title, 'Integration Test Task');
      expect(task.priority, Priority.urgentImportant);
      expect(task.isCompleted, false);
      expect(task.priorityWeight, 4);

      // Add subtasks
      final taskWithSubtasks = task
          .addSubtask('First subtask')
          .addSubtask('Second subtask');

      expect(taskWithSubtasks.subtasks.length, 2);
      expect(taskWithSubtasks.subtaskCompletionRate, 0.0);

      // Complete one subtask
      final firstSubtaskId = taskWithSubtasks.subtasks.first.id;
      final taskWithCompletedSubtask = taskWithSubtasks.updateSubtask(
        firstSubtaskId,
        isCompleted: true,
      );

      expect(taskWithCompletedSubtask.subtaskCompletionRate, 0.5);

      // Complete the main task
      final completedTask = taskWithCompletedSubtask.markAsCompleted();
      expect(completedTask.isCompleted, true);
      expect(completedTask.completionDate, isNotNull);
      expect(completedTask.isOverdue, false);

      // Test task load calculation
      final tasks = [task, completedTask];
      final incompleteLoad = TaskLoadCalculationService.calculateDailyTaskLoad([task]);
      final mixedLoad = TaskLoadCalculationService.calculateDailyTaskLoad(tasks);

      expect(incompleteLoad, 4); // One urgent important task
      expect(mixedLoad, 4); // Completed task doesn't count

      // Test quadrant distribution
      final distribution = TaskLoadCalculationService.calculateQuadrantDistribution(tasks);
      expect(distribution[Priority.urgentImportant], 2);

      // Test completion rate
      final completionRate = TaskLoadCalculationService.calculateCompletionRate(tasks);
      expect(completionRate, 0.5); // One completed out of two

      // Test highlights calculation
      final highlights = HighlightsCalculationService.calculateHighlights([completedTask]);
      expect(highlights.length, 1);
      expect(highlights.first.id, completedTask.id);
    });

    test('should validate business rules correctly', () {
      // Test valid task passes validation
      final validTask = Task.create(
        title: 'Valid Task',
        dueDate: DateTime.now().add(const Duration(days: 1)),
      );

      expect(() => TaskValidationService.validateTask(validTask), returnsNormally);

      // Test invalid task fails validation
      expect(
        () => Task.create(title: '', dueDate: DateTime.now()),
        throwsA(isA<TaskValidationException>()),
      );

      // Test subtask validation
      expect(
        () => SubTask.create(parentTaskId: '', title: 'Test'),
        throwsA(isA<SubTaskException>()),
      );
    });

    test('should handle edge cases in load calculation', () {
      final now = DateTime.now();
      
      // Test with empty task list
      expect(TaskLoadCalculationService.calculateDailyTaskLoad([]), 0);
      expect(TaskLoadCalculationService.calculateMaxLoad({}), 0);
      expect(TaskLoadCalculationService.calculateCompletionRate([]), 0.0);

      // Test with all priorities
      final allPriorityTasks = [
        Task.create(title: 'Q1', dueDate: now, priority: Priority.urgentImportant),
        Task.create(title: 'Q2', dueDate: now, priority: Priority.importantNotUrgent),
        Task.create(title: 'Q3', dueDate: now, priority: Priority.urgentNotImportant),
        Task.create(title: 'Q4', dueDate: now, priority: Priority.notUrgentNotImportant),
      ];

      final totalLoad = TaskLoadCalculationService.calculateDailyTaskLoad(allPriorityTasks);
      expect(totalLoad, 10); // 4 + 3 + 2 + 1

      // Test heatmap opacity calculation
      expect(TaskLoadCalculationService.calculateHeatmapOpacity(5, 10), 0.5);
      expect(TaskLoadCalculationService.calculateHeatmapOpacity(0, 0), 0.0);
      expect(TaskLoadCalculationService.calculateHeatmapOpacity(15, 10), 1.0);
    });

    test('should create comprehensive summary reports', () {
      final now = DateTime.now();
      final completedTasks = [
        Task.create(
          title: 'Important Completed Task',
          dueDate: now,
          priority: Priority.urgentImportant,
        ).markAsCompleted(),
        Task.create(
          title: 'Simple Completed Task',
          dueDate: now,
          priority: Priority.notUrgentNotImportant,
        ).markAsCompleted(),
      ];

      // Test highlights calculation
      final highlights = HighlightsCalculationService.calculateHighlights(completedTasks);
      expect(highlights.isNotEmpty, true);
      
      // Higher priority task should be first highlight
      expect(highlights.first.priority, Priority.urgentImportant);

      // Test quadrant distribution
      final distribution = TaskLoadCalculationService.calculateQuadrantDistribution(completedTasks);
      expect(distribution[Priority.urgentImportant], 1);
      expect(distribution[Priority.notUrgentNotImportant], 1);

      // Test completion rate
      final completionRate = TaskLoadCalculationService.calculateCompletionRate(completedTasks);
      expect(completionRate, 1.0); // All tasks completed
    });
  });
}