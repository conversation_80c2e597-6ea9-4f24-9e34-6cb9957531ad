import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/domain.dart';

void main() {
  group('TaskLoadCalculationService Tests', () {
    late DateTime testDate;
    late List<Task> testTasks;

    setUp(() {
      testDate = DateTime.now();
      testTasks = [
        Task.create(
          title: 'Urgent Important Task',
          dueDate: testDate,
          priority: Priority.urgentImportant,
        ),
        Task.create(
          title: 'Important Not Urgent Task',
          dueDate: testDate,
          priority: Priority.importantNotUrgent,
        ),
        Task.create(
          title: 'Urgent Not Important Task',
          dueDate: testDate,
          priority: Priority.urgentNotImportant,
        ),
        Task.create(
          title: 'Not Urgent Not Important Task',
          dueDate: testDate,
          priority: Priority.notUrgentNotImportant,
        ),
      ];
    });

    group('calculateDailyTaskLoad', () {
      test('should calculate correct load for incomplete tasks', () {
        final load = TaskLoadCalculationService.calculateDailyTaskLoad(testTasks);
        
        // Expected: 4 + 3 + 2 + 1 = 10
        expect(load, 10);
      });

      test('should exclude completed tasks from load calculation', () {
        final completedTasks = testTasks.map((task) => task.markAsCompleted()).toList();
        final load = TaskLoadCalculationService.calculateDailyTaskLoad(completedTasks);
        
        expect(load, 0);
      });

      test('should handle mixed completed and incomplete tasks', () {
        final mixedTasks = [
          testTasks[0], // Urgent Important (4 points)
          testTasks[1].markAsCompleted(), // Completed, should not count
          testTasks[2], // Urgent Not Important (2 points)
        ];
        
        final load = TaskLoadCalculationService.calculateDailyTaskLoad(mixedTasks);
        
        // Expected: 4 + 0 + 2 = 6
        expect(load, 6);
      });

      test('should return 0 for empty task list', () {
        final load = TaskLoadCalculationService.calculateDailyTaskLoad([]);
        expect(load, 0);
      });
    });

    group('calculateTaskLoadForDates', () {
      test('should calculate load for multiple dates', () {
        final date1 = testDate;
        final date2 = testDate.add(const Duration(days: 1));
        
        final tasksByDate = {
          date1: [testTasks[0], testTasks[1]], // 4 + 3 = 7
          date2: [testTasks[2]], // 2
        };
        
        final result = TaskLoadCalculationService.calculateTaskLoadForDates(tasksByDate);
        
        expect(result[date1], 7);
        expect(result[date2], 2);
      });

      test('should handle empty dates', () {
        final tasksByDate = <DateTime, List<Task>>{
          testDate: [],
        };
        
        final result = TaskLoadCalculationService.calculateTaskLoadForDates(tasksByDate);
        
        expect(result[testDate], 0);
      });
    });

    group('calculateMaxLoad', () {
      test('should find maximum load from daily loads', () {
        final dailyLoads = {
          testDate: 10,
          testDate.add(const Duration(days: 1)): 5,
          testDate.add(const Duration(days: 2)): 15,
        };
        
        final maxLoad = TaskLoadCalculationService.calculateMaxLoad(dailyLoads);
        expect(maxLoad, 15);
      });

      test('should return 0 for empty loads', () {
        final maxLoad = TaskLoadCalculationService.calculateMaxLoad({});
        expect(maxLoad, 0);
      });
    });

    group('calculateHeatmapOpacity', () {
      test('should calculate correct opacity values', () {
        expect(TaskLoadCalculationService.calculateHeatmapOpacity(0, 10), 0.0);
        expect(TaskLoadCalculationService.calculateHeatmapOpacity(5, 10), 0.5);
        expect(TaskLoadCalculationService.calculateHeatmapOpacity(10, 10), 1.0);
        expect(TaskLoadCalculationService.calculateHeatmapOpacity(15, 10), 1.0); // Clamped
      });

      test('should handle zero max load', () {
        expect(TaskLoadCalculationService.calculateHeatmapOpacity(5, 0), 0.0);
      });
    });

    group('getPriorityWeight', () {
      test('should return correct weights for all priorities', () {
        expect(TaskLoadCalculationService.getPriorityWeight(Priority.urgentImportant), 4);
        expect(TaskLoadCalculationService.getPriorityWeight(Priority.importantNotUrgent), 3);
        expect(TaskLoadCalculationService.getPriorityWeight(Priority.urgentNotImportant), 2);
        expect(TaskLoadCalculationService.getPriorityWeight(Priority.notUrgentNotImportant), 1);
      });
    });

    group('calculateCompletionRate', () {
      test('should calculate correct completion rate', () {
        final mixedTasks = [
          testTasks[0].markAsCompleted(),
          testTasks[1].markAsCompleted(),
          testTasks[2], // Incomplete
          testTasks[3], // Incomplete
        ];
        
        final rate = TaskLoadCalculationService.calculateCompletionRate(mixedTasks);
        expect(rate, 0.5); // 2 out of 4 completed
      });

      test('should return 0 for empty task list', () {
        final rate = TaskLoadCalculationService.calculateCompletionRate([]);
        expect(rate, 0.0);
      });

      test('should return 1 for all completed tasks', () {
        final completedTasks = testTasks.map((task) => task.markAsCompleted()).toList();
        final rate = TaskLoadCalculationService.calculateCompletionRate(completedTasks);
        expect(rate, 1.0);
      });
    });

    group('calculateQuadrantDistribution', () {
      test('should calculate correct distribution', () {
        final distribution = TaskLoadCalculationService.calculateQuadrantDistribution(testTasks);
        
        expect(distribution[Priority.urgentImportant], 1);
        expect(distribution[Priority.importantNotUrgent], 1);
        expect(distribution[Priority.urgentNotImportant], 1);
        expect(distribution[Priority.notUrgentNotImportant], 1);
      });

      test('should handle empty task list', () {
        final distribution = TaskLoadCalculationService.calculateQuadrantDistribution([]);
        
        expect(distribution[Priority.urgentImportant], 0);
        expect(distribution[Priority.importantNotUrgent], 0);
        expect(distribution[Priority.urgentNotImportant], 0);
        expect(distribution[Priority.notUrgentNotImportant], 0);
      });

      test('should handle uneven distribution', () {
        final unevenTasks = [
          Task.create(title: 'Task 1', dueDate: testDate, priority: Priority.urgentImportant),
          Task.create(title: 'Task 2', dueDate: testDate, priority: Priority.urgentImportant),
          Task.create(title: 'Task 3', dueDate: testDate, priority: Priority.importantNotUrgent),
        ];
        
        final distribution = TaskLoadCalculationService.calculateQuadrantDistribution(unevenTasks);
        
        expect(distribution[Priority.urgentImportant], 2);
        expect(distribution[Priority.importantNotUrgent], 1);
        expect(distribution[Priority.urgentNotImportant], 0);
        expect(distribution[Priority.notUrgentNotImportant], 0);
      });
    });
  });
}