import 'package:flutter_test/flutter_test.dart';
import 'package:mytodospace/domain/domain.dart';

void main() {
  group('TaskValidationService Tests', () {
    late DateTime testDate;
    late DateTime futureDate;

    setUp(() {
      testDate = DateTime.now();
      futureDate = testDate.add(const Duration(days: 7));
    });

    group('validateTask', () {
      test('should validate a correct task', () {
        final task = Task.create(
          title: 'Valid Task',
          dueDate: futureDate,
        );

        expect(() => TaskValidationService.validateTask(task), returnsNormally);
      });

      test('should throw exception for empty title', () {
        final task = Task(
          id: 'test123',
          title: '',
          creationDate: testDate,
          dueDate: futureDate,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<TaskValidationException>()),
        );
      });

      test('should throw exception for title too long', () {
        final task = Task(
          id: 'test123',
          title: 'a' * 201,
          creationDate: testDate,
          dueDate: futureDate,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<TaskValidationException>()),
        );
      });

      test('should throw exception for future creation date', () {
        final futureCreationDate = testDate.add(const Duration(days: 1));
        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: futureCreationDate,
          dueDate: futureDate,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<DateValidationException>()),
        );
      });

      test('should throw exception for due date too far in future', () {
        final farFutureDate = testDate.add(const Duration(days: 4000));
        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: testDate,
          dueDate: farFutureDate,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<DateValidationException>()),
        );
      });

      test('should allow past due date before creation date', () {
        // This test verifies that past due tasks are allowed even if due date is before creation date
        final creationDate = DateTime.now().subtract(const Duration(hours: 1));
        final pastDueDate = DateTime.now().subtract(const Duration(hours: 2));

        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: creationDate,
          dueDate: pastDueDate, // Past due date before creation date - should be allowed
        );

        expect(() => TaskValidationService.validateTask(task), returnsNormally);
      });

      test('should throw exception for completed task without completion date', () {
        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: testDate,
          dueDate: futureDate,
          isCompleted: true,
          completionDate: null,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<TaskValidationException>()),
        );
      });

      test('should throw exception for incomplete task with completion date', () {
        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: testDate,
          dueDate: futureDate,
          isCompleted: false,
          completionDate: testDate,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<TaskValidationException>()),
        );
      });

      test('should throw exception for too many subtasks', () {
        final subtasks = List.generate(
          TaskValidationService.maxSubtasks + 1,
          (index) => SubTask(
            id: 'subtask_$index',
            parentTaskId: 'parent123',
            title: 'Subtask $index',
          ),
        );

        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: testDate,
          dueDate: futureDate,
          subtasks: subtasks,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<SubTaskException>()),
        );
      });

      test('should throw exception for duplicate subtask IDs', () {
        final subtasks = [
          const SubTask(
            id: 'duplicate_id',
            parentTaskId: 'parent123',
            title: 'Subtask 1',
          ),
          const SubTask(
            id: 'duplicate_id',
            parentTaskId: 'parent123',
            title: 'Subtask 2',
          ),
        ];

        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: testDate,
          dueDate: futureDate,
          subtasks: subtasks,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<SubTaskException>()),
        );
      });
    });

    group('subtask validation', () {
      test('should throw exception for empty subtask title', () {
        final subtasks = [
          const SubTask(
            id: 'subtask1',
            parentTaskId: 'parent123',
            title: '',
          ),
        ];

        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: testDate,
          dueDate: futureDate,
          subtasks: subtasks,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<SubTaskException>()),
        );
      });

      test('should throw exception for subtask title too long', () {
        final subtasks = [
          SubTask(
            id: 'subtask1',
            parentTaskId: 'parent123',
            title: 'a' * 201,
          ),
        ];

        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: testDate,
          dueDate: futureDate,
          subtasks: subtasks,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<SubTaskException>()),
        );
      });

      test('should throw exception for empty subtask ID', () {
        final subtasks = [
          const SubTask(
            id: '',
            parentTaskId: 'parent123',
            title: 'Valid Title',
          ),
        ];

        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: testDate,
          dueDate: futureDate,
          subtasks: subtasks,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<SubTaskException>()),
        );
      });

      test('should throw exception for empty parent task ID', () {
        final subtasks = [
          const SubTask(
            id: 'subtask1',
            parentTaskId: '',
            title: 'Valid Title',
          ),
        ];

        final task = Task(
          id: 'test123',
          title: 'Valid Title',
          creationDate: testDate,
          dueDate: futureDate,
          subtasks: subtasks,
        );

        expect(
          () => TaskValidationService.validateTask(task),
          throwsA(isA<SubTaskException>()),
        );
      });
    });
  });
}