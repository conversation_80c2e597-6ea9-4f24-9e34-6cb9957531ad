import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mockito/mockito.dart';
import 'package:mytodospace/features/tasks/presentation/task_list_panel.dart';
import 'package:mytodospace/features/tasks/presentation/widgets/high_performance_task_list.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_bloc.dart';
import 'package:mytodospace/features/tasks/bloc/task_list_state.dart';
import 'package:mytodospace/domain/models/task_model.dart';
import 'package:mytodospace/app/theme.dart';

class MockTaskListBloc extends Mock implements TaskListBloc {}

void main() {
  group('Enhanced Tab Navigation Tests', () {
    late MockTaskListBloc mockBloc;
    late List<Task> testTasks;

    setUp(() {
      mockBloc = MockTaskListBloc();
      
      // Create test tasks with different priorities
      testTasks = [
        Task.create(
          title: 'Urgent Important Task',
          dueDate: DateTime.now().add(const Duration(days: 1)),
          priority: Priority.urgentImportant,
        ),
        Task.create(
          title: 'Important Not Urgent Task',
          dueDate: DateTime.now().add(const Duration(days: 2)),
          priority: Priority.importantNotUrgent,
        ),
        Task.create(
          title: 'Urgent Not Important Task',
          dueDate: DateTime.now().add(const Duration(days: 3)),
          priority: Priority.urgentNotImportant,
        ),
        Task.create(
          title: 'Not Urgent Not Important Task',
          dueDate: DateTime.now().add(const Duration(days: 4)),
          priority: Priority.notUrgentNotImportant,
        ),
        // Add completed tasks to test progress indicators
        Task.create(
          title: 'Completed Urgent Important Task',
          dueDate: DateTime.now().add(const Duration(days: 1)),
          priority: Priority.urgentImportant,
        ).copyWith(isCompleted: true),
      ];
    });

    testWidgets('Tab navigation displays correct progress indicators', (WidgetTester tester) async {
      // Setup mock bloc state
      when(mockBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: testTasks,
          filteredTasks: testTasks,
          date: DateTime.now(),
        ),
      );
      when(mockBloc.stream).thenAnswer((_) => Stream.value(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: testTasks,
          filteredTasks: testTasks,
          date: DateTime.now(),
        ),
      ));

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider<TaskListBloc>.value(
            value: mockBloc,
            child: const Scaffold(
              body: TaskListPanel(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify tab structure exists
      expect(find.byType(TabBar), findsOneWidget);
      expect(find.byType(TabBarView), findsOneWidget);

      // Verify all 4 tabs are present
      expect(find.byType(Tab), findsNWidgets(4));

      // Check for progress indicators showing (incomplete/total) format
      // Urgent Important: 1 incomplete out of 2 total = (1/2)
      expect(find.text('(1/2)'), findsOneWidget);
      
      // Important Not Urgent: 1 incomplete out of 1 total = (1/1)
      expect(find.text('(1/1)'), findsOneWidget);
      
      // Urgent Not Important: 1 incomplete out of 1 total = (1/1)
      expect(find.text('(1/1)'), findsOneWidget);
      
      // Not Urgent Not Important: 1 incomplete out of 1 total = (1/1)
      expect(find.text('(1/1)'), findsOneWidget);
    });

    testWidgets('Tab icons are displayed correctly', (WidgetTester tester) async {
      when(mockBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: testTasks,
          filteredTasks: testTasks,
          date: DateTime.now(),
        ),
      );
      when(mockBloc.stream).thenAnswer((_) => Stream.value(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: testTasks,
          filteredTasks: testTasks,
          date: DateTime.now(),
        ),
      ));

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider<TaskListBloc>.value(
            value: mockBloc,
            child: const Scaffold(
              body: TaskListPanel(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify enhanced rounded icons are present
      expect(find.byIcon(Icons.warning_rounded), findsOneWidget);
      expect(find.byIcon(Icons.star_rounded), findsOneWidget);
      expect(find.byIcon(Icons.flash_on_rounded), findsOneWidget);
      expect(find.byIcon(Icons.low_priority_rounded), findsOneWidget);
    });

    testWidgets('Virtual lists are present in each tab', (WidgetTester tester) async {
      when(mockBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: testTasks,
          filteredTasks: testTasks,
          date: DateTime.now(),
        ),
      );
      when(mockBloc.stream).thenAnswer((_) => Stream.value(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: testTasks,
          filteredTasks: testTasks,
          date: DateTime.now(),
        ),
      ));

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider<TaskListBloc>.value(
            value: mockBloc,
            child: const Scaffold(
              body: TaskListPanel(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify HighPerformanceTaskList widgets are present
      expect(find.byType(HighPerformanceTaskList), findsNWidgets(4));
    });

    testWidgets('Tab switching works correctly', (WidgetTester tester) async {
      when(mockBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: testTasks,
          filteredTasks: testTasks,
          date: DateTime.now(),
        ),
      );
      when(mockBloc.stream).thenAnswer((_) => Stream.value(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: testTasks,
          filteredTasks: testTasks,
          date: DateTime.now(),
        ),
      ));

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider<TaskListBloc>.value(
            value: mockBloc,
            child: const Scaffold(
              body: TaskListPanel(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the second tab (Important Not Urgent)
      final secondTab = find.byIcon(Icons.star_rounded);
      expect(secondTab, findsOneWidget);
      
      await tester.tap(secondTab);
      await tester.pumpAndSettle();

      // Verify tab switching worked (this is a basic test)
      expect(find.byType(TabBarView), findsOneWidget);
    });

    testWidgets('Empty state is handled correctly', (WidgetTester tester) async {
      when(mockBloc.state).thenReturn(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: const [],
          filteredTasks: const [],
          date: DateTime.now(),
        ),
      );
      when(mockBloc.stream).thenAnswer((_) => Stream.value(
        TaskListState(
          status: TaskListStatus.success,
          currentOperation: TaskListOperation.none,
          tasks: const [],
          filteredTasks: const [],
          date: DateTime.now(),
        ),
      ));

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: BlocProvider<TaskListBloc>.value(
            value: mockBloc,
            child: const Scaffold(
              body: TaskListPanel(),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify tabs show (0/0) for empty state
      expect(find.text('(0/0)'), findsNWidgets(4));
    });
  });
}
