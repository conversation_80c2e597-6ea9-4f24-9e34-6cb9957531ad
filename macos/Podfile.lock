PODS:
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.50.4):
    - sqlite3/common (= 3.50.4)
  - sqlite3/common (3.50.4)
  - sqlite3/dbstatvtab (3.50.4):
    - sqlite3/common
  - sqlite3/fts5 (3.50.4):
    - sqlite3/common
  - sqlite3/math (3.50.4):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.50.4):
    - sqlite3/common
  - sqlite3/rtree (3.50.4):
    - sqlite3/common
  - sqlite3/session (3.50.4):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.50.4)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/math
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
    - sqlite3/session

DEPENDENCIES:
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - sqlite3_flutter_libs (from `Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/darwin`)

SPEC REPOS:
  trunk:
    - sqlite3

EXTERNAL SOURCES:
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  sqlite3_flutter_libs:
    :path: Flutter/ephemeral/.symlinks/plugins/sqlite3_flutter_libs/darwin

SPEC CHECKSUMS:
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  sqlite3: 73513155ec6979715d3904ef53a8d68892d4032b
  sqlite3_flutter_libs: 83f8e9f5b6554077f1d93119fe20ebaa5f3a9ef1

PODFILE CHECKSUM: 7eb978b976557c8c1cd717d8185ec483fd090a82

COCOAPODS: 1.16.2
