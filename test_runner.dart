import 'dart:io';
import 'dart:convert';

/// 自动化测试运行器
/// 
/// 功能：
/// - 运行所有测试套件
/// - 生成测试报告
/// - 验证代码覆盖率
/// - 检查UI设计稿还原度
/// - 性能基准测试
void main(List<String> args) async {
  print('🚀 开始执行全面自动化测试...\n');
  
  final testRunner = TestRunner();
  await testRunner.runAllTests();
}

class TestRunner {
  final List<TestResult> results = [];
  
  Future<void> runAllTests() async {
    try {
      // 1. 运行单元测试
      await _runUnitTests();
      
      // 2. 运行Widget测试
      await _runWidgetTests();
      
      // 3. 运行集成测试
      await _runIntegrationTests();
      
      // 4. 运行性能测试
      await _runPerformanceTests();
      
      // 5. 检查代码覆盖率
      await _checkCodeCoverage();
      
      // 6. 验证UI设计稿还原
      await _validateUIDesign();
      
      // 7. 生成测试报告
      await _generateTestReport();
      
      print('\n✅ 所有测试执行完成！');
      
    } catch (e) {
      print('\n❌ 测试执行失败: $e');
      exit(1);
    }
  }
  
  Future<void> _runUnitTests() async {
    print('📋 运行单元测试...');
    
    final result = await _runCommand([
      'flutter', 'test', 
      'test/unit/',
      '--reporter=json',
      '--coverage'
    ]);
    
    results.add(TestResult(
      name: '单元测试',
      success: result.exitCode == 0,
      output: result.stdout,
      duration: result.duration,
    ));
    
    if (result.exitCode == 0) {
      print('✅ 单元测试通过');
    } else {
      print('❌ 单元测试失败');
      print(result.stderr);
    }
  }
  
  Future<void> _runWidgetTests() async {
    print('🎨 运行Widget测试...');
    
    final result = await _runCommand([
      'flutter', 'test', 
      'test/widget/',
      '--reporter=json'
    ]);
    
    results.add(TestResult(
      name: 'Widget测试',
      success: result.exitCode == 0,
      output: result.stdout,
      duration: result.duration,
    ));
    
    if (result.exitCode == 0) {
      print('✅ Widget测试通过');
    } else {
      print('❌ Widget测试失败');
      print(result.stderr);
    }
  }
  
  Future<void> _runIntegrationTests() async {
    print('🔗 运行集成测试...');
    
    final result = await _runCommand([
      'flutter', 'test', 
      'test/integration/',
      '--reporter=json'
    ]);
    
    results.add(TestResult(
      name: '集成测试',
      success: result.exitCode == 0,
      output: result.stdout,
      duration: result.duration,
    ));
    
    if (result.exitCode == 0) {
      print('✅ 集成测试通过');
    } else {
      print('❌ 集成测试失败');
      print(result.stderr);
    }
  }
  
  Future<void> _runPerformanceTests() async {
    print('⚡ 运行性能测试...');
    
    final result = await _runCommand([
      'flutter', 'test', 
      'test/performance/',
      '--reporter=json'
    ]);
    
    results.add(TestResult(
      name: '性能测试',
      success: result.exitCode == 0,
      output: result.stdout,
      duration: result.duration,
    ));
    
    if (result.exitCode == 0) {
      print('✅ 性能测试通过');
    } else {
      print('❌ 性能测试失败');
      print(result.stderr);
    }
  }
  
  Future<void> _checkCodeCoverage() async {
    print('📊 检查代码覆盖率...');
    
    // 生成覆盖率报告
    final result = await _runCommand([
      'genhtml', 
      'coverage/lcov.info', 
      '-o', 'coverage/html'
    ]);
    
    if (result.exitCode == 0) {
      print('✅ 代码覆盖率报告生成成功');
      
      // 解析覆盖率数据
      final coverage = await _parseCoverageData();
      results.add(TestResult(
        name: '代码覆盖率',
        success: coverage >= 80.0,
        output: '覆盖率: ${coverage.toStringAsFixed(1)}%',
        duration: result.duration,
      ));
      
      if (coverage >= 80.0) {
        print('✅ 代码覆盖率达标: ${coverage.toStringAsFixed(1)}%');
      } else {
        print('⚠️ 代码覆盖率不足: ${coverage.toStringAsFixed(1)}% (目标: 80%)');
      }
    } else {
      print('❌ 代码覆盖率检查失败');
      results.add(TestResult(
        name: '代码覆盖率',
        success: false,
        output: result.stderr,
        duration: result.duration,
      ));
    }
  }
  
  Future<void> _validateUIDesign() async {
    print('🎨 验证UI设计稿还原...');
    
    final designFiles = [
      'ui/1.1新建任务.png',
      'ui/1.2编辑删除.png',
      'ui/1.3编辑任务.png',
      'ui/1.月视图.png',
      'ui/2.年视图.png',
      'ui/3.四象限视图.png',
      'ui/4.本月总结.png',
      'ui/5.本年总结.png',
    ];
    
    bool allDesignsValidated = true;
    final validationResults = <String>[];
    
    for (final designFile in designFiles) {
      final file = File(designFile);
      if (await file.exists()) {
        validationResults.add('✅ $designFile - 设计稿存在');
      } else {
        validationResults.add('❌ $designFile - 设计稿缺失');
        allDesignsValidated = false;
      }
    }
    
    results.add(TestResult(
      name: 'UI设计稿验证',
      success: allDesignsValidated,
      output: validationResults.join('\n'),
      duration: Duration(milliseconds: 100),
    ));
    
    if (allDesignsValidated) {
      print('✅ 所有UI设计稿验证通过');
    } else {
      print('❌ 部分UI设计稿缺失');
    }
  }
  
  Future<void> _generateTestReport() async {
    print('📄 生成测试报告...');
    
    final report = TestReport(results);
    final reportContent = report.generateHtmlReport();
    
    final reportFile = File('test_reports/test_report.html');
    await reportFile.create(recursive: true);
    await reportFile.writeAsString(reportContent);
    
    final summaryContent = report.generateSummary();
    final summaryFile = File('test_reports/test_summary.txt');
    await summaryFile.writeAsString(summaryContent);
    
    print('✅ 测试报告生成完成');
    print('📄 HTML报告: test_reports/test_report.html');
    print('📄 摘要报告: test_reports/test_summary.txt');
    
    // 打印摘要到控制台
    print('\n📊 测试摘要:');
    print(summaryContent);
  }
  
  Future<CommandResult> _runCommand(List<String> command) async {
    final stopwatch = Stopwatch()..start();
    
    final process = await Process.run(
      command.first,
      command.skip(1).toList(),
      workingDirectory: Directory.current.path,
    );
    
    stopwatch.stop();
    
    return CommandResult(
      exitCode: process.exitCode,
      stdout: process.stdout.toString(),
      stderr: process.stderr.toString(),
      duration: stopwatch.elapsed,
    );
  }
  
  Future<double> _parseCoverageData() async {
    try {
      final lcovFile = File('coverage/lcov.info');
      if (!await lcovFile.exists()) {
        return 0.0;
      }
      
      final content = await lcovFile.readAsString();
      final lines = content.split('\n');
      
      int totalLines = 0;
      int coveredLines = 0;
      
      for (final line in lines) {
        if (line.startsWith('LF:')) {
          totalLines += int.parse(line.substring(3));
        } else if (line.startsWith('LH:')) {
          coveredLines += int.parse(line.substring(3));
        }
      }
      
      if (totalLines == 0) return 0.0;
      return (coveredLines / totalLines) * 100;
    } catch (e) {
      print('解析覆盖率数据失败: $e');
      return 0.0;
    }
  }
}

class CommandResult {
  final int exitCode;
  final String stdout;
  final String stderr;
  final Duration duration;
  
  CommandResult({
    required this.exitCode,
    required this.stdout,
    required this.stderr,
    required this.duration,
  });
}

class TestResult {
  final String name;
  final bool success;
  final String output;
  final Duration duration;
  
  TestResult({
    required this.name,
    required this.success,
    required this.output,
    required this.duration,
  });
}

class TestReport {
  final List<TestResult> results;
  
  TestReport(this.results);
  
  String generateHtmlReport() {
    final buffer = StringBuffer();
    
    buffer.writeln('<!DOCTYPE html>');
    buffer.writeln('<html>');
    buffer.writeln('<head>');
    buffer.writeln('<title>企业级Todo应用测试报告</title>');
    buffer.writeln('<meta charset="UTF-8">');
    buffer.writeln('<style>');
    buffer.writeln(_getReportStyles());
    buffer.writeln('</style>');
    buffer.writeln('</head>');
    buffer.writeln('<body>');
    
    buffer.writeln('<h1>企业级Todo应用测试报告</h1>');
    buffer.writeln('<p>生成时间: ${DateTime.now()}</p>');
    
    // 测试摘要
    buffer.writeln('<h2>测试摘要</h2>');
    buffer.writeln('<div class="summary">');
    
    final totalTests = results.length;
    final passedTests = results.where((r) => r.success).length;
    final failedTests = totalTests - passedTests;
    final successRate = totalTests > 0 ? (passedTests / totalTests * 100) : 0;
    
    buffer.writeln('<div class="summary-item">');
    buffer.writeln('<span class="label">总测试数:</span>');
    buffer.writeln('<span class="value">$totalTests</span>');
    buffer.writeln('</div>');
    
    buffer.writeln('<div class="summary-item">');
    buffer.writeln('<span class="label">通过:</span>');
    buffer.writeln('<span class="value success">$passedTests</span>');
    buffer.writeln('</div>');
    
    buffer.writeln('<div class="summary-item">');
    buffer.writeln('<span class="label">失败:</span>');
    buffer.writeln('<span class="value failure">$failedTests</span>');
    buffer.writeln('</div>');
    
    buffer.writeln('<div class="summary-item">');
    buffer.writeln('<span class="label">成功率:</span>');
    buffer.writeln('<span class="value">${successRate.toStringAsFixed(1)}%</span>');
    buffer.writeln('</div>');
    
    buffer.writeln('</div>');
    
    // 详细结果
    buffer.writeln('<h2>详细结果</h2>');
    buffer.writeln('<div class="results">');
    
    for (final result in results) {
      final statusClass = result.success ? 'success' : 'failure';
      final statusIcon = result.success ? '✅' : '❌';
      
      buffer.writeln('<div class="result-item $statusClass">');
      buffer.writeln('<h3>$statusIcon ${result.name}</h3>');
      buffer.writeln('<p><strong>状态:</strong> ${result.success ? '通过' : '失败'}</p>');
      buffer.writeln('<p><strong>耗时:</strong> ${result.duration.inMilliseconds}ms</p>');
      
      if (result.output.isNotEmpty) {
        buffer.writeln('<details>');
        buffer.writeln('<summary>详细输出</summary>');
        buffer.writeln('<pre>${_escapeHtml(result.output)}</pre>');
        buffer.writeln('</details>');
      }
      
      buffer.writeln('</div>');
    }
    
    buffer.writeln('</div>');
    buffer.writeln('</body>');
    buffer.writeln('</html>');
    
    return buffer.toString();
  }
  
  String generateSummary() {
    final buffer = StringBuffer();
    
    buffer.writeln('企业级Todo应用测试摘要');
    buffer.writeln('=' * 50);
    buffer.writeln('生成时间: ${DateTime.now()}');
    buffer.writeln();
    
    final totalTests = results.length;
    final passedTests = results.where((r) => r.success).length;
    final failedTests = totalTests - passedTests;
    final successRate = totalTests > 0 ? (passedTests / totalTests * 100) : 0;
    
    buffer.writeln('测试统计:');
    buffer.writeln('  总测试数: $totalTests');
    buffer.writeln('  通过: $passedTests');
    buffer.writeln('  失败: $failedTests');
    buffer.writeln('  成功率: ${successRate.toStringAsFixed(1)}%');
    buffer.writeln();
    
    buffer.writeln('测试结果详情:');
    for (final result in results) {
      final status = result.success ? '✅ 通过' : '❌ 失败';
      final duration = '${result.duration.inMilliseconds}ms';
      buffer.writeln('  ${result.name}: $status ($duration)');
    }
    
    buffer.writeln();
    
    if (failedTests > 0) {
      buffer.writeln('失败的测试:');
      for (final result in results.where((r) => !r.success)) {
        buffer.writeln('  ❌ ${result.name}');
        if (result.output.isNotEmpty) {
          buffer.writeln('     ${result.output.split('\n').first}');
        }
      }
    } else {
      buffer.writeln('🎉 所有测试都通过了！');
    }
    
    return buffer.toString();
  }
  
  String _getReportStyles() {
    return '''
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        line-height: 1.6;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }
      
      h1, h2, h3 {
        color: #333;
      }
      
      .summary {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
      }
      
      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 4px;
      }
      
      .label {
        font-weight: bold;
        color: #666;
      }
      
      .value {
        font-size: 1.2em;
        font-weight: bold;
      }
      
      .value.success {
        color: #28a745;
      }
      
      .value.failure {
        color: #dc3545;
      }
      
      .results {
        display: grid;
        gap: 15px;
      }
      
      .result-item {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #ddd;
      }
      
      .result-item.success {
        border-left-color: #28a745;
      }
      
      .result-item.failure {
        border-left-color: #dc3545;
      }
      
      details {
        margin-top: 10px;
      }
      
      summary {
        cursor: pointer;
        font-weight: bold;
        color: #666;
      }
      
      pre {
        background: #f8f9fa;
        padding: 10px;
        border-radius: 4px;
        overflow-x: auto;
        font-size: 0.9em;
      }
    ''';
  }
  
  String _escapeHtml(String text) {
    return text
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&#x27;');
  }
}